# 数据源配置
spring:
    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        driverClassName: com.mysql.cj.jdbc.Driver
        dynamic:
            primary: master
            datasource:
            # 主库数据源
                master:
                    driver-class-name: com.mysql.cj.jdbc.Driver
                    url: *******************************************************************************************************************************************************************************************************************
                    username: root
                    password: Juzhun123321!
            # 从库数据源
#                slave:
#                    driver-class-name: com.mysql.cj.jdbc.Driver
#                    url: *********************************************************************************************************************************************************************************
#                    username: root
#                    password: Juzhun123321!
            druid:
                # 初始连接数
                initialSize: 5
                # 最小连接池数量
                minIdle: 30
                # 最大连接池数量
                maxActive: 30
                # 配置获取连接等待超时的时间
                maxWait: 60000
                # 配置连接超时时间
                connectTimeout: 30000
                # 配置网络超时时间
                socketTimeout: 60000
                # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
                timeBetweenEvictionRunsMillis: 60000
                # 配置一个连接在池中最小生存的时间，单位是毫秒
                minEvictableIdleTimeMillis: 300000
                # 配置一个连接在池中最大生存的时间，单位是毫秒
                maxEvictableIdleTimeMillis: 900000
                # 配置检测连接是否有效
                validationQuery: SELECT 1 FROM DUAL
                testWhileIdle: true
                testOnBorrow: false
                testOnReturn: false
                webStatFilter:
                    enabled: true
                statViewServlet:
                    enabled: true
                    # 设置白名单，不填则允许所有访问
                    allow:
                    url-pattern: /druid/*
                    # 控制台管理用户名和密码
                    login-username: ruoyi
                    login-password: 123456
                filter:
                    stat:
                        enabled: true
                        # 慢SQL记录
                        log-slow-sql: true
                        slow-sql-millis: 1000
                        merge-sql: true
                    wall:
                        config:
                            multi-statement-allow: true
    # redis 配置
    redis:
        # 地址
        host: ************
        # 端口，默认为6379
        port: 6379
        # 数据库索引
        database: 15
        # 密码
        password: juzhun2023
        # 连接超时时间
        timeout: 10s
        lettuce:
            pool:
                # 连接池中的最小空闲连接
                min-idle: 0
                # 连接池中的最大空闲连接
                max-idle: 8
                # 连接池的最大数据库连接数
                max-active: 8
                # #连接池最大阻塞等待时间（使用负值表示没有限制）
                max-wait: -1ms

# 添加R2配置项
cloud:
    r2:
        file-prefix: https://testup.veryshortvideos.com/
        access-key: 1b6cae32d2772f116e8cdeffc24341be
        secret-key: 8fb142abff43943760f63a456fb222ea4fa571428f0a295d60e8c4ee285a1d6f
        endpoint: https://ee93a2dd66a3a741438af78442815b3d.r2.cloudflarestorage.com
        bucket-name: testjava
        region: auto  # R2特殊区域标识

# 支付配置
payment:
  use:
    dev: true
pay:
    temp:
        id: 59
test-pid:
    temp:
        id: 62
test-pid-vip:
    temp:
        id: 182
ruoyi:
    downloadPrefix: https://test-java.shortplaycenter.com/files/
    uploadPath: https://test-java.shortplaycenter.com/prod-api/common/upload
# 蚂蚁支付配置
antom:
    publicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAq+oo9TO7aSyoDswrfYUwKJKnOxRRlG3C5bya7DVdeL8pOW0uUMH3D7QNQVZ53WTIFTnP9eD9xFnOFq87qVFS5JSIFmLrz3JxFO+0mgSapX7vU9Sx4EhxmxtoQw9RID+CNwrNy0aLK/YjMfqB0L7QgjY9hn6lkYJJYmt41rScXjBfsO2mLa3fHY/kD28b624w5Qvz+eaR/PhbuLFfUw1ePN0FqN+u3HMgFZZoTAw+amefYHyyMOkNjQco+eJDy3HpRYgTPxb7O7+ZaqVTRavQiyAI+UCP75Hl1I7/SMW3xdbvSr98VkQW+hLSTsS77AeLcwckgNF8sFkxl3dH0A6yvQIDAQAB
    url: https://test-java.shortplaycenter.com/api/antom/payment/notify
    paymentMethods: CARD,GOOGLEPAY,APPLEPAY
    uiUrl: https://test-java.shortplaycenter.com/opt/mayi/index.html
    locale: en_US
# useepay 支付配置
useepay:
    livemode: false
    domain: https://openapi1.uat.useepay.com
    createCustomerUrl: /api/v1/customers/create
    createInvoiceUrl: /api/v1/invoices/create
    createPaymentIntentsUrl: /api/v1/payment_intents/create
    getPaymentIntentUrl: /api/v1/payment_intents/
    createSubscriptions: /api/v1/subscriptions/create
    createInvoices: /api/v1/invoices/create
    rsaPublicKey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDfhBebK/ruaesQeq5y+X1VnoW/CWGilSVZqO2VMkU7LCGXOC5X2+dG4vg4e6p1U7BGgd+95PDoD9WUq3E3ekBddjqtKGyo9icpDp69Uw5D8N3QbseR/CNkmy2Ubxf1bW7hXtoGQrPJV4TWq1N+i85h+rQY6LgawlTXN6PP2EKAMQIDAQAB