package com.ruoyi.mapper;

import com.ruoyi.domain.ShortMovie;
import com.ruoyi.domain.ShortMovieRec;
import com.ruoyi.dto.ShortMovieRecDataDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 影片推荐Mapper接口
 *
 * <AUTHOR>
 * @date 2025-09-08
 */
public interface ShortMovieRecMapper
{
    /**
     * 查询影片推荐
     *
     * @param id 影片推荐主键
     * @return 影片推荐
     */
    public ShortMovieRec selectShortMovieRecById(Long id);

    /**
     * 查询影片推荐列表
     *
     * @param shortMovieRec 影片推荐
     * @return 影片推荐集合
     */
    public List<ShortMovieRec> selectShortMovieRecList(ShortMovieRec shortMovieRec);

    /**
     * 新增影片推荐
     *
     * @param shortMovieRec 影片推荐
     * @return 结果
     */
    public int insertShortMovieRec(ShortMovieRec shortMovieRec);

    /**
     * 修改影片推荐
     *
     * @param shortMovieRec 影片推荐
     * @return 结果
     */
    public int updateShortMovieRec(ShortMovieRec shortMovieRec);

    /**
     * 删除影片推荐
     *
     * @param id 影片推荐主键
     * @return 结果
     */
    public int deleteShortMovieRecById(Long id);

    /**
     * 批量删除影片推荐
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteShortMovieRecByIds(Long[] ids);

    List<ShortMovie> getMoviesByRec(@Param("nid") Integer nid, @Param("type") String type, @Param("ifApp") int ifApp, @Param("exTime") Date exTime, @Param("languageCode") String languageCode);

    int check(@Param("appId") Long appId, @Param("movieId") Long movieId, @Param("languageCode") String languageCode, @Param("rec") String rec);

    int updateStatus(ShortMovieRec shortMovieRec);

    List<ShortMovieRecDataDTO> pageQuery(ShortMovieRec shortMovieRec);

    List<ShortMovieRec> selectByMovieIds(List<Long> list);

    int deleteMovieRec(ShortMovieRec shortMovieRec);
}
