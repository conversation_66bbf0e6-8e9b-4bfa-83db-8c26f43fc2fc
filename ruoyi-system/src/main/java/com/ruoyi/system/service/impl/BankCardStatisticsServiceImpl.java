package com.ruoyi.system.service.impl;

import com.alibaba.fastjson2.JSON;

import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.BankCardStatistics;
import com.ruoyi.system.mapper.BankCardStatisticsMapper;
import com.ruoyi.system.service.IBankCardStatisticsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 银行卡统计Service业务层处理
 */
@Service
public class BankCardStatisticsServiceImpl implements IBankCardStatisticsService
{
    private static final Logger log = LoggerFactory.getLogger(BankCardStatisticsServiceImpl.class);

    @Autowired
    private BankCardStatisticsMapper bankCardStatisticsMapper;

    /**
     * 查询银行卡统计
     *
     * @param id 银行卡统计主键
     * @return 银行卡统计
     */
    @Override
    public BankCardStatistics selectBankCardStatisticsById(Long id)
    {
        return bankCardStatisticsMapper.selectBankCardStatisticsById(id);
    }

    /**
     * 根据银行卡号查询统计信息
     */
    @Override
    public BankCardStatistics selectBankCardStatisticsByCardNumber(String cardNumber)
    {
        if (StringUtils.isEmpty(cardNumber))
        {
            return null;
        }
        return bankCardStatisticsMapper.selectBankCardStatisticsByCardNumber(cardNumber);
    }

    /**
     * 查询银行卡统计列表
     *
     * @param bankCardStatistics 银行卡统计
     * @return 银行卡统计
     */
    @Override
    public List<BankCardStatistics> selectBankCardStatisticsList(BankCardStatistics bankCardStatistics)
    {
        return bankCardStatisticsMapper.selectBankCardStatisticsList(bankCardStatistics);
    }

    /**
     * 新增银行卡统计
     *
     * @param bankCardStatistics 银行卡统计
     * @return 结果
     */
    @Override
    @Transactional
    public int insertBankCardStatistics(BankCardStatistics bankCardStatistics)
    {
        bankCardStatistics.setCreateTime(DateUtils.getNowDate());
        return bankCardStatisticsMapper.insertBankCardStatistics(bankCardStatistics);
    }

    /**
     * 修改银行卡统计
     *
     * @param bankCardStatistics 银行卡统计
     * @return 结果
     */
    @Override
    @Transactional
    public int updateBankCardStatistics(BankCardStatistics bankCardStatistics)
    {
        bankCardStatistics.setUpdateTime(DateUtils.getNowDate());
        return bankCardStatisticsMapper.updateBankCardStatistics(bankCardStatistics);
    }

    /**
     * 批量删除银行卡统计
     *
     * @param ids 需要删除的银行卡统计主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteBankCardStatisticsByIds(Long[] ids)
    {
        return bankCardStatisticsMapper.deleteBankCardStatisticsByIds(ids);
    }

    /**
     * 删除银行卡统计信息
     *
     * @param id 银行卡统计主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteBankCardStatisticsById(Long id)
    {
        return bankCardStatisticsMapper.deleteBankCardStatisticsById(id);
    }

    /**
     * 获取或创建银行卡统计记录
     */
    @Override
    @Transactional
    public BankCardStatistics getOrCreateStatistics(String cardNumber)
    {
        if (StringUtils.isEmpty(cardNumber))
        {
            throw new IllegalArgumentException("银行卡号不能为空");
        }

        BankCardStatistics statistics = selectBankCardStatisticsByCardNumber(cardNumber);
        if (statistics == null)
        {
            // 创建新记录
            statistics = new BankCardStatistics();
            statistics.setCardNumber(cardNumber);
            statistics.setHistoryUserIds("[]");
            insertBankCardStatistics(statistics);
            log.info("创建新的银行卡统计记录: {}", cardNumber);
        }

        return statistics;
    }

    /**
     * 更新用户关联信息
     */
    @Override
    @Transactional
    public boolean updateUserAssociation(String cardNumber, Long userId, List<Long> allUserIds)
    {
        if (StringUtils.isEmpty(cardNumber) || userId == null)
        {
            return false;
        }

        try
        {
            // 合并用户ID列表
            Set<Long> userIdSet = new HashSet<>(allUserIds);
            userIdSet.add(userId);

            String historyUserIds = JSON.toJSONString(new ArrayList<>(userIdSet));
            int relatedUsers = userIdSet.size();

            int result = bankCardStatisticsMapper.updateUserAssociation(cardNumber, userId, historyUserIds, relatedUsers);

            if (result > 0)
            {
                log.info("更新银行卡用户关联信息: {} -> 用户数: {}", cardNumber, relatedUsers);
                return true;
            }
        }
        catch (Exception e)
        {
            log.error("更新银行卡用户关联信息失败: cardNumber={}", cardNumber, e);
        }

        return false;
    }

    /**
     * 增加订阅统计
     */
    @Override
    @Transactional
    public boolean addSubscriptionStats(String cardNumber, Integer count, BigDecimal amount)
    {
        if (StringUtils.isEmpty(cardNumber) || count <= 0 || amount.compareTo(BigDecimal.ZERO) <= 0)
        {
            return false;
        }

        try
        {
            int result = bankCardStatisticsMapper.updateSubscriptionStats(cardNumber, count, amount);

            if (result > 0)
            {
                log.info("更新银行卡订阅统计: {} -> 次数+{}, 金额+{}", cardNumber, count, amount);
                return true;
            }
        }
        catch (Exception e)
        {
            log.error("更新银行卡订阅统计失败: cardNumber={}", cardNumber, e);
        }

        return false;
    }

    /**
     * 增加充值统计
     */
    @Override
    @Transactional
    public boolean addRechargeStats(String cardNumber, Integer count, BigDecimal amount)
    {
        if (StringUtils.isEmpty(cardNumber) || count <= 0 || amount.compareTo(BigDecimal.ZERO) <= 0)
        {
            return false;
        }

        try
        {
            int result = bankCardStatisticsMapper.updateRechargeStats(cardNumber, count, amount);

            if (result > 0)
            {
                log.info("更新银行卡充值统计: {} -> 次数+{}, 金额+{}", cardNumber, count, amount);
                return true;
            }
        }
        catch (Exception e)
        {
            log.error("更新银行卡充值统计失败: cardNumber={}", cardNumber, e);
        }

        return false;
    }

    /**
     * 获取指定日期有订单的银行卡列表
     */
    @Override
    public List<String> getActiveCardNumbers(LocalDate targetDate)
    {
        if (targetDate == null)
        {
            targetDate = LocalDate.now().minusDays(1);
        }

        try
        {
            return bankCardStatisticsMapper.selectActiveCardNumbers(targetDate);
        }
        catch (Exception e)
        {
            log.error("获取活跃银行卡列表失败: targetDate={}", targetDate, e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取用户订阅开始时间
     */
    @Override
    public LocalDate getUserSubscriptionStartDate(Long userId)
    {
        if (userId == null)
        {
            return null;
        }

        try
        {
            return bankCardStatisticsMapper.selectUserSubscriptionStartDate(userId);
        }
        catch (Exception e)
        {
            log.error("获取用户订阅开始时间失败: userId={}", userId, e);
            return null;
        }
    }

    /**
     * 获取多用户使用的银行卡列表
     */
    @Override
    public List<BankCardStatistics> getMultiUserCards()
    {
        try
        {
            return bankCardStatisticsMapper.selectMultiUserCards();
        }
        catch (Exception e)
        {
            log.error("获取多用户银行卡列表失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 按状态统计银行卡数量
     */
    @Override
    public List<Object[]> countByStatus()
    {
        try
        {
            return bankCardStatisticsMapper.countByStatus();
        }
        catch (Exception e)
        {
            log.error("按状态统计银行卡数量失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 查询指定银行卡在指定日期的订单数据
     */
    @Override
    public List<Map<String, Object>> selectCardOrdersByDate(String cardNumber, LocalDate targetDate)
    {
        if (StringUtils.isEmpty(cardNumber) || targetDate == null)
        {
            return new ArrayList<>();
        }

        try
        {
            return bankCardStatisticsMapper.selectCardOrdersByDate(cardNumber, targetDate);
        }
        catch (Exception e)
        {
            log.error("查询银行卡订单数据失败: cardNumber={}, date={}", cardNumber, targetDate, e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取银行卡首次使用时间
     */
    @Override
    public LocalDate getCardFirstUseDate(String cardNumber)
    {
        if (StringUtils.isEmpty(cardNumber))
        {
            return null;
        }

        try
        {
            return bankCardStatisticsMapper.getCardFirstUseDate(cardNumber);
        }
        catch (Exception e)
        {
            log.error("获取银行卡首次使用时间失败: cardNumber={}", cardNumber, e);
            return null;
        }
    }

    /**
     * 获取银行卡在指定日期范围内的统计数据
     */
    @Override
    public Map<String, Object> getCardStatsByDateRange(String cardNumber, LocalDate startDate, LocalDate endDate)
    {
        if (StringUtils.isEmpty(cardNumber) || startDate == null || endDate == null)
        {
            return new HashMap<>();
        }

        try
        {
            return bankCardStatisticsMapper.getCardStatsByDateRange(cardNumber, startDate, endDate);
        }
        catch (Exception e)
        {
            log.error("获取银行卡日期范围统计数据失败: cardNumber={}, startDate={}, endDate={}", cardNumber, startDate, endDate, e);
            return new HashMap<>();
        }
    }

    /**
     * 获取银行卡在指定日期范围内有订阅订单的用户ID列表
     */
    @Override
    public List<Long> getCardSubscriptionUsersByDateRange(String cardNumber, LocalDate startDate, LocalDate endDate)
    {
        if (StringUtils.isEmpty(cardNumber) || startDate == null || endDate == null)
        {
            return new ArrayList<>();
        }

        try
        {
            return bankCardStatisticsMapper.getCardSubscriptionUsersByDateRange(cardNumber, startDate, endDate);
        }
        catch (Exception e)
        {
            log.error("获取银行卡日期范围用户列表失败: cardNumber={}, startDate={}, endDate={}", cardNumber, startDate, endDate, e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取银行卡的所有订阅续费用户ID列表
     */
    @Override
    public List<Long> getCardSubscriptionRenewalUsers(String cardNumber)
    {
        if (StringUtils.isEmpty(cardNumber))
        {
            return new ArrayList<>();
        }

        try
        {
            return bankCardStatisticsMapper.getCardSubscriptionRenewalUsers(cardNumber);
        }
        catch (Exception e)
        {
            log.error("获取银行卡订阅续费用户列表失败: cardNumber={}", cardNumber, e);
            return new ArrayList<>();
        }
    }
}