package com.ruoyi.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 退订对象 short_unsubscribe
 *
 * <AUTHOR>
 * @date 2025-09-22
 */
public class ShortUnsubscribe extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 选择app */
    @Excel(name = "选择app")
    private Long appId;

    /** 选择用户 */
    @Excel(name = "选择用户")
    private Long userId;

    /** 反馈内容 */
    @Excel(name = "反馈内容")
    private String content;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 自动添加当前系统时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "自动添加当前系统时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date addtime;

    /** 邮箱 */
    @Excel(name = "邮箱")
    private String email;

    /** 类型 */
    @Excel(name = "类型")
    private String type;

    /** 处理状态：0.未处理，1已处理 */
    @Excel(name = "处理状态：0.未处理，1已处理")
    private Long replyType;

    private String domain;

    private String isSubscriber;

    public String getIsSubscriber() {
        return isSubscriber;
    }

    public void setIsSubscriber(String isSubscriber) {
        this.isSubscriber = isSubscriber;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }

    public void setAppId(Long appId)
    {
        this.appId = appId;
    }

    public Long getAppId()
    {
        return appId;
    }

    public void setUserId(Long userId)
    {
        this.userId = userId;
    }

    public Long getUserId()
    {
        return userId;
    }

    public void setContent(String content)
    {
        this.content = content;
    }

    public String getContent()
    {
        return content;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }

    public void setAddtime(Date addtime)
    {
        this.addtime = addtime;
    }

    public Date getAddtime()
    {
        return addtime;
    }

    public void setEmail(String email)
    {
        this.email = email;
    }

    public String getEmail()
    {
        return email;
    }

    public void setType(String type)
    {
        this.type = type;
    }

    public String getType()
    {
        return type;
    }

    public void setReplyType(Long replyType)
    {
        this.replyType = replyType;
    }

    public Long getReplyType()
    {
        return replyType;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("appId", getAppId())
                .append("userId", getUserId())
                .append("content", getContent())
                .append("status", getStatus())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .append("addtime", getAddtime())
                .append("email", getEmail())
                .append("type", getType())
                .append("replyType", getReplyType())
                .toString();
    }
}
