<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.ShortUserUnlockVideoMapper">
    
    <resultMap type="ShortUserUnlockVideo" id="ShortUserUnlockVideoResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="appId"    column="app_id"    />
        <result property="unlockType"    column="unlock_type"    />
        <result property="movieId"    column="movie_id"    />
        <result property="videoId"    column="video_id"    />
        <result property="channelCoinId"    column="channel_coin_id"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectShortUserUnlockVideoVo">
        select id, user_id, app_id, unlock_type, movie_id, video_id, channel_coin_id, status, create_by, create_time, update_by, update_time, remark from short_user_unlock_video
    </sql>

    <select id="selectShortUserUnlockVideoList" parameterType="ShortUserUnlockVideo" resultMap="ShortUserUnlockVideoResult">
        SELECT
        suuv.id,
        suuv.user_id,
        suuv.app_id,
        suuv.unlock_type,
        suuv.movie_id,
        suuv.video_id,
        suuv.channel_coin_id,
        suuv.STATUS,
        suuv.create_by,
        suuv.create_time,
        suuv.update_by,
        suuv.update_time,
        suuv.remark,
        sa.`name` appName,
        su.username userName,
        sm.`name` movieName
        FROM
        short_user_unlock_video suuv
        LEFT JOIN short_app sa on suuv.app_id =sa.id
        LEFT JOIN short_user su on su.id = suuv.user_id
        LEFT JOIN short_movie sm on suuv.movie_id = sm.id
        <where>  
            <if test="userId != null "> and suuv.user_id = #{userId}</if>
            <if test="appId != null "> and suuv.app_id = #{appId}</if>
            <if test="unlockType != null  and unlockType != ''"> and suuv.unlock_type = #{unlockType}</if>
            <if test="movieId != null "> and suuv.movie_id = #{movieId}</if>
            <if test="videoId != null "> and suuv.video_id = #{videoId}</if>
            <if test="channelCoinId != null "> and suuv.channel_coin_id = #{channelCoinId}</if>
            <if test="status != null  and status != ''"> and suuv.status = #{status}</if>
        </where>
    </select>
    
    <select id="selectShortUserUnlockVideoById" parameterType="Long" resultMap="ShortUserUnlockVideoResult">
        <include refid="selectShortUserUnlockVideoVo"/>
        where id = #{id}
    </select>
    <select id="findVideoIdByMovieIdAndUser" resultType="java.lang.Integer">
        SELECT IFNULL(video_id, channel_coin_id) from short_user_unlock_video WHERE movie_id = #{movieId}
        <if test="userId != null">
            and user_id = #{userId}
        </if>
    </select>

    <insert id="insertShortUserUnlockVideo" parameterType="ShortUserUnlockVideo" useGeneratedKeys="true" keyProperty="id">
        insert into short_user_unlock_video
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="appId != null">app_id,</if>
            <if test="unlockType != null">unlock_type,</if>
            <if test="movieId != null">movie_id,</if>
            <if test="videoId != null">video_id,</if>
            <if test="channelCoinId != null">channel_coin_id,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="appId != null">#{appId},</if>
            <if test="unlockType != null">#{unlockType},</if>
            <if test="movieId != null">#{movieId},</if>
            <if test="videoId != null">#{videoId},</if>
            <if test="channelCoinId != null">#{channelCoinId},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>
    <insert id="insertShortUserUnlockVideoList">
        insert into short_user_unlock_video (user_id, app_id, unlock_type, movie_id, video_id, channel_coin_id, status,
        create_by, create_time, update_by, update_time, remark) values
        <foreach collection="unlockVideos" item="item" separator=",">
            (#{item.userId}, #{item.appId}, #{item.unlockType}, #{item.movieId}, #{item.videoId}, #{item.channelCoinId},
            #{item.status}, #{item.createBy}, #{item.createTime}, #{item.updateBy},#{item.updateTime},#{item.remark})
        </foreach>
    </insert>

    <update id="updateShortUserUnlockVideo" parameterType="ShortUserUnlockVideo">
        update short_user_unlock_video
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="appId != null">app_id = #{appId},</if>
            <if test="unlockType != null">unlock_type = #{unlockType},</if>
            <if test="movieId != null">movie_id = #{movieId},</if>
            <if test="videoId != null">video_id = #{videoId},</if>
            <if test="channelCoinId != null">channel_coin_id = #{channelCoinId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteShortUserUnlockVideoById" parameterType="Long">
        delete from short_user_unlock_video where id = #{id}
    </delete>

    <delete id="deleteShortUserUnlockVideoByIds" parameterType="String">
        delete from short_user_unlock_video where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteShortUserUnlockVideoByUserIdAndMovieId">
        delete from short_user_unlock_video where user_id = #{userId} and movie_id = #{movieId}
    </delete>
</mapper>