package com.ruoyi.web.controller.short_center.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.domain.ShortUserCollectAndHistory;
import com.ruoyi.service.IShortUserCollectAndHistoryService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 【请填写功能名称】Controller
 * 
 * <AUTHOR>
 * @date 2025-09-25
 */
@RestController
@RequestMapping("/system/history")
public class ShortUserCollectAndHistoryController extends BaseController
{
    @Autowired
    private IShortUserCollectAndHistoryService shortUserCollectAndHistoryService;

    /**
     * 查询【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:history:list')")
    @GetMapping("/list")
    public TableDataInfo list(ShortUserCollectAndHistory shortUserCollectAndHistory)
    {
        startPage();
        List<ShortUserCollectAndHistory> list = shortUserCollectAndHistoryService.selectShortUserCollectAndHistoryList(shortUserCollectAndHistory);
        return getDataTable(list);
    }

    /**
     * 导出【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:history:export')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ShortUserCollectAndHistory shortUserCollectAndHistory)
    {
        List<ShortUserCollectAndHistory> list = shortUserCollectAndHistoryService.selectShortUserCollectAndHistoryList(shortUserCollectAndHistory);
        ExcelUtil<ShortUserCollectAndHistory> util = new ExcelUtil<ShortUserCollectAndHistory>(ShortUserCollectAndHistory.class);
        util.exportExcel(response, list, "【请填写功能名称】数据");
    }

    /**
     * 获取【请填写功能名称】详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:history:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(shortUserCollectAndHistoryService.selectShortUserCollectAndHistoryById(id));
    }

    /**
     * 新增【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:history:add')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ShortUserCollectAndHistory shortUserCollectAndHistory)
    {
        return toAjax(shortUserCollectAndHistoryService.insertShortUserCollectAndHistory(shortUserCollectAndHistory));
    }

    /**
     * 修改【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:history:edit')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ShortUserCollectAndHistory shortUserCollectAndHistory)
    {
        return toAjax(shortUserCollectAndHistoryService.updateShortUserCollectAndHistory(shortUserCollectAndHistory));
    }

    /**
     * 删除【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:history:remove')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(shortUserCollectAndHistoryService.deleteShortUserCollectAndHistoryByIds(ids));
    }
}
