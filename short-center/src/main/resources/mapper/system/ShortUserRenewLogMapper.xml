<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.ShortUserRenewLogMapper">

    <resultMap type="ShortUserRenewLog" id="ShortUserRenewLogResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="type"    column="type"    />
        <result property="amount"    column="amount"    />
        <result property="content"    column="content"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="code"    column="code"    />
    </resultMap>

    <sql id="selectShortUserRenewLogVo">
        select id, user_id, type, amount, content, status, create_by, create_time, update_by, update_time, remark,code from short_user_renew_log
    </sql>

    <select id="selectShortUserRenewLogList" parameterType="ShortUserRenewLog" resultMap="ShortUserRenewLogResult">
        <include refid="selectShortUserRenewLogVo"/>
        <where>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="amount != null "> and amount = #{amount}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="code != null  and code != ''"> and code = #{code}</if>
        </where>
    </select>

    <select id="selectShortUserRenewLogById" parameterType="Long" resultMap="ShortUserRenewLogResult">
        <include refid="selectShortUserRenewLogVo"/>
        where id = #{id}
    </select>
    <select id="selectShortUserCodeIs51" resultType="java.lang.Long">
        SELECT
        s.user_id
        FROM
        short_user_renew_log s
        JOIN (
        SELECT
        user_id,
        MAX(id) AS max_id
        FROM
        short_user_renew_log
        WHERE
        user_id IN
        <foreach item="userId" collection="userIds" separator="," open="(" close=")">
            #{userId}
        </foreach>
        GROUP BY
        user_id
        ) latest_log ON s.user_id = latest_log.user_id AND s.id = latest_log.max_id
        WHERE
        s.CODE in ('51','ok');
    </select>
    <select id="selectShortUserCodeByUserId" resultType="java.lang.String">
        SELECT
        code
        FROM
        short_user_renew_log
        WHERE
        user_id = #{userId}
        ORDER BY
        id DESC
        LIMIT 1;
    </select>
    <select id="checkLast3ByUserId" resultType="java.lang.Integer">
        WITH last_three_codes AS (
            SELECT
                code
            FROM
                short_user_renew_log
            WHERE
                user_id = #{id}
            ORDER BY
                id DESC
            LIMIT 3
            )
        SELECT
            COUNT(DISTINCT code) = 1 AS are_codes_identical
        FROM
            last_three_codes
    </select>
    <select id="getBankCardByInitId" resultType="java.lang.String">
        SELECT bank_card from temp_payment_success_python WHERE payment_intent_id = #{paymentIntentId}
    </select>
    <select id="getByIds" resultType="java.lang.Long">
        SELECT sul.user_id
        FROM short_user_renew_log sul
                 JOIN (
            SELECT user_id, MAX(create_time) as latest_time
            FROM short_user_renew_log
            WHERE user_id IN
            <foreach item="id" collection="lsit" open="(" separator="," close=")">
                #{id}
            </foreach>
              AND code IN ('51', 'ok')
            GROUP BY user_id
        ) latest ON sul.user_id = latest.user_id AND sul.create_time = latest.latest_time
        WHERE sul.code IN ('51', 'ok')
    </select>
    <select id="getLastCodeByUserId" resultType="java.lang.String">
        SELECT `code` from short_user_renew_log WHERE user_id =#{id} ORDER BY  create_time desc LIMIT 1
    </select>


    <insert id="insertShortUserRenewLog" parameterType="ShortUserRenewLog" useGeneratedKeys="true" keyProperty="id">
        insert into short_user_renew_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="type != null">type,</if>
            <if test="amount != null">amount,</if>
            <if test="content != null">content,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="code != null">code,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="type != null">#{type},</if>
            <if test="amount != null">#{amount},</if>
            <if test="content != null">#{content},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="code != null">#{code},</if>
        </trim>
    </insert>

    <update id="updateShortUserRenewLog" parameterType="ShortUserRenewLog">
        update short_user_renew_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="type != null">type = #{type},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="content != null">content = #{content},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="code != null">code = #{code},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteShortUserRenewLogById" parameterType="Long">
        delete from short_user_renew_log where id = #{id}
    </delete>

    <delete id="deleteShortUserRenewLogByIds" parameterType="String">
        delete from short_user_renew_log where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>