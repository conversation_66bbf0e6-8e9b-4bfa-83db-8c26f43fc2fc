<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.ShortOrderMapper">

    <resultMap type="ShortOrder" id="ShortOrderResult">
        <result property="id"    column="id"    />
        <result property="appId"    column="app_id"    />
        <result property="userId"    column="user_id"    />
        <result property="vipId"    column="vip_id"    />
        <result property="linkId"    column="link_id"    />
        <result property="adid"    column="adid"    />
        <result property="payType"    column="pay_type"    />
        <result property="ordersn"    column="ordersn"    />
        <result property="merchantOrderId"    column="merchant_order_id"    />
        <result property="paymentIntentId"    column="paymentIntentId"    />
        <result property="paymentMethod"    column="paymentMethod"    />
        <result property="requestId"    column="requestId"    />
        <result property="paymentCurrency"    column="paymentCurrency"    />
        <result property="paymentAmount"    column="paymentAmount"    />
        <result property="feeCurrency"    column="feeCurrency"    />
        <result property="feeAmount"    column="feeAmount"    />
        <result property="amount"    column="amount"    />
        <result property="currency"    column="currency"    />
        <result property="status"    column="status"    />
        <result property="clientSecret"    column="clientSecret"    />
        <result property="rawData"    column="raw_data"    />
        <result property="linkTime"    column="link_time"    />
        <result property="other"    column="other"    />
        <result property="pushState"    column="push_state"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="createdAt"    column="createdAt"    />
        <result property="updatedAt"    column="updatedAt"    />
        <result property="payTime"    column="pay_time"    />
        <result property="pixelId" column="pixel_id"/>
        <result property="pixelStatus" column="pixel_status"/>
        <result property="isEmailMarketingOrder" column="is_email_marketing_order"/>
        <result property="payChannel" column="pay_channel" />
        <result property="disputeId" column="dispute_id" />
        <result property="disputeStatus" column="dispute_status" />
        <result property="appType" column="app_type"/>
        <result property="deviceType" column="device_type"/>
        <result property="subscriptionType" column="subscription_type"/>
        <result property="vipType" column="vip_type"/>
        <result property="upshift" column="upshift"/>
        <result property="originalPrice" column="original_price"/>
        <result property="markType" column="mark_type"/>
        <result property="country" column="country"/>
        <result property="cardNumber" column="card_number"/>

    </resultMap>

    <sql id="selectShortOrderVo">
        select id, app_id, user_id, vip_id, link_id, adid, pay_type, ordersn, merchant_order_id, paymentIntentId, paymentMethod, requestId, paymentCurrency, paymentAmount, feeCurrency, feeAmount, amount, currency, status, clientSecret, raw_data, link_time, other, push_state, create_by, create_time, update_by, update_time, remark, createdAt, updatedAt, pay_time, pixel_id, pixel_status, is_email_marketing_order,  pay_channel, dispute_id, dispute_status, app_type, device_type, subscription_type,vip_type,upshift,original_price,mark_type,country,card_number from short_order
    </sql>

    <select id="selectShortOrderList" parameterType="ShortOrder" resultMap="ShortOrderResult">
        <include refid="selectShortOrderVo"/>
        <where>
            <if test="appId != null "> and app_id = #{appId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="vipId != null "> and vip_id = #{vipId}</if>
            <if test="linkId != null "> and link_id = #{linkId}</if>
            <if test="adid != null "> and adid = #{adid}</if>
            <if test="payType != null  and payType != ''"> and pay_type = #{payType}</if>
            <if test="ordersn != null  and ordersn != ''"> and ordersn = #{ordersn}</if>
            <if test="merchantOrderId != null  and merchantOrderId != ''"> and merchant_order_id = #{merchantOrderId}</if>
            <if test="paymentIntentId != null  and paymentIntentId != ''"> and paymentIntentId = #{paymentIntentId}</if>
            <if test="paymentMethod != null  and paymentMethod != ''"> and paymentMethod = #{paymentMethod}</if>
            <if test="requestId != null  and requestId != ''"> and requestId = #{requestId}</if>
            <if test="paymentCurrency != null  and paymentCurrency != ''"> and paymentCurrency = #{paymentCurrency}</if>
            <if test="paymentAmount != null "> and paymentAmount = #{paymentAmount}</if>
            <if test="feeCurrency != null  and feeCurrency != ''"> and feeCurrency = #{feeCurrency}</if>
            <if test="feeAmount != null "> and feeAmount = #{feeAmount}</if>
            <if test="amount != null "> and amount = #{amount}</if>
            <if test="currency != null  and currency != ''"> and currency = #{currency}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="clientSecret != null  and clientSecret != ''"> and clientSecret = #{clientSecret}</if>
            <if test="rawData != null  and rawData != ''"> and raw_data = #{rawData}</if>
            <if test="linkTime != null "> and link_time = #{linkTime}</if>
            <if test="other != null  and other != ''"> and other = #{other}</if>
            <if test="pushState != null  and pushState != ''"> and push_state = #{pushState}</if>
            <if test="pixelStatus != null  and pixelStatus != ''"> and pixel_status = #{pixelStatus}</if>
            <if test="isEmailMarketingOrder != null"> and is_email_marketing_order = #{isEmailMarketingOrder}</if>
            <if test="disputeId != null  and disputeId != ''"> and dispute_id = #{disputeId}</if>
            <if test="markType != null  and markType != ''"> and mark_type = #{markType}</if>
            <if test="disputeStatus != null  and disputeStatus != ''"> and dispute_status = #{disputeStatus}</if>

            <if test="createdStartDate != null">
                AND IFNULL(DATE_FORMAT(create_time, '%Y-%m-%d'),DATE_FORMAT(createdAt, '%Y-%m-%d')) &gt;= #{createdStartDate}
            </if>
            <if test="createdEndDate != null">
                AND IFNULL(DATE_FORMAT(create_time, '%Y-%m-%d'),DATE_FORMAT(createdAt, '%Y-%m-%d')) &lt;= #{createdEndDate}
            </if>
            <if test="registerStartDate != null">
                AND DATE_FORMAT(link_time, '%Y-%m-%d') &gt;= #{registerStartDate}
            </if>
            <if test="registerEndDate != null">
                AND DATE_FORMAT(link_time, '%Y-%m-%d') &lt;= #{registerEndDate}
            </if>
           <!-- <if test="dto.paymentStartDate != null and dto.paymentEndDate != null">
                AND t1.pay_time BETWEEN #{dto.paymentStartDate} and #{dto.paymentEndDate}
            </if>-->
        </where>
    </select>

    <select id="selectShortOrderById" parameterType="Long" resultMap="ShortOrderResult">
        <include refid="selectShortOrderVo"/>
        where id = #{id}
    </select>
    <select id="selectByUserIdList" resultMap="ShortOrderResult">
        <include refid="selectShortOrderVo"/> where `status` = 'SUCCEEDED' and user_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <insert id="insertShortOrder" parameterType="ShortOrder" useGeneratedKeys="true" keyProperty="id">
        insert into short_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appId != null">app_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="vipId != null">vip_id,</if>
            <if test="linkId != null">link_id,</if>
            <if test="adid != null">adid,</if>
            <if test="payType != null">pay_type,</if>
            <if test="ordersn != null">ordersn,</if>
            <if test="merchantOrderId != null">merchant_order_id,</if>
            <if test="paymentIntentId != null">paymentIntentId,</if>
            <if test="paymentMethod != null">paymentMethod,</if>
            <if test="requestId != null">requestId,</if>
            <if test="paymentCurrency != null">paymentCurrency,</if>
            <if test="paymentAmount != null">paymentAmount,</if>
            <if test="feeCurrency != null">feeCurrency,</if>
            <if test="feeAmount != null">feeAmount,</if>
            <if test="amount != null">amount,</if>
            <if test="currency != null">currency,</if>
            <if test="status != null">status,</if>
            <if test="clientSecret != null">clientSecret,</if>
            <if test="rawData != null">raw_data,</if>
            <if test="linkTime != null">link_time,</if>
            <if test="other != null">other,</if>
            <if test="pushState != null">push_state,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="payTime != null">pay_time,</if>
            <if test="pixelId != null ">pixel_id,</if>
            <if test="pixelStatus != null ">pixel_status,</if>
            <if test="isEmailMarketingOrder != null">is_email_marketing_order,</if>
            <if test="payChannel != null "> pay_channel,</if>
            <if test="disputeId != null">dispute_id,</if>
            <if test="disputeStatus != null">dispute_status,</if>
            <if test="appType != null "> app_type,</if>
            <if test="deviceType != null "> device_type,</if>
            <if test="subscriptionType != null "> subscription_type,</if>
            <if test="vipType != null">vip_type,</if>
            <if test="upshift != null">upshift,</if>
            <if test="originalPrice != null">original_price,</if>
            <if test="markType != null">mark_type,</if>
            <if test="country != null">country,</if>
            <if test="cardNumber != null">card_number,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appId != null">#{appId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="vipId != null">#{vipId},</if>
            <if test="linkId != null">#{linkId},</if>
            <if test="adid != null">#{adid},</if>
            <if test="payType != null">#{payType},</if>
            <if test="ordersn != null">#{ordersn},</if>
            <if test="merchantOrderId != null">#{merchantOrderId},</if>
            <if test="paymentIntentId != null">#{paymentIntentId},</if>
            <if test="paymentMethod != null">#{paymentMethod},</if>
            <if test="requestId != null">#{requestId},</if>
            <if test="paymentCurrency != null">#{paymentCurrency},</if>
            <if test="paymentAmount != null">#{paymentAmount},</if>
            <if test="feeCurrency != null">#{feeCurrency},</if>
            <if test="feeAmount != null">#{feeAmount},</if>
            <if test="amount != null">#{amount},</if>
            <if test="currency != null">#{currency},</if>
            <if test="status != null">#{status},</if>
            <if test="clientSecret != null">#{clientSecret},</if>
            <if test="rawData != null">#{rawData},</if>
            <if test="linkTime != null">#{linkTime},</if>
            <if test="other != null">#{other},</if>
            <if test="pushState != null">#{pushState},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="payTime != null">#{payTime},</if>
            <if test="pixelId != null">#{pixelId},</if>
            <if test="pixelStatus != null">#{pixelStatus},</if>
            <if test="isEmailMarketingOrder != null">#{isEmailMarketingOrder},</if>
            <if test="payChannel != null">#{payChannel},</if>
            <if test="disputeId != null">#{disputeId},</if>
            <if test="disputeStatus != null">#{disputeStatus},</if>
            <if test="appType != null">#{appType},</if>
            <if test="deviceType != null">#{deviceType},</if>
            <if test="subscriptionType != null">#{subscriptionType},</if>
            <if test="vipType != null">#{vipType},</if>
            <if test="upshift != null">#{upshift},</if>
            <if test="originalPrice != null">#{originalPrice},</if>
            <if test="markType != null">#{markType},</if>
            <if test="country != null">#{country},</if>
            <if test="cardNumber != null">#{cardNumber},</if>
         </trim>
    </insert>

    <update id="updateShortOrder" parameterType="ShortOrder">
        update short_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="appId != null">app_id = #{appId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="vipId != null">vip_id = #{vipId},</if>
            <if test="linkId != null">link_id = #{linkId},</if>
            <if test="adid != null">adid = #{adid},</if>
            <if test="payType != null">pay_type = #{payType},</if>
            <if test="ordersn != null">ordersn = #{ordersn},</if>
            <if test="merchantOrderId != null">merchant_order_id = #{merchantOrderId},</if>
            <if test="paymentIntentId != null">paymentIntentId = #{paymentIntentId},</if>
            <if test="paymentMethod != null">paymentMethod = #{paymentMethod},</if>
            <if test="requestId != null">requestId = #{requestId},</if>
            <if test="paymentCurrency != null">paymentCurrency = #{paymentCurrency},</if>
            <if test="paymentAmount != null">paymentAmount = #{paymentAmount},</if>
            <if test="feeCurrency != null">feeCurrency = #{feeCurrency},</if>
            <if test="feeAmount != null">feeAmount = #{feeAmount},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="currency != null">currency = #{currency},</if>
            <if test="status != null">status = #{status},</if>
            <if test="clientSecret != null">clientSecret = #{clientSecret},</if>
            <if test="rawData != null">raw_data = #{rawData},</if>
            <if test="linkTime != null">link_time = #{linkTime},</if>
            <if test="other != null">other = #{other},</if>
            <if test="pushState != null">push_state = #{pushState},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="payTime != null">pay_time = #{payTime},</if>
            <if test="pixelId != null">pixel_id = #{pixelId},</if>
            <if test="pixelStatus != null">pixel_status = #{pixelStatus},</if>
            <if test="isEmailMarketingOrder != null">is_email_marketing_order = #{isEmailMarketingOrder},</if>
            <if test="payChannel != null">pay_channel = #{payChannel},</if>
            <if test="disputeId != null">dispute_id = #{disputeId},</if>
            <if test="disputeStatus != null">dispute_status = #{disputeStatus},</if>
            <if test="appType != null">app_type = #{appType},</if>
            <if test="deviceType != null">device_type = #{deviceType},</if>
            <if test="subscriptionType != null">subscription_type = #{subscriptionType},</if>
            <if test="vipType != null">vip_type = #{vipType},</if>
            <if test="upshift != null">upshift = #{upshift},</if>
            <if test="originalPrice != null">original_price = #{originalPrice},</if>
            <if test="markType != null">mark_type = #{markType},</if>
            <if test="country != null">country = #{country},</if>
            <if test="cardNumber != null">card_number = #{cardNumber},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateBatchOrder">
        <foreach collection="list" item="item" separator=";">
            update short_order set app_type = #{item.appType} , subscription_type = #{item.subscriptionType},device_type = #{item.deviceType} where id = #{item.id}
        </foreach>
    </update>
    <update id="updateUpshift">
        UPDATE short_order SET upshift = 1,update_time = NOW() WHERE id in(SELECT id
        FROM (
        SELECT id,subscription_type,
        ROW_NUMBER() OVER (PARTITION BY subscription_type ORDER BY pay_time DESC) as rn
        FROM short_order
        WHERE user_id = #{userId}
        AND status = 'SUCCEEDED'
        AND pay_type = '订阅'
        and subscription_type &lt; #{subscriptionType}
        ) t
        WHERE rn = 1
        ORDER BY subscription_type)
    </update>
    <update id="updateMarkTypeByAppIdAndNullEmail">
        UPDATE short_order
        SET mark_type = 0
        WHERE user_id IN (
            SELECT id FROM short_user
            WHERE  1=1
            <if test="appId != null">AND app_id  = #{appId}</if>
            <if test="appId != null">AND (email IS NULL OR email = '')</if>

        );

        UPDATE short_order o
            JOIN (
            SELECT
            so.user_id,
            MAX(so.id) AS last_order_id
            FROM short_order so
            JOIN short_user su ON so.user_id = su.id
            WHERE 1=1
            <if test="appId != null">AND su.app_id  = #{appId}</if>
            <if test="appId != null">AND (su.email IS NULL OR su.email = '')</if>
            AND so.status = 'SUCCEEDED'
            and (pay_type = '订阅' or pay_type = '订阅续费')
            GROUP BY so.user_id
            ) last_orders ON o.id = last_orders.last_order_id
            SET o.mark_type = 1;
    </update>
    <update id="updateMarkTypeByAppId">
        UPDATE short_order
        SET mark_type = 0
        WHERE user_id IN (
            SELECT id
            FROM short_user
            WHERE app_id = #{appId}
              AND email > ''
              AND email IN (
                SELECT email
                FROM short_user
                WHERE app_id = #{appId}
                  AND email > ''
                GROUP BY email
                HAVING COUNT(*) = 1
            )
        );

        UPDATE short_order o
            JOIN (
            SELECT
            so.user_id,
            MAX(so.id) AS last_order_id
            FROM short_order so
            JOIN short_user su ON so.user_id = su.id
            WHERE su.app_id = #{appId}
            AND su.email > ''
            AND so.status = 'SUCCEEDED'
            and (so.pay_type= '订阅' or so.pay_type ='订阅续费')
            AND su.email IN (
            SELECT email
            FROM short_user
            WHERE app_id = #{appId}
            AND email > ''
            GROUP BY email
            HAVING COUNT(*) = 1
            )
            GROUP BY so.user_id
            ) last_orders ON o.id = last_orders.last_order_id
            SET o.mark_type = 1;
    </update>
    <update id="updateMarkTypeByEmail">
        -- 第一步：创建临时表存储每个邮箱的最新成功订单ID
        CREATE TEMPORARY TABLE temp_latest_orders AS
        SELECT
            o.id AS order_id
        FROM short_order o
                 JOIN short_user u ON o.user_id = u.id
                 JOIN (
            SELECT
                email,
                MAX(o.create_time) AS latest_time
            FROM short_order o
                     JOIN short_user u ON o.user_id = u.id
            WHERE u.app_id = 25
              AND u.email > ''
              and (o.pay_type= '订阅' or o.pay_type ='订阅续费')
              AND o.status = 'SUCCEEDED' -- 假设成功状态为'success'
              AND u.email IN (
                SELECT email
                FROM short_user
                WHERE app_id = 25
                  AND email > ''
                GROUP BY email
                HAVING COUNT(*) > 1
            )
            GROUP BY email
        ) latest ON u.email = latest.email AND o.create_time = latest.latest_time
        WHERE o.status = 'SUCCEEDED';

-- 第二步：先将所有符合条件的订单mark_type设为0
        UPDATE short_order
        SET mark_type = 0
        WHERE user_id IN (
            SELECT id
            FROM short_user
            WHERE app_id = 25
              AND email > ''
              AND email IN (
                SELECT email
                FROM short_user
                WHERE app_id = 25
                  AND email > ''
                GROUP BY email
                HAVING COUNT(*) > 1
            )
        );

-- 第三步：将每个邮箱的最新成功订单mark_type设为1
        UPDATE short_order o
            JOIN temp_latest_orders t ON o.id = t.order_id
            SET o.mark_type = 1;

-- 第四步：删除临时表
        DROP TEMPORARY TABLE IF EXISTS temp_latest_orders;
    </update>
    <update id="updateMarkTypeNotStatus">
        UPDATE short_order SET mark_type = 0 WHERE `status` != 'SUCCEEDED';
        UPDATE short_order SET mark_type = 0 WHERE pay_type = '充值';
    </update>
    <update id="updateMarkTypeInUserIds">
        UPDATE short_order
        SET mark_type = 0
        WHERE
           pay_type != '充值'
        <if test="userIds != null and userIds.size() > 0">
            AND user_id IN
            <foreach item="userId" index="index" collection="userIds" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        AND status = 'SUCCEEDED'
        and mark_type is null;

        UPDATE short_order
        SET mark_type = 1
        WHERE
            id = (
            SELECT id FROM (
                               SELECT id
                               FROM short_order
                               WHERE
                                pay_type != '充值'
                                <if test="userIds != null and userIds.size() > 0">
                                    AND user_id IN
                                    <foreach item="userId" index="index" collection="userIds" open="(" separator="," close=")">
                                        #{userId}
                                    </foreach>
                                </if>
                                AND status = 'SUCCEEDED'
                               ORDER BY create_time DESC
                                   LIMIT 1
                           ) AS latest_order
        );
    </update>
    <update id="updateMarkTypeInUserIds0">
        UPDATE short_order
        SET mark_type = 0
        WHERE
        pay_type != '充值'
        <if test="userIds != null and userIds.size() > 0">
            AND user_id IN
            <foreach item="userId" index="index" collection="userIds" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        AND status = 'SUCCEEDED'
        and mark_type is null;
    </update>
    <update id="updateMarkTypeByUserId">
        UPDATE short_order SET mark_type = #{markType} WHERE id = #{userId}
    </update>

    <delete id="deleteShortOrderById" parameterType="Long">
        delete from short_order where id = #{id}
    </delete>

    <delete id="deleteShortOrderByIds" parameterType="String">
        delete from short_order where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="orderOverView" resultType="com.ruoyi.vo.ShortOrderOverViewVO">
        SELECT
            tmp3.user_count,
            tmp1.user_pay_count,
            tmp1.pay_amount_sum,
            TRUNCATE((tmp1.user_pay_count/tmp3.user_count)*100,2) pay_rate,
            TRUNCATE((tmp2.ios_pay_count/tmp3.ios_count)*100,2) ios_pay_rate,
            TRUNCATE((tmp2.android_pay_count/tmp3.android_count)*100,2) android_pay_rate
        FROM
            (
                SELECT
                    COUNT(DISTINCT CASE WHEN status = 'SUCCEEDED' THEN user_id END) user_pay_count,
                    IFNULL(SUM( CASE WHEN status = 'SUCCEEDED' THEN paymentAmount END),0.00) pay_amount_sum
                FROM short_order
            ) AS tmp1,
            (
                SELECT
                    count(DISTINCT CASE WHEN t1.status = 'SUCCEEDED' AND t2.phone_version='Apple' THEN t2.id END) ios_pay_count,
                    count(DISTINCT CASE WHEN t1.status = 'SUCCEEDED' AND t2.phone_version!='Apple' THEN t2.id END) android_pay_count
                FROM short_order t1
                         LEFT JOIN short_user t2 ON t1.user_id=t2.id
            ) AS tmp2,
            (
                SELECT
                    COUNT(id) user_count,
                    COUNT(DISTINCT CASE WHEN phone_version='Apple' THEN id END) ios_count,
                    COUNT(DISTINCT CASE WHEN phone_version!='Apple' THEN id END) android_count
                FROM short_user
            ) AS tmp3
    </select>


    <select id="orderStatisticView" resultType="com.ruoyi.vo.ShortOrderStatisticVO">
        SELECT
            tmp1.user_count,
            tmp1.user_pay_count,
            tmp1.recharge_count,
            tmp1.subscribe_count,
            tmp1.renewal_count,
            TRUNCATE((tmp1.subscribe_count/tmp1.order_pay_count)*100,2) subscribe_rate,
            tmp1.pay_sum_amount,
            tmp1.google_pay_count,
            TRUNCATE((tmp1.google_pay_count/tmp1.order_pay_count)*100,2) google_pay_rate,
            tmp1.card_pay_count,
            TRUNCATE((tmp1.card_pay_count/tmp1.order_pay_count)*100,2) card_pay_rate,
            tmp1.apple_pay_count,
            TRUNCATE((tmp1.apple_pay_count/tmp1.order_pay_count)*100,2) apple_pay_rate,
            IFNULL(TRUNCATE((tmp1.today_subscribe_sum/tmp1.today_recharge_sum)*100,2),0.00) subscribe_recharge_rate
        FROM
            (
                SELECT
                    COUNT(DISTINCT t1.user_id) user_count,
                    COUNT(DISTINCT CASE WHEN t1.status = 'SUCCEEDED' THEN t1.user_id END) user_pay_count,
                    COUNT(DISTINCT CASE WHEN t1.status = 'SUCCEEDED' THEN t1.id END) order_pay_count,
                    COUNT(CASE WHEN t1.pay_type = '充值' AND t1.status = 'SUCCEEDED' THEN t1.id END) recharge_count,
                    COUNT(CASE WHEN t1.pay_type = '订阅' AND t1.status = 'SUCCEEDED' THEN t1.id END) subscribe_count,
                    COUNT(CASE WHEN t1.pay_type = '订阅续费' AND t1.status = 'SUCCEEDED' THEN t1.id END) renewal_count,
                    SUM(CASE WHEN t1.status = 'SUCCEEDED' THEN t1.paymentAmount ELSE 0.00 END) pay_sum_amount,
                    COUNT(CASE WHEN t1.status = 'SUCCEEDED' AND t1.paymentMethod='card' THEN t1.id END) card_pay_count,
                    COUNT(CASE WHEN t1.status = 'SUCCEEDED' AND t1.paymentMethod='googlepay' THEN t1.id END) google_pay_count,
                    COUNT(CASE WHEN t1.status = 'SUCCEEDED' AND t1.paymentMethod='applepay' THEN t1.id END) apple_pay_count,
                    SUM(CASE WHEN t1.pay_type = '充值' AND t1.status = 'SUCCEEDED' AND DATE(IFNULL(t1.update_time,t1.updatedAt))=CURDATE() THEN t1.paymentAmount ELSE 0.00 END) today_recharge_sum,
                    SUM(CASE WHEN t1.pay_type = '订阅' AND t1.status = 'SUCCEEDED' AND DATE(IFNULL(t1.update_time,t1.updatedAt))=CURDATE() THEN t1.paymentAmount ELSE 0.00 END) today_subscribe_sum
                FROM short_order t1
                LEFT JOIN short_user t2 ON t1.user_id=t2.id
                LEFT JOIN short_sem_link t3 ON t1.link_id=t3.id
                LEFT JOIN short_app t4 ON t1.app_id=t4.id
                WHERE 1=1
                <if test="dto.orderId != null">
                    AND t1.id= #{dto.orderId}
                </if>
                <if test="dto.pixelStatus != null">
                    AND t1.pixel_status= #{dto.pixelStatus}
                </if>
                <if test="dto.orderStatus != null and dto.orderStatus != ''">
                    AND t1.`status`= #{dto.orderStatus}
                </if>
                <if test="dto.orderId != null">
                    AND t1.id= #{dto.orderId}
                </if>
                <if test="dto.search != null and dto.search != ''">
                    AND t1.paymentIntentId=#{dto.search}
                </if>
                <if test="dto.userId != null">
                    AND t1.user_id= #{dto.userId}
                </if>
                <if test="dto.paymentStartDate != null and dto.paymentEndDate != null">
                    AND t1.pay_time BETWEEN #{dto.paymentStartDate} and #{dto.paymentEndDate}
                    AND t1.`status`= 'SUCCEEDED'
                </if>
                <if test="dto.registerStartDate != null and dto.registerEndDate != null">
                    AND t1.link_time BETWEEN #{dto.registerStartDate} and #{dto.registerEndDate}
                </if>
                <if test="dto.adInfo != null and dto.adInfo != ''">
                    AND t1.other LIKE concat ('%',#{dto.adInfo},'%')
                </if>
                <if test="dto.adName != null and dto.adName != ''">
                    AND t1.other LIKE concat ('%',#{dto.adName},'%')
                </if>
                <if test="dto.linkName != null and dto.linkName != ''">
                    AND t3.`name` LIKE concat ('%',#{dto.linkName},'%')
                </if>
                <if test="dto.payType != null and dto.payType != ''">
                    AND t1.pay_type= #{dto.payType}
                </if>
                <if test="dto.paymentMethod != null and dto.paymentMethod != ''">
                    AND t1.paymentMethod= #{dto.paymentMethod}
                </if>
                <if test="dto.paymentAmount != null">
                    AND t1.paymentAmount= #{dto.paymentAmount}
                </if>
                <if test="dto.phoneVersion != null and dto.phoneVersion != ''">
                    AND t1.phone_version= #{dto.phoneVersion}
                </if>
            ) tmp1
    </select>


    <select id="orderPageList" resultType="com.ruoyi.vo.ShortOrderVO">
        SELECT
        t1.id,
        t1.app_id,
        t1.user_id,
        t1.adid,
        t1.paymentIntentId,
        t1.other ad_info,
        t1.other other,
        t1.vip_type vipType,
        t3.`name` link_name,
        t3.movie_id movieId,
        t4.`name` app_name,
        t1.ordersn,
        t1.pay_type,
        t1.paymentMethod,
        t1.paymentAmount,
        t1.`status`,
        t2.phone_version,t2.`language`,
        t1.link_time,
        t1.pixel_id,
        t1.pixel_status,
        t1.original_price originalPrice,
        t1.mark_type markType,
        IFNULL(t1.update_time,t1.updatedAt) update_time,
        t1.pay_time ,
        t2.country country_name,
        case when t1.vip_type = '0' then t5.subscription_type
             when t1.vip_type = '1' then t1.subscription_type
             else '0' end as subscriptionType,
        t4.type,
        t2.state userState,
        IFNULL(t3.`language`,'en-US') link_language,
        t7.`name` payName,
        t7.pay_channel payChannel
        FROM short_order t1
        LEFT JOIN short_user t2 ON t1.user_id=t2.id
        LEFT JOIN short_sem_link t3 ON t1.link_id=t3.id
        LEFT JOIN short_app t4 ON t1.app_id=t4.id
        LEFT JOIN short_vip t5 on t5.id =t1.vip_id
        LEFT JOIN short_link_vip t6 on t1.link_id = t6.link_id and t1.vip_id = t6.id
        LEFT JOIN short_extplats t7 on t7.id = t4.extplats_id
        WHERE 1=1
        <if test="loggedInUserId != null and loggedInUserId != 1">
            AND t3.create_by = #{loggedInUserId}
        </if>
        <if test="dto.childIds != null and dto.childIds.size() > 0">
            AND t3.create_by IN
            <foreach item="childId" index="index" collection="dto.childIds" open="(" separator="," close=")">
                #{childId}
            </foreach>
        </if>
        <if test="dto.orderId != null">
            AND t1.id= #{dto.orderId}
        </if>
        <if test="dto.pixelStatus != null">
            AND t1.pixel_status= #{dto.pixelStatus}
        </if>
        <if test="dto.extplatsId != null">
            AND t4.extplats_id= #{dto.extplatsId}
        </if>
        <if test="dto.subscriptionType != null and dto.subscriptionType != 'pay_all'"> and t5.subscription_type = #{dto.subscriptionType} and t6.subscription_type = #{dto.subscriptionType}</if>
        <if test="dto.subscriptionType == 'pay_all'"> and t5.subscription_type in(0,7,30,365) </if>
        <if test="dto.adid != null and dto.adid != ''">
            and t1.adid = #{dto.adid}
        </if>
        <if test="dto.orderStatus != null and dto.orderStatus != ''">
            AND t1.`status`= #{dto.orderStatus}
        </if>
        <if test="dto.paymentIntentId != null and dto.paymentIntentId != ''">
            AND t1.paymentIntentId=#{dto.paymentIntentId}
        </if>
        <if test="dto.search != null and dto.search != ''">
            AND t1.ordersn=#{dto.search}
        </if>
        <if test="dto.userId != null">
            AND t1.user_id= #{dto.userId}
        </if>
        <if test="dto.paymentStartDate != null and dto.paymentEndDate != null">
        AND t1.pay_time BETWEEN #{dto.paymentStartDate} and #{dto.paymentEndDate}
        AND t1.`status`= 'SUCCEEDED'
        </if>

        <if test="dto.registerStartDate != null and dto.registerEndDate != null">
            AND t1.link_time BETWEEN #{dto.registerStartDate} and #{dto.registerEndDate}
        </if>

        <if test="dto.adInfo != null and dto.adInfo != ''">
            AND t1.other LIKE concat ('%',#{dto.adInfo},'%')
        </if>
        <if test="dto.adName != null and dto.adName != ''">
            AND t1.other LIKE concat ('%',#{dto.adName},'%')
        </if>
        <if test="dto.linkName != null and dto.linkName != ''">
            AND t3.`name` LIKE concat ('%',#{dto.linkName},'%')
        </if>
        <if test="dto.payType != null and dto.payType != ''">
            AND t1.pay_type= #{dto.payType}
        </if>
        <if test="dto.payTypes != null and dto.payTypes.size() > 0">
            AND t1.pay_type IN
            <foreach item="item" index="index" collection="dto.payTypes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.type != null">
            AND t4.type= #{dto.type}
        </if>
        <if test="dto.paymentMethod != null and dto.paymentMethod != ''">
            AND t1.paymentMethod= #{dto.paymentMethod}
        </if>
        <if test="dto.paymentAmount != null">
            AND t1.paymentAmount= #{dto.paymentAmount}
        </if>
        <if test="dto.phoneVersion != null and dto.phoneVersion != ''">
            AND t2.phone_version= #{dto.phoneVersion}
        </if>
        <if test="dto.appName != null and dto.appName != ''">
            AND t4.`name`= #{dto.appName}
        </if>
        <if test="dto.appId != null and dto.appId != ''">
            AND t1.app_id = #{dto.appId}
        </if>
        <if test="dto.appIds != null and dto.appIds.size() > 0">
            AND t1.app_id IN
            <foreach item="item" index="index" collection="dto.appIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.countryName != null and dto.countryName != ''">
            AND t2.country= #{dto.countryName}
        </if>
        <if test="dto.language != null and dto.language != ''">
            AND t3.language= #{dto.language}
        </if>
        ORDER BY t1.id DESC
    </select>

    <select id="payTopCountry" resultType="com.ruoyi.common.core.domain.KvEntity">
--         SELECT tmp1.country_name FROM
--             (
--                 SELECT IFNULL(t3.country_name,t4.country_name) country_name
--                 FROM short_order t1
--                          LEFT JOIN short_user t2 ON t1.user_id=t2.id
--                          LEFT JOIN ipv4_code t3 ON INET_ATON(t2.ip) BETWEEN t3.ip_min AND t3.ip_max
--                          LEFT JOIN ipv6_code t4 ON INET_ATON(t2.ip) BETWEEN t4.ip_min AND t4.ip_max
--                 WHERE t1.status = 'SUCCEEDED'
--             ) tmp1
--         WHERE tmp1.country_name IS NOT NULL
--         GROUP BY tmp1.country_name
--         ORDER BY COUNT(tmp1.country_name) DESC LIMIT 5
        SELECT
            SUM(IFNULL(t1.paymentAmount,0.00)) bigDecimalKey,
            IFNULL(t2.country,'未知') stringKey
        FROM short_order t1
        LEFT JOIN short_user t2 ON t1.user_id=t2.id
        WHERE t1.status = 'SUCCEEDED'
        GROUP BY t2.country
        ORDER BY bigDecimalKey DESC LIMIT 20
    </select>
    <select id="selectByPaymentIntentId" resultMap="ShortOrderResult">
        <include refid="selectShortOrderVo"/>
        where paymentIntentId = #{pid}
    </select>
    <select id="findFirstByUserAndPay_typeAndStatusOrderByUpdatedAtDesc"
            resultType="com.ruoyi.domain.ShortOrder">
        <include refid="selectShortOrderVo"/>
        WHERE
            user_id = #{userId}
          AND STATUS = #{status}
          AND pay_type = #{payType}
        ORDER BY update_time desc LIMIT 1
    </select>
    <select id="selectNewByUserId" resultType="java.lang.String">
        SELECT paymentMethod from short_order WHERE user_id = #{userId} and `status` = 'SUCCEEDED' ORDER BY pay_time DESC LIMIT 1
    </select>

    <!-- 首页数据统计查询 -->
    <select id="dashboardStatistics" resultType="com.ruoyi.vo.ShortOrderDashboardVO">
        SELECT
        <!-- 用户数量统计 -->
        COUNT(DISTINCT so.user_id) totalUserCount,
        COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' THEN so.user_id END) totalPayUserCount,
        COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' AND su.phone_version = 'Apple' THEN so.user_id END) totalIosPayUserCount,
        COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' AND su.phone_version != 'Apple' THEN so.user_id END) totalAndroidPayUserCount,
        COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '订阅' THEN so.user_id END) totalSubscribeUserCount,
        COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '订阅续费' THEN so.user_id END) totalRenewalUserCount,
        -- 金额统计
        SUM(CASE WHEN so.status = 'SUCCEEDED' THEN so.paymentAmount ELSE 0 END) totalPayAmount,
        SUM(CASE WHEN so.status = 'SUCCEEDED' AND su.phone_version = 'Apple' THEN so.paymentAmount ELSE 0 END) totalIosPayAmount,
        SUM(CASE WHEN so.status = 'SUCCEEDED' AND su.phone_version != 'Apple' THEN so.paymentAmount ELSE 0 END) totalAndroidPayAmount,
        SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '订阅' THEN so.paymentAmount ELSE 0 END) totalSubscribeAmount,
        SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '订阅' AND su.phone_version = 'Apple' THEN so.paymentAmount ELSE 0 END) totalIosSubscribeAmount,
        SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '订阅' AND su.phone_version != 'Apple' THEN so.paymentAmount ELSE 0 END) totalAndroidSubscribeAmount,
        SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '订阅续费' THEN so.paymentAmount ELSE 0 END) totalRenewalAmount,
        SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '充值' THEN so.paymentAmount ELSE 0 END) totalRechargeAmount,
        SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '充值' AND su.phone_version = 'Apple' THEN so.paymentAmount ELSE 0 END) totalIosRechargeAmount,
        SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '充值' AND su.phone_version != 'Apple' THEN so.paymentAmount ELSE 0 END) totalAndroidRechargeAmount,
        -- 计算比率
        TRUNCATE((COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' THEN so.user_id END) / NULLIF(COUNT(DISTINCT so.user_id), 0)) * 100, 2) totalPayRate,
        TRUNCATE((COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' AND su.phone_version = 'Apple' THEN so.user_id END) / NULLIF(COUNT(DISTINCT CASE WHEN su.phone_version = 'Apple' THEN so.user_id END), 0)) * 100, 2) totalIosPayRate,
        TRUNCATE((COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' AND su.phone_version != 'Apple' THEN so.user_id END) / NULLIF(COUNT(DISTINCT CASE WHEN su.phone_version != 'Apple' THEN so.user_id END), 0)) * 100, 2) totalAndroidPayRate,
        -- 订阅收入占比
        TRUNCATE((SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '订阅' THEN so.paymentAmount ELSE 0 END) /
        NULLIF(SUM(CASE WHEN so.status = 'SUCCEEDED' THEN so.paymentAmount ELSE 0 END), 0)) * 100, 2) totalSubscribeRatio,
        TRUNCATE((SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '订阅' AND su.phone_version = 'Apple' THEN so.paymentAmount ELSE 0 END) /
        NULLIF(SUM(CASE WHEN so.status = 'SUCCEEDED' AND su.phone_version = 'Apple' THEN so.paymentAmount ELSE 0 END), 0)) * 100, 2) totalIosSubscribeRatio,
        TRUNCATE((SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '订阅' AND su.phone_version != 'Apple' THEN so.paymentAmount ELSE 0 END) /
        NULLIF(SUM(CASE WHEN so.status = 'SUCCEEDED' AND su.phone_version != 'Apple' THEN so.paymentAmount ELSE 0 END), 0)) * 100, 2) totalAndroidSubscribeRatio,
        -- 人均统计
        TRUNCATE(SUM(CASE WHEN so.status = 'SUCCEEDED' THEN so.paymentAmount ELSE 0 END) / NULLIF(COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' THEN so.user_id END), 0), 2) avgPayAmount,
        TRUNCATE(SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '订阅' THEN so.paymentAmount ELSE 0 END) / NULLIF(COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '订阅' THEN so.user_id END), 0), 2) avgSubscribeAmount,
        TRUNCATE(SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '订阅续费' THEN so.paymentAmount ELSE 0 END) / NULLIF(COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '订阅续费' THEN so.user_id END), 0), 2) avgRenewalAmount,
        TRUNCATE(SUM(CASE WHEN so.status = 'SUCCEEDED' AND su.phone_version = 'Apple' THEN so.paymentAmount ELSE 0 END) / NULLIF(COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' AND su.phone_version = 'Apple' THEN so.user_id END), 0), 2) avgIosPayAmount,
        TRUNCATE(SUM(CASE WHEN so.status = 'SUCCEEDED' AND su.phone_version != 'Apple' THEN so.paymentAmount ELSE 0 END) / NULLIF(COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' AND su.phone_version != 'Apple' THEN so.user_id END), 0), 2) avgAndroidPayAmount
        FROM
        short_order so
        LEFT JOIN short_user su ON so.user_id = su.id
        LEFT JOIN short_sem_link slink ON so.link_id = slink.id
        LEFT JOIN short_app t4 ON so.app_id = t4.id
        WHERE
        1=1
            <if test="loggedInUserId != null and loggedInUserId != 1">
                AND slink.create_by = #{loggedInUserId}
            </if>
            <!-- 筛选条件 -->
            <if test="dto.appId != null">
                AND so.app_id = #{dto.appId}
            </if>
            <if test="dto.appIds != null and dto.appIds.size() > 0">
                AND so.app_id IN
                <foreach item="item" index="index" collection="dto.appIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.userId != null">
                AND so.user_id = #{dto.userId}
            </if>
            <if test="dto.pixelStatus != null">
                AND so.pixel_status= #{dto.pixelStatus}
            </if>
            <if test="dto.linkId != null">
                AND so.link_id = #{dto.linkId}
            </if>
            <if test="dto.orderStatus != null and dto.orderStatus != ''">
                AND so.status = #{dto.orderStatus}
            </if>
            <if test="dto.payType != null and dto.payType != ''">
                AND so.pay_type = #{dto.payType}
            </if>
            <if test="dto.countryName != null and dto.countryName != ''">
                AND su.country = #{dto.countryName}
            </if>
            <if test="dto.paymentMethod != null and dto.paymentMethod != ''">
                AND so.paymentMethod = #{dto.paymentMethod}
            </if>
            <if test="dto.adid != null and dto.adid != ''">
                AND so.adid = #{dto.adid}
            </if>
            <if test="dto.adInfo != null and dto.adInfo != ''">
                AND so.other LIKE CONCAT('%', #{dto.adInfo}, '%')
            </if>
            <if test="dto.adName != null and dto.adName != ''">
                AND so.other LIKE CONCAT('%', #{dto.adName}, '%')
            </if>
            <if test="dto.linkName != null and dto.linkName != ''">
                AND slink.name LIKE CONCAT('%', #{dto.linkName}, '%')
            </if>
            <if test="dto.appName != null and dto.appName != ''">
                AND t4.name = #{dto.appName}
            </if>
            <if test="dto.type != null">
                AND t4.type = #{dto.type}
            </if>
            <if test="dto.phoneVersion != null and dto.phoneVersion != ''">
                AND su.phone_version = #{dto.phoneVersion}
            </if>
            <!-- 时间范围筛选 -->
            <if test="dto.registerStartDate != null and dto.registerStartDate != ''">
                AND so.link_time &gt;= #{dto.registerStartDate}
            </if>
            <if test="dto.registerEndDate != null and dto.registerEndDate != ''">
                AND so.link_time &lt;= #{dto.registerEndDate}
            </if>
            <if test="dto.paymentStartDate != null and dto.paymentStartDate != ''">
                AND so.pay_time &gt;= #{dto.paymentStartDate}
            </if>
            <if test="dto.paymentEndDate != null and dto.paymentEndDate != ''">
                AND so.pay_time &lt;= #{dto.paymentEndDate}
            </if>
            <if test="dto.search != null and dto.search != ''">
                AND (so.ordersn = #{dto.search} OR so.paymentIntentId = #{dto.search})
            </if>
    </select>

    <!-- 获取上期订阅用户数 -->
    <select id="getLastPeriodSubscribeUserCount" resultType="java.lang.Long">
        SELECT
            COUNT(DISTINCT so.user_id)
        FROM
            short_order so
            LEFT JOIN short_user su ON so.user_id = su.id
            LEFT JOIN short_sem_link slink ON so.link_id = slink.id
            LEFT JOIN short_app sa ON so.app_id = sa.id
        WHERE
            so.status = 'SUCCEEDED'
            AND so.pay_type = '订阅'
            AND DATE_FORMAT(so.pay_time, '%Y-%m') = DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 MONTH), '%Y-%m')
            <if test="loggedInUserId != null and loggedInUserId != 1">
                AND slink.create_by = #{loggedInUserId}
            </if>
            <if test="dto.userId != null">
                AND so.user_id = #{dto.userId}
            </if>
            <if test="dto.pixelStatus != null">
                AND so.pixel_status= #{dto.pixelStatus}
            </if>
            <if test="dto.appId != null">
                AND so.app_id = #{dto.appId}
            </if>
            <if test="dto.appIds != null and dto.appIds.size() > 0">
                AND so.app_id IN
                <foreach item="item" index="index" collection="dto.appIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.countryName != null and dto.countryName != ''">
                AND su.country = #{dto.countryName}
            </if>
            <if test="dto.linkId != null">
                AND so.link_id = #{dto.linkId}
            </if>
            <if test="dto.adid != null and dto.adid != ''">
                AND so.adid = #{dto.adid}
            </if>
            <if test="dto.adInfo != null and dto.adInfo != ''">
                AND so.other LIKE CONCAT('%', #{dto.adInfo}, '%')
            </if>
            <if test="dto.adName != null and dto.adName != ''">
                AND so.other LIKE CONCAT('%', #{dto.adName}, '%')
            </if>
            <if test="dto.linkName != null and dto.linkName != ''">
                AND slink.name LIKE CONCAT('%', #{dto.linkName}, '%')
            </if>
            <if test="dto.appName != null and dto.appName != ''">
                AND sa.name = #{dto.appName}
            </if>
            <if test="dto.type != null">
                AND sa.type = #{dto.type}
            </if>
    </select>

    <!-- 支付率Top20国家 -->
    <select id="payRateTopCountries" resultType="com.ruoyi.vo.CountryStatisticVO">
        SELECT
            su.country countryName,
            COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' THEN so.user_id END) userCount,
            TRUNCATE((COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' THEN so.user_id END) /
                      NULLIF(COUNT(DISTINCT so.user_id), 0)) * 100, 2) value
        FROM
            short_order so
            LEFT JOIN short_user su ON so.user_id = su.id
            LEFT JOIN short_sem_link slink ON so.link_id = slink.id
            LEFT JOIN short_app sa ON so.app_id = sa.id
        WHERE
            su.country IS NOT NULL
            AND su.country != ''
            <if test="loggedInUserId != null and loggedInUserId != 1">
                AND slink.create_by = #{loggedInUserId}
            </if>
            <if test="dto.userId != null">
                AND so.user_id = #{dto.userId}
            </if>
            <if test="dto.pixelStatus != null">
                AND so.pixel_status= #{dto.pixelStatus}
            </if>
            <if test="dto.appId != null">
                AND so.app_id = #{dto.appId}
            </if>
            <if test="dto.appIds != null and dto.appIds.size() > 0">
                AND so.app_id IN
                <foreach item="item" index="index" collection="dto.appIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.registerStartDate != null and dto.registerStartDate != ''">
                AND so.link_time &gt;= #{dto.registerStartDate}
            </if>
            <if test="dto.registerEndDate != null and dto.registerEndDate != ''">
                AND so.link_time &lt;= #{dto.registerEndDate}
            </if>
            <if test="dto.paymentStartDate != null and dto.paymentStartDate != ''">
                AND so.pay_time &gt;= #{dto.paymentStartDate}
            </if>
            <if test="dto.paymentEndDate != null and dto.paymentEndDate != ''">
                AND so.pay_time &lt;= #{dto.paymentEndDate}
            </if>
            <if test="dto.linkId != null">
                AND so.link_id = #{dto.linkId}
            </if>
            <if test="dto.adid != null and dto.adid != ''">
                AND so.adid = #{dto.adid}
            </if>
            <if test="dto.adInfo != null and dto.adInfo != ''">
                AND so.other LIKE CONCAT('%', #{dto.adInfo}, '%')
            </if>
            <if test="dto.adName != null and dto.adName != ''">
                AND so.other LIKE CONCAT('%', #{dto.adName}, '%')
            </if>
            <if test="dto.linkName != null and dto.linkName != ''">
                AND slink.name LIKE CONCAT('%', #{dto.linkName}, '%')
            </if>
            <if test="dto.appName != null and dto.appName != ''">
                AND sa.name = #{dto.appName}
            </if>
            <if test="dto.payType != null and dto.payType != ''">
                AND so.pay_type = #{dto.payType}
            </if>
            <if test="dto.paymentMethod != null and dto.paymentMethod != ''">
                AND so.paymentMethod = #{dto.paymentMethod}
            </if>
            <if test="dto.type != null">
                AND sa.type = #{dto.type}
            </if>
        GROUP BY
            su.country
        HAVING
            COUNT(DISTINCT so.user_id) &gt; 10
        ORDER BY
            value DESC
        LIMIT 20
    </select>

    <!-- 付费人均支付Top20国家 -->
    <select id="avgPayTopCountries" resultType="com.ruoyi.vo.CountryStatisticVO">
        SELECT
            su.country countryName,
            COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' THEN so.user_id END) userCount,
            TRUNCATE(SUM(CASE WHEN so.status = 'SUCCEEDED' THEN so.paymentAmount ELSE 0 END) /
                NULLIF(COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' THEN so.user_id END), 0), 2) value
        FROM
            short_order so
            LEFT JOIN short_user su ON so.user_id = su.id
            LEFT JOIN short_sem_link slink ON so.link_id = slink.id
            LEFT JOIN short_app sa ON so.app_id = sa.id
        WHERE
            su.country IS NOT NULL
            AND su.country != ''
            <if test="loggedInUserId != null and loggedInUserId != 1">
                AND slink.create_by = #{loggedInUserId}
            </if>
            <if test="dto.userId != null">
                AND so.user_id = #{dto.userId}
            </if>
            <if test="dto.pixelStatus != null">
                AND so.pixel_status= #{dto.pixelStatus}
            </if>
            <if test="dto.appId != null">
                AND so.app_id = #{dto.appId}
            </if>
            <if test="dto.appIds != null and dto.appIds.size() > 0">
                AND so.app_id IN
                <foreach item="item" index="index" collection="dto.appIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.registerStartDate != null and dto.registerStartDate != ''">
                AND so.link_time &gt;= #{dto.registerStartDate}
            </if>
            <if test="dto.registerEndDate != null and dto.registerEndDate != ''">
                AND so.link_time &lt;= #{dto.registerEndDate}
            </if>
            <if test="dto.paymentStartDate != null and dto.paymentStartDate != ''">
                AND so.pay_time &gt;= #{dto.paymentStartDate}
            </if>
            <if test="dto.paymentEndDate != null and dto.paymentEndDate != ''">
                AND so.pay_time &lt;= #{dto.paymentEndDate}
            </if>
            <if test="dto.linkId != null">
                AND so.link_id = #{dto.linkId}
            </if>
            <if test="dto.adid != null and dto.adid != ''">
                AND so.adid = #{dto.adid}
            </if>
            <if test="dto.adInfo != null and dto.adInfo != ''">
                AND so.other LIKE CONCAT('%', #{dto.adInfo}, '%')
            </if>
            <if test="dto.adName != null and dto.adName != ''">
                AND so.other LIKE CONCAT('%', #{dto.adName}, '%')
            </if>
            <if test="dto.linkName != null and dto.linkName != ''">
                AND slink.name LIKE CONCAT('%', #{dto.linkName}, '%')
            </if>
            <if test="dto.appName != null and dto.appName != ''">
                AND sa.name = #{dto.appName}
            </if>
            <if test="dto.payType != null and dto.payType != ''">
                AND so.pay_type = #{dto.payType}
            </if>
            <if test="dto.paymentMethod != null and dto.paymentMethod != ''">
                AND so.paymentMethod = #{dto.paymentMethod}
            </if>
            <if test="dto.type != null">
                AND sa.type = #{dto.type}
            </if>
        GROUP BY
            su.country
        HAVING
            COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' THEN so.user_id END) &gt; 10
        ORDER BY
            value DESC
        LIMIT 20
    </select>

    <!-- 订阅收入占比Top20国家 -->
    <select id="subscribeRatioTopCountries" resultType="com.ruoyi.vo.CountryStatisticVO">
        SELECT
            su.country countryName,
            COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '订阅' THEN so.user_id END) userCount,
            TRUNCATE(
                SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '订阅' THEN so.paymentAmount ELSE 0 END) /
                NULLIF(SUM(CASE WHEN so.status = 'SUCCEEDED' THEN so.paymentAmount ELSE 0 END), 0) * 100,
                2
            ) value
        FROM
            short_order so
            LEFT JOIN short_user su ON so.user_id = su.id
            LEFT JOIN short_sem_link slink ON so.link_id = slink.id
            LEFT JOIN short_app sa ON so.app_id = sa.id
        WHERE
            su.country IS NOT NULL
            AND su.country != ''
            <if test="loggedInUserId != null and loggedInUserId != 1">
                AND slink.create_by = #{loggedInUserId}
            </if>
            <if test="dto.userId != null">
                AND so.user_id = #{dto.userId}
            </if>
            <if test="dto.pixelStatus != null">
                AND so.pixel_status= #{dto.pixelStatus}
            </if>
            <if test="dto.appId != null">
                AND so.app_id = #{dto.appId}
            </if>
            <if test="dto.appIds != null and dto.appIds.size() > 0">
                AND so.app_id IN
                <foreach item="item" index="index" collection="dto.appIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.registerStartDate != null and dto.registerStartDate != ''">
                AND so.link_time &gt;= #{dto.registerStartDate}
            </if>
            <if test="dto.registerEndDate != null and dto.registerEndDate != ''">
                AND so.link_time &lt;= #{dto.registerEndDate}
            </if>
            <if test="dto.paymentStartDate != null and dto.paymentStartDate != ''">
                AND so.pay_time &gt;= #{dto.paymentStartDate}
            </if>
            <if test="dto.paymentEndDate != null and dto.paymentEndDate != ''">
                AND so.pay_time &lt;= #{dto.paymentEndDate}
            </if>
            <if test="dto.linkId != null">
                AND so.link_id = #{dto.linkId}
            </if>
            <if test="dto.adid != null and dto.adid != ''">
                AND so.adid = #{dto.adid}
            </if>
            <if test="dto.adInfo != null and dto.adInfo != ''">
                AND so.other LIKE CONCAT('%', #{dto.adInfo}, '%')
            </if>
            <if test="dto.adName != null and dto.adName != ''">
                AND so.other LIKE CONCAT('%', #{dto.adName}, '%')
            </if>
            <if test="dto.linkName != null and dto.linkName != ''">
                AND slink.name LIKE CONCAT('%', #{dto.linkName}, '%')
            </if>
            <if test="dto.appName != null and dto.appName != ''">
                AND sa.name = #{dto.appName}
            </if>
            <if test="dto.paymentMethod != null and dto.paymentMethod != ''">
                AND so.paymentMethod = #{dto.paymentMethod}
            </if>
            <if test="dto.type != null">
                AND sa.type = #{dto.type}
            </if>
        GROUP BY
            su.country
        HAVING
            SUM(CASE WHEN so.status = 'SUCCEEDED' THEN so.paymentAmount ELSE 0 END) &gt; 0
        ORDER BY
            value DESC
        LIMIT 20
    </select>

    <!-- 订阅留存率Top20国家 -->
    <select id="retentionRateTopCountries" resultType="com.ruoyi.vo.CountryStatisticVO">
        SELECT
            current.country_name countryName,
            current.renewal_count userCount,
            TRUNCATE(
                (current.renewal_count / NULLIF(previous.subscribe_count, 0)) * 100,
                2
            ) value
        FROM
            (
                SELECT
                    su.country country_name,
                    COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '订阅续费' THEN so.user_id END) renewal_count
                FROM
                    short_order so
                    LEFT JOIN short_user su ON so.user_id = su.id
                    LEFT JOIN short_sem_link slink ON so.link_id = slink.id
                    LEFT JOIN short_app sa ON so.app_id = sa.id
                WHERE
                    su.country IS NOT NULL
                    AND su.country != ''
                    <if test="loggedInUserId != null and loggedInUserId != 1">
                        AND slink.create_by = #{loggedInUserId}
                    </if>
                    <if test="dto.userId != null">
                        AND so.user_id = #{dto.userId}
                    </if>
                    <if test="dto.pixelStatus != null">
                        AND so.pixel_status= #{dto.pixelStatus}
                    </if>
                    <if test="dto.appId != null">
                        AND so.app_id = #{dto.appId}
                    </if>
                    <if test="dto.appIds != null and dto.appIds.size() > 0">
                        AND so.app_id IN
                        <foreach item="item" index="index" collection="dto.appIds" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="dto.registerStartDate != null and dto.registerStartDate != ''">
                        AND so.link_time &gt;= #{dto.registerStartDate}
                    </if>
                    <if test="dto.registerEndDate != null and dto.registerEndDate != ''">
                        AND so.link_time &lt;= #{dto.registerEndDate}
                    </if>
                    <if test="dto.paymentStartDate != null and dto.paymentStartDate != ''">
                        AND so.pay_time &gt;= #{dto.paymentStartDate}
                    </if>
                    <if test="dto.paymentEndDate != null and dto.paymentEndDate != ''">
                        AND so.pay_time &lt;= #{dto.paymentEndDate}
                    </if>
                    <if test="dto.linkId != null">
                        AND so.link_id = #{dto.linkId}
                    </if>
                    <if test="dto.adid != null and dto.adid != ''">
                        AND so.adid = #{dto.adid}
                    </if>
                    <if test="dto.adInfo != null and dto.adInfo != ''">
                        AND so.other LIKE CONCAT('%', #{dto.adInfo}, '%')
                    </if>
                    <if test="dto.adName != null and dto.adName != ''">
                        AND so.other LIKE CONCAT('%', #{dto.adName}, '%')
                    </if>
                    <if test="dto.linkName != null and dto.linkName != ''">
                        AND slink.name LIKE CONCAT('%', #{dto.linkName}, '%')
                    </if>
                    <if test="dto.appName != null and dto.appName != ''">
                        AND sa.name = #{dto.appName}
                    </if>
                    <if test="dto.paymentMethod != null and dto.paymentMethod != ''">
                        AND so.paymentMethod = #{dto.paymentMethod}
                    </if>
                    <if test="dto.type != null">
                        AND sa.type = #{dto.type}
                    </if>
                GROUP BY
                    su.country
            ) current
            JOIN (
                SELECT
                    su.country country_name,
                    COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '订阅' THEN so.user_id END) subscribe_count
                FROM
                    short_order so
                    LEFT JOIN short_user su ON so.user_id = su.id
                    LEFT JOIN short_sem_link slink ON so.link_id = slink.id
                    LEFT JOIN short_app sa ON so.app_id = sa.id
                WHERE
                    su.country IS NOT NULL
                    AND su.country != ''
                    AND DATE_FORMAT(so.pay_time, '%Y-%m') = DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 MONTH), '%Y-%m')
                    <if test="loggedInUserId != null and loggedInUserId != 1">
                        AND slink.create_by = #{loggedInUserId}
                    </if>
                    <if test="dto.appId != null">
                        AND so.app_id = #{dto.appId}
                    </if>
                    <if test="dto.appIds != null and dto.appIds.size() > 0">
                        AND so.app_id IN
                        <foreach item="item" index="index" collection="dto.appIds" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="dto.pixelStatus != null">
                        AND so.pixel_status= #{dto.pixelStatus}
                    </if>
                    <if test="dto.linkId != null">
                        AND so.link_id = #{dto.linkId}
                    </if>
                    <if test="dto.adid != null and dto.adid != ''">
                        AND so.adid = #{dto.adid}
                    </if>
                    <if test="dto.adInfo != null and dto.adInfo != ''">
                        AND so.other LIKE CONCAT('%', #{dto.adInfo}, '%')
                    </if>
                    <if test="dto.adName != null and dto.adName != ''">
                        AND so.other LIKE CONCAT('%', #{dto.adName}, '%')
                    </if>
                    <if test="dto.linkName != null and dto.linkName != ''">
                        AND slink.name LIKE CONCAT('%', #{dto.linkName}, '%')
                    </if>
                    <if test="dto.appName != null and dto.appName != ''">
                        AND sa.name = #{dto.appName}
                    </if>
                    <if test="dto.type != null">
                        AND sa.type = #{dto.type}
                    </if>
                GROUP BY
                    su.country
            ) previous ON current.country_name = previous.country_name
        WHERE
            previous.subscribe_count &gt; 10
        ORDER BY
            value DESC
        LIMIT 20
    </select>

    <!-- 订单表数据统计 -->
    <select id="dailyStatistics" resultType="com.ruoyi.vo.ShortOrderDailyVO">
        SELECT
            <!-- 用户数量统计 -->
            COUNT(DISTINCT so.user_id) userCount,
            COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' THEN so.user_id END) payUserCount,
            COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' AND su.phone_version = 'Apple' THEN so.user_id END) iosPayUserCount,
            COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' AND su.phone_version != 'Apple' THEN so.user_id END) androidPayUserCount,
            COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '订阅' THEN so.user_id END) subscribeUserCount,
            COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '订阅续费' THEN so.user_id END) renewalUserCount,

            <!-- 金额统计 -->
            SUM(CASE WHEN so.status = 'SUCCEEDED' THEN so.paymentAmount ELSE 0 END) payAmount,
            SUM(CASE WHEN so.status = 'SUCCEEDED' AND su.phone_version = 'Apple' THEN so.paymentAmount ELSE 0 END) iosPayAmount,
            SUM(CASE WHEN so.status = 'SUCCEEDED' AND su.phone_version != 'Apple' THEN so.paymentAmount ELSE 0 END) androidPayAmount,
            SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '订阅' THEN so.paymentAmount ELSE 0 END) subscribeAmount,
            SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '订阅' AND su.phone_version = 'Apple' THEN so.paymentAmount ELSE 0 END) iosSubscribeAmount,
            SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '订阅' AND su.phone_version != 'Apple' THEN so.paymentAmount ELSE 0 END) androidSubscribeAmount,
            SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '充值' THEN so.paymentAmount ELSE 0 END) rechargeAmount,
            SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '充值' AND su.phone_version = 'Apple' THEN so.paymentAmount ELSE 0 END) iosRechargeAmount,
            SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '充值' AND su.phone_version != 'Apple' THEN so.paymentAmount ELSE 0 END) androidRechargeAmount,

            <!-- 支付方式金额统计 -->
            SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.paymentMethod = 'applepay' THEN so.paymentAmount ELSE 0 END) applePayAmount,
            SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.paymentMethod = 'card' THEN so.paymentAmount ELSE 0 END) cardPayAmount,
            SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.paymentMethod = 'googlepay' THEN so.paymentAmount ELSE 0 END) googlePayAmount,

            <!-- 比率计算 -->
            TRUNCATE((COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' THEN so.user_id END) / NULLIF(COUNT(DISTINCT so.user_id), 0)) * 100, 2) payRate,
            TRUNCATE((COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' AND su.phone_version = 'Apple' THEN so.user_id END) / NULLIF(COUNT(DISTINCT CASE WHEN su.phone_version = 'Apple' THEN so.user_id END), 0)) * 100, 2) iosPayRate,
            TRUNCATE((COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' AND su.phone_version != 'Apple' THEN so.user_id END) / NULLIF(COUNT(DISTINCT CASE WHEN su.phone_version != 'Apple' THEN so.user_id END), 0)) * 100, 2) androidPayRate,

            <!-- 人均统计 -->
            TRUNCATE(SUM(CASE WHEN so.status = 'SUCCEEDED' THEN so.paymentAmount ELSE 0 END) / NULLIF(COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' THEN so.user_id END), 0), 2) arppu,
            TRUNCATE(SUM(CASE WHEN so.status = 'SUCCEEDED' AND su.phone_version = 'Apple' THEN so.paymentAmount ELSE 0 END) / NULLIF(COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' AND su.phone_version = 'Apple' THEN so.user_id END), 0), 2) avgIosPayAmount,
            TRUNCATE(SUM(CASE WHEN so.status = 'SUCCEEDED' AND su.phone_version != 'Apple' THEN so.paymentAmount ELSE 0 END) / NULLIF(COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' AND su.phone_version != 'Apple' THEN so.user_id END), 0), 2) avgAndroidPayAmount,

            <!-- 订阅占比 -->
            TRUNCATE((SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '订阅' THEN so.paymentAmount ELSE 0 END) /
                      NULLIF(SUM(CASE WHEN so.status = 'SUCCEEDED' THEN so.paymentAmount ELSE 0 END), 0)) * 100, 2) subscribeRatio,
            TRUNCATE((SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '订阅' AND su.phone_version = 'Apple' THEN so.paymentAmount ELSE 0 END) /
                      NULLIF(SUM(CASE WHEN so.status = 'SUCCEEDED' AND su.phone_version = 'Apple' THEN so.paymentAmount ELSE 0 END), 0)) * 100, 2) iosSubscribeRatio,
            TRUNCATE((SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '订阅' AND su.phone_version != 'Apple' THEN so.paymentAmount ELSE 0 END) /
                      NULLIF(SUM(CASE WHEN so.status = 'SUCCEEDED' AND su.phone_version != 'Apple' THEN so.paymentAmount ELSE 0 END), 0)) * 100, 2) androidSubscribeRatio,

            <!-- 支付方式占比计算 -->
            TRUNCATE((SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.paymentMethod = 'applepay' THEN so.paymentAmount ELSE 0 END) /
                      NULLIF(SUM(CASE WHEN so.status = 'SUCCEEDED' THEN so.paymentAmount ELSE 0 END), 0)) * 100, 2) applePayRatio,
            TRUNCATE((SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.paymentMethod = 'card' THEN so.paymentAmount ELSE 0 END) /
                      NULLIF(SUM(CASE WHEN so.status = 'SUCCEEDED' THEN so.paymentAmount ELSE 0 END), 0)) * 100, 2) cardPayRatio,
            TRUNCATE((SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.paymentMethod = 'googlepay' THEN so.paymentAmount ELSE 0 END) /
                      NULLIF(SUM(CASE WHEN so.status = 'SUCCEEDED' THEN so.paymentAmount ELSE 0 END), 0)) * 100, 2) googlePayRatio,

            <!-- 广告相关 -->
            COUNT(DISTINCT CASE WHEN so.adid IS NOT NULL AND so.adid != '' THEN so.user_id END) adUserCount,
            TRUNCATE((COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' AND so.adid IS NOT NULL AND so.adid != '' THEN so.user_id END) /
                      NULLIF(COUNT(DISTINCT CASE WHEN so.adid IS NOT NULL AND so.adid != '' THEN so.user_id END), 0)) * 100, 2) adConversionRate
        FROM
            short_order so
            LEFT JOIN short_user su ON so.user_id = su.id
            LEFT JOIN short_sem_link slink ON so.link_id = slink.id
            LEFT JOIN short_app sa ON so.app_id = sa.id
            LEFT JOIN short_vip sv ON so.vip_id = sv.id
        WHERE
            1=1
            <if test="loggedInUserId != null and loggedInUserId != 1">
                AND slink.create_by = #{loggedInUserId}
            </if>
        <if test="dto.childIds != null and dto.childIds.size() > 0">
            AND slink.create_by IN
            <foreach item="childId" index="index" collection="dto.childIds" open="(" separator="," close=")">
                #{childId}
            </foreach>
        </if>
            <!-- 默认当日链接时间  2025-05-07 修改逻辑：全部按筛选条件，不需要默认
            <if test="dto.registerStartDate == null and dto.registerEndDate == null">
                AND DATE(so.link_time) = UTC_DATE()
            </if>-->
            <!-- 筛选条件 -->
            <if test="dto.appId != null">
                AND so.app_id = #{dto.appId}
            </if>
            <if test="dto.appIds != null and dto.appIds.size() > 0">
                AND so.app_id IN
                <foreach item="item" index="index" collection="dto.appIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.pixelStatus != null">
                AND so.pixel_status= #{dto.pixelStatus}
            </if>
            <if test="dto.userId != null">
                AND so.user_id = #{dto.userId}
            </if>
            <if test="dto.linkId != null">
                AND so.link_id = #{dto.linkId}
            </if>
            <if test="dto.orderStatus != null and dto.orderStatus != ''">
                AND so.status = #{dto.orderStatus}
            </if>
            <if test="dto.payType != null and dto.payType != ''">
                AND so.pay_type = #{dto.payType}
            </if>
            <if test="dto.payTypes != null and dto.payTypes.size() > 0">
                AND so.pay_type IN
                <foreach item="item" index="index" collection="dto.payTypes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.countryName != null and dto.countryName != ''">
                AND su.country = #{dto.countryName}
            </if>
            <if test="dto.paymentMethod != null and dto.paymentMethod != ''">
                AND so.paymentMethod = #{dto.paymentMethod}
            </if>
            <if test="dto.adid != null and dto.adid != ''">
                AND so.adid = #{dto.adid}
            </if>
            <if test="dto.adInfo != null and dto.adInfo != ''">
                AND so.other LIKE CONCAT('%', #{dto.adInfo}, '%')
            </if>
            <if test="dto.adName != null and dto.adName != ''">
                AND so.other LIKE CONCAT('%', #{dto.adName}, '%')
            </if>
            <if test="dto.linkName != null and dto.linkName != ''">
                AND slink.name LIKE CONCAT('%', #{dto.linkName}, '%')
            </if>
            <if test="dto.appName != null and dto.appName != ''">
                AND sa.name = #{dto.appName}
            </if>
            <if test="dto.phoneVersion != null and dto.phoneVersion != ''">
                AND su.phone_version = #{dto.phoneVersion}
            </if>
            <if test="dto.subscriptionType != null and dto.subscriptionType != ''">
                AND sv.subscription_type = #{dto.subscriptionType}
            </if>
            <if test="dto.type != null">
                AND sa.type = #{dto.type}
            </if>
            <!-- 时间范围筛选 -->
            <if test="dto.registerStartDate != null and dto.registerStartDate != ''">
                AND so.link_time &gt;= #{dto.registerStartDate}
            </if>
            <if test="dto.registerEndDate != null and dto.registerEndDate != ''">
                AND so.link_time &lt;= #{dto.registerEndDate}
            </if>
            <if test="dto.paymentStartDate != null and dto.paymentStartDate != ''">
                AND so.pay_time &gt;= #{dto.paymentStartDate}
            </if>
            <if test="dto.paymentEndDate != null and dto.paymentEndDate != ''">
                AND so.pay_time &lt;= #{dto.paymentEndDate}
            </if>
            <if test="dto.search != null and dto.search != ''">
                AND (so.ordersn = #{dto.search} OR so.paymentIntentId = #{dto.search})
            </if>
            <if test="dto.language != null and dto.language != ''">
                AND slink.language = #{dto.language}
            </if>
    </select>
    <select id="getSubscribeDataByDate" resultType="com.ruoyi.dto.ShortSubscribeDataDTO">
        SELECT
            COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '订阅' THEN so.user_id END) subscribePersonNum,
            SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '订阅' THEN so.paymentAmount ELSE 0 END) subscribeAmount,
            COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '订阅' AND su.phone_version = 'Apple' THEN so.user_id END) iosSubscribeNum,
            SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '订阅' AND su.phone_version = 'Apple' THEN so.paymentAmount ELSE 0 END) iosSubscribeAmount,
            COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '订阅' AND su.phone_version != 'Apple' THEN so.user_id END) androidSubscribeNum,
            SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '订阅' AND su.phone_version != 'Apple' THEN so.paymentAmount ELSE 0 END) androidSubscribeAmount,
            COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '订阅' AND svip.subscription_type = '7' THEN so.user_id END) weekSubscribeNum,
            SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '订阅' AND svip.subscription_type = '7' THEN so.paymentAmount ELSE 0 END) weekSubscribeAmount,
            COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '订阅' AND svip.subscription_type = '30' THEN so.user_id END) monthSubscribeNum,
            SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '订阅' AND svip.subscription_type = '30' THEN so.paymentAmount ELSE 0 END) monthSubscribeAmount,
            COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '订阅' AND svip.subscription_type = '365' THEN so.user_id END) yearSubscribeNum,
            SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '订阅' AND svip.subscription_type = '365' THEN so.paymentAmount ELSE 0 END) yearSubscribeAmount
        FROM
            short_order so
            LEFT JOIN short_user su ON so.user_id = su.id
            LEFT JOIN short_vip svip ON so.vip_id = svip.id
        WHERE
            1=1
            <if test="date != null and date != ''">
                and DATE_FORMAT(so.create_time,'%Y-%m-%d') = #{date}
            </if>
    </select>
    <select id="getOldUserRenewAmount" resultType="java.math.BigDecimal">
        SELECT
        SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '订阅续费' THEN so.paymentAmount ELSE 0 END) oldUserRenewAmount
        FROM
        short_order so
        LEFT JOIN short_user su ON so.user_id = su.id
        LEFT JOIN short_sem_link slink ON so.link_id = slink.id
        LEFT JOIN short_app sa ON so.app_id = sa.id
        LEFT JOIN short_vip sv ON so.vip_id = sv.id
        WHERE
            DATE_FORMAT(su.create_time, '%Y-%m-%d') &lt; #{date}
        <if test="loggedInUserId != null and loggedInUserId != 1">
            AND slink.create_by = #{loggedInUserId}
        </if>
        <!-- 筛选条件 -->
        <if test="dto.appId != null">
            AND so.app_id = #{dto.appId}
        </if>
        <if test="dto.appIds != null and dto.appIds.size() > 0">
            AND so.app_id IN
            <foreach item="item" index="index" collection="dto.appIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.pixelStatus != null">
            AND so.pixel_status= #{dto.pixelStatus}
        </if>
        <if test="dto.userId != null">
            AND so.user_id = #{dto.userId}
        </if>
        <if test="dto.linkId != null">
            AND so.link_id = #{dto.linkId}
        </if>
        <if test="dto.orderStatus != null and dto.orderStatus != ''">
            AND so.status = #{dto.orderStatus}
        </if>
        <if test="dto.payType != null and dto.payType != ''">
            AND so.pay_type = #{dto.payType}
        </if>
        <if test="dto.countryName != null and dto.countryName != ''">
            AND su.country = #{dto.countryName}
        </if>
        <if test="dto.paymentMethod != null and dto.paymentMethod != ''">
            AND so.paymentMethod = #{dto.paymentMethod}
        </if>
        <if test="dto.adid != null and dto.adid != ''">
            AND so.adid = #{dto.adid}
        </if>
        <if test="dto.adInfo != null and dto.adInfo != ''">
            AND so.other LIKE CONCAT('%', #{dto.adInfo}, '%')
        </if>
        <if test="dto.adName != null and dto.adName != ''">
            AND so.other LIKE CONCAT('%', #{dto.adName}, '%')
        </if>
        <if test="dto.linkName != null and dto.linkName != ''">
            AND slink.name LIKE CONCAT('%', #{dto.linkName}, '%')
        </if>
        <if test="dto.appName != null and dto.appName != ''">
            AND sa.name = #{dto.appName}
        </if>
        <if test="dto.phoneVersion != null and dto.phoneVersion != ''">
            AND su.phone_version = #{dto.phoneVersion}
        </if>
        <if test="dto.search != null and dto.search != ''">
            AND (so.ordersn = #{dto.search} OR so.paymentIntentId = #{dto.search})
        </if>
        <if test="dto.subscriptionType != null and dto.subscriptionType != ''">
            AND sv.subscription_type = #{dto.subscriptionType}
        </if>
        <if test="dto.type != null">
            AND sa.type = #{dto.type}
        </if>
    </select>

    <select id="getRenewSubscribeDataByDate" resultType="com.ruoyi.domain.ShortRenewSubscribeData">
        SELECT so.user_id,
               DATE_FORMAT(so.create_time, '%Y-%m-%d') as log_date,
               so.paymentMethod AS pay_method,
               su.phone_version,
               so.paymentAmount     AS renew_amount,
               so.`status`          AS order_status,
               sv.subscription_type AS renew_type
        FROM short_order so
                 LEFT JOIN short_user su ON su.id = so.user_id
                 LEFT JOIN short_vip sv ON sv.id = su.vip_id
        WHERE DATE_FORMAT(so.create_time, '%Y-%m-%d') = #{date}
          AND so.pay_type = '订阅续费'
          AND sv.subscription_type != 0
        ORDER BY user_id desc
    </select>
    <select id="getFirstSubscribeDataByDate" resultType="com.ruoyi.domain.ShortRenewSubscribeData">
        SELECT so.user_id,
               so.paymentMethod AS pay_method,
               su.phone_version,
               so.paymentAmount     AS renew_amount,
               so.`status`          AS order_status,
               sv.subscription_type AS renew_type
        FROM short_order so
                 LEFT JOIN short_user su ON su.id = so.user_id
                 LEFT JOIN short_vip sv ON sv.id = su.vip_id
        WHERE DATE_FORMAT(so.create_time, '%Y-%m-%d') = #{date}
          AND so.pay_type = '订阅'
          AND so.status = 'SUCCEEDED'
          AND sv.subscription_type != 0
        ORDER BY user_id desc
    </select>
    <select id="findRenewSubscribeDataByUserId" resultType="com.ruoyi.domain.ShortRenewSubscribeData">
        SELECT so.user_id,
               so.paymentMethod AS pay_method,
               su.phone_version,
               so.paymentAmount     AS renew_amount,
               so.`status`          AS order_status,
               sv.subscription_type AS renew_type
        FROM short_order so
                 LEFT JOIN short_user su ON su.id = so.user_id
                 LEFT JOIN short_vip sv ON sv.id = su.vip_id
        WHERE so.user_id = #{userId}
          AND so.pay_type = '订阅续费'
          AND so.status = 'SUCCEEDED'
          AND sv.subscription_type != 0
          AND so.create_time &lt; #{date}
        ORDER BY user_id desc
    </select>
    <select id="getOldUserRenewAmountByStartDate" resultType="java.math.BigDecimal">
        SELECT
        SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '订阅续费' THEN so.paymentAmount ELSE 0 END) oldUserRenewAmount
        FROM
        short_order so
        LEFT JOIN short_user su ON so.user_id = su.id
        LEFT JOIN short_sem_link slink ON so.link_id = slink.id
        LEFT JOIN short_app sa ON so.app_id = sa.id
        LEFT JOIN short_vip sv ON so.vip_id = sv.id
        WHERE
        1=1
        <if test="loggedInUserId != null and loggedInUserId != 1">
            AND slink.create_by = #{loggedInUserId}
        </if>
        <!-- 筛选条件 -->
        <if test="dto.appId != null">
            AND so.app_id = #{dto.appId}
        </if>
        <if test="dto.appIds != null and dto.appIds.size() > 0">
            AND so.app_id IN
            <foreach item="item" index="index" collection="dto.appIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.pixelStatus != null">
            AND so.pixel_status= #{dto.pixelStatus}
        </if>
        <if test="dto.userId != null">
            AND so.user_id = #{dto.userId}
        </if>
        <if test="dto.linkId != null">
            AND so.link_id = #{dto.linkId}
        </if>
        <if test="dto.orderStatus != null and dto.orderStatus != ''">
            AND so.status = #{dto.orderStatus}
        </if>
        <if test="dto.payType != null and dto.payType != ''">
            AND so.pay_type = #{dto.payType}
        </if>
        <if test="dto.countryName != null and dto.countryName != ''">
            AND su.country = #{dto.countryName}
        </if>
        <if test="dto.paymentMethod != null and dto.paymentMethod != ''">
            AND so.paymentMethod = #{dto.paymentMethod}
        </if>
        <if test="dto.adid != null and dto.adid != ''">
            AND so.adid = #{dto.adid}
        </if>
        <if test="dto.adInfo != null and dto.adInfo != ''">
            AND so.other LIKE CONCAT('%', #{dto.adInfo}, '%')
        </if>
        <if test="dto.adName != null and dto.adName != ''">
            AND so.other LIKE CONCAT('%', #{dto.adName}, '%')
        </if>
        <if test="dto.linkName != null and dto.linkName != ''">
            AND slink.name LIKE CONCAT('%', #{dto.linkName}, '%')
        </if>
        <if test="dto.appName != null and dto.appName != ''">
            AND sa.name = #{dto.appName}
        </if>
        <if test="dto.phoneVersion != null and dto.phoneVersion != ''">
            AND su.phone_version = #{dto.phoneVersion}
        </if>
        <if test="dto.search != null and dto.search != ''">
            AND (so.ordersn = #{dto.search} OR so.paymentIntentId = #{dto.search})
        </if>
        <if test="dto.subscriptionType != null and dto.subscriptionType != ''">
            AND sv.subscription_type = #{dto.subscriptionType}
        </if>
        <if test="dto.type != null">
            AND sa.type = #{dto.type}
        </if>
        <!-- 时间范围筛选 -->
        <if test="dto.registerStartDate != null and dto.registerStartDate != ''">
            AND DATE_FORMAT(so.link_time, '%Y-%m-%d') &lt; #{dto.registerStartDate}
        </if>
        <if test="dto.paymentStartDate != null and dto.paymentStartDate != ''">
            AND DATE_FORMAT(so.pay_time, '%Y-%m-%d') &lt; #{dto.paymentStartDate}
        </if>
    </select>
    <select id="userRateTopCountries" resultType="com.ruoyi.vo.CountryStatisticVO">
        SELECT
            count(id) userCount,
            TRUNCATE(count(id)/(select count(id) from short_user)*100 ,2) value,
            country countryName
        FROM
            short_user
        WHERE
            country IS NOT NULL
        GROUP BY
            country
        ORDER BY
            userCount DESC
            limit 20
    </select>

    <select id="countSuccessfulOrdersByPixelIdBeforeTime" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM short_order
        WHERE user_id = #{userId}
          AND pixel_id = #{pixelId}
          AND status = 'SUCCEEDED'
          AND update_time &lt;= #{orderTime}
          <if test="currentOrderId != null">
              AND id != #{currentOrderId}
          </if>
    </select>

    <!-- 新增：基于pixel_id和link_id组合的首单检查查询 -->
    <select id="countSuccessfulOrdersByPixelIdAndLinkIdBeforeTime" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM short_order
        WHERE user_id = #{userId}
          AND pixel_id = #{pixelId}
          AND link_id = #{linkId}
          AND status = 'SUCCEEDED'
          AND update_time &lt;= #{orderTime}
          <if test="currentOrderId != null">
              AND id != #{currentOrderId}
          </if>
    </select>

    <select id="selectListLikeProductId" resultType="com.ruoyi.domain.ShortOrder">
        SELECT
        *
        FROM
        short_order
        WHERE
        1=1
        <if test="other != null">
            AND other LIKE #{other}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        <if test="userId != null">
            AND user_id = #{userId}
        </if>
        ORDER BY id DESC
    </select>
    <select id="dailyStatisticsReal" resultType="com.ruoyi.vo.ShortOrderDailyVO">
        SELECT
        COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' THEN so.user_id END) payUserCount,
        COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' AND su.phone_version = 'Apple' THEN so.user_id END) iosPayUserCount,
        COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' AND su.phone_version != 'Apple' THEN so.user_id END) androidPayUserCount,
        <!-- 金额统计 -->
        SUM(CASE WHEN so.status = 'SUCCEEDED' THEN so.paymentAmount ELSE 0 END) payAmount,
        SUM(CASE WHEN so.status = 'SUCCEEDED' AND su.phone_version = 'Apple' THEN so.paymentAmount ELSE 0 END) iosPayAmount,
        SUM(CASE WHEN so.status = 'SUCCEEDED' AND su.phone_version != 'Apple' THEN so.paymentAmount ELSE 0 END) androidPayAmount,
        SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '订阅' THEN so.paymentAmount ELSE 0 END) subscribeAmount,
        SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '订阅' AND su.phone_version = 'Apple' THEN so.paymentAmount ELSE 0 END) iosSubscribeAmount,
        SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '订阅' AND su.phone_version != 'Apple' THEN so.paymentAmount ELSE 0 END) androidSubscribeAmount,
        SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '充值' THEN so.paymentAmount ELSE 0 END) rechargeAmount,
        SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '充值' AND su.phone_version = 'Apple' THEN so.paymentAmount ELSE 0 END) iosRechargeAmount,
        SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '充值' AND su.phone_version != 'Apple' THEN so.paymentAmount ELSE 0 END) androidRechargeAmount

        FROM
        short_order so
        LEFT JOIN short_user su ON so.user_id = su.id
        LEFT JOIN short_sem_link slink ON so.link_id = slink.id
        LEFT JOIN short_app sa ON so.app_id = sa.id
        LEFT JOIN short_vip sv ON so.vip_id = sv.id
        WHERE
        1=1
        <if test="loggedInUserId != null and loggedInUserId != 1">
            AND slink.create_by = #{loggedInUserId}
        </if>
        <if test="dto.childIds != null and dto.childIds.size() > 0">
            AND slink.create_by IN
            <foreach item="childId" index="index" collection="dto.childIds" open="(" separator="," close=")">
                #{childId}
            </foreach>
        </if>
        <!-- 默认当日链接时间  2025-05-07 修改逻辑：全部按筛选条件，不需要默认
        <if test="dto.registerStartDate == null and dto.registerEndDate == null">
            AND DATE(so.link_time) = UTC_DATE()
        </if>-->
        <!-- 筛选条件 -->
        <if test="dto.appId != null">
            AND so.app_id = #{dto.appId}
        </if>
        <if test="dto.appIds != null and dto.appIds.size() > 0">
            AND so.app_id IN
            <foreach item="item" index="index" collection="dto.appIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.pixelStatus != null">
            AND so.pixel_status= #{dto.pixelStatus}
        </if>
        <if test="dto.userId != null">
            AND so.user_id = #{dto.userId}
        </if>
        <if test="dto.linkId != null">
            AND so.link_id = #{dto.linkId}
        </if>
        <if test="dto.orderStatus != null and dto.orderStatus != ''">
            AND so.status = #{dto.orderStatus}
        </if>
        <if test="dto.payType != null and dto.payType != ''">
            AND so.pay_type = #{dto.payType}
        </if>
        <if test="dto.payTypes != null and dto.payTypes.size() > 0">
            AND so.pay_type IN
            <foreach item="item" index="index" collection="dto.payTypes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.countryName != null and dto.countryName != ''">
            AND su.country = #{dto.countryName}
        </if>
        <if test="dto.paymentMethod != null and dto.paymentMethod != ''">
            AND so.paymentMethod = #{dto.paymentMethod}
        </if>
        <if test="dto.adid != null and dto.adid != ''">
            AND so.adid = #{dto.adid}
        </if>
        <if test="dto.adInfo != null and dto.adInfo != ''">
            AND so.other LIKE CONCAT('%', #{dto.adInfo}, '%')
        </if>
        <if test="dto.adName != null and dto.adName != ''">
            AND so.other LIKE CONCAT('%', #{dto.adName}, '%')
        </if>
        <if test="dto.linkName != null and dto.linkName != ''">
            AND slink.name LIKE CONCAT('%', #{dto.linkName}, '%')
        </if>
        <if test="dto.appName != null and dto.appName != ''">
            AND sa.name = #{dto.appName}
        </if>
        <if test="dto.phoneVersion != null and dto.phoneVersion != ''">
            AND su.phone_version = #{dto.phoneVersion}
        </if>
        <if test="dto.subscriptionType != null and dto.subscriptionType != ''">
            AND sv.subscription_type = #{dto.subscriptionType}
        </if>
        <if test="dto.type != null">
            AND sa.type = #{dto.type}
        </if>
        <!-- 时间范围筛选 -->
        <if test="dto.registerStartDate != null and dto.registerStartDate != ''">
            AND so.link_time &gt;= #{dto.registerStartDate}
        </if>
        <if test="dto.registerEndDate != null and dto.registerEndDate != ''">
            AND so.link_time &lt;= #{dto.registerEndDate}
        </if>
        <if test="dto.paymentStartDate != null and dto.paymentStartDate != ''">
            AND so.pay_time &gt;= #{dto.paymentStartDate}
        </if>
        <if test="dto.paymentEndDate != null and dto.paymentEndDate != ''">
            AND so.pay_time &lt;= #{dto.paymentEndDate}
        </if>
        <if test="dto.search != null and dto.search != ''">
            AND (so.ordersn = #{dto.search} OR so.paymentIntentId = #{dto.search})
        </if>
        <if test="dto.language != null and dto.language != ''">
            AND slink.language = #{dto.language}
        </if>
    </select>
    <select id="dailyStatisticsOld" resultType="com.ruoyi.vo.ShortOrderDashboardVO">
        SELECT
        <!-- 用户数量统计 -->
        COUNT(DISTINCT so.user_id) userCount,
        COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' THEN so.user_id END) payUserCount,
        COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' AND su.phone_version = 'Apple' THEN so.user_id END) iosPayUserCount,
        COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' AND su.phone_version != 'Apple' THEN so.user_id END) androidPayUserCount,
        COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '订阅' THEN so.user_id END) subscribeUserCount,
        COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '订阅续费' THEN so.user_id END) renewalUserCount,

        <!-- 支付方式金额统计 -->
        SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.paymentMethod = 'applepay' THEN so.paymentAmount ELSE 0 END) applePayAmount,
        SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.paymentMethod = 'card' THEN so.paymentAmount ELSE 0 END) cardPayAmount,
        SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.paymentMethod = 'googlepay' THEN so.paymentAmount ELSE 0 END) googlePayAmount,

        <!-- 比率计算 -->
        TRUNCATE((COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' THEN so.user_id END) / NULLIF(COUNT(DISTINCT so.user_id), 0)) * 100, 2) payRate,
        TRUNCATE((COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' AND su.phone_version = 'Apple' THEN so.user_id END) / NULLIF(COUNT(DISTINCT CASE WHEN su.phone_version = 'Apple' THEN so.user_id END), 0)) * 100, 2) iosPayRate,
        TRUNCATE((COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' AND su.phone_version != 'Apple' THEN so.user_id END) / NULLIF(COUNT(DISTINCT CASE WHEN su.phone_version != 'Apple' THEN so.user_id END), 0)) * 100, 2) androidPayRate,

        <!-- 人均统计 -->
        TRUNCATE(SUM(CASE WHEN so.status = 'SUCCEEDED' THEN so.paymentAmount ELSE 0 END) / NULLIF(COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' THEN so.user_id END), 0), 2) arppu,
        TRUNCATE(SUM(CASE WHEN so.status = 'SUCCEEDED' AND su.phone_version = 'Apple' THEN so.paymentAmount ELSE 0 END) / NULLIF(COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' AND su.phone_version = 'Apple' THEN so.user_id END), 0), 2) avgIosPayAmount,
        TRUNCATE(SUM(CASE WHEN so.status = 'SUCCEEDED' AND su.phone_version != 'Apple' THEN so.paymentAmount ELSE 0 END) / NULLIF(COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' AND su.phone_version != 'Apple' THEN so.user_id END), 0), 2) avgAndroidPayAmount,

        <!-- 订阅占比 -->
        TRUNCATE((SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '订阅' THEN so.paymentAmount ELSE 0 END) /
        NULLIF(SUM(CASE WHEN so.status = 'SUCCEEDED' THEN so.paymentAmount ELSE 0 END), 0)) * 100, 2) subscribeRatio,
        TRUNCATE((SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '订阅' AND su.phone_version = 'Apple' THEN so.paymentAmount ELSE 0 END) /
        NULLIF(SUM(CASE WHEN so.status = 'SUCCEEDED' AND su.phone_version = 'Apple' THEN so.paymentAmount ELSE 0 END), 0)) * 100, 2) iosSubscribeRatio,
        TRUNCATE((SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.pay_type = '订阅' AND su.phone_version != 'Apple' THEN so.paymentAmount ELSE 0 END) /
        NULLIF(SUM(CASE WHEN so.status = 'SUCCEEDED' AND su.phone_version != 'Apple' THEN so.paymentAmount ELSE 0 END), 0)) * 100, 2) androidSubscribeRatio,

        <!-- 支付方式占比计算 -->
        TRUNCATE((SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.paymentMethod = 'applepay' THEN so.paymentAmount ELSE 0 END) /
        NULLIF(SUM(CASE WHEN so.status = 'SUCCEEDED' THEN so.paymentAmount ELSE 0 END), 0)) * 100, 2) applePayRatio,
        TRUNCATE((SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.paymentMethod = 'card' THEN so.paymentAmount ELSE 0 END) /
        NULLIF(SUM(CASE WHEN so.status = 'SUCCEEDED' THEN so.paymentAmount ELSE 0 END), 0)) * 100, 2) cardPayRatio,
        TRUNCATE((SUM(CASE WHEN so.status = 'SUCCEEDED' AND so.paymentMethod = 'googlepay' THEN so.paymentAmount ELSE 0 END) /
        NULLIF(SUM(CASE WHEN so.status = 'SUCCEEDED' THEN so.paymentAmount ELSE 0 END), 0)) * 100, 2) googlePayRatio,

        <!-- 广告相关 -->
        COUNT(DISTINCT CASE WHEN so.adid IS NOT NULL AND so.adid != '' THEN so.user_id END) adUserCount,
        TRUNCATE((COUNT(DISTINCT CASE WHEN so.status = 'SUCCEEDED' AND so.adid IS NOT NULL AND so.adid != '' THEN so.user_id END) /
        NULLIF(COUNT(DISTINCT CASE WHEN so.adid IS NOT NULL AND so.adid != '' THEN so.user_id END), 0)) * 100, 2) adConversionRate
        FROM
        short_order so
        LEFT JOIN short_user su ON so.user_id = su.id
        LEFT JOIN short_sem_link slink ON so.link_id = slink.id
        LEFT JOIN short_app sa ON so.app_id = sa.id
        LEFT JOIN short_vip sv ON so.vip_id = sv.id
        WHERE
        1=1
        <if test="loggedInUserId != null and loggedInUserId != 1">
            AND slink.create_by = #{loggedInUserId}
        </if>
        <!-- 默认当日链接时间  2025-05-07 修改逻辑：全部按筛选条件，不需要默认
        <if test="dto.registerStartDate == null and dto.registerEndDate == null">
            AND DATE(so.link_time) = UTC_DATE()
        </if>-->
        <!-- 筛选条件 -->
        <if test="dto.appId != null">
            AND so.app_id = #{dto.appId}
        </if>
        <if test="dto.appIds != null and dto.appIds.size() > 0">
            AND so.app_id IN
            <foreach item="item" index="index" collection="dto.appIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.pixelStatus != null">
            AND so.pixel_status= #{dto.pixelStatus}
        </if>
        <if test="dto.userId != null">
            AND so.user_id = #{dto.userId}
        </if>
        <if test="dto.linkId != null">
            AND so.link_id = #{dto.linkId}
        </if>
        <if test="dto.orderStatus != null and dto.orderStatus != ''">
            AND so.status = #{dto.orderStatus}
        </if>
        <if test="dto.payType != null and dto.payType != ''">
            AND so.pay_type = #{dto.payType}
        </if>
        <if test="dto.countryName != null and dto.countryName != ''">
            AND su.country = #{dto.countryName}
        </if>
        <if test="dto.paymentMethod != null and dto.paymentMethod != ''">
            AND so.paymentMethod = #{dto.paymentMethod}
        </if>
        <if test="dto.adid != null and dto.adid != ''">
            AND so.adid = #{dto.adid}
        </if>
        <if test="dto.adInfo != null and dto.adInfo != ''">
            AND so.other LIKE CONCAT('%', #{dto.adInfo}, '%')
        </if>
        <if test="dto.adName != null and dto.adName != ''">
            AND so.other LIKE CONCAT('%', #{dto.adName}, '%')
        </if>
        <if test="dto.linkName != null and dto.linkName != ''">
            AND slink.name LIKE CONCAT('%', #{dto.linkName}, '%')
        </if>
        <if test="dto.appName != null and dto.appName != ''">
            AND sa.name = #{dto.appName}
        </if>
        <if test="dto.phoneVersion != null and dto.phoneVersion != ''">
            AND su.phone_version = #{dto.phoneVersion}
        </if>
        <if test="dto.subscriptionType != null and dto.subscriptionType != ''">
            AND sv.subscription_type = #{dto.subscriptionType}
        </if>
        <if test="dto.type != null">
            AND sa.type = #{dto.type}
        </if>
        <!-- 时间范围筛选 -->
        <if test="dto.registerStartDate != null and dto.registerStartDate != ''">
            AND so.link_time &gt;= #{dto.registerStartDate}
        </if>
        <if test="dto.registerEndDate != null and dto.registerEndDate != ''">
            AND so.link_time &lt;= #{dto.registerEndDate}
        </if>
        <if test="dto.paymentStartDate != null and dto.paymentStartDate != ''">
            AND so.pay_time &gt;= #{dto.paymentStartDate}
        </if>
        <if test="dto.paymentEndDate != null and dto.paymentEndDate != ''">
            AND so.pay_time &lt;= #{dto.paymentEndDate}
        </if>
        <if test="dto.search != null and dto.search != ''">
            AND (so.ordersn = #{dto.search} OR so.paymentIntentId = #{dto.search})
        </if>
    </select>
    <select id="selectByPaymentRequestId" resultMap="ShortOrderResult">
        <include refid="selectShortOrderVo"/>
        where requestId = #{paymentRequestId}
    </select>
    <select id="getSuccessOrder" resultType="com.ruoyi.domain.ShortOrder">
        select id,user_id,app_id,vip_id,pay_type,status,subscription_type from short_order where status = 'SUCCEEDED'
        <if test=" null != beginTime and null != endTime">
            and create_time BETWEEN #{beginTime} and #{endTime}
        </if>
         <if test="nextId != null">
             and id > #{nextId}
         </if>
        order by id
        limit 1000
    </select>
    <select id="getPayTypeByUserId" resultType="java.lang.String">
        SELECT
            pay_type
        FROM
            short_order
        WHERE
            user_id = #{id}
          AND `status` = 'SUCCEEDED'
          AND app_id = #{appId}
        ORDER BY
            create_time DESC
            LIMIT 1
    </select>
    <select id="getSumPriceByUserId" resultType="java.math.BigDecimal">
        SELECT SUM(paymentAmount) from short_order WHERE  user_id = #{id} and `status` = 'SUCCEEDED'
    </select>
    <select id="getByUserId" resultType="com.ruoyi.domain.ShortOrder">
        SELECT * from short_order WHERE  user_id = #{id} and `status` = 'SUCCEEDED' ORDER BY create_time desc
    </select>

    <select id="selectUserSubscribeRecord" resultType="com.ruoyi.vo.SubscribeRecordVO">
        select o.app_id appId, o.pay_type payType, o.ordersn ordersn, o.paymentCurrency currency, o.paymentAmount
        amount, o.status status, o.pay_time payTime, o.update_time updateTime, o.subscription_type subscriptionType,
        a.name appName,su.unsub,su.expire_time expireTime
        FROM short_order o
        LEFT JOIN short_app a on a.id = o.app_id
--         LEFT JOIN short_vip v on v.id = o.vip_id
        LEFT JOIN short_user su on su.id = o.user_id
        WHERE o.status in('SUCCEEDED', 'REFUNDED')
        AND o.pay_type in ('订阅', '订阅续费')
        <if test="userIds != null and userIds.size() > 0">
            AND o.user_id IN
            <foreach item="userId" index="index" collection="userIds" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        order by o.id desc
    </select>
    <select id="getInitIdByUserId" resultType="com.ruoyi.domain.ShortOrder">
        SELECT
            *
        FROM
            short_order
        WHERE
            user_id = #{userId}
          AND `status` = 'SUCCEEDED'
          AND (pay_type = '订阅' OR pay_type = '订阅续费')
        ORDER BY
            pay_time DESC
    </select>
    <select id="getLastRenewal" resultType="com.ruoyi.domain.ShortOrder">
        SELECT * from short_order WHERE user_id = #{id} and (pay_type = '订阅' or pay_type = '订阅续费')
        <if test="status != null">
            and `status` = #{status}
        </if>
        ORDER BY create_time desc LIMIT 1
    </select>

    <select id="selectBySubscriptionTypeBy" resultType="java.lang.String">
        SELECT subscription_type from short_order
        WHERE `status` = 'SUCCEEDED'
          and (pay_type = '订阅续费' or pay_type = '订阅')
          and user_id = #{id}
        ORDER BY pay_time desc
            LIMIT 1
    </select>
    <select id="getByUserIdAndMarkType" resultType="com.ruoyi.domain.ShortOrder">
        SELECT
            *
        FROM
            short_order
        WHERE
            user_id = #{id}
          AND `status` = 'SUCCEEDED'
          AND mark_type = 1
        ORDER BY pay_time desc
            LIMIT 1
    </select>
    <select id="getOnlyCardList" resultType="java.lang.Long">
        SELECT
            user_id
        FROM
            short_order so
        WHERE
            mark_type = 1
          AND pay_type != '充值'
    AND `status` = 'SUCCEEDED'
    AND user_id IN
        <foreach item="userId" index="index" collection="userIds" open="(" separator="," close=")">
            #{userId}
        </foreach>
    AND (card_number, pay_time) IN (
        SELECT
            card_number,
            MAX(pay_time) as latest_pay_time
        FROM
            short_order
        WHERE
            mark_type = 1
            AND pay_type != '充值'
            AND `status` = 'SUCCEEDED'
            AND user_id IN
            <foreach item="userId" index="index" collection="userIds" open="(" separator="," close=")">
                #{userId}
            </foreach>
        GROUP BY
            card_number
    )
        ORDER BY
            pay_time DESC;
    </select>
    <select id="getByUserIdAndPayType" resultType="java.lang.Long">
        SELECT id from short_order WHERE user_id= #{userId}  and `status` = 'SUCCEEDED' and pay_type = '订阅' ORDER BY pay_time desc LIMIT 1
    </select>
    <select id="selectByMerchantOrderId" resultMap="ShortOrderResult">
        <include refid="selectShortOrderVo"/>
        where merchant_order_id = #{merchantOrderId}
    </select>
    <select id="selectNewSubscriptionOrder" resultType="com.ruoyi.domain.ShortOrder">
        SELECT * from short_order WHERE user_id = #{userId} and pay_type = '订阅' and `status` = 'SUCCEEDED' order by id desc limit 1
    </select>


</mapper>
