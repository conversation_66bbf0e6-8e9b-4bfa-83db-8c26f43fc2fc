package com.ruoyi.mapper;

import com.ruoyi.domain.ShortMovie;
import com.ruoyi.domain.ShortMovieApp;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 影片归属Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-04
 */
public interface ShortMovieAppMapper
{
    /**
     * 查询影片归属
     *
     * @param id 影片归属主键
     * @return 影片归属
     */
    public ShortMovieApp selectShortMovieAppById(Long id);

    /**
     * 查询影片归属列表
     *
     * @param shortMovieApp 影片归属
     * @return 影片归属集合
     */
    public List<ShortMovieApp> selectShortMovieAppList(ShortMovieApp shortMovieApp);

    /**
     * 新增影片归属
     *
     * @param shortMovieApp 影片归属
     * @return 结果
     */
    public int insertShortMovieApp(ShortMovieApp shortMovieApp);

    /**
     * 修改影片归属
     *
     * @param shortMovieApp 影片归属
     * @return 结果
     */
    public int updateShortMovieApp(ShortMovieApp shortMovieApp);

    /**
     * 删除影片归属
     *
     * @param id 影片归属主键
     * @return 结果
     */
    public int deleteShortMovieAppById(Long id);

    /**
     * 批量删除影片归属
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteShortMovieAppByIds(Long[] ids);

    List<ShortMovieApp> selectByMovieIds(List<Long> ids);

    void batchInsert(@Param("list")List<ShortMovieApp> shortMovieAppList);

    int selectByMovieIdAndAppId(@Param("movieId") Long movieId, @Param("appId") Long appId);
}
