package com.ruoyi.web.controller.short_center.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.domain.ShortUserLinkMovieBind;
import com.ruoyi.service.IShortUserLinkMovieBindService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 【请填写功能名称】Controller
 *
 * <AUTHOR>
 * @date 2025-09-22
 */
@RestController
@RequestMapping("/system/bind")
public class ShortUserLinkMovieBindController extends BaseController {
    @Autowired
    private IShortUserLinkMovieBindService shortUserLinkMovieBindService;

    /**
     * 查询【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:bind:list')")
    @GetMapping("/list")
    public TableDataInfo list(ShortUserLinkMovieBind shortUserLinkMovieBind) {
        startPage();
        List<ShortUserLinkMovieBind> list = shortUserLinkMovieBindService.selectShortUserLinkMovieBindList(shortUserLinkMovieBind);
        return getDataTable(list);
    }

    /**
     * 导出【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:bind:export')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ShortUserLinkMovieBind shortUserLinkMovieBind) {
        List<ShortUserLinkMovieBind> list = shortUserLinkMovieBindService.selectShortUserLinkMovieBindList(shortUserLinkMovieBind);
        ExcelUtil<ShortUserLinkMovieBind> util = new ExcelUtil<ShortUserLinkMovieBind>(ShortUserLinkMovieBind.class);
        util.exportExcel(response, list, "【请填写功能名称】数据");
    }

    /**
     * 获取【请填写功能名称】详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:bind:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(shortUserLinkMovieBindService.selectShortUserLinkMovieBindById(id));
    }

    /**
     * 新增【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:bind:add')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ShortUserLinkMovieBind shortUserLinkMovieBind) {
        return toAjax(shortUserLinkMovieBindService.insertShortUserLinkMovieBind(shortUserLinkMovieBind));
    }

    /**
     * 修改【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:bind:edit')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ShortUserLinkMovieBind shortUserLinkMovieBind) {
        return toAjax(shortUserLinkMovieBindService.updateShortUserLinkMovieBind(shortUserLinkMovieBind));
    }

    /**
     * 删除【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:bind:remove')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(shortUserLinkMovieBindService.deleteShortUserLinkMovieBindByIds(ids));
    }
}
