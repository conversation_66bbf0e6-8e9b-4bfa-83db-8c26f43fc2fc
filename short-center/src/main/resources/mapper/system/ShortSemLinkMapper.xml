<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.ShortSemLinkMapper">

    <resultMap type="ShortSemLink" id="ShortSemLinkResult">
        <result property="id"    column="id"    />
        <result property="platform"    column="platform"    />
        <result property="name"    column="name"    />
        <result property="target"    column="target"    />
        <result property="callbackType"    column="callback_type"    />
        <result property="pixelCode"    column="pixel_code"    />
        <result property="landingTemplate"    column="landing_template"    />
        <result property="addtime"    column="addtime"    />
        <result property="updatetime"    column="updatetime"    />
        <result property="appId"    column="app_id"    />
        <result property="movieId"    column="movie_id"    />
        <result property="payTemplateId"    column="pay_template_id"    />
        <result property="link"    column="link"    />
        <result property="system"    column="system"    />
        <result property="url"    column="url"    />
        <result property="videoCoinPlanId"    column="video_coin_plan_id"    />
        <result property="pdata"    column="pdata"    />
        <result property="adminId"    column="admin_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="linkAppId"    column="link_app_id"    />
        <result property="pixelId"    column="pixel_id"    />
        <result property="language"    column="language"    />
    </resultMap>

    <sql id="selectShortSemLinkVo">
        select id, platform, name, target, callback_type, pixel_code, landing_template, addtime, updatetime, app_id, movie_id, pay_template_id, link, `system`, url, video_coin_plan_id, pdata, admin_id, create_by, create_time, update_by, update_time, remark, link_app_id, pixel_id, `language` from short_sem_link
    </sql>

    <select id="selectShortSemLinkList" parameterType="ShortSemLink" resultMap="ShortSemLinkResult">
        <include refid="selectShortSemLinkVo"/>
        <where>
            <if test="platform != null  and platform != ''"> and platform = #{platform}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="target != null  and target != ''"> and target = #{target}</if>
            <if test="callbackType != null  and callbackType != ''"> and callback_type = #{callbackType}</if>
            <if test="pixelCode != null  and pixelCode != ''"> and pixel_code = #{pixelCode}</if>
            <if test="landingTemplate != null  and landingTemplate != ''"> and landing_template = #{landingTemplate}</if>
            <if test="addtime != null "> and addtime = #{addtime}</if>
            <if test="updatetime != null "> and updatetime = #{updatetime}</if>
            <if test="appId != null "> and app_id = #{appId}</if>
            <if test="appIds != null and appIds.size() > 0">
                AND app_id IN
                <foreach item="item" index="index" collection="appIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="movieId != null "> and movie_id = #{movieId}</if>
            <if test="movieIds != null and movieIds.size() > 0">
                AND movie_id IN
                <foreach item="item" index="index" collection="movieIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="payTemplateId != null "> and pay_template_id = #{payTemplateId}</if>
            <if test="link != null  and link != ''"> and link = #{link}</if>
            <if test="system != null  and system != ''"> and `system` = #{system}</if>
            <if test="url != null  and url != ''"> and url = #{url}</if>
            <if test="videoCoinPlanId != null "> and video_coin_plan_id = #{videoCoinPlanId}</if>
            <if test="pdata != null  and pdata != ''"> and pdata = #{pdata}</if>
            <if test="adminId != null "> and admin_id = #{adminId}</if>
            <if test="createBy != null "> and create_by = #{createBy}</if>
            <if test="childIds != null and childIds.size() > 0">
                AND create_by IN
                <foreach item="childId" index="index" collection="childIds" open="(" separator="," close=")">
                    #{childId}
                </foreach>
            </if>
            <if test="linkAppId != null "> and link_app_id = #{linkAppId}</if>
            <if test="pixelId != null "> and pixel_id = #{pixelId}</if>
            <if test="language != null "> and `language` = #{language}</if>
        </where>
    </select>

    <select id="selectShortSemLinkById" parameterType="Long" resultMap="ShortSemLinkResult">
        <include refid="selectShortSemLinkVo"/>
        where id = #{id}
    </select>
    <select id="findByUrl" resultType="com.ruoyi.domain.ShortSemLink">
        <include refid="selectShortSemLinkVo"/>
        where link = #{url}
    </select>
    <select id="getCanguageCodeByUserId" resultType="java.lang.String">
        SELECT
            sl.`language`
        FROM
            short_order so INNER JOIN short_sem_link sl on so.link_id = sl.id
        WHERE
            so.user_id = #{userId}
          AND so.pay_type = '订阅'
          AND so.`status` = 'SUCCEEDED'
        ORDER BY
            so.pay_time DESC
            LIMIT 1
    </select>
    <select id="selectCountByMovieId" resultType="java.lang.Integer">
        select count(1)
        from short_sem_link
        where movie_id = #{movieId}
    </select>

    <insert id="insertShortSemLink" parameterType="ShortSemLink" useGeneratedKeys="true" keyProperty="id">
        insert into short_sem_link
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="platform != null">platform,</if>
            <if test="name != null">name,</if>
            <if test="target != null">target,</if>
            <if test="callbackType != null">callback_type,</if>
            <if test="pixelCode != null">pixel_code,</if>
            <if test="landingTemplate != null">landing_template,</if>
            <if test="addtime != null">addtime,</if>
            <if test="updatetime != null">updatetime,</if>
            <if test="appId != null">app_id,</if>
            <if test="movieId != null">movie_id,</if>
            <if test="payTemplateId != null">pay_template_id,</if>
            <if test="link != null">link,</if>
            <if test="system != null">`system`,</if>
            <if test="url != null">url,</if>
            <if test="videoCoinPlanId != null">video_coin_plan_id,</if>
            <if test="pdata != null">pdata,</if>
            <if test="adminId != null">admin_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="linkAppId != null">link_app_id,</if>
            <if test="pixelId != null">pixel_id,</if>
            <if test="language != null">`language`,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="platform != null">#{platform},</if>
            <if test="name != null">#{name},</if>
            <if test="target != null">#{target},</if>
            <if test="callbackType != null">#{callbackType},</if>
            <if test="pixelCode != null">#{pixelCode},</if>
            <if test="landingTemplate != null">#{landingTemplate},</if>
            <if test="addtime != null">#{addtime},</if>
            <if test="updatetime != null">#{updatetime},</if>
            <if test="appId != null">#{appId},</if>
            <if test="movieId != null">#{movieId},</if>
            <if test="payTemplateId != null">#{payTemplateId},</if>
            <if test="link != null">#{link},</if>
            <if test="system != null">#{system},</if>
            <if test="url != null">#{url},</if>
            <if test="videoCoinPlanId != null">#{videoCoinPlanId},</if>
            <if test="pdata != null">#{pdata},</if>
            <if test="adminId != null">#{adminId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="linkAppId != null">#{linkAppId},</if>
            <if test="pixelId != null">#{pixelId},</if>
            <if test="language != null">#{language},</if>
         </trim>
    </insert>

    <update id="updateShortSemLink" parameterType="ShortSemLink">
        update short_sem_link
        <trim prefix="SET" suffixOverrides=",">
            <if test="platform != null">platform = #{platform},</if>
            <if test="name != null">name = #{name},</if>
            <if test="target != null">target = #{target},</if>
            <if test="callbackType != null">callback_type = #{callbackType},</if>
            <if test="pixelCode != null">pixel_code = #{pixelCode},</if>
            <if test="landingTemplate != null">landing_template = #{landingTemplate},</if>
            <if test="addtime != null">addtime = #{addtime},</if>
            <if test="updatetime != null">updatetime = #{updatetime},</if>
            <if test="appId != null">app_id = #{appId},</if>
            <if test="movieId != null">movie_id = #{movieId},</if>
            <if test="payTemplateId != null">pay_template_id = #{payTemplateId},</if>
            <if test="link != null">link = #{link},</if>
            <if test="system != null">`system` = #{system},</if>
            <if test="url != null">url = #{url},</if>
            <if test="videoCoinPlanId != null">video_coin_plan_id = #{videoCoinPlanId},</if>
            <if test="pdata != null">pdata = #{pdata},</if>
            <if test="adminId != null">admin_id = #{adminId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="linkAppId != null">link_app_id = #{linkAppId},</if>
            <if test="pixelId != null">pixel_id = #{pixelId},</if>
            <if test="language != null">`language` = #{language},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateCreateByByPIdAndCId">
        update short_sem_link SET create_by = #{cId} WHERE create_by = #{pId};
    </update>

    <delete id="deleteShortSemLinkById" parameterType="Long">
        delete from short_sem_link where id = #{id}
    </delete>

    <delete id="deleteShortSemLinkByIds" parameterType="String">
        delete from short_sem_link where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
