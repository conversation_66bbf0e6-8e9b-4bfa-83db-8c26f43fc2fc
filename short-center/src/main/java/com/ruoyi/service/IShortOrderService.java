package com.ruoyi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.domain.ShortCountLog;
import com.ruoyi.domain.ShortOrder;
import com.ruoyi.domain.ShortUser;
import com.ruoyi.domain.ShortVip;
import com.ruoyi.dto.ShortOrderDTO;
import com.ruoyi.vo.ShortOrderOverViewVO;
import com.ruoyi.vo.ShortOrderStatisticVO;
import com.ruoyi.vo.ShortOrderVO;
import com.ruoyi.vo.ShortOrderDashboardVO;
import com.ruoyi.vo.ShortOrderDailyVO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 订单表Service接口
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
public interface IShortOrderService extends IService<ShortOrder>
{
    /**
     * 查询订单表
     *
     * @param id 订单表主键
     * @return 订单表
     */
    public ShortOrder selectShortOrderById(Long id);

    /**
     * 查询订单表列表
     *
     * @param shortOrder 订单表
     * @return 订单表集合
     */
    public List<ShortOrder> selectShortOrderList(ShortOrder shortOrder);

    /**
     * 新增订单表
     *
     * @param shortOrder 订单表
     * @return 结果
     */
    public int insertShortOrder(ShortOrder shortOrder);

    /**
     * 修改订单表
     *
     * @param shortOrder 订单表
     * @return 结果
     */
    public int updateShortOrder(ShortOrder shortOrder);

    /**
     * 批量删除订单表
     *
     * @param ids 需要删除的订单表主键集合
     * @return 结果
     */
    public int deleteShortOrderByIds(Long[] ids);

    /**
     * 删除订单表信息
     *
     * @param id 订单表主键
     * @return 结果
     */
    public int deleteShortOrderById(Long id);

    ShortOrderOverViewVO orderOverView();

    ShortOrderStatisticVO orderStatisticView(ShortOrderDTO orderDTO);

    List<ShortOrderVO> orderPageList(ShortOrderDTO orderDTO,Long filterUserId);

    /**
     * 查询最新的未支付订单
     *
     * @param userId 用户ID
     * @param appId 应用ID
     * @param vipId VIP ID
     * @return 未支付订单信息
     */
    ShortOrder findLatestUnpaidOrder(Long userId, Long appId, Long vipId);

    /**
     * 根据支付意图ID查询订单
     * @param pid
     * @return
     */
    List<ShortOrder> selectByPaymentIntentId(String pid);

    ShortOrder findFirstByUserAndPay_typeAndStatusOrderByUpdatedAtDesc(Long userId, String payType, String status);

    /**
     * 获取首页统计数据
     *
     * @param orderDTO 查询参数
     * @return 首页统计数据
     */
    ShortOrderDashboardVO dashboardStatistics(ShortOrderDTO orderDTO);

    /**
     * 订单表数据统计
     *
     * @param orderDTO 查询参数
     * @return 订单表统计数据
     */
    ShortOrderDailyVO dailyStatistics(ShortOrderDTO orderDTO);

    /**
     * 查询用户针对特定Pixel ID，在指定时间点前（含）的成功订单数量
     *
     * @param userId 用户ID
     * @param pixelId 像素ID
     * @param orderTime 时间点
     * @param currentOrderId 当前订单ID，用于排除自身
     * @return 成功订单数量
     */
    Integer countSuccessfulOrdersByPixelIdBeforeTime(Long userId, Long pixelId, java.util.Date orderTime, Long currentOrderId);

    /**
     * 查询用户针对特定Pixel ID和Link ID组合，在指定时间点前（含）的成功订单数量（用于更精确的首单判断）
     *
     * @param userId 用户ID
     * @param pixelId 像素ID
     * @param linkId 链接ID
     * @param orderTime 时间点
     * @param currentOrderId 当前订单ID，用于排除自身
     * @return 成功订单数量
     */
    Integer countSuccessfulOrdersByPixelIdAndLinkIdBeforeTime(Long userId, Long pixelId, Long linkId, java.util.Date orderTime, Long currentOrderId);

    AjaxResult countLog();

    ShortCountLog getCountList(ShortOrderDTO orderDTO);

    AjaxResult countOrderLog(String publicKey);

    ShortCountLog getCountOrderList(ShortOrderDTO orderDTO);

    List<ShortOrder> selectByPaymentRequestId(String paymentRequestId);

    List<ShortOrderVO> orderPageExportAllList(ShortOrderDTO orderDTO);

    List<ShortOrder> getSuccessOrder(Long nextId, String beginTime, String endTime);

    void updateBatchOrder(List<ShortOrder> shortOrderList);

    void updateMarkTypeByAppIdAndNullEmail(Long appId, String email);

    void updateMarkTypeByAppId(Long appId);

    void updateMarkTypeByEmail();

    void updateMarkTypeNotStatus();

    void updateMarkTypeInUserIds(List<Long> id);

    void updateMarkTypeInUserIds0(List<Long> ids);

    ShortOrder getByUserIdAndMarkType(Long id);

    List<Long> getOnlyCardList(List<Long> lastList);

    ShortOrder selectByMerchantOrderId(String merchantOrderId);
}
