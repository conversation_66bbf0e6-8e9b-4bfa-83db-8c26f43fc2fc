package com.ruoyi.app.api.strategy;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.app.api.request.PayOrderRequest;
import com.ruoyi.app.api.service.PaymentStrategy;
import com.ruoyi.app.api.service.impl.AirwallexDisputeService;
import com.ruoyi.app.api.utils.GenerateRandomCodeUtil;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.PayChannel;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.domain.*;
import com.ruoyi.service.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.util.*;

/**
 * Airwallex支付策略实现
 */
@Service
public class AirwallexStrategy implements PaymentStrategy {

    private static final Logger log = LoggerFactory.getLogger(AirwallexStrategy.class);

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private IShortUserService userService;

    @Autowired
    private IShortOrderService orderService;

    @Autowired
    private IShortRunlogService runlogService;

    @Autowired
    private IShortExtplatsService shortExtplatsService;

    @Autowired
    private IShortEmailDomainService shortEmailDomainService;

    @Autowired
    private IShortSemLinkService shortSemLinkService;

    @Value("${payment.use.dev:false}")
    private boolean useDevApi;

    @Value("${payment.api.dev.url:https://api-demo.airwallex.com}")
    private String devApiUrl;

    @Value("${payment.api.prod.url:https://api.airwallex.com}")
    private String prodApiUrl;

    @Override
    public Boolean createOrder(PayOrderRequest request, Map<String, Object> result) {
        // 1.获取参数
        ShortUser user = request.getShortUser();
        String userId = request.getUserId();
        String appId = request.getAppId();
        String amountId = request.getAmountId();
        BigDecimal amount = request.getAmount();
        BigDecimal originalPrice = request.getOriginalPrice();
        Long coin = request.getCoin();
        ShortVip vip = request.getShortVip();
        String upOrSubscribe = vip.getPayType();
        Boolean isEmailMarketing = request.getIsEmailMarketing();
        String vipType = request.getVipType();
        // 2.获取支付令牌
        String token = shortExtplatsService.getPaymentToken(appId);
        if (StringUtils.isEmpty(token)) {
            result.put("code", 405);
            result.put("msg", "没有拿到token 无法发送订单要求");
            return false;
        }

        // 3.获取或创建customer_id
        String customerId = getOrCreateCustomerId(user, token);
        if (StringUtils.isEmpty(customerId)) {
            result.put("code", 405);
            result.put("msg", "创建客户ID失败");
            return false;
        }

        // 4.查询最新的未支付订单
        ShortOrder existingOrder = orderService.findLatestUnpaidOrder(Long.parseLong(userId), Long.parseLong(appId), Long.parseLong(amountId));
        if (existingOrder != null && StringUtils.isNotEmpty(existingOrder.getPaymentIntentId())) {
            // 存在历史未支付订单，获取最新状态
            Map<String, Object> orderStatus = getPaymentIntentStatus(existingOrder.getPaymentIntentId(), token);
            if (orderStatus != null && orderStatus.containsKey("client_secret")) {
                String clientSecret = (String) orderStatus.get("client_secret");

                // 更新订单信息
                updateOrderInfo(existingOrder, user, clientSecret);

                // 如果订单中没有金币数量，但现在获取到了，则更新
                if (coin != null && coin > 0) {
                    // 使用other字段存储金币数量
                    String otherJson = existingOrder.getOther();
                    JSONObject otherData = StringUtils.isEmpty(otherJson) ? new JSONObject() : JSON.parseObject(otherJson);
                    otherData.put("coin", coin);

                    // 确保user.getOther()中的数据也被保存
                    if (StringUtils.isNotEmpty(user.getOther())) {
                        JSONObject userOtherData = JSON.parseObject(user.getOther());
                        // 遍历用户other数据中的所有键值对，添加到订单other中
                        for (Map.Entry<String, Object> entry : userOtherData.entrySet()) {
                            // 如果是coin字段，已经在上面设置过，不再覆盖
                            if (!"coin".equals(entry.getKey())) {
                                otherData.put(entry.getKey(), entry.getValue());
                            }
                        }
                    }

                    existingOrder.setOther(otherData.toJSONString());
                    orderService.updateShortOrder(existingOrder);
                }

                // 构建返回数据
                Map<String, Object> data = new HashMap<>();
                data.put("id", existingOrder.getPaymentIntentId());
                data.put("merchant_order_id", existingOrder.getMerchantOrderId());
                data.put("client_secret", clientSecret);
                data.put("currency", existingOrder.getCurrency());
                data.put("amount", amount);
                data.put("up_or_subscribe", upOrSubscribe);
                data.put("customer_id", customerId);

                result.put("code", 200);
                result.put("data", data);
                return true;

            } else {
                log.error("更新意向订单秘钥接口访问失败");

                // 使用Runlog记录异常
                try {
                    // 创建运行日志记录
                    ShortRunlog runlog = new ShortRunlog();
                    runlog.setType("异常报错");
                    runlog.setState("0"); // 0表示异常
                    runlog.setContent("更新意向订单秘钥接口访问失败, 订单ID: " + existingOrder.getPaymentIntentId());
                    runlogService.insertShortRunlog(runlog);
                } catch (Exception e) {
                    log.error("记录异常日志失败: {}", e.getMessage(), e);
                }

                result.put("code", 400);
                result.put("msg", "更新意向订单秘钥接口访问失败");
                return false;
            }
        }
        // 5. 创建新的支付意图
        Map<String, Object> paymentIntent = Collections.emptyMap();
        if(null == originalPrice){
            paymentIntent = createPaymentIntent(token, amount, customerId,Long.valueOf(appId));
        }else{
            paymentIntent = createPaymentIntent(token, originalPrice, customerId,Long.valueOf(appId));
        }
        if (paymentIntent == null) {
            result.put("code", 405);
            result.put("msg", "创建支付意图失败");
            return false;
        }

        // 6. 更新用户的VIP ID
        // userService.updateUserVipId(Long.parseLong(userId), Long.parseLong(amountId));

        // 7. 保存订单信息，传递邮件营销标识
        if(null == originalPrice)
            saveOrderInfo(paymentIntent, user, vip, appId, amount, coin, isEmailMarketing,vipType, null);
        else
            saveOrderInfo(paymentIntent, user, vip, appId, originalPrice, coin, isEmailMarketing,vipType,amount);


        log.info("【空中云汇】创建意向单：{}，并保存本地订单", JSONObject.toJSONString(paymentIntent));

        // 8. 构建返回数据
        Map<String, Object> data = new HashMap<>();
        data.put("id", paymentIntent.get("id"));
        data.put("merchant_order_id", paymentIntent.get("merchant_order_id"));
        data.put("client_secret", paymentIntent.get("client_secret"));
        data.put("currency", paymentIntent.get("currency"));
        if(null == originalPrice)
            data.put("amount", amount);
        else
            data.put("amount", originalPrice);
        data.put("up_or_subscribe", upOrSubscribe);
        data.put("customer_id", customerId);

        result.put("code", 200);
        result.put("data", data);

        log.info("创建意向订单成功");
        return true;
    }

    @Override
    public AjaxResult disputeOrder(ShortOrder order, String disputeId) {
        // 空中云汇争议处理 - 调用专门的争议服务
        log.info("处理空中云汇争议退款, 订单ID: {}, 争议ID: {}", order.getId(), disputeId);
        // 获取AirwallexDisputeService实例并调用人工处理方法
        AirwallexDisputeService airwallexDisputeService = SpringUtils.getBean(AirwallexDisputeService.class);
        return airwallexDisputeService.handleManualAcceptDispute(disputeId, order);
    }

    /**
     * 获取或创建客户ID
     */
    private String getOrCreateCustomerId(ShortUser user, String token) {
        // 检查用户是否已有customer_id
        String payInfo = user.getPayInfo();
        if (StringUtils.isNotEmpty(payInfo)) {
            try {
                // 尝试解析为JSON数组
                if (payInfo.trim().startsWith("[")) {
                    JSONArray payInfoArray = JSON.parseArray(payInfo);
                    if (payInfoArray != null && !payInfoArray.isEmpty()) {
                        JSONObject firstPayInfo = payInfoArray.getJSONObject(0);
                        if (firstPayInfo.containsKey("customer_id")) {
                            String customerId = firstPayInfo.getString("customer_id");
                            if (StringUtils.isNotEmpty(customerId)) { // 确保 customerId 不为空
                                log.info("从用户支付信息获取到客户ID: {}", customerId);
                                return customerId;
                            }
                        }
                    }
                } else {
                    // 尝试解析为JSON对象
                    JSONObject jsonPayInfo = JSON.parseObject(payInfo);
                    if (jsonPayInfo != null && jsonPayInfo.containsKey("customer_id")) {
                        String customerId = jsonPayInfo.getString("customer_id");
                        if (StringUtils.isNotEmpty(customerId)) { // 确保 customerId 不为空
                            log.info("从用户支付信息获取到客户ID: {}", customerId);
                            return customerId;
                        }
                    }
                }
                log.info("用户支付信息中不包含有效的客户ID: {}", payInfo);
            } catch (Exception e) {
                log.error("解析用户支付信息失败: {}, 原始数据: {}", e.getMessage(), payInfo, e);
                // 解析失败，继续尝试创建
            }
        }

        // 如果本地没有有效的 customer_id，则尝试创建或获取客户ID
        try {
            String merchantCustomerId = "merchant_" + user.getHashId();
            log.info("本地无有效客户ID，尝试处理客户ID: {}", merchantCustomerId);

            // 首先尝试查询客户是否已存在
            String customerId = null;
            String clientSecret = null;
            boolean customerExists = false;

            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + token);
            headers.setContentType(MediaType.APPLICATION_JSON);

            try {
                // 尝试获取已存在的客户
                ResponseEntity<String> getResponse = restTemplate.exchange(
                        (useDevApi ? devApiUrl : prodApiUrl) + "/api/v1/pa/customers/" + merchantCustomerId,
                        HttpMethod.GET,
                        new HttpEntity<>(headers),
                        String.class
                );

                if (getResponse.getStatusCodeValue() == 200) {
                    JSONObject customerJson = JSON.parseObject(getResponse.getBody());
                    customerId = customerJson.getString("id");
                    clientSecret = customerJson.getString("client_secret");
                    customerExists = true;
                    log.info("客户ID已存在，获取信息成功: {}", customerId);
                }
            } catch (HttpClientErrorException e) {
                // 404表示客户不存在，这不是错误，而是预期的情况之一
                if (e.getStatusCode().value() != 404) {
                    log.error("查询客户时发生HTTP错误: {} - {}", e.getStatusCode(), e.getResponseBodyAsString());
                }
            } catch (Exception e) {
                log.error("查询客户时发生异常: {}", e.getMessage());
            }

            // 如果客户不存在，创建新客户
            if (!customerExists) {
                String requestId = UUID.randomUUID().toString();
                Map<String, Object> requestBody = new HashMap<>();
                requestBody.put("request_id", requestId);
                requestBody.put("merchant_customer_id", merchantCustomerId);
                requestBody.put("email", user.getEmail());

                HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

                try {
                    ResponseEntity<String> createResponse = restTemplate.exchange(
                            (useDevApi ? devApiUrl : prodApiUrl) + "/api/v1/pa/customers/create",
                            HttpMethod.POST,
                            entity,
                            String.class
                    );

                    if (createResponse.getStatusCodeValue() == 201) {
                        JSONObject jsonResponse = JSON.parseObject(createResponse.getBody());
                        customerId = jsonResponse.getString("id");
                        clientSecret = jsonResponse.getString("client_secret");
                        log.info("成功创建新客户: {}", customerId);
                    } else {
                        log.error("创建客户失败，状态码: {}, 响应: {}",
                                createResponse.getStatusCodeValue(), createResponse.getBody());
                    }
                } catch (Exception e) {
                    log.error("创建客户时发生异常: {}", e.getMessage());
                }
            }

            // 如果成功获取或创建了客户ID，保存到用户信息中
            if (StringUtils.isNotEmpty(customerId)) {
                // 构建支付信息并更新用户 - 使用数组格式
                JSONObject customerInfo = new JSONObject();
                customerInfo.put("type", "airwallex");
                customerInfo.put("customer_id", customerId);
                if (StringUtils.isNotEmpty(clientSecret)) {
                    customerInfo.put("client_secret", clientSecret);
                }

                JSONArray payInfoArray = new JSONArray();
                payInfoArray.add(customerInfo);

                // 将支付信息保存到用户
                String newPayInfo = payInfoArray.toJSONString();
                ShortUser updateUser = new ShortUser();
                updateUser.setId(user.getId());
                updateUser.setPayInfo(newPayInfo);
                userService.updateShortUser(updateUser);

                log.info("成功获取/创建并保存客户ID: {}", customerId);
                return customerId;
            }

            return null; // 如果获取或创建失败，返回null
        } catch (Exception e) {
            // 捕获流程中的任何意外错误
            log.error("获取或创建客户ID过程中发生意外异常: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取支付意图状态
     */
    private Map<String, Object> getPaymentIntentStatus(String paymentIntentId, String token) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + token);
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<String> entity = new HttpEntity<>(headers);

            ResponseEntity<String> response = restTemplate.exchange(
                    (useDevApi ? devApiUrl : prodApiUrl) + "/api/v1/pa/payment_intents/" + paymentIntentId,
                    HttpMethod.GET,
                    entity,
                    String.class
            );

            if (response.getStatusCodeValue() == 200) {
                return JSON.parseObject(response.getBody(), Map.class);
            }
        } catch (Exception e) {
            log.error("获取支付意图状态失败: {}", e.getMessage(), e);
        }
        return null;
    }

    /**
     * 更新订单信息
     */
    private void updateOrderInfo(ShortOrder order, ShortUser user, String clientSecret) {
        // 更新client_secret
        order.setClientSecret(clientSecret);

        // 更新时间
        Date now = new Date();
        order.setUpdatedAt(now);
        order.setUpdateTime(now);

        // 更新用户相关信息
        if (user.getOther() != null) {
            JSONObject otherJson = JSON.parseObject(user.getOther());
            if (otherJson != null && otherJson.containsKey("adId")) {
                order.setAdid(otherJson.getString("adId"));
            }
            // 完整保存用户的other信息
            order.setOther(user.getOther());
        }

        // 更新链接信息
        if (user.getLinkidId() != null) {
            order.setLinkId(user.getLinkidId());
        }
        order.setLinkTime(user.getLinkTime());

        // 保存更新
        orderService.updateShortOrder(order);
    }

    /**
     * 创建支付意图
     */
    private Map<String, Object> createPaymentIntent(String token, BigDecimal amount, String customerId,Long appId) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + token);
            headers.setContentType(MediaType.APPLICATION_JSON);

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("request_id", GenerateRandomCodeUtil.generateRandomCode());
            requestBody.put("amount", amount.doubleValue());
            requestBody.put("currency", "USD");
            requestBody.put("merchant_order_id", "Merchant_Order" + GenerateRandomCodeUtil.generateRandomCode());
            requestBody.put("customer_id", customerId);

            ShortEmailDomain shortEmailDomain = shortEmailDomainService.getrandomDomain(appId);
            if(null != shortEmailDomain)
                requestBody.put("descriptor", shortEmailDomain.getAppName());

//            HashMap hashMap = new HashMap();
//            hashMap.put("email",email);
//            hashMap.put("merchant_customer_id",customerId);
//            hashMap.put("first_name","name");
//            hashMap.put("last_name","name");
//
//            requestBody.put("customer", hashMap);
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            ResponseEntity<String> response = restTemplate.exchange(
                    (useDevApi ? devApiUrl : prodApiUrl) + "/api/v1/pa/payment_intents/create",
                    HttpMethod.POST,
                    entity,
                    String.class
            );

            return JSON.parseObject(response.getBody(), Map.class);
        } catch (Exception e) {
            log.error("创建支付意图失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 保存订单信息
     */
    private void saveOrderInfo(Map<String, Object> paymentIntent,
                               ShortUser user,
                               ShortVip vip,
                               String appId,
                               BigDecimal amount,
                               Long coin,
                               boolean isEmailMarketing,
                               String vipType,
                               BigDecimal originalPrice) {
        // 重新查询用户最新信息，确保pixelId是最新的
        ShortUser latestUser = userService.selectShortUserById(user.getId());

        // 创建订单
        ShortOrder order = new ShortOrder();
        order.setAppId(Long.parseLong(appId));
        order.setUserId(user.getId());
        order.setVipId(vip.getId());
        order.setPayType(vip.getPayType());
        order.setOrdersn((String) paymentIntent.get("merchant_order_id"));
        order.setMerchantOrderId((String) paymentIntent.get("merchant_order_id"));
        order.setPaymentIntentId((String) paymentIntent.get("id"));
        order.setRequestId((String) paymentIntent.get("request_id"));
        order.setPaymentCurrency("USD");
        order.setPaymentAmount(amount);
        order.setAmount(amount);
        order.setOriginalPrice(originalPrice);
        order.setCurrency((String) paymentIntent.get("currency"));
        order.setStatus((String) paymentIntent.get("status"));
        order.setClientSecret((String) paymentIntent.get("client_secret"));
        order.setMarkType(0);
        // 设置邮件营销订单标识
        order.setIsEmailMarketingOrder(isEmailMarketing ? 1 : 0);
        if (isEmailMarketing) {
            log.info("创建邮件营销订单，用户ID: {}, 支付意图ID: {}", user.getId(), paymentIntent.get("id"));
        }
        order.setVipType(vipType);
        // 设置push_state字段
        order.setPushState("0"); // 初始状态为0，表示未推送

        // 设置时间字段
        Date now = new Date();
        order.setCreatedAt(now);
        order.setUpdatedAt(now);
        order.setCreateTime(now);
        order.setUpdateTime(now);
        order.setCountry(user.getCountry());
        // 设置用户相关信息
        if (user.getOther() != null) {
            JSONObject otherJson = JSON.parseObject(user.getOther());
            if (otherJson != null && otherJson.containsKey("adId")) {
                order.setAdid(otherJson.getString("adId"));
            }
        }

        // 设置链接信息 - 使用最新的用户信息
        if (latestUser.getLinkidId() != null) {
            order.setLinkId(latestUser.getLinkidId());
            
            // 根据linkId获取对应的pixelId，确保数据一致性
            try {
                ShortSemLink shortSemLink = shortSemLinkService.selectShortSemLinkById(latestUser.getLinkidId());
                if (shortSemLink != null && shortSemLink.getPixelId() != null) {
                    order.setPixelId(shortSemLink.getPixelId());
                    log.info("订单使用深链pixelId: linkId={}, pixelId={}", 
                             latestUser.getLinkidId(), shortSemLink.getPixelId());
                } else {
                    order.setPixelId(latestUser.getPixelId());
                    log.warn("深链pixelId为空，使用用户pixelId: {}", latestUser.getPixelId());
                }
            } catch (Exception e) {
                log.error("获取深链pixelId失败，使用用户pixelId: {}", latestUser.getPixelId(), e);
                order.setPixelId(latestUser.getPixelId());
            }
        } else {
            order.setPixelId(latestUser.getPixelId());
        }
        order.setLinkTime(user.getLinkTime());

        // 设置other字段，存储额外信息（包括金币数量）
        JSONObject otherData = new JSONObject();
        if (StringUtils.isNotEmpty(latestUser.getOther())) {
            otherData = JSON.parseObject(latestUser.getOther());
        }
        if (coin != null && coin > 0) {
            otherData.put("coin", coin);
        }
        order.setOther(otherData.toJSONString());
        // 保存订单
        order.setPixelStatus(null != order.getPixelStatus() ? order.getPixelStatus() : 0);
        // 设置订单渠道为空中云汇
        order.setPayChannel(PayChannel.AIRWALLEX.getCode());
        orderService.insertShortOrder(order);
    }

}
