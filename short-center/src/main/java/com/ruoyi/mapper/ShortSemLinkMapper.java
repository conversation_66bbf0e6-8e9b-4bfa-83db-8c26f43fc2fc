package com.ruoyi.mapper;

import com.ruoyi.domain.ShortSemLink;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 推广链接Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
public interface ShortSemLinkMapper {
    /**
     * 查询推广链接
     *
     * @param id 推广链接主键
     * @return 推广链接
     */
    public ShortSemLink selectShortSemLinkById(Long id);

    /**
     * 查询推广链接列表
     *
     * @param shortSemLink 推广链接
     * @return 推广链接集合
     */
    public List<ShortSemLink> selectShortSemLinkList(ShortSemLink shortSemLink);

    /**
     * 新增推广链接
     *
     * @param shortSemLink 推广链接
     * @return 结果
     */
    public int insertShortSemLink(ShortSemLink shortSemLink);

    /**
     * 修改推广链接
     *
     * @param shortSemLink 推广链接
     * @return 结果
     */
    public int updateShortSemLink(ShortSemLink shortSemLink);

    /**
     * 删除推广链接
     *
     * @param id 推广链接主键
     * @return 结果
     */
    public int deleteShortSemLinkById(Long id);

    /**
     * 批量删除推广链接
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteShortSemLinkByIds(Long[] ids);

    ShortSemLink findByUrl(String url);

    int updateCreateByByPIdAndCId(@Param("pId") Long pId,@Param("cId") Long cId);

    String getCanguageCodeByUserId(String userId);

    int selectCountByMovieId(@Param("movieId") Long movieId);
}
