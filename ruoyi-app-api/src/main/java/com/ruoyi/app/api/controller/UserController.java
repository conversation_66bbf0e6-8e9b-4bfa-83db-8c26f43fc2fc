package com.ruoyi.app.api.controller;

import com.ruoyi.app.api.annotation.TokenRequired;
import com.ruoyi.app.api.service.UserApiService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.domain.ShortUser;
import com.ruoyi.domain.ShortUserCollectAndHistory;
import com.ruoyi.dto.CheckShortUserDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.util.Map;

/**
 * 用户API控制器
 */
@Api(tags = "用户API")
@RestController
@RequestMapping("/api")
public class UserController {

    @Autowired
    private UserApiService userApiService;

    /**
     * 获取用户信息
     */
    @ApiOperation(value = "获取用户信息", notes = "根据token获取用户详细信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "认证token", paramType = "header", required = true),
            @ApiImplicitParam(name = "nid", value = "app_id", paramType = "header", required = true),
            @ApiImplicitParam(name = "uid", value = "用户ID", paramType = "header")
    })
    @GetMapping("/user/")
    public AjaxResult getUserInfo(HttpServletRequest request) throws ParseException {
        // 从Header获取AppId
        String appId = request.getHeader("nid");
        // 获取token
        String token = request.getHeader("Authorization");
        // 获取用户ID
        String userId = request.getHeader("uid");

        // 调用服务获取用户信息
        Map<String, Object> userInfo = userApiService.getUserInfo(userId, appId, token);

        return AjaxResult.success("获取用户信息成功", userInfo);
    }

    /**
     * 用户退出登录
     */
    @ApiOperation(value = "用户退出登录", notes = "用户退出登录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "认证token", paramType = "header", required = true),
            @ApiImplicitParam(name = "nid", value = "app_id", paramType = "header", required = true)
    })
    @TokenRequired
    @PostMapping("/logout/")
    public AjaxResult logout(@RequestBody Map<String, Object> requestBody, HttpServletRequest request) {

        // 从请求体获取用户ID
        Object userObj = requestBody.get("user");
        String userIdFromRequest = null;
        if (userObj != null) {
            userIdFromRequest = userObj.toString();
        } else {
            return AjaxResult.error("请求体缺少 'user' 字段");
        }

        String appId = request.getHeader("nid"); // 从 Header 获取 appId

        Map<String, Object> result = userApiService.logout(userIdFromRequest, appId);

        return AjaxResult.success((String) result.get("msg"));
    }

    /**
     * 用户删除账户
     */
    @ApiOperation(value = "用户删除账户", notes = "用户删除自己的账户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "认证token", paramType = "header", required = true),
            @ApiImplicitParam(name = "nid", value = "app_id", paramType = "header", required = true)
    })
    @TokenRequired
    @PostMapping("/user_delete/")
    public AjaxResult deleteUser(@RequestBody Map<String, Object> requestBody, HttpServletRequest request) {
        Object userObj = requestBody.get("user");
        String userIdFromRequest = null;
        if (userObj != null) {
            userIdFromRequest = userObj.toString();
        } else {
            return AjaxResult.error("请求体缺少 'user' 字段");
        }

        String appId = request.getHeader("nid");

        Map<String, Object> result = userApiService.deleteUser(userIdFromRequest, appId);

        return AjaxResult.success((String) result.get("msg"));
    }

    /**
     * 用户注册和登录
     */
    @ApiOperation(value = "用户注册和登录", notes = "用户注册或登录并返回token")
    @ApiImplicitParam(name = "nid", value = "app_id", paramType = "header", required = true)
    @PostMapping("/register/")
    public AjaxResult register(@RequestBody Map<String, Object> registrationData, HttpServletRequest request) {
        // 获取AppId
        String appId = request.getHeader("nid");
        registrationData.put("appId", appId);

        // 获取 packageName
        String packageName = request.getHeader("PACKAGENAME");
        registrationData.put("packageName", packageName);

        // 调用服务
        Map<String, Object> result = userApiService.registerOrLogin(registrationData);

        // 检查返回码
        Integer code = (Integer) result.get("code");
        String msg = (String) result.get("msg");

        // 用户已注销的情况
        if (code != null && code == 1) {
            return AjaxResult.error(code, msg);
        }

        // 其他错误情况
        if (code != null && code != 200) {
            return AjaxResult.error(msg);
        }

        // 成功情况下返回data部分
        @SuppressWarnings("unchecked")
        Map<String, Object> data = (Map<String, Object>) result.get("data");
        return AjaxResult.success("登陆成功", data);
    }

    /**
     * 邮箱注册
     *
     * @param registrationData
     * @param request
     * @return
     */
    @ApiOperation(value = "用户邮箱注册和登录", notes = "用户邮箱注册和登录并返回token")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uid", value = "userId", paramType = "header", required = true),
            @ApiImplicitParam(name = "nid", value = "app_id", paramType = "header", required = true)
    })
    @PostMapping("/email_register/")
    public AjaxResult emailRegister(@RequestBody Map<String, Object> registrationData, HttpServletRequest request) {
        // 获取AppId
        String appId = request.getHeader("nid");
        registrationData.put("appId", appId);
        //获取userId
        String userId = request.getHeader("uid");
        if (StringUtils.isNotEmpty(userId)) {
            // 处理uid前缀
            if (userId.length() > 4) {
                userId = userId.substring(4);
            }
        }
        registrationData.put("userId", userId);
        // 获取 packageName
        String packageName = request.getHeader("PACKAGENAME");
        registrationData.put("packageName", packageName);
        Map<String, Object> result = userApiService.registerByEmail(registrationData);
        // 检查返回码
        Integer code = (Integer) result.get("code");
        String msg = (String) result.get("msg");
        // 用户已注销的情况
        if (code != null && code == 1) {
            return AjaxResult.error(code, msg);
        }
        // 其他错误情况
        if (code != null && code != 200) {
            return AjaxResult.error(msg);
        }
        // 成功情况下返回data部分
        @SuppressWarnings("unchecked")
        Map<String, Object> data = (Map<String, Object>) result.get("data");
        return AjaxResult.success("登陆成功", data);
    }

    /**
     * 用户签到
     */
    @ApiOperation(value = "用户签到", notes = "用户进行签到操作")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "认证token", paramType = "header", required = true),
            @ApiImplicitParam(name = "nid", value = "app_id", paramType = "header", required = true)
    })
    @TokenRequired
    @PostMapping("/sign_in/")
    public AjaxResult signIn(HttpServletRequest request) {
        String userId = (String) request.getAttribute("userId");
        String appId = request.getHeader("nid");

        // 调用服务进行签到
        Map<String, Object> result = userApiService.signIn(userId, appId);

        // 处理返回结果
        Integer code = (Integer) result.get("code");
        String msg = (String) result.get("msg");

        if (code == 200 && result.containsKey("data")) {
            @SuppressWarnings("unchecked")
            Map<String, Object> data = (Map<String, Object>) result.get("data");
            return AjaxResult.success("签到成功", data);
        }

        return AjaxResult.error(code, msg);
    }

    /**
     * 获取用户签到信息
     */
    @ApiOperation(value = "获取用户签到信息", notes = "获取用户签到状态和天数")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "认证token", paramType = "header", required = true),
            @ApiImplicitParam(name = "nid", value = "app_id", paramType = "header", required = true)
    })
    @GetMapping("/get_sign_in/")
    public AjaxResult getSignInInfo(HttpServletRequest request) {
        String appId = request.getHeader("nid");
        // 获取token
        String token = request.getHeader("Authorization");

        // 调用服务获取签到信息
        Map<String, Object> result = userApiService.getSignInInfo(appId, token);

        // 处理返回结果
        Integer code = (Integer) result.get("code");
        String msg = (String) result.get("msg");

        if (code == 200 && result.containsKey("data")) {
            @SuppressWarnings("unchecked")
            Map<String, Object> data = (Map<String, Object>) result.get("data");
            return AjaxResult.success(msg, data);
        }

        return AjaxResult.error(code, msg);
    }

    @GetMapping(value = "/refreshCountry")
    public void refreshCountry(Integer flag) {
        userApiService.refreshCountry(flag);
    }

    /**
     * 根据用户 IP + linkID 查询用户信息
     *
     * @param request
     * @param shortUserDTO
     * @return
     */
    @ApiOperation(value = "根据linkId IP linkUrl查询用户信息", notes = "查询登录用户信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "nid", value = "app_id", paramType = "header", required = true)
    })
    @PostMapping(value = "/checkExistUser")
    public AjaxResult getExistUser(HttpServletRequest request, @RequestBody CheckShortUserDTO shortUserDTO) {
        String appId = request.getHeader("nid");
        shortUserDTO.setAppId(Long.valueOf(appId));
        ShortUser user = userApiService.getUserByLinkIdAndIp(shortUserDTO);
        return AjaxResult.success(user);
    }


    /**
     * 用户订阅记录
     */
    @ApiOperation(value = "用户订阅记录", notes = "用户订阅记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "认证token", paramType = "header", required = true)
    })
    @GetMapping("/subscribe_record")
    public AjaxResult subscribeRecord(String email) {
        if (StringUtils.isEmpty(email)) {
            return AjaxResult.error("邮箱不能为空");
        }
        Map<String, Object> result = userApiService.subscribeRecord(email);
        return AjaxResult.success("成功", result);
    }

    @ApiOperation(value = "同步异站用户续订数据到新用户", notes = "同步异站用户续订数据到新用户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uid", value = "userId", paramType = "header", required = true),
            @ApiImplicitParam(name = "Authorization", value = "认证token", paramType = "header", required = true)
    })
    @GetMapping("/sync_user_data")
    public AjaxResult syncUserData(HttpServletRequest request) {
        //获取userId
        String userId = request.getHeader("uid");
        if (StringUtils.isNotEmpty(userId)) {
            // 处理uid前缀
            if (userId.length() > 4) {
                userId = userId.substring(4);
            }
        }
        return userApiService.syncUserData(userId);
    }


    @ApiOperation(value = "用户收藏或观看历史", notes = "type:collect|history")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uid", value = "userId", paramType = "header", required = true),
            @ApiImplicitParam(name = "Authorization", value = "认证token", paramType = "header", required = true)
    })
    @GetMapping("/user_collect_history")
    public AjaxResult userCollectHistory(HttpServletRequest request, String type) {
        //获取userId
        String userId = request.getHeader("uid");
        if (StringUtils.isNotEmpty(userId)) {
            // 处理uid前缀
            if (userId.length() > 4) {
                userId = userId.substring(4);
            }
        }
        return userApiService.userCollectHistory(userId, type);
    }

    @ApiOperation(value = "新增用户收藏或观看历史", notes = "type:collect|history")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uid", value = "userId", paramType = "header", required = true),
            @ApiImplicitParam(name = "Authorization", value = "认证token", paramType = "header", required = true)
    })
    @PostMapping("/add_user_collect_history")
    public AjaxResult addUserCollectHistory(HttpServletRequest request, @RequestBody ShortUserCollectAndHistory shortUserCollectAndHistory) {
        //获取userId
        String userId = request.getHeader("uid");
        if (StringUtils.isNotEmpty(userId)) {
            // 处理uid前缀
            if (userId.length() > 4) {
                userId = userId.substring(4);
            }
        }
        shortUserCollectAndHistory.setUserId(Long.valueOf(userId));
        return userApiService.addUserCollectHistory(shortUserCollectAndHistory);
    }

    @ApiOperation(value = "删除用户收藏或观看历史", notes = "type:collect|history")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "认证token", paramType = "header", required = true)
    })
    @DeleteMapping("/delete_user_collect_history")
    public AjaxResult deleteUserCollectHistory(String id) {
        return userApiService.deleteUserCollectHistory(id);
    }

    @ApiOperation(value = "用户是否收藏", notes = "用户是否收藏")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uid", value = "userId", paramType = "header", required = true),
            @ApiImplicitParam(name = "Authorization", value = "认证token", paramType = "header", required = true)
    })
    @GetMapping("/is_collect")
    public AjaxResult isCollect(HttpServletRequest request, String movieId) {
        //获取userId
        String userId = request.getHeader("uid");
        if (StringUtils.isNotEmpty(userId)) {
            // 处理uid前缀
            if (userId.length() > 4) {
                userId = userId.substring(4);
            }
        }
        return userApiService.isCollect(Long.valueOf(userId), movieId);
    }
}
