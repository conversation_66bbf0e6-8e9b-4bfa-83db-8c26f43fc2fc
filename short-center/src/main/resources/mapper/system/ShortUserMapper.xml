<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.ShortUserMapper">

    <resultMap type="ShortUser" id="ShortUserResult">
        <result property="id"    column="id"    />
        <result property="appId"    column="app_id"    />
        <result property="vipId"    column="vip_id"    />
        <result property="payTemplateId"    column="pay_template_id" />
        <result property="payByEmailFlag"  column="pay_by_email_flag"/>
        <result property="linkidId"    column="linkid_id"    />
        <result property="username"    column="username"    />
        <result property="password"    column="password"    />
        <result property="avatar"    column="avatar"    />
        <result property="state"    column="state"    />
        <result property="source"    column="source"    />
        <result property="email"    column="email"    />
        <result property="emailBindDate"    column="email_bind_date"    />
        <result property="emailBindLink"    column="email_bind_link"    />
        <result property="phoneVersion"    column="phone_version"    />
        <result property="systemVersion"    column="system_version"    />
        <result property="country"    column="country"    />
        <result property="language"    column="language"    />
        <result property="appVersion"    column="app_version"    />
        <result property="accountType"    column="account_type"    />
        <result property="uniqueId"    column="unique_id"    />
        <result property="ip"    column="ip"    />
        <result property="subId"    column="sub_id"    />
        <result property="token"    column="token"    />
        <result property="coin"    column="coin"    />
        <result property="signInDays"    column="sign_in_days"    />
        <result property="lastSignIn"    column="last_sign_in"    />
        <result property="expireTime"    column="expire_time"    />
        <result property="other"    column="other"    />
        <result property="isFirstPay"    column="is_first_pay"    />
        <result property="pushNum"    column="push_num"    />
        <result property="hashId"    column="hash_id"    />
        <result property="isSubscriber"    column="is_subscriber"    />
        <result property="payInfo"    column="pay_info"    />
        <result property="linkTime"    column="link_time"    />
        <result property="note"    column="note"    />
        <result property="renewalOk"    column="renewal_ok"    />
        <result property="renewalNo"    column="renewal_no"    />
        <result property="userType"    column="user_type"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="addtime"    column="addtime"    />
        <result property="updatetime"    column="updatetime"    />
        <result property="unsub"    column="unsub"    />
        <result property="pixelId"    column="pixel_id"    />
        <result property="pixelStatus"    column="pixel_status"    />
        <result property="validStatus"    column="valid_status"    />
        <result property="subIntervalDays"    column="sub_interval_days"    />
        <result property="lastChargeTime"    column="last_charge_time"    />
        <result property="adId"    column="ad_id"  />
        <result property="appType" column="app_type"/>
        <result property="deviceType" column="device_type"/>
        <result property="subscriptionOldUserId" column="subscription_old_user_id"/>
    </resultMap>

    <sql id="selectShortUserVo">
        select id, app_id, vip_id, pay_template_id,pay_by_email_flag, linkid_id, username, password, avatar, state, source, email,email_bind_link, email_bind_date,phone_version, system_version,country, `language`, app_version, account_type, unique_id, ip, sub_id, token, coin, sign_in_days, last_sign_in, expire_time, other, is_first_pay, push_num, hash_id, is_subscriber, pay_info, link_time, note, renewal_ok, renewal_no, user_type, status, create_by, create_time, update_by, update_time, remark, addtime, updatetime,unsub,pixel_id,pixel_status,valid_status,sub_interval_days,last_charge_time,choose_language,ad_id ,app_type,device_type,unsub_time,is_check,subscription_old_user_id,code_num,cycle_num,last_code from short_user
    </sql>

    <select id="selectShortUserList" parameterType="ShortUser" resultMap="ShortUserResult">
        <include refid="selectShortUserVo"/>
        <where>
            <if test="appId != null "> and app_id = #{appId}</if>
            <if test="vipId != null "> and vip_id = #{vipId}</if>
            <if test="payTemplateId != null "> and pay_template_id = #{payTemplateId}</if>
            <if test="linkidId != null "> and linkid_id = #{linkidId}</if>
            <if test="username != null  and username != ''"> and username like concat('%', #{username}, '%')</if>
            <if test="password != null  and password != ''"> and password = #{password}</if>
            <if test="avatar != null  and avatar != ''"> and avatar = #{avatar}</if>
            <if test="state != null  and state != ''"> and state = #{state}</if>
            <if test="source != null  and source != ''"> and source = #{source}</if>
            <if test="email != null  and email != ''"> and email = #{email}</if>
            <if test="phoneVersion != null  and phoneVersion != ''"> and phone_version = #{phoneVersion}</if>
            <if test="systemVersion != null  and systemVersion != ''"> and system_version = #{systemVersion}</if>
            <if test="language != null  and language != ''"> and language = #{language}</if>
            <if test="appVersion != null  and appVersion != ''"> and app_version = #{appVersion}</if>
            <if test="accountType != null  and accountType != ''"> and account_type = #{accountType}</if>
            <if test="uniqueId != null  and uniqueId != ''"> and unique_id = #{uniqueId}</if>
            <if test="ip != null  and ip != ''"> and ip = #{ip}</if>
            <if test="subId != null  and subId != ''"> and sub_id = #{subId}</if>
            <if test="token != null  and token != ''"> and token = #{token}</if>
            <if test="coin != null "> and coin = #{coin}</if>
            <if test="signInDays != null "> and sign_in_days = #{signInDays}</if>
            <if test="lastSignIn != null "> and last_sign_in = #{lastSignIn}</if>
            <if test="expireTime != null "> and expire_time = #{expireTime}</if>
            <if test="other != null  and other != ''"> and other = #{other}</if>
            <if test="isFirstPay != null  and isFirstPay != ''"> and is_first_pay = #{isFirstPay}</if>
            <if test="pushNum != null "> and push_num = #{pushNum}</if>
            <if test="hashId != null  and hashId != ''"> and hash_id = #{hashId}</if>
            <if test="isSubscriber != null  and isSubscriber != ''"> and is_subscriber = #{isSubscriber}</if>
            <if test="payInfo != null  and payInfo != ''"> and pay_info = #{payInfo}</if>
            <if test="linkTime != null "> and link_time = #{linkTime}</if>
            <if test="note != null  and note != ''"> and note = #{note}</if>
            <if test="renewalOk != null "> and renewal_ok = #{renewalOk}</if>
            <if test="renewalNo != null "> and renewal_no = #{renewalNo}</if>
            <if test="userType != null  and userType != ''"> and user_type = #{userType}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="unsub != null  and unsub != ''"> and unsub = #{unsub}</if>
             <if test="pixelId != null "> and pixel_id = #{pixelId}</if>
            <if test="pixelStatus != null "> and pixel_status = #{pixelStatus}</if>
            <if test="validStatus != null "> and valid_status = #{validStatus}</if>
            <if test="subIntervalDays != null "> and sub_interval_days = #{subIntervalDays}</if>
            <if test="lastChargeTime != null "> and last_charge_time = #{lastChargeTime}</if>
            <if test="chooseLanguage != null "> and choose_language = #{chooseLanguage}</if>
            <if test="subscriptionOldUserId != null">and subscription_old_user_id = #{subscriptionOldUserId}</if>
        </where>
        ORDER BY id desc
    </select>

    <select id="selectShortUserById" parameterType="Long" resultMap="ShortUserResult">
        SELECT
            su.id,
            su.app_id,
            su.vip_id,
            su.pay_template_id,
            su.linkid_id,
            su.username,
            su.PASSWORD,
            su.avatar,
            su.state,
            su.source,
            su.email,
            su.phone_version,
            su.system_version,
            su.country,
            su.LANGUAGE,
            su.app_version,
            su.account_type,
            su.unique_id,
            su.ip,
            su.pay_by_email_flag,
            su.sub_id,
            su.token,
            su.coin,
            su.sign_in_days,
            su.last_sign_in,
            su.expire_time,
            su.other,
            su.is_first_pay,
            su.push_num,
            su.hash_id,
            su.is_subscriber,
            su.pay_info,
            su.link_time,
            su.note,
            su.renewal_ok,
            su.renewal_no,
            su.user_type,
            su.STATUS,
            su.create_by,
            su.create_time,
            su.update_by,
            su.update_time,
            su.remark,
            su.addtime,
            su.updatetime,
            sa.`name` appName,
            su.unsub,
            su.pixel_id,
            su.pixel_status,
            su.valid_status,
            su.email_bind_link,
            su.email_bind_date,
            su.sub_interval_days,
            su.last_charge_time,
            su.unsub_time,
            su.is_check,
            su.subscription_old_user_id,
            su.code_num,
            su.cycle_num,
            su.last_code
        FROM
            short_user su
            LEFT JOIN short_app sa on su.app_id = sa.id
        where su.id = #{id}
    </select>


    <select id="selectByLinkIdList" resultMap="ShortUserResult">
        <include refid="selectShortUserVo"/> where
         linkid_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>

    </select>

    <select id="selectShortUserBySubId" parameterType="String" resultMap="ShortUserResult">
        <include refid="selectShortUserVo"/>
        where sub_id = #{subId}
    </select>
    <select id="findByIdAndAppIdAndSource" resultType="com.ruoyi.domain.ShortUser">
        <include refid="selectShortUserVo"/>
        WHERE
        state = 1
        <if test="nid != null">
            AND app_id = #{nid}
        </if>
        <if test="uid != null">
            AND id = #{uid}
        </if>
        <if test="source != null">
            AND source = #{source}
        </if>
    </select>
    <select id="getCount" resultType="java.lang.Integer">
        SELECT
        COUNT(su.id)
        FROM
        short_user su LEFT JOIN short_vip sv on su.vip_id= sv.id
        LEFT JOIN short_sem_link sl on sl.id = su.linkid_id
        <where>
            <if test="filterUserId != null and filterUserId != 1">
                AND sl.create_by = #{filterUserId}
            </if>
            <if test="childIds != null and childIds.size() > 0">
                AND sl.create_by IN
                <foreach item="childId" index="index" collection="childIds" open="(" separator="," close=")">
                    #{childId}
                </foreach>
            </if>
            <if test="appId != null">
                AND su.app_id = #{appId}
            </if>
            <if test="appIds != null and appIds.size() > 0">
                AND su.app_id IN
                <foreach item="item" index="index" collection="appIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="state != null">and su.state = #{state} </if>
            <if test="countType ==1"> and su.linkid_id > 0 </if>
<!--            <if test="subscriptionType != null"> and sv.subscription_type =#{subscriptionType} </if>-->
            <if test="subscriptionType != null and subscriptionType != 'pay_all'"> and sv.subscription_type =#{subscriptionType} </if>
            <if test="subscriptionType == 'pay_all'"> and sv.subscription_type in(0,7,30,365) </if>
            <if test="beginTime != null">
                AND DATE_FORMAT(su.create_time, '%Y-%m-%d') >= #{beginTime}
            </if>
            <if test="endTime != null">
                AND DATE_FORMAT(su.create_time, '%Y-%m-%d') &lt;= #{endTime}
            </if>
            <if test="userIdOrName != null"> and (su.id like concat('%', #{userIdOrName}, '%') or su.username like concat('%', #{userIdOrName}, '%')) </if>
        </where>
    </select>

    <!-- 查询用户并添加行锁 -->
    <select id="selectShortUserWithLock" parameterType="Long" resultMap="ShortUserResult">
        <include refid="selectShortUserVo"/>
        where id = #{userId} FOR UPDATE
    </select>

    <insert id="insertShortUser" parameterType="ShortUser" useGeneratedKeys="true" keyProperty="id">
        insert into short_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appId != null">app_id,</if>
            <if test="vipId != null">vip_id,</if>
            <if test="payTemplateId != null">pay_template_id,</if>
            <if test="payByEmailFlag != null">pay_by_email_flag,</if>
            <if test="linkidId != null">linkid_id,</if>
            <if test="username != null">username,</if>
            <if test="password != null">password,</if>
            <if test="avatar != null">avatar,</if>
            <if test="state != null">state,</if>
            <if test="source != null">source,</if>
            <if test="email != null">email,</if>
            <if test="phoneVersion != null">phone_version,</if>
            <if test="systemVersion != null">system_version,</if>
            <if test="country != null">country,</if>
            <if test="language != null">language,</if>
            <if test="appVersion != null">app_version,</if>
            <if test="accountType != null">account_type,</if>
            <if test="uniqueId != null">unique_id,</if>
            <if test="ip != null">ip,</if>
            <if test="subId != null">sub_id,</if>
            <if test="token != null">token,</if>
            <if test="coin != null">coin,</if>
            <if test="signInDays != null">sign_in_days,</if>
            <if test="lastSignIn != null">last_sign_in,</if>
            <if test="expireTime != null">expire_time,</if>
            <if test="other != null">other,</if>
            <if test="isFirstPay != null">is_first_pay,</if>
            <if test="pushNum != null">push_num,</if>
            <if test="hashId != null">hash_id,</if>
            <if test="isSubscriber != null">is_subscriber,</if>
            <if test="payInfo != null">pay_info,</if>
            <if test="linkTime != null">link_time,</if>
            <if test="note != null">note,</if>
            <if test="renewalOk != null">renewal_ok,</if>
            <if test="renewalNo != null">renewal_no,</if>
            <if test="userType != null">user_type,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="unsub != null">unsub,</if>
            <if test="pixelId != null">pixel_id,</if>
            <if test="pixelStatus != null">pixel_status,</if>
            <if test="validStatus != null">valid_status,</if>
            <if test="subIntervalDays != null">sub_interval_days,</if>
            <if test="lastChargeTime != null">last_charge_time,</if>
            <if test="adId != null">ad_id,</if>
            <if test="appType != null">app_type,</if>
            <if test="deviceType != null">device_type,</if>
            <if test="subscriptionOldUserId != null">subscription_old_user_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appId != null">#{appId},</if>
            <if test="vipId != null">#{vipId},</if>
            <if test="payTemplateId != null">#{payTemplateId},</if>
            <if test="payByEmailFlag != null">#{payByEmailFlag},</if>
            <if test="linkidId != null">#{linkidId},</if>
            <if test="username != null">#{username},</if>
            <if test="password != null">#{password},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="state != null">#{state},</if>
            <if test="source != null">#{source},</if>
            <if test="email != null">#{email},</if>
            <if test="phoneVersion != null">#{phoneVersion},</if>
            <if test="systemVersion != null">#{systemVersion},</if>
            <if test="country != null">#{country},</if>
            <if test="language != null">#{language},</if>
            <if test="appVersion != null">#{appVersion},</if>
            <if test="accountType != null">#{accountType},</if>
            <if test="uniqueId != null">#{uniqueId},</if>
            <if test="ip != null">#{ip},</if>
            <if test="subId != null">#{subId},</if>
            <if test="token != null">#{token},</if>
            <if test="coin != null">#{coin},</if>
            <if test="signInDays != null">#{signInDays},</if>
            <if test="lastSignIn != null">#{lastSignIn},</if>
            <if test="expireTime != null">#{expireTime},</if>
            <if test="other != null">#{other},</if>
            <if test="isFirstPay != null">#{isFirstPay},</if>
            <if test="pushNum != null">#{pushNum},</if>
            <if test="hashId != null">#{hashId},</if>
            <if test="isSubscriber != null">#{isSubscriber},</if>
            <if test="payInfo != null">#{payInfo},</if>
            <if test="linkTime != null">#{linkTime},</if>
            <if test="note != null">#{note},</if>
            <if test="renewalOk != null">#{renewalOk},</if>
            <if test="renewalNo != null">#{renewalNo},</if>
            <if test="userType != null">#{userType},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="unsub != null">#{unsub},</if>
            <if test="pixelId != null">#{pixelId},</if>
            <if test="pixelStatus != null">#{pixelStatus},</if>
            <if test="validStatus != null">#{validStatus},</if>
            <if test="subIntervalDays != null">#{subIntervalDays},</if>
            <if test="lastChargeTime != null">#{lastChargeTime},</if>
            <if test="adId != null">#{adId},</if>
            <if test="appType != null">#{appType},</if>
            <if test="deviceType != null">#{deviceType},</if>
            <if test="subscriptionOldUserId != null">#{subscriptionOldUserId},</if>
         </trim>
    </insert>

    <update id="updateShortUser" parameterType="ShortUser">
        update short_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="appId != null">app_id = #{appId},</if>
            <if test="vipId != null">vip_id = #{vipId},</if>
            <if test="payTemplateId != null">pay_template_id = #{payTemplateId},</if>
            <if test="payByEmailFlag != null">pay_by_email_flag = #{payByEmailFlag},</if>
            <if test="linkidId != null">linkid_id = #{linkidId},</if>
            <if test="username != null">username = #{username},</if>
            <if test="password != null">password = #{password},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="state != null">state = #{state},</if>
            <if test="source != null">source = #{source},</if>
            <if test="email != null">email = #{email},</if>
            <if test="emailBindLink != null">email_bind_link = #{emailBindLink},</if>
            <if test="emailBindDate != null">email_bind_date = #{emailBindDate},</if>
            <if test="phoneVersion != null">phone_version = #{phoneVersion},</if>
            <if test="systemVersion != null">system_version = #{systemVersion},</if>
            <if test="country != null">country = #{country},</if>
            <if test="language != null">language = #{language},</if>
            <if test="appVersion != null">app_version = #{appVersion},</if>
            <if test="accountType != null">account_type = #{accountType},</if>
            <if test="uniqueId != null">unique_id = #{uniqueId},</if>
            <if test="ip != null">ip = #{ip},</if>
            <if test="subId != null">sub_id = #{subId},</if>
            <if test="token != null">token = #{token},</if>
            <if test="coin != null">coin = #{coin},</if>
            <if test="signInDays != null">sign_in_days = #{signInDays},</if>
            <if test="lastSignIn != null">last_sign_in = #{lastSignIn},</if>
            <if test="expireTime != null">expire_time = #{expireTime},</if>
            <if test="other != null">other = #{other},</if>
            <if test="isFirstPay != null">is_first_pay = #{isFirstPay},</if>
            <if test="pushNum != null">push_num = #{pushNum},</if>
            <if test="hashId != null">hash_id = #{hashId},</if>
            <if test="isSubscriber != null">is_subscriber = #{isSubscriber},</if>
            <if test="payInfo != null">pay_info = #{payInfo},</if>
            <if test="linkTime != null">link_time = #{linkTime},</if>
            <if test="note != null">note = #{note},</if>
            <if test="renewalOk != null">renewal_ok = #{renewalOk},</if>
            <if test="renewalNo != null">renewal_no = #{renewalNo},</if>
            <if test="userType != null">user_type = #{userType},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="unsub != null">unsub = #{unsub},</if>
            <if test="pixelId != null">pixel_id = #{pixelId},</if>
            <if test="pixelStatus != null">pixel_status = #{pixelStatus},</if>
            <if test="validStatus != null">valid_status = #{validStatus},</if>
            <if test="subIntervalDays != null">sub_interval_days = #{subIntervalDays},</if>
            <if test="lastChargeTime != null">last_charge_time = #{lastChargeTime},</if>
            <if test="city != null">city = #{city},</if>
            <if test="region != null">region = #{region},</if>
            <if test="adId != null"> ad_id = #{adId},</if>
            <if test="appType != null">app_type = #{appType},</if>
            <if test="deviceType != null">device_type = #{deviceType},</if>
            <if test="subscriptionOldUserId != null">subscription_old_user_id = #{subscriptionOldUserId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteShortUserById" parameterType="Long">
        delete from short_user where id = #{id}
    </delete>

    <delete id="deleteShortUserByIds" parameterType="String">
        delete from short_user where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="scanUser" resultMap="ShortUserResult">
        SELECT * FROM short_user WHERE country IS NULL ORDER BY id ASC
    </select>
    <select id="getAllSubUsers" resultType="com.ruoyi.domain.ShortUser">
        SELECT
        DISTINCT(a.user_id) id,
        a.expire_time,
        a.amount price
        FROM
        (
        SELECT
        su.id user_id,
        su.expire_time expire_time,
        so.updatedAt AS updatedAt,
        so.amount amount
        FROM
        short_user su
        INNER JOIN short_order so ON su.id = so.user_id
        WHERE
        su.is_subscriber = 1
        AND DATE_FORMAT(su.expire_time, '%Y-%m-%d') &lt; DATE_ADD(DATE_FORMAT(NOW(), '%Y-%m-%d'), INTERVAL 1 DAY)
        AND (so.pay_type = '订阅' OR so.pay_type = '订阅续费')
        AND so.`status` = 'SUCCEEDED'
        ORDER BY
        updatedAt DESC
        ) a
        LEFT JOIN (
        SELECT
        su.id user_id,
        su.expire_time expire_time,
        so.updatedAt AS updatedAt,
        so.amount amount
        FROM
        short_user su
        INNER JOIN short_order so ON su.id = so.user_id
        WHERE
        su.is_subscriber = 1
        AND DATE_FORMAT(su.expire_time, '%Y-%m-%d') &lt; DATE_ADD(DATE_FORMAT(NOW(), '%Y-%m-%d'), INTERVAL 1 DAY)
        AND (so.pay_type = '订阅' OR so.pay_type = '订阅续费')
        AND so.`status` = 'SUCCEEDED'
        ORDER BY
        updatedAt DESC
        ) b ON a.updatedAt > b.updatedAt
        WHERE
        b.user_id IS NOT NULL
        ORDER BY
        a.user_id desc
    </select>
    <select id="selectShortUserListAll" resultType="com.ruoyi.domain.ShortUserDo">
        SELECT
        su.id,
        su.app_id,
        su.vip_id,
        su.pay_template_id,
        su.pay_by_email_flag,
        su.linkid_id,
        su.username,
        su.PASSWORD,
        su.avatar,
        su.state,
        su.source,
        su.email,
        su.phone_version,
        su.system_version,
        su.country,
        su.LANGUAGE,
        su.app_version,
        su.account_type,
        su.unique_id,
        su.ip,
        su.sub_id,
        su.token,
        su.coin,
        su.sign_in_days,
        su.last_sign_in,
        su.expire_time expireTime,
        su.other,
        su.is_first_pay,
        su.push_num,
        su.hash_id,
        su.is_subscriber,
        su.pay_info,
        su.link_time,
        su.note,
        su.renewal_ok,
        su.renewal_no,
        su.user_type,
        su.STATUS,
        su.create_by,
        su.create_time,
        su.update_by,
        su.update_time as update_time,
        su.remark,
        su.addtime,
        su.pixel_id,
        su.pixel_status,
        su.valid_status,
        su.sub_interval_days,
        su.last_charge_time,
--         su.updatetime updatetime,
        sv.`name` vipName,
        sa.`name` appName,
        sv.subscription_type subscriptionType,
        sl.`name` linkname,
        su.unsub,
        sa.type,
        su.unsub_time,
        IFNULL(sl.`language`,'en-US') link_language,
        su.is_check,
        su.code_num,
        su.cycle_num,
        su.last_code
        FROM
        short_user su left JOIN short_vip sv on su.vip_id= sv.id
        LEFT JOIN short_app sa on su.app_id =sa.id
        LEFT JOIN short_sem_link sl on sl.id = su.linkid_id
        <where>
            <if test="filterUserId != null and filterUserId != 1">
                AND sl.create_by = #{filterUserId}
            </if>
            <if test="childIds != null and childIds.size() > 0">
                AND sl.create_by IN
                <foreach item="childId" index="index" collection="childIds" open="(" separator="," close=")">
                    #{childId}
                </foreach>
            </if>
            <if test="id != null "> and su.id = #{id}</if>
            <if test="appId != null "> and su.app_id = #{appId}</if>
            <if test="appIds != null and appIds.size() > 0">
                AND su.app_id IN
                <foreach item="item" index="index" collection="appIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="vipId != null "> and su.vip_id = #{vipId}</if>
            <if test="payTemplateId != null "> and su.pay_template_id = #{payTemplateId}</if>
            <if test="linkidId != null "> and su.linkid_id = #{linkidId}</if>
            <if test="username != null  and username != ''"> and su.username like concat('%', #{username}, '%')</if>
            <if test="password != null  and password != ''"> and su.password = #{password}</if>
            <if test="avatar != null  and avatar != ''"> and su.avatar = #{avatar}</if>
            <if test="state != null  and state != ''"> and su.state = #{state}</if>
            <if test="source != null  and source != ''"> and su.source = #{source}</if>
            <if test="email != null  and email != ''"> and su.email = #{email}</if>
            <if test="phoneVersion != null  and phoneVersion != ''"> and su.phone_version = #{phoneVersion}</if>
            <if test="systemVersion != null  and systemVersion != ''"> and su.system_version = #{systemVersion}</if>
            <if test="language != null  and language != ''"> and sl.language = #{language}</if>
            <if test="appVersion != null  and appVersion != ''"> and su.app_version = #{appVersion}</if>
            <if test="accountType != null  and accountType != ''"> and su.account_type = #{accountType}</if>
            <if test="uniqueId != null  and uniqueId != ''"> and su.unique_id = #{uniqueId}</if>
            <if test="ip != null  and ip != ''"> and su.ip = #{ip}</if>
            <if test="subId != null  and subId != ''"> and su.sub_id = #{subId}</if>
            <if test="token != null  and token != ''"> and su.token = #{token}</if>
            <if test="coin != null "> and su.coin = #{coin}</if>
            <if test="signInDays != null "> and su.sign_in_days = #{signInDays}</if>
            <if test="lastSignIn != null "> and su.last_sign_in = #{lastSignIn}</if>
            <if test="sStratTime != null  and eStratTime != ''"> and su.expire_time BETWEEN #{sStratTime} and #{eStratTime}</if>
            <if test="other != null  and other != ''"> and su.other = #{other}</if>
            <if test="isFirstPay != null  and isFirstPay != ''"> and su.is_first_pay = #{isFirstPay}</if>
            <if test="pushNum != null "> and su.push_num = #{pushNum}</if>
            <if test="hashId != null  and hashId != ''"> and su.hash_id = #{hashId}</if>
            <if test="isSubscriber != null  and isSubscriber != ''"> and su.is_subscriber = #{isSubscriber}</if>
            <if test="payInfo != null  and payInfo != ''"> and su.pay_info = #{payInfo}</if>
            <if test="linkTime != null "> and su.link_time = #{linkTime}</if>
            <if test="note != null  and note != ''"> and su.note = #{note}</if>
            <if test="renewalOk != null "> and su.renewal_ok = #{renewalOk}</if>
            <if test="renewalNo != null "> and su.renewal_no = #{renewalNo}</if>
            <if test="userType != null  and userType != ''"> and su.user_type = #{userType}</if>
            <if test="status != null  and status != ''"> and su.status = #{status}</if>
            <if test="unsub != null  and unsub != ''"> and su.unsub = #{unsub}</if>
            <if test="type != null  and type != ''"> and sa.type = #{type}</if>
            <if test="pixelId != null"> and su.pixel_id = #{pixelId}</if>
            <if test="pixelStatus != null"> and su.pixel_status = #{pixelStatus}</if>
            <if test="validStatus != null"> and su.valid_status = #{validStatus}</if>
            <if test="subIntervalDays != null"> and su.sub_interval_days = #{subIntervalDays}</if>

            <if test="subscriptionType != null and subscriptionType != 'pay_all'"> and sv.subscription_type =#{subscriptionType} </if>
            <if test="subscriptionType == 'pay_all'"> and sv.subscription_type in(0,7,30,365) </if>
            <if test="beginTime != null">
                AND su.create_time <![CDATA[ >= ]]> #{beginTime}
            </if>
            <if test="endTime != null">
                AND su.create_time <![CDATA[ <= ]]> #{endTime}
            </if>
            <if test="updateStartDate != null and updateStartDate != ''">
                and su.update_time <![CDATA[ >= ]]> #{updateStartDate}
            </if>
            <if test="updateEndDate != null and updateEndDate != ''">
                and su.update_time <![CDATA[ <= ]]> #{updateEndDate}
            </if>
            <if test="userIdOrName != null"> and (su.id like concat('%', #{userIdOrName}, '%') or su.username like concat('%', #{userIdOrName}, '%')) </if>
        </where>
        ORDER BY su.id DESC
    </select>

    <update id="updateIpv4Country">
        UPDATE short_user su
        JOIN ipv4_code ic ON #{ipCode} BETWEEN ic.ip_min AND ic.ip_max
        SET su.`country` = ic.country_name
        WHERE su.`id`=#{userId}
    </update>

    <update id="updateIpv6Country">
        UPDATE short_user su
        JOIN ipv6_code ic ON #{ipCode} BETWEEN ic.ip_min AND ic.ip_max
        SET su.`country` = ic.country_name
        WHERE su.`id`=#{userId}
    </update>
    <update id="updateUnsub">
        update short_user set unsub = 1 WHERE id = #{id}
    </update>
    <update id="updateUserCountry">
        update short_user set country = #{country} WHERE id = #{id}
    </update>
    <update id="updateState">
        update short_user set state = #{state} WHERE id = #{userId}
    </update>
    <update id="updateOther">
        update short_user SET  other = #{ot} where id = #{id}
    </update>
    <update id="updateExpireTimeById">
        update short_user SET  expire_time = #{afterExpireTime} where id = #{id}
    </update>
    <update id="setIsSubscriber">
        update short_user SET  is_subscriber = 1 where id = #{id}
    </update>
    <update id="updateUnsubTime">
        update short_user SET  unsub_time = now() where id = #{id}
    </update>
    <update id="updateUserVipStatus">
        update short_user SET  is_subscriber = '0', expire_time = null ,unsub = '1' where id = #{userId}
    </update>
    <update id="batchUpdateUserVipStatus">
        update short_user SET  is_subscriber = '0', expire_time = null ,unsub = '1' where id in
        <foreach item="item" collection="list" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>
    <update id="updatePasswardByEmail">
        update short_user SET  password = #{newPassword} where email = #{email}
    </update>
    <update id="updateIsSubscriberByIds">
        UPDATE short_user SET is_subscriber = 0 WHERE id in
        <foreach item="id" collection="ids" separator="," open="(" close=")">
            #{id}
        </foreach>
    </update>
    <update id="updateUnsubSource">
        update short_user SET  unsub_source = #{content} where id = #{id}
    </update>
    <update id="updateIsCheck">
        update short_user SET  is_check = 1 where id = #{id}
    </update>
    <update id="updateCodeAndCycleNum">
        update short_user SET  code_num = 0,cycle_num = #{cycleNum} where id = #{id}
    </update>
    <update id="updateLastCode">
        update short_user SET  last_code = #{code} where id = #{userId}
    </update>

    <select id="getCountryByIpv4" resultType="java.lang.String">
        SELECT country_name
        FROM ipv4_code
        WHERE INET_ATON(#{ip}) BETWEEN ip_min AND ip_max
        LIMIT 1
    </select>

    <!-- 旧的IPv6查询，使用INET6_ATON，与 decimal(39,0) 不兼容 -->
    <!--
    <select id="getCountryByIpv6" resultType="java.lang.String">
        SELECT country_name
        FROM ipv6_code
        WHERE INET6_ATON(#{ip}) BETWEEN ip_min AND ip_max
        LIMIT 1
    </select>
    -->

    <!-- 新增：使用 BigDecimal 参数查询 IPv6 国家 -->
    <select id="getCountryByIpv6Decimal" resultType="java.lang.String" parameterType="java.math.BigDecimal">
        SELECT country_name
        FROM ipv6_code
        WHERE #{ipDecimal} BETWEEN ip_min AND ip_max
        LIMIT 1
    </select>
    <select id="getAllByTypeAndExpireTime" resultType="com.ruoyi.domain.ShortUser">
        SELECT
        user_id id,
        expire_time,
        update_time,
        amount price,
        app_id,
        email
        FROM (
        SELECT
        su.id AS user_id,
        su.app_id app_id,
        su.email,
        su.expire_time AS expire_time,
        so.update_time AS update_time,
        so.amount AS amount,
        ROW_NUMBER() OVER (PARTITION BY su.id ORDER BY so.update_time DESC) AS rn
        FROM
        short_user su
        INNER JOIN short_order so ON su.id = so.user_id
        LEFT JOIN short_vip sv on sv.id = so.vip_id
        LEFT JOIN short_pay_template st on st.id = sv.pay_template_id
        WHERE
        su.is_subscriber = 1 and (su.unsub != 1  or su.unsub is null)  and su.state = 1  and st.`name` != '禁止自动续费'
          AND su.expire_time &lt;= NOW() + INTERVAL 1 DAY
        AND so.pay_type = #{type}
        AND so.`status` = 'SUCCEEDED'
        <if test="payChannel != null and payType == 0">
            and (so.pay_channel = #{payChannel} or  so.pay_channel is null )
        </if>
        <if test="payChannel != null and payType > 0">
            AND pay_channel = #{payChannel}
        </if>
        ) t
        WHERE rn = 1
        ORDER BY update_time DESC
    </select>
    <select id="getRenewSubscribe" resultType="com.ruoyi.domain.ShortUser">
        SELECT user_id id from short_renew_subscribe_data WHERE
            order_status = 'REQUIRES_PAYMENT_METHOD'
                                                   AND log_date = CURDATE()
    </select>
    <select id="getSystemAddUserCount" resultType="com.ruoyi.vo.ShortUserForLinkVO">
        SELECT
        COUNT(CASE WHEN su.linkid_id > 0 THEN su.id END) as link_user,
        COUNT(CASE WHEN su.linkid_id > 0 AND su.phone_version = 'Apple' THEN su.id END) AS ios_user,
        COUNT(CASE WHEN su.linkid_id > 0 AND su.phone_version != 'Apple' THEN su.id END) AS android_user
        FROM short_user su
        LEFT JOIN short_vip sv on su.vip_id= sv.id
        LEFT JOIN short_app sa on su.app_id = sa.id
        WHERE
        1=1
        <if test="dto.appId != null">
            AND su.app_id = #{dto.appId}
        </if>
        <if test="dto.appIds != null and dto.appIds.size() > 0">
            AND su.app_id IN
            <foreach item="item" index="index" collection="dto.appIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.userId != null">
            AND su.id = #{dto.userId}
        </if>
        <if test="dto.registerStartDate != null and dto.registerStartDate != ''">
            AND su.create_time &gt;= #{dto.registerStartDate}
        </if>
        <if test="dto.registerEndDate != null and dto.registerEndDate != ''">
            AND su.create_time &lt;= #{dto.registerEndDate}
        </if>
        <if test="dto.type != null">
            AND sa.type = #{dto.type}
        </if>
        <if test="dto.language != null">
            AND su.language = #{dto.language}
        </if>
    </select>
    <select id="getIconByUserId" resultType="java.lang.String">
        SELECT
            sm.icon
        FROM
            short_user su
                INNER JOIN short_sem_link sl on su.linkid_id= sl.id
                INNER JOIN short_movie sm on sm.id = sl.movie_id
        WHERE
            su.id = #{userId}
    </select>
    <select id="selectShortUserEmailFlag" resultType="java.lang.Boolean">
        select pay_by_email_flag from short_user where id = #{userId}
    </select>
    <select id="getByLaset" resultType="com.ruoyi.domain.ShortUser">
        SELECT
            s.app_id,
            s.email,
            s.id,
            s.hash_id,
            s.unique_id,
            s.expire_time,
            s.linkid_id,
            s.source
        FROM
            short_user s
                LEFT JOIN (SELECT DISTINCT user_id FROM short_user_activity_log WHERE content = '订阅通知邮件发送成功' AND create_time >= DATE_SUB(NOW(), INTERVAL 2 DAY)) a ON s.id = a.user_id
        WHERE
            s.expire_time BETWEEN NOW()
                AND DATE_ADD(NOW(), INTERVAL 2 DAY)
          AND s.unsub = 0
          AND state = 1
          AND a.user_id IS NULL
          AND s.email > ''
          and s.source is null
        ORDER BY
            s.expire_time ASC
    </select>
    <select id="selectShortUserByTime" resultType="com.ruoyi.domain.ShortUserCP">
        select id, app_id, linkid_id, username,state, ip, other, create_time,update_time from short_user
        where create_time between #{beginTime} and #{endTime}
        <if test="nextId != null">
            and id > #{nextId}
        </if>
        limit 5000
    </select>
    <select id="getBackRegEmail" resultType="com.ruoyi.domain.ShortUser">
--             SELECT
--                 s.app_id,
--                 s.email,
--                 s.id,
--                 s.hash_id,
--                 s.unique_id,
--                 s.expire_time
--             FROM
--                 short_user s
--             WHERE
--                 s.email is not null  and s.email != ''
--       and s.expire_time> '2025-08-01 00:00:00'
--       and s.app_id in (SELECT id from short_app WHERE type = 1)

SELECT
    s.app_id,
    s.email,
    s.id,
    s.hash_id,
    s.unique_id,
    s.expire_time
FROM
    short_user s
WHERE
    s.id IN (
             '1141471',
             '1015492',
             '845414',
             '908797',
             '1016060',
             '938428',
             '768080',
             '939017',
             '835009',
             '1072124',
             '625050',
             '1174375',
             '1006692',
             '1005928',
             '1074225',
             '928791',
             '1000872',
             '2816426',
             '2766571',
             '2823821',
             '2776328',
             '2775488',
             '2780193',
             '1019808',
             '1013242',
             '1043977',
             '571489',
             '553729',
             '528086',
             '558114',
             '86889',
             '94165',
             '490886',
             '457217',
             '587425',
             '207628',
             '199008',
             '29080',
             '482255',
             '50268',
             '516726',
             '504845',
             '600316',
             '588672',
             '594034',
             '609639',
             '612879',
             '73753',
             '574111',
             '37335',
             '643021',
             '522662',
             '535342',
             '1177225',
             '777131',
             '606896',
             '549337',
             '527725',
             '551669',
             '508559',
             '349081',
             '526552',
             '484497',
             '554856',
             '572581',
             '569394',
             '704352',
             '572674',
             '571413',
             '589158',
             '106041',
             '605237',
             '192662',
             '550505',
             '146161',
             '378647',
             '26929',
             '554114',
             '581150',
             '462871',
             '529900',
             '557730',
             '579910',
             '540608',
             '52890',
             '48776',
             '590885',
             '46084',
             '344412',
             '599748',
             '551238',
             '605389',
             '608182',
             '574114',
             '43934',
             '551395',
             '37622',
             '557793',
             '589024',
             '99548',
             '798646',
             '44656',
             '171484',
             '581683',
             '572191',
             '696614',
             '590244',
             '605355',
             '57222',
             '597105',
             '482778',
             '89293',
             '644936',
             '231503',
             '594290',
             '622422',
             '562412',
             '482544',
             '203914',
             '75299',
             '221954',
             '106984',
             '269578',
             '128504',
             '624287',
             '35645',
             '660459',
             '71990',
             '552317',
             '690015',
             '810792',
             '696756',
             '744296',
             '140155',
             '684699',
             '816929',
             '734874',
             '532679',
             '814854',
             '647556',
             '608624',
             '553818',
             '617959',
             '825532',
             '974137',
             '942720',
             '1403161',
             '550051',
             '800258',
             '817036',
             '767929',
             '884155',
             '660997',
             '646721',
             '626439',
             '479709',
             '616921',
             '55515',
             '572526',
             '817770',
             '44801',
             '651987',
             '687552',
             '694170',
             '606174',
             '824817',
             '532018',
             '679507',
             '664425',
             '45967',
             '781172',
             '814924',
             '913703',
             '531848',
             '697172',
             '549502',
             '46988',
             '689859',
             '721003',
             '771361',
             '690871',
             '736177',
             '700842',
             '700369',
             '734439',
             '695014',
             '804327',
             '832714',
             '832972',
             '996024',
             '739264',
             '908126',
             '698346',
             '832633',
             '573607',
             '102380',
             '785966',
             '681589',
             '687105',
             '699179',
             '818417',
             '901357',
             '894669',
             '892836',
             '1003412',
             '752318',
             '916034',
             '750388',
             '736190',
             '282809',
             '771679',
             '887093',
             '688119',
             '466578',
             '741285',
             '885291',
             '688980',
             '36871',
             '365425',
             '571999',
             '63195',
             '548735',
             '604161',
             '888496',
             '744201',
             '590696',
             '518679',
             '644410',
             '773797',
             '821387',
             '831980',
             '121190',
             '475227',
             '564194',
             '479228',
             '541949',
             '44682',
             '37168',
             '555537',
             '504010',
             '625001',
             '608712',
             '627317',
             '699244',
             '812704',
             '70388',
             '1022855',
             '1025445',
             '851471',
             '129290',
             '489507',
             '490001',
             '45778',
             '484494',
             '479943',
             '608395',
             '591893',
             '622765',
             '642998',
             '76862',
             '976889',
             '552116',
             '741084',
             '645227',
             '578504',
             '541657',
             '532262',
             '551937',
             '480606',
             '607187',
             '628002',
             '609770',
             '569007',
             '531630',
             '614219',
             '30331',
             '95609',
             '131417',
             '952529',
             '770888',
             '610477',
             '560320',
             '521261',
             '574426',
             '919608',
             '572493',
             '32262',
             '779554',
             '786178',
             '626305',
             '646127',
             '389767',
             '623728',
             '2101530',
             '569896',
             '45644',
             '579486',
             '573181',
             '550254',
             '588759',
             '607981',
             '630017',
             '1033759',
             '904130',
             '964807',
             '549529',
             '647522',
             '571022',
             '610212',
             '822096',
             '574377',
             '691621',
             '570597',
             '610196',
             '627780',
             '624078',
             '622345',
             '572848',
             '48602',
             '571319',
             '401015',
             '75112',
             '106027',
             '555051',
             '588753',
             '588183',
             '45447',
             '303803',
             '604063',
             '628495',
             '631236',
             '665727',
             '905152',
             '37305',
             '610514',
             '568960',
             '737524',
             '50725',
             '843332',
             '484471',
             '88219',
             '663662',
             '681803',
             '163335',
             '139197',
             '630080',
             '26651',
             '590640',
             '664651',
             '629032',
             '545724',
             '74008',
             '586562',
             '1076146',
             '1055428',
             '956775',
             '1055882',
             '1049707',
             '1042254',
             '959987',
             '1018956',
             '1074791',
             '1033871',
             '1107190',
             '633758',
             '620139',
             '591448',
             '74029',
             '662709',
             '739093',
             '820693',
             '782649',
             '704733',
             '662924',
             '461665',
             '638526',
             '567912',
             '741021',
             '805372',
             '591234',
             '1016252',
             '650980',
             '743526',
             '707981',
             '663674',
             '810988',
             '820851',
             '485235',
             '797803',
             '562614',
             '733412',
             '250296',
             '555441',
             '883817',
             '33995',
             '1002220',
             '599074',
             '622091',
             '689106',
             '703959',
             '596628',
             '832378',
             '649041',
             '703409',
             '836645',
             '785297',
             '582094',
             '826915',
             '601571',
             '799338',
             '573332',
             '101018',
             '557886',
             '520288',
             '679793',
             '605636',
             '722614',
             '734563',
             '664991',
             '177007',
             '955105',
             '903236',
             '903265',
             '736108',
             '751099',
             '692122',
             '711626',
             '685761',
             '679511',
             '744147',
             '760733',
             '749085',
             '706655',
             '829038',
             '808509',
             '905106',
             '741211',
             '686798',
             '754659',
             '704330',
             '830105',
             '782454',
             '799494',
             '686682',
             '784175',
             '703555',
             '709628',
             '814810',
             '972227',
             '700417',
             '844373',
             '486365',
             '878479',
             '88166',
             '863575',
             '708474',
             '738531',
             '699876',
             '888300',
             '527761',
             '836766',
             '753340',
             '699415',
             '783338',
             '684408',
             '686818',
             '703909',
             '684996',
             '781412',
             '886977',
             '1010328',
             '736134',
             '836224',
             '771620',
             '772378',
             '776387',
             '774635',
             '764317',
             '761026',
             '746676',
             '527380',
             '730222',
             '746648',
             '747095',
             '774139',
             '739957',
             '827534',
             '736342',
             '723170',
             '751312',
             '785675',
             '801655',
             '901105',
             '929497',
             '778176',
             '779432',
             '845327',
             '907432',
             '886851',
             '936550',
             '894629',
             '822351',
             '929613',
             '1126948',
             '1096188',
             '851375',
             '851566',
             '845724',
             '1051727',
             '891732',
             '896142',
             '771236',
             '810041',
             '1051735',
             '927861',
             '937674',
             '1053391',
             '906947',
             '782588',
             '783716',
             '807620',
             '821523',
             '771395',
             '827377',
             '836084',
             '783275',
             '1013430',
             '815583',
             '789820',
             '1047315',
             '1047729',
             '778368',
             '840675',
             '840718',
             '875961',
             '839981',
             '876656',
             '847431',
             '825055',
             '881298',
             '876520',
             '1106204',
             '1021972',
             '1042473',
             '517482',
             '1041989',
             '854114',
             '843413',
             '884708',
             '852266',
             '934646',
             '831081',
             '957223',
             '746259',
             '902760',
             '940863',
             '1009108',
             '891309',
             '906807',
             '915233',
             '971412',
             '888162',
             '946174',
             '45442',
             '944933',
             '508234',
             '964093',
             '1001977',
             '1043721',
             '1044366',
             '1080908',
             '1116287',
             '823041',
             '959487',
             '747443',
             '1016884',
             '1053895',
             '963875',
             '982674',
             '995311',
             '928978',
             '937924',
             '941363',
             '30973',
             '903894',
             '737467',
             '886078',
             '771394',
             '836935',
             '1046767',
             '1051795',
             '963060',
             '1059005',
             '1061911',
             '1097344',
             '1042778',
             '1135632',
             '1044058',
             '1118645',
             '993959',
             '1055141',
             '1040831',
             '1200936',
             '1220031',
             '1058205',
             '1197180',
             '779856',
             '1141287',
             '946605',
             '936650',
             '746969',
             '989620',
             '1094683',
             '1045331',
             '1032549',
             '892661',
             '1044816',
             '830096',
             '819627',
             '784805',
             '982580',
             '1013411',
             '1070067',
             '1001102',
             '1063059',
             '1381020',
             '1102803',
             '1101798',
             '1332798',
             '1078435',
             '1140355',
             '1403062',
             '1142857',
             '842924',
             '1140383',
             '1313432',
             '45398',
             '832749',
             '951648',
             '1000524',
             '873240',
             '1097947',
             '1076600',
             '1033351',
             '117012',
             '1048860',
             '1042353',
             '1031863',
             '975392',
             '1065170',
             '1084194',
             '1043923',
             '1055271',
             '1049913',
             '1044722',
             '1075749',
             '1006792',
             '1091652',
             '1034130',
             '1053915',
             '1047986',
             '1048675',
             '908581',
             '1061910',
             '1153757',
             '1112183',
             '1145583',
             '961019',
             '1088113',
             '1072137',
             '1067942',
             '1176769',
             '1147437',
             '1084135',
             '1083296',
             '1052639',
             '1040498',
             '723971',
             '1057862',
             '36884',
             '1002018',
             '1051250',
             '1072170',
             '1045561',
             '1119324',
             '1140537',
             '1040163',
             '161491',
             '780202',
             '1114218',
             '959512',
             '986047',
             '1083534',
             '1971548',
             '1679614',
             '1574335',
             '2081874',
             '2028987',
             '1596033',
             '719686',
             '1626702',
             '1044890',
             '1577148',
             '1589244',
             '1793560',
             '1701539',
             '1702225',
             '1892722',
             '1636975',
             '1787622',
             '1975416',
             '1711340',
             '1751436',
             '1637679',
             '1617818',
             '1005373',
             '1622465',
             '1700393',
             '1726833',
             '882741',
             '1616202',
             '1007692',
             '1313784',
             '837342',
             '1043841',
             '898213',
             '1348408',
             '1010931',
             '1141498',
             '827991',
             '1627255',
             '2253766',
             '982410',
             '1663899',
             '1199211',
             '1044786',
             '1172826',
             '2384810',
             '1696444',
             '804270',
             '816890',
             '996126',
             '1014994',
             '1010053',
             '1073537',
             '947883',
             '1049914',
             '1637768',
             '916010',
             '613390',
             '583061',
             '588391',
             '48161',
             '591717',
             '603041',
             '642437',
             '627128',
             '816633',
             '624659',
             '649758',
             '645989',
             '768670',
             '849274',
             '772561',
             '823660',
             '828511',
             '840707',
             '663151',
             '515267',
             '839175',
             '971019',
             '1003237',
             '752538',
             '661639',
             '27526',
             '685885',
             '737940',
             '721710',
             '839552',
             '730751',
             '869648',
             '767579',
             '698464',
             '800824',
             '790436',
             '835924',
             '772581',
             '1177037',
             '1181297',
             '1223988',
             '1221693',
             '1297866',
             '1282024',
             '1309384',
             '1388064',
             '1462127',
             '1549652',
             '1273172',
             '1499049',
             '1534589',
             '1558858',
             '2224781',
             '1444897',
             '1656589',
             '1763544',
             '1616634',
             '1682833',
             '1672162',
             '1694756',
             '1683373',
             '1674705',
             '1679318',
             '1716947',
             '1763659',
             '1793614',
             '1764270',
             '1803546',
             '1833546',
             '1831644',
             '1778906',
             '1886689',
             '1326309',
             '1927791',
             '1949437',
             '1978481',
             '1957021',
             '2054395',
             '2025634',
             '1998445',
             '2103293',
             '2012622',
             '1715379',
             '2056084',
             '2016478',
             '1780241',
             '2051289',
             '2782583',
             '2093463',
             '2091357',
             '2140483',
             '2141665',
             '2126436',
             '2121325',
             '1873703',
             '2145312',
             '2159657',
             '2166952',
             '2176588',
             '2194146',
             '2234335',
             '1960720',
             '2236496',
             '2230112',
             '2228559',
             '2321220',
             '2349242',
             '2079902',
             '2268816',
             '2280205',
             '2254096',
             '2277851',
             '2278531',
             '1354975',
             '2270111',
             '2374963',
             '2381428',
             '2317185',
             '2316456',
             '2314639',
             '2296323',
             '2297350',
             '1831279',
             '2322348',
             '2316769',
             '2315432',
             '2320166',
             '2318194',
             '2334728',
             '2338017',
             '1780687',
             '2386672',
             '2352968',
             '2278118',
             '2336584',
             '2379447',
             '2348440',
             '2389503',
             '2387183',
             '2284517',
             '2429047',
             '2365202',
             '2455304',
             '2504280',
             '2408272',
             '2422250',
             '2336538',
             '2412703',
             '2466909',
             '2503230',
             '2596403',
             '2449637',
             '2821531',
             '2448783',
             '2616149',
             '2489201',
             '2583287',
             '2767229',
             '2379497',
             '2426123',
             '2495784',
             '2453082',
             '2458248',
             '2365115',
             '2530258',
             '1266988',
             '2489686',
             '2498747',
             '2539254',
             '2530749',
             '2606138',
             '2466005',
             '2466348',
             '2520981',
             '2528535',
             '2526435',
             '2587829',
             '2554878',
             '2546395',
             '2546461',
             '2557744',
             '2570083',
             '2567971',
             '2590978',
             '2604085',
             '2597868',
             '2632066',
             '2478055',
             '2660695',
             '2657002',
             '2482845',
             '2697217',
             '2691416',
             '2691346',
             '2723668',
             '2677444',
             '2742789',
             '2766225',
             '2746687',
             '2739713',
             '2450813',
             '2402822',
             '2240938',
             '2820826',
             '2769659',
             '1888024',
             '2767105',
             '2824711',
             '2822230',
             '2737893',
             '2334246',
             '2448075',
             '2491317',
             '2812999',
             '2490353',
             '2546345',
             '1881680',
             '2062586',
             '756728',
             '2842519',
             '1203656',
             '1366511',
             '2763462',
             '2774432',
             '2819983',
             '2770938',
             '2777483',
             '2821414',
             '2762447',
             '2828166',
             '2824139',
             '2791726',
             '2811676',
             '2832331',
             '2792044',
             '2774554',
             '2827037',
             '2815862',
             '2818945',
             '2822803',
             '2821862',
             '2764833',
             '2803727',
             '2819716',
             '2822299',
             '2781204',
             '2772109',
             '2767512',
             '2825665',
             '2827715',
             '2832849',
             '2822363',
             '1018460',
             '797207',
             '1195333',
             '858611',
             '1005623',
             '1371835',
             '743057',
             '1072840',
             '1074766',
             '1089531',
             '1094149',
             '674973',
             '700122',
             '762658',
             '678232',
             '717300',
             '664293',
             '661043',
             '722305',
             '1112243',
             '1437248',
             '910761',
             '1429339',
             '1293608',
             '1012390',
             '1095279',
             '673742',
             '804585',
             '710870',
             '865702',
             '873323',
             '680294',
             '694509',
             '784193',
             '766187',
             '795747',
             '699924',
             '734473',
             '680116',
             '807000',
             '679534',
             '760511',
             '908383',
             '743853',
             '702007',
             '905870',
             '876575',
             '698734',
             '710911',
             '704163',
             '731449',
             '741866',
             '755518',
             '1690157',
             '790312',
             '894924',
             '719500',
             '749777',
             '846887',
             '837447',
             '752454',
             '730299',
             '955922',
             '808185',
             '867455',
             '773301',
             '743656',
             '718613',
             '833115',
             '859093',
             '819720',
             '821812',
             '824919',
             '789944',
             '839148',
             '832067',
             '842327',
             '803927',
             '795367',
             '797246',
             '791907',
             '854131',
             '791981',
             '783236',
             '881663',
             '885183',
             '888145',
             '796134',
             '835029',
             '869967',
             '873333',
             '793203',
             '777921',
             '842135',
             '810398',
             '813959',
             '762046',
             '773188',
             '819361',
             '781662',
             '783522',
             '816420',
             '863068',
             '779506',
             '780024',
             '805918',
             '789605',
             '792259',
             '882370',
             '781347',
             '788715',
             '885865',
             '883507',
             '891215',
             '828810',
             '793576',
             '836799',
             '837208',
             '854954',
             '867478',
             '875878',
             '825307',
             '783805',
             '877725',
             '799321',
             '875654',
             '859208',
             '770282',
             '840481',
             '839525',
             '758107',
             '834197',
             '786999',
             '792552',
             '773329',
             '883392',
             '760946',
             '727725',
             '767934',
             '718490',
             '944584',
             '742767',
             '741475',
             '780141',
             '908760',
             '805504',
             '750587',
             '840360',
             '842586',
             '902333',
             '817960',
             '744778',
             '773547',
             '891827',
             '746684',
             '778473',
             '772598',
             '758849',
             '843086',
             '704904',
             '768073',
             '792559',
             '904862',
             '817981',
             '790672',
             '1018865',
             '1024881',
             '1026687',
             '787861',
             '790444',
             '1077942',
             '1020167',
             '802106',
             '839482',
             '836037',
             '913206',
             '798187',
             '799452',
             '804370',
             '779612',
             '906200',
             '882430',
             '868565',
             '798023',
             '798875',
             '820346',
             '870161',
             '829802',
             '834129',
             '794787',
             '877482',
             '809803',
             '800004',
             '777678',
             '911107',
             '958274',
             '987136',
             '908959',
             '1039387',
             '1001689',
             '948420',
             '869168',
             '814783',
             '847009',
             '820904',
             '719660',
             '819878',
             '676253',
             '1004831',
             '767554',
             '842749',
             '875430',
             '940802',
             '711513',
             '830846',
             '886836',
             '877418',
             '838148',
             '881277',
             '941865',
             '904416',
             '1051042',
             '969470',
             '1151828',
             '1012770',
             '895379',
             '956061',
             '880956',
             '899447',
             '882048',
             '1052449',
             '951435',
             '876427',
             '964465',
             '909027',
             '897643',
             '938056',
             '1052595',
             '1049874',
             '884640',
             '1144001',
             '960419',
             '1058534',
             '1258137',
             '1139791',
             '896699',
             '995427',
             '923978',
             '973757',
             '779680',
             '1001738',
             '1003650',
             '963128',
             '937901',
             '928335',
             '1047656',
             '1071337',
             '962252',
             '906658',
             '1002036',
             '959452',
             '950632',
             '1013664',
             '1013928',
             '957440',
             '857544',
             '943591',
             '785153',
             '1011388',
             '1084330',
             '1182658',
             '1040940',
             '1011167',
             '995903',
             '762244',
             '770891',
             '1009199',
             '876413',
             '1024614',
             '1040265',
             '1014263',
             '1010265',
             '979624',
             '798664',
             '1001133',
             '1003925',
             '1001653',
             '1143115',
             '1003454',
             '1012155',
             '1014709',
             '1018205',
             '1017817',
             '778863',
             '1047436',
             '1049754',
             '803431',
             '1070215',
             '1045738',
             '1053100',
             '1056498',
             '990851',
             '1044456',
             '1047855',
             '1109136',
             '1015503',
             '1019972',
             '963329',
             '1016785',
             '1022834',
             '1031851',
             '1077679',
             '1069607',
             '790995',
             '888565',
             '1050609',
             '1068070',
             '950222',
             '1146998',
             '1059734',
             '1054577',
             '1099468',
             '1095801',
             '1053108',
             '1087044',
             '1042339',
             '1197233',
             '1181531',
             '1005465',
             '1046675',
             '1054005',
             '1044987',
             '1040988',
             '1113765',
             '958885',
             '1123557',
             '1072060',
             '1011534',
             '1057460',
             '1071824',
             '1086147',
             '987165',
             '1172003',
             '1053849',
             '1082765',
             '1138138',
             '1179811',
             '1186438',
             '1051377',
             '839795',
             '1031698',
             '1061797',
             '1055099',
             '1054944',
             '1072584',
             '1097233',
             '1116126',
             '1149714',
             '1013785',
             '1135474',
             '1088619',
             '1065810',
             '1090645',
             '1026509',
             '958758',
             '1123518',
             '1016125',
             '1090124',
             '1068351',
             '1080070',
             '1000710',
             '1114302',
             '1087687',
             '912662',
             '1080143',
             '903651',
             '1120519',
             '1087585',
             '1087283',
             '1190619',
             '1271904',
             '1193107',
             '1225508',
             '1176921',
             '1111754',
             '1039993',
             '1129103',
             '1049166',
             '1185872',
             '1043913',
             '1146997',
             '1121209',
             '1149876',
             '1118722',
             '1083479',
             '1127406',
             '1109714',
             '1089325',
             '1167410',
             '1108447',
             '1079262',
             '1111912',
             '1003208',
             '1071586',
             '1138687',
             '1140533',
             '1106898',
             '1080018',
             '1141204',
             '1110538',
             '1055466',
             '1106769',
             '836081',
             '1110593',
             '1085011',
             '1046338',
             '1131105',
             '1170222',
             '1175996',
             '1121511',
             '1142639',
             '1150861',
             '1139224',
             '1127892',
             '1150352',
             '958381',
             '1148314',
             '1106435',
             '1200017',
             '1136110',
             '1168366',
             '1183153',
             '1279658',
             '1109284',
             '1119242',
             '1189878',
             '1143504',
             '1135524',
             '1140411',
             '942272',
             '1185278',
             '1143691',
             '1138653',
             '1170611',
             '1160562',
             '1219670',
             '1141609',
             '1177346',
             '1174990',
             '1177710',
             '1178671',
             '905018',
             '1195401',
             '1201880',
             '1209069',
             '1198049',
             '1222624',
             '839072',
             '1181799',
             '1175354',
             '1101654',
             '1195291',
             '1184217',
             '1218587',
             '1176914',
             '1175101',
             '1104101',
             '1185989',
             '1180484',
             '1185590',
             '783426',
             '1282396',
             '1147593',
             '1590858',
             '1175727',
             '1216694',
             '1185978',
             '1173950',
             '1067170',
             '1142654',
             '1240225',
             '1144680',
             '1146279',
             '1226034',
             '1199306',
             '778936',
             '1150015',
             '1202135',
             '1208889',
             '1295919',
             '1068172',
             '1214856',
             '1168852',
             '1397001',
             '1214115',
             '1227923',
             '1057441',
             '1111428',
             '962845',
             '1058151',
             '1179218',
             '1276174',
             '1323389',
             '1383626',
             '1174192',
             '1260956',
             '1271774',
             '1230136',
             '1293836',
             '840522',
             '1182199',
             '1008798',
             '1325393',
             '1432582',
             '1316690',
             '1329876',
             '1313021',
             '1337861',
             '945144',
             '1358820',
             '1134299',
             '1389978',
             '1082287',
             '1408235',
             '1215567',
             '1380117',
             '1362265',
             '1117113',
             '1365158',
             '1149769',
             '1362601',
             '1740208',
             '1730966',
             '1406891',
             '1522900',
             '1627914',
             '1369575',
             '1392262',
             '1399286',
             '1329710',
             '1407782',
             '1222928',
             '1390503',
             '1415758',
             '1489489',
             '1533951',
             '1442530',
             '1526206',
             '1063477',
             '1076591',
             '1501903',
             '1571611',
             '1489253',
             '1496107',
             '1225393',
             '1487523',
             '1178459',
             '1585000',
             '1520496',
             '1530948',
             '1524187',
             '1212544',
             '1582956',
             '796396',
             '1604041',
             '1572439',
             '1075499',
             '1617895',
             '2044019',
             '1815101',
             '1648784',
             '1885834',
             '1719577',
             '1752244',
             '1725165',
             '1324024',
             '1211779',
             '989812',
             '1488972',
             '1170301',
             '1726911',
             '1711217',
             '1680439',
             '897969',
             '1669227',
             '1491771',
             '1673608',
             '1620750',
             '1668598',
             '1665697',
             '1680763',
             '1674915',
             '1710567',
             '759992',
             '1719313',
             '1685842',
             '1384219',
             '1719794',
             '1671569',
             '1704080',
             '1691918',
             '1723151',
             '1671508',
             '1708790',
             '1669461',
             '1693094',
             '1659015',
             '1433081',
             '1695699',
             '1704721',
             '1713805',
             '1674015',
             '1670572',
             '1676842',
             '775907',
             '1722607',
             '766290',
             '1676939',
             '1649806',
             '1677913',
             '1695115',
             '1135680',
             '1695040',
             '1705726',
             '1678108',
             '1656169',
             '1691932',
             '1685239',
             '1226131',
             '1706506',
             '1686050',
             '1688886',
             '1680481',
             '1682765',
             '770439',
             '1674241',
             '1668696',
             '1706838',
             '1694864',
             '1721278',
             '831025',
             '1387596',
             '1665778',
             '1672492',
             '1678765',
             '1706473',
             '1670209',
             '1703444',
             '1682845',
             '1665864',
             '1534243',
             '1689244',
             '1695410',
             '1225290',
             '1666760',
             '1673855',
             '1681324',
             '1684974',
             '1019194',
             '1659298',
             '1674082',
             '1719412',
             '1698222',
             '1731587',
             '1700516',
             '1641637',
             '1667739',
             '1689855',
             '1691794',
             '1276165',
             '2537158',
             '1686493',
             '1700379',
             '1596869',
             '1665847',
             '1342470',
             '1710488',
             '1851185',
             '1119723',
             '949103',
             '1136723',
             '1707800',
             '1144280',
             '1833986',
             '1777427',
             '897333',
             '2087628',
             '1148828',
             '1779715',
             '961771',
             '1678607',
             '1781689',
             '1828160',
             '1693460',
             '1352907',
             '1824070',
             '1832605',
             '844148',
             '1859628',
             '1919765',
             '984678',
             '1845629',
             '1863950',
             '1683426',
             '1965839',
             '1654113',
             '1413783',
             '1846163',
             '1893310',
             '1864163',
             '1846589',
             '1878521',
             '1014438',
             '1851813',
             '1849871',
             '1893358',
             '772753',
             '817081',
             '1869363',
             '1879992',
             '1385643',
             '2058062',
             '1883374',
             '1874228',
             '1216488',
             '1895294',
             '1225522',
             '1868335',
             '1603133',
             '1877423',
             '1878131',
             '1906118',
             '1953057',
             '1819895',
             '1884910',
             '1871724',
             '1855932',
             '1438555',
             '1175649',
             '1977478',
             '1778736',
             '1936694',
             '1762521',
             '1969682',
             '1599400',
             '1958326',
             '2440611',
             '1960426',
             '2609513',
             '2713042',
             '2341358',
             '2064141',
             '2165428',
             '2597309',
             '1970496',
             '2068079',
             '1381947',
             '1015209',
             '1662624',
             '1629659',
             '1963377',
             '2062669',
             '1971084',
             '2000860',
             '1972159',
             '1990399',
             '1090541',
             '1878748',
             '780196',
             '961965',
             '1582191',
             '2020785',
             '1888013',
             '2051423',
             '2022462',
             '1320689',
             '1476914',
             '717436',
             '2012542',
             '2032214',
             '2015325',
             '2024483',
             '2018393',
             '2054803',
             '2063866',
             '2054475',
             '1775612',
             '1371584',
             '2068439',
             '1325157',
             '1124666',
             '2062348',
             '2090952',
             '2050725',
             '2023300',
             '1670245',
             '2070826',
             '837417',
             '2088799',
             '2058683',
             '1114141',
             '2072009',
             '1196585',
             '2096834',
             '2135606',
             '1667132',
             '2081399',
             '2080129',
             '2010425',
             '2095425',
             '2092881',
             '1141696',
             '1489829',
             '1138728',
             '1138431',
             '737740',
             '2130195',
             '2108336',
             '2137597',
             '2041924',
             '2182374',
             '886709',
             '1292888',
             '1972642',
             '2156986',
             '2018005',
             '2147638',
             '2032770',
             '2149816',
             '2149704',
             '1679453',
             '2136148',
             '2147680',
             '2352047',
             '2196176',
             '2125532',
             '2197871',
             '2184085',
             '2185443',
             '2060621',
             '2182974',
             '2234155',
             '2207803',
             '2288310',
             '2284229',
             '2317377',
             '2025870',
             '2194313',
             '1148169',
             '2012545',
             '1380490',
             '2226629',
             '2185921',
             '2185975',
             '2178763',
             '1978636',
             '2196594',
             '2191730',
             '2083266',
             '2195484',
             '1990734',
             '1824339',
             '2212653',
             '2191634',
             '2272156',
             '2029360',
             '2384016',
             '2149430',
             '2336934',
             '2532406',
             '1640706',
             '2232944',
             '831997',
             '2277163',
             '2236633',
             '2323252',
             '1712310',
             '2291887',
             '2287804',
             '2266913',
             '2278801',
             '1157231',
             '2276588',
             '1937965',
             '2381423',
             '2281274',
             '2374924',
             '2009612',
             '2333184',
             '1627445',
             '2086791',
             '2290790',
             '1002142',
             '2275383',
             '2278512',
             '2271969',
             '2275711',
             '2313000',
             '1064577',
             '2314010',
             '2339367',
             '2324011',
             '2240289',
             '2360783',
             '1914012',
             '2320043',
             '2594417',
             '2648842',
             '2410765',
             '2065916',
             '2260690',
             '2492853',
             '2337301',
             '2405511',
             '2572341',
             '2759400',
             '1178878',
             '1176001',
             '1121358',
             '750858',
             '833315',
             '692638',
             '892941',
             '704531',
             '706034',
             '734517',
             '758677',
             '1074588',
             '1090882',
             '1080557',
             '1450976',
             '1319980',
             '1275226',
             '891632',
             '774757',
             '788044',
             '736718',
             '736193',
             '894837',
             '743040',
             '756501',
             '1000739',
             '824588',
             '808371',
             '825224',
             '841273',
             '759712',
             '814602',
             '810302',
             '1057683',
             '794887',
             '898987',
             '900827',
             '785282',
             '960761',
             '845014',
             '901984',
             '840784',
             '837443',
             '894737',
             '839469',
             '836853',
             '946117',
             '890743',
             '957185',
             '958697',
             '889951',
             '887014',
             '1078643',
             '789238',
             '1027141',
             '1088338',
             '1172360',
             '979206',
             '1003354',
             '793625',
             '909839',
             '875837',
             '884852',
             '1175672',
             '1057500',
             '1017312',
             '996526',
             '1022265',
             '895047',
             '1004367',
             '1050413',
             '1002193',
             '1018541',
             '997354',
             '967703',
             '1010649',
             '831272',
             '957654',
             '926677',
             '907047',
             '971914',
             '1107307',
             '1054090',
             '1013006',
             '1034583',
             '750860',
             '1036985',
             '967751',
             '832320',
             '1052923',
             '957735',
             '1004751',
             '1018322',
             '785860',
             '993512',
             '1040703',
             '1132824',
             '1071093',
             '1055201',
             '1003134',
             '1141241'
        )



    </select>
    <select id="selectShortUserByUserId" resultMap="ShortUserResult">
        <include refid="selectShortUserVo"/>
        where id = #{id}
    </select>
    <select id="selectByAppIdAndUnid" resultType="com.ruoyi.domain.ShortUser">
        SELECT
            *
        FROM
            short_user
        WHERE
            app_id = #{nid}
          AND unique_id = #{unid}
    </select>

    <select id="getPayTypeById" resultType="java.lang.String">
        SELECT
        CASE
        WHEN EXISTS
        (SELECT 1 FROM short_order o WHERE o.user_id = u.id AND o.STATUS = 'SUCCEEDED' AND o.pay_type = '充值')
        AND EXISTS (SELECT 1 FROM short_order o WHERE o.user_id = u.id AND o.STATUS = 'SUCCEEDED' AND o.pay_type = '订阅') THEN
        CASE
        WHEN u.expire_time &lt; CURRENT_TIMESTAMP THEN
        '充值'
        ELSE
        '订阅'
        END
        WHEN EXISTS (SELECT 1 FROM short_order o WHERE o.user_id = u.id AND o.STATUS = 'SUCCEEDED' AND o.pay_type = '充值') THEN
        '充值'
        WHEN EXISTS (SELECT 1 FROM short_order o WHERE o.user_id = u.id AND o.STATUS = 'SUCCEEDED' AND o.pay_type = '订阅') THEN
        '订阅'
        ELSE
        '无成功订单'
        END AS display_type
        FROM
        short_user u
        WHERE
        u.id = #{id} and u.app_id= #{appId}
    </select>

    <select id="getResList" resultType="java.lang.Long">
        SELECT ff.id FROM (
                              WITH ranked_users AS (
                                  SELECT
                                      su.*,
                                      sa.extplats_id,
                                      ROW_NUMBER() OVER (PARTITION BY sa.extplats_id, su.email ORDER BY su.expire_time DESC) AS rn
                                  FROM
                                      short_user su
                                          JOIN
                                      short_app sa ON su.app_id = sa.id
                                  WHERE
                                      su.id IN
                                <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                                    #{userId}
                                </foreach>
                              ),
                                   filtered_users AS (
                                       SELECT
                                           su.*,
                                           sa.extplats_id
                                       FROM
                                           short_user su
                                               JOIN
                                           short_app sa ON su.app_id = sa.id
                                       WHERE
                                           su.email IN (SELECT email FROM short_user WHERE id IN
                                        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                                            #{userId}
                                        </foreach>
                              )
                                         AND su.expire_time > (SELECT MAX(expire_time) FROM short_user WHERE id IN
                                        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                                            #{userId}
                                        </foreach>
                              )
                                   ),
                                   filtered_ranked_users AS (
                                       SELECT
                                           *,
                                           ROW_NUMBER() OVER (PARTITION BY extplats_id, email ORDER BY expire_time DESC) AS rn
                                       FROM
                                           filtered_users
                                   )
                              SELECT
                                  ru.*
                              FROM
                                  ranked_users ru
                              WHERE
                                  ru.rn = 1
                                AND NOT EXISTS (
                                  SELECT
                                      1
                                  FROM
                                      filtered_ranked_users fru
                                  WHERE
                                      fru.rn = 1
                                    AND fru.extplats_id = ru.extplats_id
                                    AND fru.email = ru.email
                              )
                          ) ff


    </select>
    <select id="selectShortUserByEmail" resultMap="ShortUserResult">
        <include refid="selectShortUserVo"/>
        where email = #{email}
        and `state` = 1
    </select>
    <select id="selectByUserIdList" resultType="com.ruoyi.domain.ShortUser">
        <include refid="selectShortUserVo"/>
        WHERE
        id IN
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="get816" resultType="com.ruoyi.domain.ShortUser">
        SELECT * from short_user WHERE
            unsub = 0
                                    and
            id in (
                843479


                )

    </select>
    <select id="getRenewSubscribe1" resultType="com.ruoyi.domain.ShortUser">
        SELECT user_id id from short_renew_subscribe_data WHERE log_date = CURDATE()
    </select>
    <select id="selectUnsubUser" resultType="java.lang.Long">
        SELECT
            ANY_VALUE(u.id) id
        FROM
            send_email_user_id e
                LEFT JOIN short_user u ON u.id = e.user_id
        WHERE
            u.email IS NOT NULL
          AND u.email != ''
  and u.expire_time is not null
        GROUP BY
            u.email
    </select>
    <select id="getBy818" resultType="com.ruoyi.domain.ShortUser">
        SELECT * from short_user WHERE
            email > ''
                                   and
            id IN (
                   3501329
                ,1962134
                ,2405435
                ,3681931
                ,3677020
                ,1591419
                ,1883333
                ,24590
                ,2118940
                ,3745137
                ,3466658
                ,897
                ,758
                ,1410525
                ,3785998
                ,2095771
                ,983
                ,1728713
                ,34497
                ,2007058
                ,7406
                ,1238050
                ,2627134
                ,743217
                ,3789705
                ,2412449
                ,15146
                ,2488096
                ,1712310
                ,12432
                ,1660986
                ,2748
                ,1657816
                ,2691824
                ,730528
                ,1656
                ,1165980
                ,3655753
                ,3111
                ,1604
                ,3472682
                ,2043871
                ,1417231
                ,2957449
                ,3436715
                ,3582848
                ,3674604
                ,3625344
                ,3792118
                ,1147954
                ,3117812
                ,3447329
                ,3696292
                ,3617538
                ,472452
                ,1284106
                ,2580962
                ,3792405
                ,3607625
                ,3671757
                ,2380143
                ,3674970
                ,1950812
                ,2579271
                ,913206
                ,5427
                ,2250885
                ,1959349
                ,5779
                ,3456526
                ,1729410
                ,2350706
                ,2937592
                ,2726442
                ,3062481
                ,2673626
                ,2956627
                ,7559
                ,2810722
                ,3618337
                ,2904790
                ,3056126
                ,2875139
                ,2952331
                ,3007789
                ,1282710
                ,1441908
                ,2871292
                ,3021968
                ,4413
                ,1398556
                ,7982
                ,2181067
                ,1688696
                ,3704937
                ,1579552
                ,829802
                ,2631375
                ,3678940
                ,7479
                ,2414468
                ,2907800
                ,2680985
                ,2450224
                ,2098346
                ,2904929
                ,3608845
                ,3020659
                ,2908300
                ,2954729
                ,1829355
                ,1374360
                ,1014438
                ,2579128
                ,2048
                ,1151828
                ,1775136
                ,18767
                ,1415904
                ,1931734
                ,51034
                ,1707945
                ,14186
                ,1669687
                ,818774
                ,2926992
                ,2852529
                ,2118885
                ,2832870
                ,1176980
                ,1026
                ,808665
                ,2354000
                ,866810
                ,685967
                ,1980670
                ,1289
                ,996024
                ,2452671
                ,1341697
                ,2274966
                ,1122812
                ,2879363
                ,1766929
                ,841
                ,14585
                ,2477
                ,3613630
                ,1146997
                ,8910
                ,1178022
                ,868113
                ,3068
                ,3087
                ,1807394
                ,2381221
                ,2334764
                ,2173314
                ,2323607
                ,4140
                ,1702644
                ,3079116
                ,2145225
                ,2753579
                ,3668553
                ,2678070
                ,1310941
                ,2225875
                ,4540
                ,2380873
                ,7691
                ,1149876
                ,24013
                ,1423236
                ,2241707
                ,2221588
                ,2382393
                ,1457574
                ,2996
                ,1316773
                ,2722018
                ,2877034
                ,21444
                ,7123
                ,25899
                ,1147894
                ,1026863
                ,22221
                ,1324535
                ,1680481
                ,2055946
                ,1671333
                ,1694820
                ,2866651
                ,26310
                ,24520
                ,531087
                ,1185416
                ,699876
                ,776486
                ,2528485
                ,26197
                ,2599018
                ,2490642
                ,793576
                ,2787
                ,1123342
                ,2593151
                ,2250850
                ,2870
                ,2227303
                ,3651318
                ,1957912
                ,711301
                ,1959215
                ,1713805
                ,9013
                ,15153
                ,610
                ,3062753
                ,910761
                ,741630
                ,2849434
                ,6436
                ,2923
                ,888145
                ,3135147
                ,3062598
                ,1147262
                ,2867586
                ,5490
                ,4073
                ,718115
                ,2413715
                ,2956802
                ,2923513
                ,1862820
                ,1630815
                ,2489873
                ,1684417
                ,23550
                ,2869495
                ,2822100
                ,2510176
                ,810302
                ,1316
                ,1659788
                ,12348
                ,1830666
                ,1206849
                ,2674119
                ,711263
                ,4581
                ,1681347
                ,3026344
                ,2788982
                ,2778241
                ,1407651
                ,2353175
                ,10143
                ,2381769
                ,1941618
                ,1739794
                ,2241753
                ,2334728
                ,665017
                ,2051716
                ,2979790
                ,1343084
                ,1086796
                ,1196785
                ,8379
                ,23378
                ,2950793
                ,22902
                ,2522106
                ,2941644
                ,7152
                ,3015992
                ,1702364
                ,10117
                ,195835
                ,2658323
                ,2314557
                ,855207
                ,2926535
                ,20787
                ,1054243
                ,10065
                ,2689749
                ,3109638
                ,3025160
                ,2287304
                ,842749
                ,674593
                ,1463442
                ,2157312
                ,3090951
                ,785395
                ,31385
                ,1118251
                ,837447
                ,1857618
                ,2534448
                ,2399072
                ,8437
                ,661509
                ,962
                ,2919
                ,3790393
                ,6218
                ,3685407
                ,2723125
                ,2742760
                ,2785240
                ,3050086
                ,2674189
                ,20978
                ,19335
                ,2350809
                ,2950190
                ,2782813
                ,3077714
                ,3784276
                ,1168366
                ,874457
                ,1072729
                ,2142848
                ,816420
                ,3292642
                ,4964
                ,2374557
                ,1082765
                ,18158
                ,2445477
                ,2181453
                ,587713
                ,821096
                ,1406953
                ,23840
                ,2370441
                ,698734
                ,1695146
                ,7602
                ,3780125
                ,2912345
                ,1124443
                ,798371
                ,2776049
                ,1470779
                ,2122281
                ,1793775
                ,945144
                ,3785307
                ,1530868
                ,3744172
                ,2424250
                ,2388605
                ,807990
                ,1678663
                ,1175756
                ,4089
                ,774691
                ,2414953
                ,1684469
                ,3503612
                ,1833413
                ,2483984
                ,2765048
                ,1388078
                ,20472
                ,2496386
                ,2039225
                ,2893
                ,2633630
                ,2280242
                ,1798636
                ,32427
                ,2581509
                ,3682621
                ,1072840
                ,3045793
                ,3671529
                ,1822988
                ,1446263
                ,2233408
                ,1072175
                ,1479565
                ,1879278
                ,159701
                ,23547
                ,1590056
                ,7815
                ,1140411
                ,1370452
                ,3753
                ,1144680
                ,90388
                ,744057
                ,3036555
                ,15769
                ,1080582
                ,3608612
                ,3647684
                ,2995935
                ,2724685
                ,1412024
                ,1925040
                ,3079024
                ,1676665
                ,2613462
                ,2588
                ,1985941
                ,26462
                ,8846
                ,3434059
                ,1718507
                ,2286829
                ,1615214
                ,2921699
                ,3040412
                ,873458
                ,882370
                ,1367313
                ,2324011
                ,2738038
                ,3609023
                ,2944177
                ,2984865
                ,2445305
                ,773301
                ,1972599
                ,2738966
                ,1139219
                ,2048745
                ,797555
                ,1969311
                ,1115927
                ,725475
                ,2722542
                ,2889643
                ,791940
                ,1239936
                ,2185033
                ,2287804
                ,1972605
                ,2745668
                ,667379
                ,5408
                ,3021
                ,2416310
                ,2233923
                ,23942
                ,17597
                ,1113161
                ,1150861
                ,9685
                ,913408
                ,1948438
                ,2189817
                ,1582360
                ,3129259
                ,11402
                ,747085
                ,782001
                ,2634335
                ,1213224
                ,3060263
                ,1181799
                ,2452041
                ,2045704
                ,1178530
                ,2693183
                ,2668635
                ,872681
                ,2720502
                ,2772237
                ,31613
                ,1903565
                ,805918
                ,22423
                ,2493439
                ,1848278
                ,2814219
                ,1429344
                ,1461524
                ,25318
                ,6209
                ,835194
                ,3002629
                ,1693217
                ,2873706
                ,1480201
                ,1126760
                ,2984457
                ,2941
                ,23952
                ,2525351
                ,425200
                ,2494830
                ,2879583
                ,2491281
                ,1208108
                ,1674550
                ,2357
                ,2309792
                ,2634647
                ,1685842
                ,1760647
                ,2238461
                ,2906493
                ,3633
                ,2252112
                ,1645
                ,3133639
                ,26678
                ,21583
                ,1188392
                ,660356
                ,2104
                ,3027275
                ,6737
                ,743977
                ,1001985
                ,2411278
                ,663944
                ,20392
                ,1463677
                ,1183599
                ,2636646
                ,1979137
                ,19499
                ,5244
                ,89293
                ,29973
                ,2532652
                ,3679785
                ,3604329
                ,3604546
                ,17895
                ,5905
                ,2488591
                ,3104
                ,1679295
                ,2138392
                ,2289706
                ,1403586
                ,2880617
                ,23278
                ,1254408
                ,1837402
                ,2587976
                ,8237
                ,2636338
                ,2613556
                ,1812104
                ,2991074
                ,3929
                ,2429
                ,1855633
                ,3409059
                ,2153812
                ,1571611
                ,2626969
                ,1469914
                ,1691918
                ,1016785
                ,2344644
                ,2685781
                ,3031929
                ,2523525
                ,2070823
                ,21460
                ,685801
                ,796133
                ,1132717
                ,1646498
                ,2281668
                ,14085
                ,837976
                ,2566950
                ,18319
                ,5089
                ,661797
                ,1762369
                ,2573443
                ,2171
                ,783236
                ,2637605
                ,2145664
                ,836666
                ,682296
                ,1441876
                ,1003208
                ,2375008
                ,2129548
                ,2866914
                ,1588000
                ,1713656
                ,2459433
                ,2520127
                ,1740298
                ,1180313
                ,2403578
                ,1675952
                ,743040
                ,2178735
                ,2016676
                ,1855543
                ,2762032
                ,2771822
                ,2954973
                ,2342266
                ,1961857
                ,2868321
                ,3072572
                ,2098003
                ,2659794
                ,21451
                ,2151502
                ,899447
                ,2975306
                ,3415875
                ,4021
                ,2372184
                ,1408769
                ,854131
                ,789944
                ,11697
                ,1033351
                ,1940080
                ,2681323
                ,2905855
                ,2958300
                ,3098239
                ,2524058
                ,2636912
                ,1480601
                ,1179811
                ,3764613
                ,2799874
                ,3620098
                ,2518066
                ,15172
                ,1182439
                ,2023578
                ,1739981
                ,1049913
                ,1416966
                ,2723627
                ,1135474
                ,20741
                ,2220477
                ,845125
                ,667563
                ,1397228
                ,3761975
                ,959987
                ,2998664
                ,3110450
                ,20339
                ,2479802
                ,17476
                ,3746324
                ,1795350
                ,808371
                ,2573675
                ,2822081
                ,3708646
                ,2578429
                ,3062283
                ,2476055
                ,1133916
                ,2582670
                ,3480
                ,1831844
                ,486624
                ,3908
                ,1374146
                ,844616
                ,2537806
                ,3048067
                ,2744885
                ,18291
                ,24357
                ,2632685
                ,3074291
                ,3778695
                ,6524
                ,13015
                ,3419
                ,9906
                ,2087049
                ,1179971
                ,2095890
                ,2495868
                ,3669114
                ,2496737
                ,2144468
                ,1845560
                ,10767
                ,23337
                ,2774127
                ,2958290
                ,841292
                ,3682366
                ,3070616
                ,2354091
                ,2702438
                ,2365359
                ,4305
                ,1337032
                ,1845997
                ,2610078
                ,3575
                )
    </select>
    <select id="getEmailAndIsSubscriber" resultType="com.ruoyi.domain.ShortUser">
        SELECT
            id,
            email,
            app_id,
            linkid_id,
            unique_id,
            `password`,
            hash_id
        FROM
            short_user
        WHERE
            is_subscriber = 1
          and email > ''
          and id = 841035
    </select>
    <select id="countEmail" resultType="java.lang.Integer">
        SELECT COUNT(id) from short_user WHERE email = #{receiveEmail}
    </select>
    <select id="getOldUser" resultType="com.ruoyi.domain.ShortUser">
        SELECT * from short_user WHERE email = #{email} AND app_id = #{appId} and state = 1 ORDER BY create_time desc LIMIT 1
    </select>
    <select id="countByEmailAndAppId" resultType="java.lang.Integer">
        SELECT COUNT(id) from short_user WHERE email =  #{email} and app_id = #{appId} and coin > 0 and state = 1
    </select>
    <select id="getLastUser" resultType="com.ruoyi.domain.ShortUser">
        SELECT * from short_user WHERE email = #{email} ORDER BY expire_time desc LIMIT 1
    </select>
    <select id="getAfterExpireTime" resultType="com.ruoyi.domain.ShortUser">
        SELECT * from short_user WHERE email = #{email} and expire_time > NOW()  ORDER BY expire_time desc LIMIT 1
    </select>
    <select id="getExpiredUser" resultType="java.lang.Long">
        SELECT id from short_user WHERE is_subscriber = 1 and  expire_time &lt; NOW()
    </select>
    <select id="getCheckUserByUserId" resultType="com.ruoyi.domain.ShortUser">
        WITH target_user AS (
            SELECT
                id,
                email
            FROM short_user
            WHERE id = #{userId}
        )
        SELECT
            su.*
        FROM short_user su
                 JOIN target_user tu ON
            (tu.email IS NOT NULL AND su.email = tu.email)
                OR
            (tu.email IS NULL AND su.id = tu.id)
        ORDER BY
            CASE WHEN su.id = tu.id THEN 0 ELSE 1 END,
            su.id
    </select>
    <select id="selectBankCard" resultType="com.ruoyi.domain.ShortUser">
        SELECT * from (WITH latest_bank_card AS (
        SELECT
        tp.bank_card
        FROM
        temp_payment_success_python tp
        INNER JOIN
        short_order so ON tp.payment_intent_id = so.paymentIntentId
        WHERE
        tp.payment_intent_id in
        <foreach item="paymentIntentId" collection="paymentIntentIds" open="(" separator="," close=")">
            #{paymentIntentId}
        </foreach>
        ORDER BY
        tp.created_at DESC
        LIMIT 1
        )

        SELECT
        su.*
        FROM
        short_user su
        INNER JOIN
        short_order so ON su.id = so.user_id
        INNER JOIN
        temp_payment_success_python tp ON tp.payment_intent_id = so.paymentIntentId
        WHERE
        tp.bank_card = (SELECT bank_card FROM latest_bank_card))a GROUP BY a.id ORDER BY a.expire_time desc
    </select>
    <select id="getNotNullExpireTime" resultType="java.lang.Long">
        SELECT id from short_user WHERE expire_time is not null
    </select>
    <select id="getAllIdsById" resultType="java.lang.Long">
        SELECT
            id
        FROM
            short_user
        WHERE
            email = (SELECT email FROM short_user WHERE id = #{id})
          AND unsub = 0
          AND expire_time > NOW()
    </select>
    <select id="getNullEmailListIds" resultType="java.lang.Long">
        SELECT
            DISTINCT (su.id)
        FROM
            short_user su LEFT JOIN short_order so on su.id = so.user_id
        WHERE
            (su.email = '' OR su.email IS NULL)
          and so.`status` = 'SUCCEEDED'
          and so.pay_type != '充值'
         and so.mark_type is null
    </select>
    <select id="getEmailListIds" resultType="java.lang.Long">

        SELECT DISTINCT(user_id) from short_order WHERE user_id in (

            SELECT a.id from ( SELECT id
                               FROM short_user
                               WHERE email IN (
                                   SELECT email
                                   FROM short_user
                                   WHERE email > ''
                                   GROUP BY email
                                   HAVING COUNT(*) = 1
                               )
                               ORDER BY id)a
        ) and  `status` = 'SUCCEEDED'
                                                    and pay_type != '充值'
          and mark_type is null
    </select>
    <select id="getEmailList" resultType="java.lang.String">
        SELECT
            email
        FROM
            short_user
        WHERE
            email > ''
          and app_id = #{appId}
        GROUP BY
            email
        HAVING
            COUNT(*) > 1
        ORDER BY
            COUNT(*) DESC
    </select>
    <select id="getByEmail" resultType="java.lang.Long">
        SELECT id from short_user WHERE email = #{email}
        <if test="type == 0">and app_id = #{appId}</if>
        <if test="type > 0">and app_id != #{appId}</if>
    </select>
    <select id="getEmailMarkTypeIds" resultType="java.lang.String">
        SELECT
            su.email
        FROM
            short_user su
                INNER  JOIN short_order so ON su.id = so.user_id
                AND so.`status` = 'SUCCEEDED'
                AND su.app_id = 25
                AND so.pay_type != '充值'
                and so.mark_type = 1
                and su.email > '';
    </select>
    <select id="getEmailNotApp25Ids" resultType="java.lang.String">
        SELECT
            DISTINCT(su.email)
        FROM
            short_user su
                INNER  JOIN short_order so ON su.id = so.user_id
                AND so.`status` = 'SUCCEEDED'
                AND su.app_id != 25
                AND so.pay_type != '充值'
                and so.mark_type is null
                and su.email > '';
    </select>
    <select id="getCycleList" resultType="com.ruoyi.domain.ShortUser">

        -- 周期重置sql
        SELECT
        su.id,
        su.app_id,
        su.vip_id,
        IFNULL(slv.subscription_type, sv.subscription_type) AS subscription_type,
        su.expire_time,
        su.is_subscriber,
        su.unsub,
        su.unsub_time,
        su.unsub_source,
        su.email,
        su.create_time,
        IFNULL(su.code_num,0) code_num ,
        IFNULL(su.cycle_num,0)cycle_num,
        su.last_code
        FROM
        short_user su
        LEFT JOIN short_link_vip slv ON slv.id = su.vip_id
        LEFT JOIN short_vip sv ON sv.id = su.vip_id
        WHERE
        -- su.id in (842225) AND
        -- AND su.app_id = 25

        su.unsub_source IS NULL
          and su.unsub = 0
          and su.last_code in('51')
        AND (su.code_num = 3 or su.code_num is null )
        AND DATE_ADD(
        su.expire_time,
        INTERVAL ((IFNULL(su.cycle_num,0) + 1) * IFNULL(slv.subscription_type, sv.subscription_type) - 1) DAY
        ) &lt; NOW();
    </select>
    <select id="getNoEmailList" resultType="java.lang.Long">
        SELECT
            user_id id
        FROM (
                 SELECT
                     su.id AS user_id,
                     su.app_id app_id,
                     su.email,
                     su.expire_time AS expire_time,
                     so.update_time AS update_time,
                     so.amount AS amount,
                     ROW_NUMBER() OVER (PARTITION BY su.id ORDER BY so.update_time DESC) AS rn
                 FROM
                     short_user su
                         INNER JOIN short_order so ON su.id = so.user_id
                 WHERE
                     (su.email = '' OR su.email IS NULL) and
                     (su.unsub != 1  or su.unsub is null)  and su.state = 1
                   AND su.expire_time &lt;= NOW() + INTERVAL 1 DAY
                   AND so.pay_type != '充值'
                   AND so.`status` = 'SUCCEEDED'
                   and so.mark_type = 1
                   and su.last_code is null
                    and (su.code_num &lt; 3 or su.code_num is null )
             ) t
        WHERE rn = 1
        ORDER BY update_time DESC
    </select>
    <select id="getEmailList1" resultType="java.lang.Long">
        SELECT
        id,
        email,
        app_id,
        unsub
        FROM
        short_user su
        WHERE
        su.id IN (
        SELECT DISTINCT
        (user_id)
        FROM
        short_order
        WHERE
        user_id IN (
        SELECT
        a.id
        FROM
        (
        SELECT
        id
        FROM
        short_user
        WHERE
        last_code IS NULL AND
        email IN (SELECT email FROM short_user WHERE email > '' GROUP BY email HAVING COUNT(*) = 1)
        ORDER BY
        id
        ) a
        )
        AND `status` = 'SUCCEEDED'
        AND pay_type != '充值'
        AND mark_type = 1
        )
        AND (su.unsub != 1 OR su.unsub IS NULL)
        AND su.state = 1
        AND (su.code_num &lt; 3 OR su.code_num IS NULL)
        AND su.expire_time &lt;= NOW() + INTERVAL 1 DAY
    </select>
    <select id="getEmailAll25" resultType="java.lang.Long">
        SELECT
            su.id
        FROM
            short_user su
                INNER JOIN short_order so ON su.id = so.user_id
        WHERE
            su.email > ''
          and su.app_id = 25
          and (su.unsub != 1  or su.unsub is null)  and su.state = 1
          AND so.pay_type != '充值'
        AND so.`status` = 'SUCCEEDED'
        and so.mark_type = 1
        and su.last_code is null
        and (su.code_num &lt; 3 or su.code_num is null )
        AND su.expire_time &lt;= NOW() + INTERVAL 1 DAY
        GROUP BY su.id
    </select>
    <select id="getEmailListByAll25" resultType="java.lang.String">
        SELECT email from short_user WHERE id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="getAllNotEmailListByAll25" resultType="java.lang.Long">
        SELECT
        DISTINCT(su.id)
        FROM
            short_user su
                INNER  JOIN short_order so ON su.id = so.user_id
                AND so.`status` = 'SUCCEEDED'
                AND su.app_id != 25
                and su.last_code is null
                AND so.pay_type != '充值'
                and (su.unsub != 1  or su.unsub is null)  and su.state = 1
                and so.mark_type = 1
                and (su.code_num &lt; 3 or su.code_num is null )
                AND su.expire_time &lt;= NOW() + INTERVAL 1 DAY
                and su.email > ''
                and su.email not in
                <foreach item="email" collection="emails" open="(" separator="," close=")">
                    #{email}
                </foreach>
    </select>
    <select id="getIds" resultType="com.ruoyi.domain.ShortUser">
        SELECT id,app_id from short_user WHERE id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="getEmail25AndOtherList" resultType="java.lang.String">

        WITH user_email_counts AS (
            SELECT
                email,
                COUNT(DISTINCT app_id) AS app_count,
                SUM(CASE WHEN app_id = 25 THEN 1 ELSE 0 END) AS app_25_count
            FROM
                short_user
            WHERE
                email > ''
            GROUP BY
                email
            HAVING
                COUNT(DISTINCT app_id) > 1
               AND SUM(CASE WHEN app_id = 25 THEN 1 ELSE 0 END) = 1
        )

        SELECT
            su.email
        FROM
            short_user su
                JOIN
            user_email_counts uec ON su.email = uec.email
        WHERE
            su.app_id = 25
          AND su.email > '';
    </select>


</mapper>
