package com.ruoyi.service;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.domain.ShortAppLanguage;
import com.ruoyi.domain.ShortMovieI18n;
import com.ruoyi.dto.ShortMovieI18nDTO;
import com.ruoyi.dto.ShortMovieI18nIconDTO;
import com.ruoyi.dto.ShortMovieI18nInDTO;

import java.util.List;

/**
 * 影片多语言Service接口
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
public interface IShortMovieI18nService
{
    /**
     * 查询影片多语言
     *
     * @param id 影片多语言主键
     * @return 影片多语言
     */
    public ShortMovieI18n selectShortMovieI18nById(Long id);

    /**
     * 查询影片多语言列表
     *
     * @param shortMovieI18n 影片多语言
     * @return 影片多语言集合
     */
    public List<ShortMovieI18n> selectShortMovieI18nList(ShortMovieI18n shortMovieI18n);

    /**
     * 新增影片多语言
     *
     * @param shortMovieI18n 影片多语言
     * @return 结果
     */
    public int insertShortMovieI18n(ShortMovieI18n shortMovieI18n);

    /**
     * 修改影片多语言
     *
     * @param shortMovieI18n 影片多语言
     * @return 结果
     */
    public int updateShortMovieI18n(ShortMovieI18n shortMovieI18n);

    /**
     * 批量删除影片多语言
     *
     * @param ids 需要删除的影片多语言主键集合
     * @return 结果
     */
    public int deleteShortMovieI18nByIds(Long[] ids);

    /**
     * 删除影片多语言信息
     *
     * @param id 影片多语言主键
     * @return 结果
     */
    public int deleteShortMovieI18nById(Long id);

    /**
     * 短剧AI翻译
     * @param shortMovieI18nDTO
     * @return
     */
    public void translateMovie(ShortMovieI18nDTO shortMovieI18nDTO);

    /**
     * 根据movieId查询已翻译的剧
     */
    List<ShortMovieI18n> queryTranslateMovieList(Long movieId, String languageCode);

    void translateAllMovie();

    AjaxResult updateState(ShortMovieI18n shortMovieI18n);

    List<ShortMovieI18n> queryTranslateMovieList(List<Long> movieIdList, String languageCode);

    int deleteShortMovieI18n(Long movieId, String languageCode);

    List<ShortAppLanguage> queryAccessTranslateLanguageList(Long movieId);

    void batchTranslateMovie(ShortMovieI18nInDTO shortMovieI18nInDTO);

    AjaxResult batchUpdateState(Long movieId);

    void batchUpdateIcon(List<ShortMovieI18nIconDTO> iconDTOS, Long movieId);

    List<ShortMovieI18n> selectByMovieIdList(List<Long> movieIdList);
}
