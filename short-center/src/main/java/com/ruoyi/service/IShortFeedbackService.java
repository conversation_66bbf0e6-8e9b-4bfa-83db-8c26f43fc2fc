package com.ruoyi.service;

import java.util.List;
import com.ruoyi.domain.ShortFeedback;

/**
 * 用户反馈Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
public interface IShortFeedbackService 
{
    /**
     * 查询用户反馈
     * 
     * @param id 用户反馈主键
     * @return 用户反馈
     */
    public ShortFeedback selectShortFeedbackById(Long id);

    /**
     * 查询用户反馈列表
     * 
     * @param shortFeedback 用户反馈
     * @return 用户反馈集合
     */
    public List<ShortFeedback> selectShortFeedbackList(ShortFeedback shortFeedback);

    /**
     * 新增用户反馈
     * 
     * @param shortFeedback 用户反馈
     * @return 结果
     */
    public int insertShortFeedback(ShortFeedback shortFeedback);

    /**
     * 修改用户反馈
     * 
     * @param shortFeedback 用户反馈
     * @return 结果
     */
    public int updateShortFeedback(ShortFeedback shortFeedback);

    /**
     * 批量删除用户反馈
     * 
     * @param ids 需要删除的用户反馈主键集合
     * @return 结果
     */
    public int deleteShortFeedbackByIds(Long[] ids);

    /**
     * 删除用户反馈信息
     * 
     * @param id 用户反馈主键
     * @return 结果
     */
    public int deleteShortFeedbackById(Long id);

    ShortFeedback getUserAndOrder(Long userId, Long appId,String email);

    /**
     * 检查邮箱在指定应用中是否存在反馈记录
     *
     * @param email 邮箱
     * @param appId 应用ID
     * @return 是否存在
     */
    public boolean existsByEmailAndAppId(String email, Long appId);

    /**
     * 批量检查邮箱在指定应用中是否存在反馈记录
     *
     * @param emails 邮箱列表
     * @param appId 应用ID
     * @return 存在的邮箱列表
     */
    public List<String> batchExistsByEmailAndAppId(List<String> emails, Long appId);
}
