package com.ruoyi.mapper;

import com.ruoyi.domain.ShortUser;
import com.ruoyi.domain.ShortUserCP;
import com.ruoyi.domain.ShortUserDo;
import com.ruoyi.dto.ShortOrderDTO;
import com.ruoyi.vo.ShortUserForLinkVO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;
import java.util.List;

/**
 * 用户管理Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
public interface ShortUserMapper
{
    /**
     * 查询用户管理
     *
     * @param id 用户管理主键
     * @return 用户管理
     */
    public ShortUser selectShortUserById(Long id);

    /**
     * 查询用户管理列表
     *
     * @param shortUser 用户管理
     * @return 用户管理集合
     */
    public List<ShortUser> selectShortUserList(ShortUser shortUser);

    /**
     * 根据第三方账号ID查询用户
     *
     * @param subId 第三方账号ID
     * @return 用户信息
     */
    public ShortUser selectShortUserBySubId(String subId);

    /**
     * 新增用户管理
     *
     * @param shortUser 用户管理
     * @return 结果
     */
    public int insertShortUser(ShortUser shortUser);

    /**
     * 修改用户管理
     *
     * @param shortUser 用户管理
     * @return 结果
     */
    public int updateShortUser(ShortUser shortUser);

    /**
     * 删除用户管理
     *
     * @param id 用户管理主键
     * @return 结果
     */
    public int deleteShortUserById(Long id);

    /**
     * 批量删除用户管理
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteShortUserByIds(Long[] ids);

    int getCount(ShortUser shortUser);

    /**
     * 根据推广链接查询用户
     * @param list
     * @return
     */
    List<ShortUser> selectByLinkIdList(List<Long> list);

    ShortUser findByIdAndAppIdAndSource(@Param("uid") String uid, @Param("nid") Integer nid, @Param("source") String source);

    /**
     * 使用行锁查询用户信息
     * 使用FOR UPDATE子句实现行级锁定
     *
     * @param userId 用户ID
     * @return 带行锁的用户对象
     */
    public ShortUser selectShortUserWithLock(Long userId);

    List<ShortUser> scanUser();

    int updateIpv4Country(@Param("userId") Long userId, @Param("ipCode") BigInteger bigInteger);

    int updateIpv6Country(@Param("userId") Long userId, @Param("ipCode") BigInteger bigInteger);

    List<ShortUser> getAllSubUsers();

    List<ShortUserDo> selectShortUserListAll(ShortUser shortUser);

    /**
     * 根据IPv4地址直接查询国家信息
     *
     * @param ip IPv4地址
     * @return 国家名称
     */
    String getCountryByIpv4(@Param("ip") String ip);

    /**
     * 根据IPv6地址直接查询国家信息
     *
     * @param ip IPv6地址
     * @return 国家名称
     */
    String getCountryByIpv6(@Param("ip") String ip);

    /**
     * 根据IPv6地址的十进制表示查询国家名称
     *
     * @param ipDecimal IPv6地址转换后的BigDecimal值
     * @return 国家名称，如果找不到则返回null
     */
    String getCountryByIpv6Decimal(@Param("ipDecimal") BigDecimal ipDecimal);

    int updateUnsub(Long id);

    void updateUserCountry(ShortUser shortUserDo);

    void updateState(@Param("userId")String userId,@Param("state") int state);

    List<ShortUser> getAllByTypeAndExpireTime(@Param("type")String type,@Param("payChannel")String payChannel,@Param("payType")int payType);

    List<ShortUser> getRenewSubscribe();

    ShortUserForLinkVO getSystemAddUserCount(@Param("dto") ShortOrderDTO orderDTO);

    void updateOther(@Param("id")Long id, @Param("ot")String ot);

    String getIconByUserId(Long userId);

    Boolean selectShortUserEmailFlag(@Param("userId") long l);

    List<ShortUser> getByLaset();

    List<ShortUserCP> selectShortUserByTime(@Param("beginTime") String beginTime, @Param("endTime") String endTime, @Param("nextId") Long nextId);

    List<ShortUser> getBackRegEmail();

    ShortUser selectShortUserByUserId(Long id);

    ShortUser selectByAppIdAndUnid(@Param("nid")Integer nid, @Param("unid")String unid);

    String getPayTypeById(@Param("id")Long id, @Param("appId")Long appId);

    ShortUser getAfterExpireTime(String email);

    void updateExpireTimeById(@Param("id")Long id, @Param("afterExpireTime")Date afterExpireTime);

    List<Long> getResList(@Param("userIds")List<Long> userIds);

    List<ShortUser> selectShortUserByEmail(@Param("email") String email);

    void setIsSubscriber(Long id);

    void updateUnsubTime(Long id);

    void updateUserVipStatus(Long userId);

    void batchUpdateUserVipStatus(List<Long> list);

    List<ShortUser> selectByUserIdList(List<Long> list);

    List<ShortUser> get816();

    List<ShortUser> getRenewSubscribe1();

    List<Long> selectUnsubUser();

    List<ShortUser> getBy818();

    List<ShortUser> getEmailAndIsSubscriber();

    void updatePasswardByEmail(@Param("email") String email, @Param("newPassword") String newPassword);

    int countEmail(String receiveEmail);

    ShortUser getOldUser(@Param("appId") Long appId, @Param("email") String email);

    int countByEmailAndAppId(@Param("appId") String appId, @Param("email") String email);

    ShortUser getLastUser(String email);

    List<Long> getExpiredUser();

    void updateIsSubscriberByIds(@Param("ids") List<Long> ids);

    void updateUnsubSource(@Param("id") Long id, @Param("content") String content);

    List<ShortUser> getCheckUserByUserId(Long userId);

    List<ShortUser> selectBankCard(@Param("paymentIntentIds") List<String> paymentIntentIds);

    void updateIsCheck(Long userId);

    List<Long> getNotNullExpireTime();

    List<Long> getAllIdsById(Long id);

    List<Long> getNullEmailListIds();

    List<Long> getEmailListIds();

    List<String> getEmailList(Long appId);

    List<Long> getByEmail(@Param("email") String email, @Param("appId") Long appId, @Param("type") int type);

    List<String> getEmailMarkTypeIds();

    List<String> getEmailNotApp25Ids();

    List<ShortUser> getCycleList();

    void updateCodeAndCycleNum(@Param("id") Long id, @Param("cycleNum") int cycleNum);

    List<Long> getNoEmailList();

    List<Long> getEmailList1();

    List<Long> getEmailAll25();

    List<String> getEmailListByAll25(@Param("ids") List<Long> ids);

    List<Long> getAllNotEmailListByAll25(@Param("emails") List<String> emails);

    void updateLastCode(@Param("userId") Long userId, @Param("code") String code);

    List<ShortUser> getIds(@Param("list") List<Long> mergedList);

    List<String> getEmail25AndOtherList();
}
