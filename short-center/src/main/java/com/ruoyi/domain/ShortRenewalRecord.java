package com.ruoyi.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 续订记录对象 short_renewal_record
 *
 * <AUTHOR>
 * @date 2025-09-17
 */
public class ShortRenewalRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 用户id */
    @Excel(name = "用户id")
    private Long userId;

    /** appid */
    @Excel(name = "appid")
    private Long appId;

    /** 日期 */
    @Excel(name = "日期")
    private String logDate;

    /** 0.未执行，1.已执行 */
    @Excel(name = "0.未执行，1.已执行")
    private Long type;

    private Long emailType;

    public Long getEmailType() {
        return emailType;
    }

    public void setEmailType(Long emailType) {
        this.emailType = emailType;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }

    public void setUserId(Long userId)
    {
        this.userId = userId;
    }

    public Long getUserId()
    {
        return userId;
    }

    public void setAppId(Long appId)
    {
        this.appId = appId;
    }

    public Long getAppId()
    {
        return appId;
    }

    public void setLogDate(String logDate)
    {
        this.logDate = logDate;
    }

    public String getLogDate()
    {
        return logDate;
    }

    public void setType(Long type)
    {
        this.type = type;
    }

    public Long getType()
    {
        return type;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("userId", getUserId())
                .append("appId", getAppId())
                .append("logDate", getLogDate())
                .append("type", getType())
                .append("createTime", getCreateTime())
                .toString();
    }
}
