package com.ruoyi.app.api.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alipay.global.api.model.Result;
import com.alipay.global.api.model.ResultStatusType;
import com.ruoyi.app.api.dto.InitUserDTO;
import com.ruoyi.app.api.dto.PaymentNotifyDTO;
import com.ruoyi.app.api.factory.PaymentFactory;
import com.ruoyi.app.api.request.PayOrderRequest;
import com.ruoyi.app.api.service.BusinessFunctionApiService;
import com.ruoyi.app.api.service.PaymentApiService;
import com.ruoyi.app.api.service.PaymentStrategy;
import com.ruoyi.app.api.utils.RunLogUtil;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.OrderStatus;
import com.ruoyi.common.enums.PayChannel;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.domain.*;
import com.ruoyi.mapper.ShortEmailSendLogMapper;
import com.ruoyi.mapper.ShortOrderMapper;
import com.ruoyi.mapper.ShortRenewSubscribeDataMapper;
import com.ruoyi.mapper.ShortUserMapper;
import com.ruoyi.service.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 支付相关API服务实现
 */
@Service
public class PaymentApiServiceImpl implements PaymentApiService {

    private static final Logger log = LoggerFactory.getLogger(PaymentApiServiceImpl.class);

    @Autowired
    private IShortVipService vipService;

    @Autowired
    private IShortUserService userService;

    @Autowired
    private IShortOrderService orderService;

    @Autowired
    private IShortAppService shortAppService;

    @Autowired
    private IShortPayTemplateAmtService paytemplateAmountService;

    @Autowired
    private IShortRunlogService runlogService;

    @Autowired
    private IShortCoinRecordService coinRecordService;

    @Value("${dispute.blacklist.user.enabled:true}")
    private boolean blacklistUserEnabled;

    @Autowired
    private IShortVideoChannelCoinService videoChannelCoinService;

    @Autowired
    private IShortVideoService videoService;

    @Autowired
    private IShortUserUnlockVideoService userUnlockVideoService;

    @Resource
    private ShortEmailSendLogMapper shortEmailSendLogMapper;
    @Value("${pay.temp.id}")
    private Long payTempId;
    @Resource
    private ShortUserMapper shortUserMapper;

    @Value("${test-pid.temp.id}")
    private String testPidTempId;

    @Autowired
    private PaymentFactory paymentFactory;

    @Autowired
    private IShortDisputeRecordService shortDisputeRecordService;

    @Autowired
    private IShortLinkVipService shortLinkVipService;

    @Autowired
    private BusinessFunctionApiService businessFunctionApiService;

    @Autowired
    private IShortMovieService shortMovieService;

    @Autowired
    private IShortSemLinkService shortSemLinkService;

    @Autowired
    private IShortUserSubscribeRecordService shortUserSubscribeRecordService;

    @Autowired
    private ShortOrderMapper shortOrderMapper;


    /**
     * 创建支付意图
     */
    @Override
    @Transactional
    public Map<String, Object> createIntent(PayOrderRequest payOrderRequest) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 1.验证请求参数
            Boolean isPass = validateParameters(payOrderRequest, result);
            if (!isPass) {
                return result;
            }
            // 2.获取支付渠道
            PaymentStrategy channelStrategy = paymentFactory.getStrategy(payOrderRequest.getPayChannel());
            // 3.创建支付意图
            Boolean isSuccess = channelStrategy.createOrder(payOrderRequest, result);
            if (!isSuccess) {
                return result;
            }
        } catch (Exception e) {
            log.error("创建支付意图失败: {}", e.getMessage(), e);
            result.put("code", 405);
            result.put("msg", "创建支付意图失败: " + e.getMessage());
        }
        // 4.返回结果
        return result;
    }

    private Boolean validateParameters(PayOrderRequest payOrderRequest, Map<String, Object> result) {
        // 提取请求参数
        String userId = payOrderRequest.getUserId();
        String amountId = payOrderRequest.getAmountId();
        String appId = payOrderRequest.getAppId();
        String token = payOrderRequest.getToken();

        // 1. userId amountId 不能为null
        if (StringUtils.isEmpty(userId) || StringUtils.isEmpty(amountId)) {
            result.put("code", 405);
            result.put("msg", "数据错误");
            return false;
        }

        // 用户真实id
        Long userIdLong = null;
        if (StringUtils.isNotEmpty(token)) {
            // 有token，通过token从数据库获取用户ID
            ShortUser queryUser = new ShortUser();
            queryUser.setToken(token);
            List<ShortUser> users = userService.selectShortUserList(queryUser);
            if (users != null && !users.isEmpty()) {
                userIdLong = users.get(0).getId();
                userId = String.valueOf(userIdLong);
            }
        } else {
            // 没有token，从userId去掉前4位
            if (userId.length() > 4) {
                userId = userId.substring(4);
                userIdLong = Long.parseLong(userId);
            }
        }

        if (userIdLong == null) {
            result.put("code", 405);
            result.put("msg", "无效的用户ID");
            return false;
        }
        // 获取当前用户
        ShortUser user = userService.selectShortUserById(Long.parseLong(userId));
        if (user == null) {
            result.put("code", 405);
            result.put("msg", "用户不存在");
            return false;
        }
        payOrderRequest.setShortUser(user);
        //从用户获取userId
        Long linkId = user.getLinkidId();
        //走深链 判断是否使用自定义付费计划
        List<ShortLinkVip> shortLinkVipList = shortLinkVipService.selectByLinkId(linkId);
        //自定义付费计划有配置 且 vipId 取得是自定义付费计划ID
        //增加过滤,  自定义付费计划有影响，单做的过滤
        if(CollectionUtil.isNotEmpty(shortLinkVipList) && payOrderRequest.getIsLink()){
            //如果自定义付费计划ID 不在自定义付费计划列表中 说明后台自定义付费计划有变动 抛异常让用户更新
            if(!shortLinkVipList.stream().map(ShortLinkVip::getId).collect(Collectors.toList()).contains(Long.valueOf(amountId))){
                result.put("code", 405);
                result.put("msg", "系统异常，请重新登录");
                return false;
            }
            List<ShortLinkVip> collect = shortLinkVipList.stream().filter(shortLinkVip -> shortLinkVip.getId().equals(Long.valueOf(amountId))).collect(Collectors.toList());
            ShortLinkVip next = collect.iterator().next();
            ShortVip shortVip = new ShortVip();
            BeanUtils.copyProperties(next, shortVip);
            payOrderRequest.setShortVip(shortVip);
            payOrderRequest.setAmount(next.getPrice());
            payOrderRequest.setCoin(next.getCoin());
            payOrderRequest.setVipType("1");
        }else {
            // 根据amountId获取充值产品信息 - 获取充值类型和金币ID
            ShortVip vip = vipService.selectShortVipById(Long.parseLong(amountId));
            if (vip == null) {
                result.put("code", 405);
                result.put("msg", "无效的金额ID");
                return false;
            }
            payOrderRequest.setShortVip(vip);

//        String upOrSubscribe = vip.getPayType();
            Long coinId = vip.getPayTemplateAmountId();

            // 获取价格和金币值
            // 从PaytemplateAmount表获取价格和金币值
            ShortPayTemplateAmt payTemplateAmt = paytemplateAmountService.selectShortPayTemplateAmtById(coinId);
            if (payTemplateAmt == null) {
                result.put("code", 405);
                result.put("msg", "无效的金额模板ID");
                return false;
            }
            payOrderRequest.setAmount(payTemplateAmt.getPrice());
            payOrderRequest.setCoin(payTemplateAmt.getCoin());
            payOrderRequest.setVipType("0");
        }
        // 获取支付渠道
        String payChannel = shortAppService.getPayChannelByAppId(appId);
        payOrderRequest.setPayChannel(payChannel);
        log.info("【校验完成】构建完整请求参数: {}", JSONObject.toJSONString(payOrderRequest));
        return true;
    }

    @Override
    public Result handleAliPaymentNotify(Map<String, Object> notifyData) throws Exception {
        //Antom 为识别支付而分配的支付 ID
        String paymentId = (String) notifyData.get("paymentId");
        // 支付请求ID
        String paymentRequestId = (String) notifyData.get("paymentRequestId");
        //根据paymentId查询订单
        ShortOrder order = orderService.selectShortOrderById(Long.valueOf(paymentRequestId));
        if (null == order) {
            throw new RuntimeException("订单不存在");
        }
        // 如果订单已经处理过（状态已经是SUCCEEDED且支付时间不为空），直接返回成功
        if (OrderStatus.SUCCEEDED.getCode().equals(order.getStatus()) && order.getPayTime() != null) {
            log.info("订单已处理过，跳过重复处理, 订单ID: {}, 支付意图ID: {}", order.getId(), paymentId);
            return Result.builder().resultCode("SUCCESS").resultMessage("成功.").resultStatus(ResultStatusType.S).build();
        }
        //设置支付渠道
        order.setPayChannel(PayChannel.ANTOM.getCode());
        //新增执行日志
        RunLogUtil.saveLog(false, "蚂蚁支付支付回调信息", JSON.toJSONString(notifyData));
        //处理订单和深链信息一致问题
        synUserAndOrderLinkInfo(order);
        //支付结果
        Object result = notifyData.get("result");
        Map<String, Object> resultMap = JSONUtil.parseObj(result);
        String resultStatus = (String) resultMap.get("resultStatus");
        if ("S".equals(resultStatus)) {
            //构造统一对象
            PaymentNotifyDTO paymentNotifyDTO = new PaymentNotifyDTO();
            paymentNotifyDTO.setPaymentId(paymentId);
            String paymentMethodType = (String) notifyData.get("paymentMethodType");
            paymentNotifyDTO.setPaymentMethodType(paymentMethodType);
            String requestId = (String) notifyData.get("paymentRequestId");
            paymentNotifyDTO.setMerchantOrderId(requestId);
            paymentNotifyDTO.setPaymentRequestId(requestId);
            paymentNotifyDTO.setOriginData(JSON.toJSONString(notifyData));
            //充值
            if ("充值".equals(order.getPayType())) {
                log.info("开始执行充值订单业务逻辑");
                handleOrderRecharge(order, paymentNotifyDTO);
            } else {
                //订阅
                log.info("开始执行订阅订单业务逻辑");
                handleOrderSubscribe(order, paymentNotifyDTO);
            }
            return Result.builder().resultCode("SUCCESS").resultMessage("成功.").resultStatus(ResultStatusType.S).build();
        } else {
            log.info("支付失败, 订单ID: {}, 支付意图ID: {}", order.getId(), paymentId);
            //支付失败
            return Result.builder().resultCode("FAIL").resultMessage("失败.").resultStatus(ResultStatusType.F).build();
        }
    }

    @Override
    public AjaxResult handleAcceptDispute(Long orderId) {
        log.info("手动处理支付争议退款, 订单ID: {}", orderId);

        // 根据orderId查询订单
        ShortOrder order = orderService.selectShortOrderById(orderId);
        Assert.notNull(order, "订单不存在");

        // 获取争议ID
        ShortDisputeRecord queryDisputeRecord = new ShortDisputeRecord();
        queryDisputeRecord.setOrderId(order.getId());
        List<ShortDisputeRecord> disputeRecords = shortDisputeRecordService.selectShortDisputeRecordList(queryDisputeRecord);
        String disputeId = order.getDisputeId();
        if(CollectionUtil.isNotEmpty(disputeRecords)){
            ShortDisputeRecord disputeRecord = disputeRecords.get(0);
            disputeId = disputeRecord.getDisputeId();
        }

        if (StringUtils.isEmpty(disputeId)) {
            return AjaxResult.error("争议ID不存在，无法处理争议");
        }

        // 根据支付渠道区分处理逻辑
        String payChannel = order.getPayChannel();
        PaymentStrategy strategy = paymentFactory.getStrategy(payChannel);
        return strategy.disputeOrder(order, disputeId);
    }

    /**
     * 获取订单状态
     */
    @Override
    public Map<String, Object> getOrderStatus(String merchantOrderId) {
        log.info("获取订单状态, 商户订单ID: {}", merchantOrderId);

        Map<String, Object> result = new HashMap<>();

        try {
            // 1. 验证参数
            if (StringUtils.isEmpty(merchantOrderId)) {
                result.put("code", 400);
                result.put("msg", "Invalid JSON data");
                return result;
            }

            // 2. 查询订单状态
            ShortOrder queryOrder = new ShortOrder();
            queryOrder.setMerchantOrderId(merchantOrderId);
            List<ShortOrder> orders = orderService.selectShortOrderList(queryOrder);

            if (orders != null && !orders.isEmpty()) {
                ShortOrder order = orders.get(0);
                Map<String, Object> data = new HashMap<>();
                data.put("status", order.getStatus());

                result.put("code", 200);
                result.put("data", data);
            } else {
                result.put("code", 404);
                result.put("msg", "Order not found");
            }

        } catch (Exception e) {
            log.error("获取订单状态失败: {}", e.getMessage(), e);
            result.put("code", 500);
            result.put("msg", "Internal server error");
        }

        return result;
    }

    //处理订单的充值逻辑
    public void handleOrderRecharge(ShortOrder order, PaymentNotifyDTO notifyData) {
        //Antom 为识别支付而分配的支付 ID
        order.setRequestId(notifyData.getPaymentRequestId());
        order.setMerchantOrderId(notifyData.getMerchantOrderId());
        order.setPaymentIntentId(notifyData.getPaymentId());
        order.setPaymentMethod(notifyData.getPaymentMethodType().toLowerCase());
        order.setStatus(OrderStatus.SUCCEEDED.getCode());
        order.setRawData(notifyData.getOriginData());
        order.setUpdateTime(DateUtils.getNowDate());
        // 设置支付时间
        order.setPayTime(new Date());
        orderService.updateShortOrder(order);

        // 通过订单获取用户信息及金币数量
        Long userId = order.getUserId();
        Long vipId = order.getVipId();

        // 获取VIP和金币信息，直接从VIP获取
        ShortVip vipInfo = vipService.selectShortVipById(vipId);
        Long payId = vipInfo.getPayTemplateAmountId();
        ShortPayTemplateAmt payTemplateAmt = paytemplateAmountService.selectShortPayTemplateAmtById(payId);
        Long amount = payTemplateAmt.getCoin();

        Long vipCoin = vipInfo.getSendCoin();
        if (vipCoin == 0) {
            // 不变
        } else {
            amount += vipCoin;
        }

        // 给用户充值金币
        ShortUser user = userService.selectShortUserById(userId);
        if (user != null && amount > 0) {
            Long currentCoin = user.getCoin() != null ? user.getCoin() : 0L;
            user.setCoin(currentCoin + amount);
            userService.updateShortUser(user);

            // 添加金币记录
            logCoinRecord(userId, order.getAppId(), amount, "1");

            // 更新用户VIP信息
            user.setVipId(vipId);
            userService.updateShortUser(user);

            // 发送Facebook购买事件，传入订单ID
            sendFacebookPurchaseEvent(userId.toString(), order.getId());
        }
    }

    //处理订阅逻辑
    public void handleOrderSubscribe(ShortOrder order, PaymentNotifyDTO notifyData) throws Exception {
        // 订阅逻辑  PaymentMethod
        order.setRequestId(notifyData.getPaymentRequestId());
        order.setMerchantOrderId(notifyData.getMerchantOrderId());
        order.setPaymentIntentId(notifyData.getPaymentId());
        order.setPaymentMethod(notifyData.getPaymentMethodType().toLowerCase());
        order.setStatus(OrderStatus.SUCCEEDED.getCode());
        if("USEEPAY".equals(order.getPayChannel())){// useepay 特殊处理 保存续订id
            JSONObject jsonObj = JSONObject.parse(notifyData.getOriginData());
            String notifyName = jsonObj.getString("name");
            if("invoice.paid".equals(notifyName)){
                // useepay 续订关键参数
                JSONObject data = jsonObj.getJSONObject("data");
                order.setOrdersn(data.getString("subscription_id"));
            }
        }
        order.setRawData(JSON.toJSONString(notifyData.getOriginData()));
        order.setUpdateTime(DateUtils.getNowDate());
        // 设置支付时间
        order.setPayTime(new Date());
        orderService.updateShortOrder(order);

        // 通过订单获取用户信息修改用户订阅状态
        Long userId = order.getUserId();
        Long vipId = order.getVipId();
        // 获取订阅类型
        String subscriptionType = "";
        //判断是否自定义付费计划
        if(order.getVipType().equals("1")){
            subscriptionType = order.getSubscriptionType();
        }else {
            ShortVip vip = vipService.selectShortVipById(vipId);
            subscriptionType = vip.getSubscriptionType();
        }

        // 动态计算天数
        int daysToAdd = Integer.parseInt(subscriptionType);

        // 获取用户信息
        ShortUser user = userService.selectShortUserById(userId);

        if (user != null) {
            // 判断用户是否已经是订阅用户，计算新的过期时间
            Date newExpireTime = null;
            if ("1".equals(user.getIsSubscriber()) && user.getExpireTime() != null &&
                    user.getExpireTime().after(DateUtils.getNowDate())) {
                // 在现有过期时间基础上添加天数
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(user.getExpireTime());
                calendar.add(Calendar.DAY_OF_YEAR, daysToAdd);
                newExpireTime = calendar.getTime();
            } else {
                // 从当前时间开始计算
                Calendar calendar = Calendar.getInstance();
                calendar.add(Calendar.DAY_OF_YEAR, daysToAdd);
                newExpireTime = calendar.getTime();
            }

            // 更新用户订阅状态
            user.setExpireTime(newExpireTime);
            user.setIsSubscriber("1");

            // 更新支付同意信息 蚂蚁支付暂时未知 todo
          /*  if (StringUtils.isNotEmpty(user.getPayInfo())) {
                try {
                    JSONArray payInfoArray = JSON.parseArray(user.getPayInfo());
                    if (payInfoArray != null && !payInfoArray.isEmpty()) {
                        JSONObject firstPayInfo = payInfoArray.getJSONObject(0);
                        firstPayInfo.put("payment_consent_id", paymentConsentId);
                        user.setPayInfo(payInfoArray.toJSONString());
                    }
                } catch (Exception e) {
                    log.error("更新用户支付同意信息失败: {}", e.getMessage(), e);
                }
            }*/
            user.setPayByEmailFlag(Boolean.TRUE);
            // 更新用户VIP信息
            user.setVipId(vipId);
            userService.updateShortUser(user);


            if("USEEPAY".equals(order.getPayChannel())){// useepay 特殊处理 保存续订id
                JSONObject jsonObj = JSONObject.parse(notifyData.getOriginData());
                String notifyName = jsonObj.getString("name");
                if("invoice.paid".equals(notifyName)){
                    // useepay 续订关键参数
                    JSONObject data = jsonObj.getJSONObject("data");
                    order.setOrdersn(data.getString("subscription_id"));
                }
            }

            //useepay订阅成功发送通知邮件
//            if(order.getPayChannel().equals("USEEPAY") && order.getPayType().equals("订阅") && order.getStatus().equals(OrderStatus.SUCCEEDED.getCode())){
//                JSONObject jsonObj = JSONObject.parse(notifyData.getOriginData());
//                String notifyName = jsonObj.getString("name");
//                if("invoice.paid".equals(notifyName)){
//                    InitUserDTO shortBrevo = new InitUserDTO();
//                    shortBrevo.setReceiveEmail(user.getEmail());
//                    shortBrevo.setAppId(user.getAppId());
//                    if(null == shortBrevo.getLanguageCode())
//                        shortBrevo.setLanguageCode("en-US");
//                    if(null != user.getLinkidId()){
//                        ShortSemLink shortSemLink = shortSemLinkService.selectShortSemLinkById(user.getLinkidId());
//                        if(null != shortSemLink && null != shortSemLink.getLanguage())
//                            shortBrevo.setLanguageCode(shortSemLink.getLanguage());
//
//                    }
//                    //先固定英语
//                    shortBrevo.setLanguageCode("en-US");
//                    shortBrevo.setUserId(String.valueOf(user.getId()));
//                    shortBrevo.setUniqueId(user.getUniqueId());
//                    shortBrevo.setExpireTime(user.getExpireTime());
//                    ShortMovie shortMovie = shortMovieService.getMovie1(user.getAppId());
//                    if(order.getPayType().equals("订阅续费")){
//                        businessFunctionApiService.sendSubEmailForward1(shortBrevo, shortMovie, "brevo",order);
//                    }else{
//                        businessFunctionApiService.sendSubEmailForward(shortBrevo, shortMovie, "brevo",order);
//                    }
//                }
//            }



            // 订单表添加字段判断是否是邮件营销活动订单,是的话不调回传事件
            if (order.getIsEmailMarketingOrder() == null || order.getIsEmailMarketingOrder() == 0) {
                // 发送Facebook购买事件，传入订单ID
                log.info("常规订单，执行像素回传: 用户ID={}, 订单ID={}", userId, order.getId());
                sendFacebookPurchaseEvent(userId.toString(), order.getId());
            } else {
                log.info("邮件营销活动订单，不调回传事件: 用户ID={}, 订单ID={}", userId, order.getId());

                // 更新订单pixelStatus为4(跳过回传)，表示已处理
                if (order.getPixelStatus() == null || order.getPixelStatus() == 0) {
                    order.setPixelStatus(4); // 设置状态为"跳过回传(非首单/邮件营销订单)"
                    orderService.updateShortOrder(order);

                    // 验证状态是否成功更新
                    ShortOrder updatedOrder = orderService.selectShortOrderById(order.getId());
                    if (updatedOrder != null && updatedOrder.getPixelStatus() == 4) {
                        log.info("邮件营销订单状态已更新为已处理(跳过回传): 订单ID={}, 状态=4", order.getId());
                    } else {
                        log.warn("邮件营销订单状态更新失败: 订单ID={}, 期望状态=4, 实际状态={}",
                                order.getId(),
                                updatedOrder != null ? updatedOrder.getPixelStatus() : "unknown");
                    }
                } else if (order.getPixelStatus() != 4) {
                    log.info("邮件营销订单状态已为 {}, 无需更新: 订单ID={}", order.getPixelStatus(), order.getId());
                }
            }

            // 无论什么来源的订单，都更新邮件发送记录
            updateEmailSendRecord(user.getId());
            // 记录订阅续订数据
            try {
                LocalDateTime now = LocalDateTime.now(); // 直接获取当前时间
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                String formattedDate = now.format(formatter);

                // 获取Mapper实例
                ShortRenewSubscribeDataMapper renewSubscribeDataMapper =
                        SpringUtils.getBean(ShortRenewSubscribeDataMapper.class);

                // 查询是否存在记录
                long count = renewSubscribeDataMapper.countByUserIdAndLogDate(userId, formattedDate);
                boolean exists = count > 0;

                if (!exists) {
                    // 只有当天不存在该用户记录时才插入
                    ShortRenewSubscribeData renewData = new ShortRenewSubscribeData();
                    renewData.setCreateTime(DateUtils.getNowDate());
                    renewData.setLogDate(formattedDate);
                    renewData.setUserId(userId);
                    // todo PaymentMethod
                    renewData.setPayMethod("");
                    renewData.setPhoneVersion(user.getPhoneVersion() != null ? user.getPhoneVersion() : "Unknown");
                    renewData.setRenewAmount(order.getAmount());
                    renewData.setOrderStatus(OrderStatus.SUCCEEDED.getCode());
                    renewData.setRenewType(subscriptionType);
                    renewData.setRenewCount(0); // 设置续订次数为0
                    renewData.setPayErrorCount(0); // 设置支付失败次数为0

                    // 插入订阅数据记录
                    renewSubscribeDataMapper.insert(renewData);
                    log.info("记录订阅数据成功: userId={}, renewType={}, logDate={}", userId, subscriptionType, formattedDate);
                } else {
                    log.info("用户今日已有订阅记录，跳过插入: userId={}, logDate={}", userId, formattedDate);
                }
            } catch (Exception e) {
                log.error("记录订阅数据失败: {}", e.getMessage(), e);
            }
        }
    }

    @Override
    public Map<String, Object> getCoinTemplate(Long payTemplateId) {
        HashMap<String, Object> result = new HashMap<>();
        // 根据充值模板ID查询VIP列表
        ShortVip queryVip = new ShortVip();
        queryVip.setPayTemplateId(payTemplateId);
        List<ShortVip> vipList = vipService.selectShortVipList2(queryVip);
        if (vipList != null && !vipList.isEmpty()) {
            // 构建返回数据
            List<Map<String, Object>> dataList = new ArrayList<>();

            for (ShortVip vip : vipList) {
                Map<String, Object> vipData = new HashMap<>();
                vipData.put("id", vip.getId());
                vipData.put("name", vip.getName());

                // 获取金币信息
                Long coin = 0L;
                BigDecimal price = BigDecimal.ZERO;
                if (vip.getPayTemplateAmountId() != null) {
                    ShortPayTemplateAmt payTemplateAmt = paytemplateAmountService.selectShortPayTemplateAmtById(vip.getPayTemplateAmountId());
                    if (payTemplateAmt != null) {
                        if (payTemplateAmt.getCoin() != null) {
                            coin = payTemplateAmt.getCoin();
                        }
                        if (payTemplateAmt.getPrice() != null) {
                            price = payTemplateAmt.getPrice();
                        }
                    }
                }

                vipData.put("coin", coin);
                vipData.put("send_coin", vip.getSendCoin() != null ? vip.getSendCoin() : 0);
                vipData.put("pay_type", vip.getPayType());

                vipData.put("subscription_type", vip.getSubscriptionType());

                vipData.put("price", price);

                dataList.add(vipData);
            }

            result.put("code", 200);
            result.put("msg", "success");
            result.put("data", dataList);
        } else {
            result.put("code", 400);
            result.put("msg", "获取vip类型数据失败");
        }
        return result;
    }

    /**
     * 同步用户和订单的Link信息
     *
     * @param order
     */
    private void synUserAndOrderLinkInfo(ShortOrder order) {
        if (!"订阅续费".equals(order.getPayType())) {
            log.info("非订阅续费订单判断用户和订单深链信息是否一致");
            ShortUser userData = userService.selectShortUserById(order.getUserId());
            //判断当前用户最新的 appId linkId linkTime  adId other pixel_id 与 order表是否一致
            if (null != userData.getLinkidId()
                    && null != userData.getLinkTime()
                    && null != userData.getOther() && StringUtils.isNotEmpty(userData.getOther())
                    && null != userData.getPixelId()
            ) {
                log.info("当前用户是从深链进入注册");
                if (userData.getAppId().equals(order.getAppId()) &&
                        userData.getLinkidId().equals(order.getLinkId())
                        && userData.getLinkTime().equals(order.getLinkTime())
                        && userData.getOther().equals(order.getOther())
                        && userData.getPixelId().equals(order.getPixelId())) {
                    log.info("当前订单广告链接信息与用户信息一致");
                } else {
                    //更新订单数据保持与用户信息一致
                    log.info("当前订单广告链接信息与用户信息不一致,执行更新订单操作");
                    order.setAppId(userData.getAppId());
                    order.setLinkId(userData.getLinkidId());
                    order.setLinkTime(userData.getLinkTime());
                    order.setOther(userData.getOther());
                    order.setPixelId(userData.getPixelId());
                    orderService.updateShortOrder(order);
                }
            } else {
                log.info("当前用户是从APP直接进入注册");
            }
        }
    }


    /**
     * 处理支付回调
     * ！！！ 预生产的支付回调走的是生产环境的接口回调
     */
    @Override
    public Map<String, Object> handlePaymentCallback(Map<String, Object> callbackData) {
        log.info("处理支付回调, 回调数据: {}", callbackData);
        Map<String, Object> result = new HashMap<>();

        try {
            // 1. 验证回调数据
            if (callbackData == null || !callbackData.containsKey("data")) {
                result.put("code", 400);
                result.put("msg", "Invalid data");
                return result;
            }

            Map<String, Object> dataObj = (Map<String, Object>) callbackData.get("data");
            if (dataObj == null || !dataObj.containsKey("object")) {
                result.put("code", 400);
                result.put("msg", "Invalid data");
                return result;
            }

            // 记录日志
            ShortRunlog runlog = new ShortRunlog();
            runlog.setType("支付回调信息");
            runlog.setState("1");
            runlog.setContent(JSON.toJSONString(callbackData));
            runlogService.insertShortRunlog(runlog);

            // 提取payment_intent_id、payment_method和status
            String paymentIntentId = null;
            String paymentMethod = null;
            String status = null;
            String paymentConsentId = "";

            // 解析数据结构
            if (dataObj.containsKey("object") && dataObj.get("object") instanceof Map) {
                Map<String, Object> objectData = (Map<String, Object>) dataObj.get("object");
                paymentIntentId = (String) objectData.get("id");
                status = (String) objectData.get("status");
                paymentConsentId = (String) objectData.getOrDefault("payment_consent_id", "");

                if (objectData.containsKey("latest_payment_attempt") &&
                        objectData.get("latest_payment_attempt") instanceof Map) {
                    Map<String, Object> attemptData = (Map<String, Object>) objectData.get("latest_payment_attempt");
                    if (attemptData.containsKey("payment_method") &&
                            attemptData.get("payment_method") instanceof Map) {
                        Map<String, Object> methodData = (Map<String, Object>) attemptData.get("payment_method");
                        paymentMethod = (String) methodData.get("type");
                    }
                }
            }

            if (paymentIntentId == null || paymentMethod == null || status == null) {
                result.put("code", 400);
                result.put("msg", "缺少必要字段");
                return result;
            }

            // 2. 查询订单
            ShortOrder queryOrder = new ShortOrder();
            queryOrder.setPaymentIntentId(paymentIntentId);
            List<ShortOrder> orders = orderService.selectShortOrderList(queryOrder);

            if (orders == null || orders.isEmpty()) {
                result.put("code", 404);
                result.put("msg", "未找到订单");
                return result;
            }
            ShortOrder order = orders.get(0);
            // 如果订单已经处理过（状态已经是SUCCEEDED且支付时间不为空），直接返回成功
            if ("SUCCEEDED".equals(order.getStatus()) && order.getPayTime() != null) {
                log.info("订单已处理过，跳过重复处理, 订单ID: {}, 支付意图ID: {}", order.getId(), paymentIntentId);
                result.put("code", 200);
                result.put("msg", "订单已处理");
                return result;
            }
            //设置支付渠道
            order.setPayChannel(PayChannel.AIRWALLEX.getCode());
            synUserAndOrderLinkInfo(order);
            // 3. 处理支付成功的情况
            if ("SUCCEEDED".equals(status)) {
                // 区分充值和订阅类型
                if ("充值".equals(order.getPayType())) {
                    // 充值逻辑
                    order.setPaymentMethod(paymentMethod);
                    order.setStatus(status);
                    order.setRawData(JSON.toJSONString(callbackData));
                    // 提取并设置银行卡号
                    String cardNumber = extractAndMaskCardNumber(callbackData);
                    if (cardNumber != null) {
                        order.setCardNumber(cardNumber);
                        log.info("充值订单设置银行卡号: 订单ID={}, 卡号={}", order.getId(), cardNumber);
                    } else {
                        log.warn("充值订单未能提取银行卡号: 订单ID={}", order.getId());
                    }
                    order.setUpdateTime(DateUtils.getNowDate());
                    // 设置支付时间
                    order.setPayTime(DateUtils.getNowDate());
                    orderService.updateShortOrder(order);

                    // 通过订单获取用户信息及金币数量
                    Long userId = order.getUserId();
                    Long vipId = order.getVipId();


                    ShortVip vipInfo;
                    ShortLinkVip shortLinkVip;
                    Long payId;
                    Long vipCoin;
                    if("0".equals(order.getVipType())){
                        // 获取VIP和金币信息，直接从VIP获取
                        vipInfo = vipService.selectShortVipById(vipId);
                        payId = vipInfo.getPayTemplateAmountId();
                        vipCoin = vipInfo.getSendCoin();
                    }else{
                        shortLinkVip = shortLinkVipService.selectShortLinkVipById(vipId);
                        payId = shortLinkVip.getPayTemplateAmountId();
                        vipCoin = shortLinkVip.getSendCoin();
                    }

                    ShortPayTemplateAmt payTemplateAmt = paytemplateAmountService.selectShortPayTemplateAmtById(payId);
                    Long amount = payTemplateAmt.getCoin();

                    if (vipCoin == 0) {
                        // 不变
                    } else {
                        amount += vipCoin;
                    }

                    // 给用户充值金币
                    ShortUser user = userService.selectShortUserById(userId);
                    if (user != null && amount > 0) {
                        Long currentCoin = user.getCoin() != null ? user.getCoin() : 0L;
                        user.setCoin(currentCoin + amount);
                        if(Objects.isNull(user.getEmailBindDate())){
                            user.setEmailBindDate(LocalDate.now());
                        }
                        if(Objects.isNull(user.getEmailBindLink())){
                            user.setEmailBindLink(order.getLinkId());
                        }
                        user.setValidStatus(1);
                        user.setPayByEmailFlag(Boolean.TRUE);
                        userService.updateShortUser(user);

                        // 添加金币记录
                        logCoinRecord(userId, order.getAppId(), amount, "1");

                        // 更新用户VIP信息
                        user.setVipId(vipId);
                        userService.updateShortUser(user);

                        // 发送Facebook购买事件，传入订单ID
                        sendFacebookPurchaseEvent(userId.toString(), order.getId());
                    }

                    result.put("code", 200);
                    result.put("msg", "订单更新成功");
                    return result;
                } else {
                    // 订阅逻辑
                    order.setPaymentMethod(paymentMethod);
                    order.setStatus(status);
                    order.setRawData(JSON.toJSONString(callbackData));
                    order.setUpdateTime(DateUtils.getNowDate());
                    // 设置支付时间
                    order.setPayTime(DateUtils.getNowDate());
                    order.setMarkType(1);

//                    orderService.updateShortOrder(order);

                    // 通过订单获取用户信息修改用户订阅状态
                    Long userId = order.getUserId();
                    Long vipId = order.getVipId();

                    // 获取订阅类型
                    String subscriptionType = "";
                    //判断是否自定义付费计划
                    if(order.getVipType().equals("1")){
                        subscriptionType = order.getSubscriptionType();
                    }else {
                        if(StringUtils.isEmpty(order.getSubscriptionType())){
                            ShortVip vip = vipService.selectShortVipById(vipId);
                            subscriptionType = vip.getSubscriptionType();
                        }else{
                            subscriptionType = order.getSubscriptionType();
                        }
                    }

                    // 动态计算天数
                    int daysToAdd = Integer.parseInt(subscriptionType);

                    if(daysToAdd == 30 && "订阅".equals(order.getPayType())) {
                        Long orderId = shortOrderMapper.getByUserIdAndPayType(order.getUserId());
                        if(null != orderId)
                            shortOrderMapper.updateMarkTypeByUserId(orderId, 2);
                    }
                    if(daysToAdd == 365 && "订阅".equals(order.getPayType())) {
                        Long orderId = shortOrderMapper.getByUserIdAndPayType(order.getUserId());
                        if(null != orderId)
                        shortOrderMapper.updateMarkTypeByUserId(orderId, 3);
                    }
//                    orderService.updateShortOrder(order);

                    // 获取用户信息
                    ShortUser user = userService.selectShortUserById(userId);

                    if (user != null) {
                        // 判断用户是否已经是订阅用户，计算新的过期时间
                        Date newExpireTime = null;
                        if (/*"1".equals(user.getIsSubscriber()) &&*/ user.getExpireTime() != null &&
                                user.getExpireTime().after(DateUtils.getNowDate())) {
                            // 在现有过期时间基础上添加天数
                            Calendar calendar = Calendar.getInstance();
                            calendar.setTime(user.getExpireTime());
                            calendar.add(Calendar.DAY_OF_YEAR, daysToAdd);
                            newExpireTime = calendar.getTime();


                        } else {
                            // 从当前时间开始计算
                            Calendar calendar = Calendar.getInstance();
                            calendar.add(Calendar.DAY_OF_YEAR, daysToAdd);
                            newExpireTime = calendar.getTime();
                        }

                        // 更新用户订阅状态
                        user.setExpireTime(newExpireTime);
                        user.setIsSubscriber("1");

                        // 更新支付同意信息
                        if (StringUtils.isNotEmpty(user.getPayInfo())) {
                            try {
                                JSONArray payInfoArray = JSON.parseArray(user.getPayInfo());
                                if (payInfoArray != null && !payInfoArray.isEmpty()) {
                                    JSONObject firstPayInfo = payInfoArray.getJSONObject(0);
                                    firstPayInfo.put("payment_consent_id", paymentConsentId);
                                    user.setPayInfo(payInfoArray.toJSONString());
                                }
                            } catch (Exception e) {
                                log.error("更新用户支付同意信息失败: {}", e.getMessage(), e);
                            }
                        }
                        if(Objects.isNull(user.getEmailBindDate())){
                            user.setEmailBindDate(LocalDate.now());
                        }
                        if(Objects.isNull(user.getEmailBindLink())){
                            user.setEmailBindLink(order.getLinkId());
                        }
                        user.setValidStatus(1);
                        user.setPayByEmailFlag(Boolean.TRUE);
                        // 更新用户VIP信息
                        user.setVipId(vipId);
                        if(user.getUnsub() == 1)
                            user.setUnsub(0);
                        userService.updateShortUser(user);

                        // 确保银行卡号在最终更新时不丢失
                        if (order.getCardNumber() == null) {
                            String finalCardNumber = extractAndMaskCardNumber(callbackData);
                            if (finalCardNumber != null) {
                                order.setCardNumber(finalCardNumber);
                                log.info("最终更新前重新设置银行卡号: 订单ID={}, 卡号={}", order.getId(), finalCardNumber);
                            }
                        }

                        orderService.updateShortOrder(order);
                        //发送邮件

                        InitUserDTO shortBrevo = new InitUserDTO();
                        shortBrevo.setReceiveEmail(user.getEmail());
                        shortBrevo.setAppId(user.getAppId());
                        if(null == shortBrevo.getLanguageCode())
                            shortBrevo.setLanguageCode("en-US");
                        if(null != user.getLinkidId()){
                            ShortSemLink shortSemLink = shortSemLinkService.selectShortSemLinkById(user.getLinkidId());
                            if(null != shortSemLink && null != shortSemLink.getLanguage())
                                shortBrevo.setLanguageCode(shortSemLink.getLanguage());

                        }

                        if(StringUtils.isEmpty(user.getSource())){
                            //先固定英语
                            shortBrevo.setLanguageCode("en-US");
                            shortBrevo.setUserId(String.valueOf(user.getId()));
                            shortBrevo.setUniqueId(user.getUniqueId());
                            shortBrevo.setExpireTime(user.getExpireTime());
                            ShortMovie shortMovie = shortMovieService.getMovie1(user.getAppId());
                            if(order.getPayType().equals("订阅续费")){
                                businessFunctionApiService.sendSubEmailForward1(shortBrevo, shortMovie, "brevo",order);
                            }else{
                                businessFunctionApiService.sendSubEmailForward(shortBrevo, shortMovie, "brevo",order);
                            }
                        }

                        // 订单表添加字段判断是否是邮件营销活动订单,是的话不调回传事件
                        if (order.getIsEmailMarketingOrder() == null || order.getIsEmailMarketingOrder() == 0) {
                            // 发送Facebook购买事件，传入订单ID
                            log.info("常规订单，执行像素回传: 用户ID={}, 订单ID={}", userId, order.getId());
                            sendFacebookPurchaseEvent(userId.toString(), order.getId());
                        } else {
                            log.info("邮件营销活动订单，不调回传事件: 用户ID={}, 订单ID={}", userId, order.getId());

                            // 更新订单pixelStatus为4(跳过回传)，表示已处理
                            if (order.getPixelStatus() == null || order.getPixelStatus() == 0) {
                                order.setPixelStatus(4); // 设置状态为"跳过回传(非首单/邮件营销订单)"

                                // 确保银行卡号在邮件营销订单更新时不丢失
                                if (order.getCardNumber() == null) {
                                    String emailCardNumber = extractAndMaskCardNumber(callbackData);
                                    if (emailCardNumber != null) {
                                        order.setCardNumber(emailCardNumber);
                                        log.info("邮件营销订单更新前重新设置银行卡号: 订单ID={}, 卡号={}", order.getId(), emailCardNumber);
                                    }
                                }

                                orderService.updateShortOrder(order);

                                // 验证状态是否成功更新
                                ShortOrder updatedOrder = orderService.selectShortOrderById(order.getId());
                                if (updatedOrder != null && updatedOrder.getPixelStatus() == 4) {
                                    log.info("邮件营销订单状态已更新为已处理(跳过回传): 订单ID={}, 状态=4", order.getId());
                                } else {
                                    log.warn("邮件营销订单状态更新失败: 订单ID={}, 期望状态=4, 实际状态={}",
                                            order.getId(),
                                            updatedOrder != null ? updatedOrder.getPixelStatus() : "unknown");
                                }
                            } else if (order.getPixelStatus() != 4) {
                                log.info("邮件营销订单状态已为 {}, 无需更新: 订单ID={}", order.getPixelStatus(), order.getId());
                            }
                        }

                        // 无论什么来源的订单，都更新邮件发送记录
                        updateEmailSendRecord(user.getId());
                        // 记录订阅续订数据
                        try {
                            LocalDateTime now = LocalDateTime.now(); // 直接获取当前时间
                            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                            String formattedDate = now.format(formatter);

                            // 获取Mapper实例
                            ShortRenewSubscribeDataMapper renewSubscribeDataMapper =
                                    SpringUtils.getBean(ShortRenewSubscribeDataMapper.class);

                            // 查询是否存在记录
                            long count = renewSubscribeDataMapper.countByUserIdAndLogDate(userId, formattedDate);
                            boolean exists = count > 0;

                            if (!exists) {
                                // 只有当天不存在该用户记录时才插入
                                ShortRenewSubscribeData renewData = new ShortRenewSubscribeData();
                                renewData.setCreateTime(DateUtils.getNowDate());
                                renewData.setLogDate(formattedDate);
                                renewData.setUserId(userId);
                                renewData.setPayMethod(paymentMethod);
                                renewData.setPhoneVersion(user.getPhoneVersion() != null ? user.getPhoneVersion() : "Unknown");
                                renewData.setRenewAmount(order.getAmount());
                                renewData.setOrderStatus(status);
                                renewData.setRenewType(subscriptionType);
                                renewData.setRenewCount(0); // 设置续订次数为0
                                renewData.setPayErrorCount(0); // 设置支付失败次数为0

                                // 插入订阅数据记录
                                renewSubscribeDataMapper.insert(renewData);
                                log.info("记录订阅数据成功: userId={}, renewType={}, logDate={}", userId, subscriptionType, formattedDate);
                            } else {
                                log.info("用户今日已有订阅记录，跳过插入: userId={}, logDate={}", userId, formattedDate);
                            }
                        } catch (Exception e) {
                            log.error("记录订阅数据失败: {}", e.getMessage(), e);
                        }
                    }

                    result.put("code", 200);
                    result.put("msg", "订单更新成功");
                    return result;
                }
            } else {
                result.put("code", 400);
                result.put("msg", "无效状态");
                return result;
            }

        } catch (Exception e) {
            log.error("处理支付回调失败: {}", e.getMessage(), e);
            result.put("code", 500);
            result.put("msg", "Internal server error");
        }

        return result;
    }

    private void updateEmailSendRecord(Long userId) {
        ShortEmailSendLog shortEmailSendLog = new ShortEmailSendLog();
        shortEmailSendLog.setUserId(userId);
        shortEmailSendLog.setStatus("1");
        shortEmailSendLogMapper.updateStatusByUserId(shortEmailSendLog);
    }

    /**
     * 记录金币变动
     *
     * @param userId 用户ID
     * @param appId  应用ID
     * @param amount 金币数量
     * @param type   类型：1充值 2消费
     */
    private void logCoinRecord(Long userId, Long appId, Long amount, String type) {
        try {
            // 获取用户信息
            ShortUser user = userService.selectShortUserById(userId);
            if (user == null) {
                log.error("记录金币变动失败: 用户不存在, userId={}", userId);
                return;
            }

            // 创建金币记录实体
            ShortCoinRecord coinRecord = new ShortCoinRecord();
            coinRecord.setUserId(userId);
            coinRecord.setAppId(appId);
            coinRecord.setCoin(amount);
            coinRecord.setConstype(type); // 1:充值, 2:消费
            coinRecord.setStatus("0"); // 0:正常

            // 计算并设置余额
            Long balance = user.getCoin() != null ? user.getCoin() : 0L;
            coinRecord.setBalance(balance);

            // 插入金币记录
            coinRecordService.insertShortCoinRecord(coinRecord);

            log.info("记录金币变动成功: 用户ID={}, 应用ID={}, 金额={}, 类型={}", userId, appId, amount, type);
        } catch (Exception e) {
            log.error("记录金币变动失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 发送Facebook购买事件
     *
     * @param userId  用户ID
     * @param orderId 订单ID
     */
    private void sendFacebookPurchaseEvent(String userId, Long orderId) {
        // 临时禁用Facebook购买事件回传
        return;

        try {
            // 获取BusinessFunctionApiServiceImpl实例
            BusinessFunctionApiServiceImpl businessFunctionApiService =
                    SpringUtils.getBean(BusinessFunctionApiServiceImpl.class);

            // 调用通用方法发送购买事件，设置为异步执行，并传入订单ID
            businessFunctionApiService.sendFacebookEvent(Long.parseLong(userId), "Purchase", orderId, true, false);

            log.info("调用通用Facebook事件发送方法: userId={}, event=Purchase, orderId={}", userId, orderId);
        } catch (Exception e) {
            log.error("调用Facebook事件发送方法失败: {}", e.getMessage(), e);

            // 记录异常日志
            try {
                ShortRunlog errorLog = new ShortRunlog();
                errorLog.setType("PIXEL回传");
                errorLog.setState("0");
                errorLog.setContent("用户：" + userId + "，支付成功回传失败");
                errorLog.setNote(e.getMessage());
                runlogService.insertShortRunlog(errorLog);
            } catch (Exception logEx) {
                log.error("记录事件错误日志失败", logEx);
            }
        }
    }

    /**
     * 获取VIP类型数据
     */
    @Override
    public Map<String, Object> getVipList(String templateId, Boolean flag, String userId, String linkAppId, String linkId) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 参数验证
            if (StringUtils.isEmpty(templateId)) {
                result.put("code", 400);
                result.put("msg", "获取vip类型数据失败");
                return result;
            }
            if (StringUtils.isNotEmpty(userId) && !"-1".equals(userId)) {
                String sub = StrUtil.sub(userId, 4, userId.length());
                Boolean flag1 = shortUserMapper.selectShortUserEmailFlag(Long.parseLong(sub));
                if (flag && !flag1) {
                    templateId = String.valueOf(payTempId);
                }
            }

            if (StringUtils.isNotEmpty(linkAppId) && Integer.parseInt(linkAppId) == 4) {
                templateId = String.valueOf(testPidTempId);
            }
            //走深链
            if(StringUtils.isNotEmpty(linkId)){
                // 根据 templateId 和 linkId 查询自定义付费计划
                List<ShortLinkVip> shortLinkVipList = shortLinkVipService.selectByTemplateIdAndLinkId(Long.valueOf(templateId), Long.valueOf(linkId));
                if(CollectionUtil.isNotEmpty(shortLinkVipList)){
                    return getLinkVipInfo(shortLinkVipList, result);
                }
            }
            // 根据充值模板ID查询VIP列表
            ShortVip queryVip = new ShortVip();
            queryVip.setPayTemplateId(Long.parseLong(templateId));
            List<ShortVip> vipList = vipService.selectShortVipList2(queryVip);

            if (vipList != null && !vipList.isEmpty()) {
                // 构建返回数据
                List<Map<String, Object>> dataList = new ArrayList<>();

                for (ShortVip vip : vipList) {
                    Map<String, Object> vipData = new HashMap<>();
                    vipData.put("id", vip.getId());
                    vipData.put("name", vip.getName());

                    // 获取金币信息
                    Long coin = 0L;
                    BigDecimal price = BigDecimal.ZERO;
                    if (vip.getPayTemplateAmountId() != null) {
                        ShortPayTemplateAmt payTemplateAmt = paytemplateAmountService.selectShortPayTemplateAmtById(vip.getPayTemplateAmountId());
                        if (payTemplateAmt != null) {
                            if (payTemplateAmt.getCoin() != null) {
                                coin = payTemplateAmt.getCoin();
                            }
                            if (payTemplateAmt.getPrice() != null) {
                                price = payTemplateAmt.getPrice();
                            }
                        }
                    }

                    vipData.put("coin", coin);
                    vipData.put("send_coin", vip.getSendCoin() != null ? vip.getSendCoin() : 0);
                    vipData.put("pay_type", vip.getPayType());

                    vipData.put("subscription_type", vip.getSubscriptionType());

                    vipData.put("price", price);

                    dataList.add(vipData);
                }

                result.put("code", 200);
                result.put("msg", "success");
                result.put("data", dataList);
            } else {
                result.put("code", 400);
                result.put("msg", "获取vip类型数据失败");
            }

        } catch (Exception e) {
            log.error("获取VIP类型数据失败: {}", e.getMessage(), e);
            result.put("code", 400);
            result.put("msg", "获取vip类型数据失败");
        }

        return result;
    }

    private Map<String, Object>  getLinkVipInfo(List<ShortLinkVip> shortLinkVipList, Map<String, Object> result){
        // 构建返回数据
        List<Map<String, Object>> dataList = new ArrayList<>();
        List<Long> paytemplateAmtIdList = shortLinkVipList.stream().map(ShortLinkVip::getPayTemplateAmountId).collect(Collectors.toList());
        List<ShortPayTemplateAmt> payTemplateAmtList = paytemplateAmountService.selectShortPayTemplateAmtByIdList(paytemplateAmtIdList);
        Map<Long, ShortPayTemplateAmt> payTemplateAmtMap = payTemplateAmtList.stream().collect(Collectors.toMap(ShortPayTemplateAmt::getId, Function.identity()));
        for (ShortLinkVip vip : shortLinkVipList) {
            Map<String, Object> vipData = new HashMap<>();
            vipData.put("id", vip.getId());
            vipData.put("name", vip.getName());
            // 获取金币信息
            Long coin = 0L;
            BigDecimal price = BigDecimal.ZERO;
            if (vip.getPayTemplateAmountId() != null && payTemplateAmtMap.containsKey(vip.getPayTemplateAmountId())) {
                ShortPayTemplateAmt payTemplateAmt = payTemplateAmtMap.get(vip.getPayTemplateAmountId());
                if (payTemplateAmt != null) {
                    if (payTemplateAmt.getCoin() != null) {
                        coin = payTemplateAmt.getCoin();
                    }
                    if (payTemplateAmt.getPrice() != null) {
                        price = payTemplateAmt.getPrice();
                    }
                }
            }
            vipData.put("coin", coin);
            vipData.put("send_coin", vip.getSendCoin() != null ? vip.getSendCoin() : 0);
            vipData.put("pay_type", vip.getPayType());
            vipData.put("subscription_type", vip.getSubscriptionType());
            vipData.put("price", price);
            dataList.add(vipData);
        }
        result.put("code", 200);
        result.put("msg", "success");
        result.put("data", dataList);
        return result;
    }

    /**
     * 获取金币消费记录
     */
    @Override
    public Map<String, Object> getCoinRecords(String token, String uid) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 通过token或uid获取用户ID
            Long userId = null;

            if (StringUtils.isNotEmpty(token)) {
                // 根据token获取用户ID
                ShortUser queryUser = new ShortUser();
                queryUser.setToken(token);
                List<ShortUser> users = userService.selectShortUserList(queryUser);
                if (users != null && !users.isEmpty()) {
                    userId = users.get(0).getId();
                }
            } else if (StringUtils.isNotEmpty(uid)) {
                // 处理uid前缀
                if (uid.length() > 4) {
                    uid = uid.substring(4);
                }
                userId = Long.parseLong(uid);
            }

            if (userId == null) {
                result.put("code", 400);
                result.put("msg", "未提供用户ID");
                return result;
            }

            // 查询用户的金币记录
            ShortCoinRecord queryCoinRecord = new ShortCoinRecord();
            queryCoinRecord.setUserId(userId);
            List<ShortCoinRecord> records = coinRecordService.selectShortCoinRecordList(queryCoinRecord);

            // 对结果按创建时间倒序排序（最新的排在前面）
            records.sort((a, b) -> {
                if (a.getCreateTime() == null && b.getCreateTime() == null) {
                    return 0;
                }
                if (a.getCreateTime() == null) {
                    return 1;
                }
                if (b.getCreateTime() == null) {
                    return -1;
                }
                return b.getCreateTime().compareTo(a.getCreateTime());
            });


            // 构建返回数据
            List<Map<String, Object>> formattedRecords = new ArrayList<>();

            for (ShortCoinRecord record : records) {
                Map<String, Object> recordMap = new HashMap<>();
                recordMap.put("id", record.getId());
                recordMap.put("user", record.getUserId());
                recordMap.put("app", record.getAppId());
                recordMap.put("coin", record.getCoin());
                recordMap.put("balance", record.getBalance());
                recordMap.put("constype", record.getConstype());

                // 格式化时间
                if (record.getCreateTime() != null) {
                    recordMap.put("addtime", record.getCreateTime().toString());
                } else {
                    recordMap.put("addtime", null);
                }

                if (record.getUpdateTime() != null) {
                    recordMap.put("updatetime", record.getUpdateTime().toString());
                } else {
                    recordMap.put("updatetime", null);
                }

                formattedRecords.add(recordMap);
            }

            result.put("code", 200);
            result.put("data", formattedRecords);

        } catch (Exception e) {
            log.error("获取金币消费记录失败: {}", e.getMessage(), e);
            result.put("code", 400);
            result.put("msg", "无效的请求数据");
        }

        return result;
    }

    /**
     * 扣除用户金币
     */
    @Override
    @Transactional
    public Map<String, Object> deductUserCoin(String token, String appId, String uid, Map<String, Object> deductData) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 获取请求参数
            Integer movieId = Integer.parseInt(deductData.get("movie").toString());
            Integer videoId = Integer.parseInt(deductData.get("video").toString());
            String constype = (String) deductData.get("constype");
            String unlockType = (String) deductData.get("unlock_type");
            String kid = (String) deductData.get("kid");

            log.info("扣除用户金币, 参数: movieId={}, videoId={}, constype={}, unlockType={}, kid={}",
                    movieId, videoId, constype, unlockType, kid);

            // 获取用户ID
            Long userId = null;

            if (StringUtils.isNotEmpty(uid)) {
                // 处理uid前缀
                if (uid.length() > 4) {
                    uid = uid.substring(4);
                }
                userId = Long.parseLong(uid);
            } else if (StringUtils.isNotEmpty(token)) {
                // 根据token获取用户
                ShortUser queryUser = new ShortUser();
                queryUser.setToken(token);
                List<ShortUser> users = userService.selectShortUserList(queryUser);
                if (users != null && !users.isEmpty()) {
                    userId = users.get(0).getId();
                }
            }

            if (userId == null) {
                result.put("code", 400);
                result.put("msg", "用户ID和金额为必填项");
                return result;
            }

            // 获取扣除金额
            Long deductAmount = null;

            // 不同解锁类型有不同的金币计算逻辑
            if ("1".equals(unlockType)) {
                // 从VideoChannelCoin表获取金币数量
                log.info("从VideoChannelCoin表获取金币数量");
                deductAmount = getChannelVideoCoin(movieId, videoId, kid);
            } else {
                // 从Video表获取金币数量
                log.info("从Video表获取金币数量");
                deductAmount = getVideoCoin(videoId);
            }
            log.info("获取查询的扣减金币金额=={}", deductAmount);
            if (deductAmount == null || deductAmount <= 0) {
                result.put("code", 400);
                result.put("msg", "无效的金额");
                return result;
            }

            // 获取用户信息并锁定行
            ShortUser user = userService.selectShortUserById(userId);

            if (user == null) {
                result.put("code", 404);
                result.put("msg", "用户不存在");
                return result;
            }

            // 检查用户金币是否足够
            if (user.getCoin() < deductAmount) {
                final Long currentCoin = user.getCoin();
                final Long requiredCoin = deductAmount;
                result.put("code", 403);
                result.put("msg", "金币不足");
                result.put("data", new HashMap<String, Object>() {{
                    put("current_coin", currentCoin);
                    put("required_coin", requiredCoin);
                }});
                return result;
            }

            // 扣除金币并保存
            user.setCoin(user.getCoin() - deductAmount);
            userService.updateShortUser(user);

            // 创建金币消费记录
            ShortCoinRecord coinRecord = new ShortCoinRecord();
            coinRecord.setUserId(userId);
            coinRecord.setAppId(Long.parseLong(appId));
            coinRecord.setCoin(deductAmount);
            coinRecord.setBalance(0L); //设置为0
            coinRecord.setConstype(constype);
            coinRecord.setStatus("0"); // 正常状态
            coinRecord.setCreateTime(DateUtils.getNowDate());
            coinRecord.setUpdateTime(DateUtils.getNowDate());
            coinRecordService.insertShortCoinRecord(coinRecord);

            // 创建用户解锁记录
            if ("1".equals(unlockType)) {
                // Python: channel_coin=VideoChannelCoin.objects.get(id=video_id)
                ShortUserUnlockVideo unlockVideo = new ShortUserUnlockVideo();
                unlockVideo.setUserId(userId);
                unlockVideo.setAppId(Long.parseLong(appId));
                unlockVideo.setUnlockType(unlockType);
                unlockVideo.setMovieId(Long.valueOf(movieId));
                unlockVideo.setVideoId(null);
                unlockVideo.setChannelCoinId(Long.valueOf(videoId)); // 直接使用videoId作为channelCoinId
                unlockVideo.setStatus("0"); // 正常状态
                unlockVideo.setCreateTime(DateUtils.getNowDate());
                userUnlockVideoService.insertShortUserUnlockVideo(unlockVideo);

                log.info("用户解锁渠道视频成功: userId={}, movieId={}, channelCoinId={}", userId, movieId, videoId);
            } else {
                // Python: video=Video.objects.get(id=video_id)
                ShortUserUnlockVideo unlockVideo = new ShortUserUnlockVideo();
                unlockVideo.setUserId(userId);
                unlockVideo.setAppId(Long.parseLong(appId));
                unlockVideo.setUnlockType(unlockType);
                unlockVideo.setMovieId(Long.valueOf(movieId));
                unlockVideo.setVideoId(Long.valueOf(videoId));
                unlockVideo.setChannelCoinId(null);
                unlockVideo.setStatus("0"); // 正常状态
                unlockVideo.setCreateTime(DateUtils.getNowDate());
                userUnlockVideoService.insertShortUserUnlockVideo(unlockVideo);

                log.info("用户解锁常规视频成功: userId={}, movieId={}, videoId={}", userId, movieId, videoId);
            }

            // 返回扣除成功结果
            final Long remainingCoin = user.getCoin();
            final Integer finalMovieId = movieId;
            final Integer finalVideoId = videoId;
            result.put("code", 200);
            result.put("msg", "成功");
            result.put("data", new HashMap<String, Object>() {{
                put("remaining_coin", remainingCoin);
                put("movie_id", finalMovieId);
                put("video_id", finalVideoId);
            }});

        } catch (Exception e) {
            log.error("扣除用户金币失败: {}", e.getMessage(), e);
            result.put("code", 500);
            result.put("msg", "扣除用户金币失败");
        }

        return result;
    }

    /**
     * 获取渠道视频金币数量
     */
    private Long getChannelVideoCoin(Integer movieId, Integer videoId, String kid) {
        if (movieId == null || videoId == null) {
            return 0L;
        }

        try {
            // Python使用: VideoChannelCoin.objects.get(movie_id=movie_id, id=video_id, coin_rule_id=kid).coin
            ShortVideoChannelCoin queryParams = new ShortVideoChannelCoin();
            queryParams.setMovieId(Long.valueOf(movieId));
            queryParams.setId(Long.valueOf(videoId));

            if (StringUtils.isNotEmpty(kid)) {
                queryParams.setCoinRuleId(Long.valueOf(kid));
            }
            log.info("执行查询视频金币配置==>{}", JSONUtil.toJsonStr(queryParams));
            // 查询视频频道金币配置
            List<ShortVideoChannelCoin> channelCoins = videoChannelCoinService.selectShortVideoChannelCoinList(queryParams);
            log.info("查询视频金币配置结果==>{}", JSONUtil.toJsonStr(channelCoins));
            if (channelCoins != null && !channelCoins.isEmpty()) {
                return channelCoins.get(0).getCoin().longValue();
            } else {
                //如果未查询到配置金币规则 走视频默认配置的金币规则
                return getVideoCoin(videoId);
            }
        } catch (Exception e) {
            log.error("获取频道视频金币数量失败: {}", e.getMessage(), e);
        }

        return 0L; // 默认返回0
    }

    /**
     * 获取视频金币数量
     */
    private Long getVideoCoin(Integer videoId) {
        if (videoId == null) {
            return 0L;
        }

        try {
            // 查询视频信息
            ShortVideo video = videoService.selectShortVideoById(Long.valueOf(videoId));
            return video != null ? video.getCoin().longValue() : 0L;
        } catch (Exception e) {
            log.error("获取视频金币数量失败: {}", e.getMessage(), e);
            return 0L;
        }
    }

    @Override
    public void processDisputeUser(Long userId, String disputeId, String disputeResult) {
        if (!blacklistUserEnabled) {
            log.info("用户黑名单功能已关闭");
            return;
        }

        try {
            // 所有争议退款的用户都拉黑
            if (OrderStatus.DISPUTE_ACCEPTED_AND_REFUNDED.getCode().equals(disputeResult) ||
                    OrderStatus.DISPUTE_LOST.getCode().equals(disputeResult)) {

                ShortUser user = userService.selectShortUserById(userId);
                if (user != null) {
                    log.info("拉黑争议用户 - 用户ID: {}, 争议ID: {}, 处理结果: {}",
                            userId, disputeId, disputeResult);

                    // 设置用户状态为0（禁用）
                    user.setState("0");
                    user.setUnsub(1);
                    user.setIsSubscriber("0");
                    user.setRemark("因支付争议被拉黑，争议ID: " + disputeId);
                    user.setUpdateTime(new Date());
                    userService.updateShortUser(user);

                    log.info("用户已被拉黑 - 用户ID: {}, 状态设置为: {}", userId, user.getState());

                    // 记录拉黑日志
                    recordUserBlacklistLog(userId, disputeId, disputeResult);
                } else {
                    log.warn("争议用户不存在 - 用户ID: {}", userId);
                }
            }
        } catch (Exception e) {
            log.error("处理争议用户失败 - 用户ID: {}, 争议ID: {}", userId, disputeId, e);
        }
    }

    /**
     * 记录用户拉黑日志
     */
    private void recordUserBlacklistLog(Long userId, String disputeId, String disputeResult) {
        try {
            ShortRunlog runlog = new ShortRunlog();
            runlog.setType("USER_BLACKLIST");
            runlog.setState("1");
            runlog.setContent(String.format("用户拉黑 - 用户ID: %d, 争议ID: %s, 处理结果: %s",
                    userId, disputeId, disputeResult));
            runlog.setCreateTime(new Date());
            runlogService.insertShortRunlog(runlog);
        } catch (Exception e) {
            log.error("记录用户拉黑日志失败", e);
        }
    }

    /**
     * 从支付回调数据中提取银行卡号并进行脱敏处理
     * 支持 card、googlepay、applepay 三种支付方式
     * @param callbackData 支付回调数据
     * @return 脱敏的银行卡号，格式如：1234****5678，如果提取失败则返回null
     */
    private String extractAndMaskCardNumber(Map<String, Object> callbackData) {
        try {
            if (callbackData == null || !callbackData.containsKey("data")) {
                return null;
            }

            Map<String, Object> dataObj = (Map<String, Object>) callbackData.get("data");
            if (dataObj == null || !dataObj.containsKey("object")) {
                return null;
            }

            Map<String, Object> objectData = (Map<String, Object>) dataObj.get("object");
            if (objectData == null || !objectData.containsKey("latest_payment_attempt")) {
                return null;
            }

            Map<String, Object> attemptData = (Map<String, Object>) objectData.get("latest_payment_attempt");
            if (attemptData == null || !attemptData.containsKey("payment_method")) {
                return null;
            }

            Map<String, Object> methodData = (Map<String, Object>) attemptData.get("payment_method");
            if (methodData == null || !methodData.containsKey("type")) {
                return null;
            }

            String paymentType = (String) methodData.get("type");
            String last4 = null;
            String bin = null;
            String first6 = null;

            // 根据支付类型提取银行卡信息
            if ("card".equals(paymentType)) {
                // 直接卡支付
                Map<String, Object> cardData = (Map<String, Object>) methodData.get("card");
                if (cardData != null) {
                    last4 = (String) cardData.get("last4");
                    first6 = (String) cardData.get("first6");
                    bin = (String) cardData.get("bin");
                }
            } else if ("googlepay".equals(paymentType)) {
                // Google Pay支付
                Map<String, Object> googlepayData = (Map<String, Object>) methodData.get("googlepay");
                if (googlepayData != null) {
                    Map<String, Object> tokenizedCard = (Map<String, Object>) googlepayData.get("tokenized_card");
                    if (tokenizedCard != null) {
                        last4 = (String) tokenizedCard.get("last4");
                        bin = (String) tokenizedCard.get("bin");
                    }
                }
            } else if ("applepay".equals(paymentType)) {
                // Apple Pay支付
                Map<String, Object> applepayData = (Map<String, Object>) methodData.get("applepay");
                if (applepayData != null) {
                    Map<String, Object> tokenizedCard = (Map<String, Object>) applepayData.get("tokenized_card");
                    if (tokenizedCard != null) {
                        last4 = (String) tokenizedCard.get("last4");
                        bin = (String) tokenizedCard.get("bin");
                    }
                }
            } else {
                log.warn("不支持的支付类型: {}", paymentType);
                return null;
            }

            if (last4 == null) {
                log.warn("银行卡回调数据中未找到last4字段，支付类型: {}", paymentType);
                return null;
            }

            // 生成脱敏银行卡号
            String maskedCardNumber;
            if (first6 != null && first6.length() >= 6) {
                // 如果有前6位，全部使用 + **** + 后4位
                maskedCardNumber = first6 + "****" + last4;
            } else if (bin != null && bin.length() >= 6) {
                // 如果有bin字段，全部使用 + **** + 后4位
                maskedCardNumber = bin + "****" + last4;
            } else if (bin != null && bin.length() >= 4) {
                // 如果bin字段少于6位，全部使用 + **** + 后4位
                maskedCardNumber = bin + "****" + last4;
            } else {
                // 如果都没有，使用********开头 + **** + 后4位
                maskedCardNumber = "********" + "****" + last4;
            }

            log.info("成功提取并脱敏银行卡号: {}, 支付类型: {}", maskedCardNumber, paymentType);
            return maskedCardNumber;

        } catch (Exception e) {
            log.error("提取银行卡号失败", e);
            return null;
        }
    }
}
