package com.ruoyi.app.api.controller;

import com.ruoyi.app.api.service.ManagerService;
import com.ruoyi.app.api.vo.MovieDo;
import com.ruoyi.app.api.vo.VideoDo;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.json.JSONException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 管理类API
 */
@Api(tags = "管理类API")
@RestController
@RequestMapping("/api")
public class ManagerController {

    @Autowired
    private ManagerService managerService;

    @ApiOperation(value = "新增短剧", notes = "新增短剧")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "app_id", value = "app_id", paramType = "header", required = true),
            @ApiImplicitParam(name = "name", value = "name", paramType = "header", required = true),
            @ApiImplicitParam(name = "oldname", value = "oldname", paramType = "header", required = true),
            @ApiImplicitParam(name = "icon", value = "icon", paramType = "header", required = true),
            @ApiImplicitParam(name = "director", value = "director", paramType = "header", required = true),
            @ApiImplicitParam(name = "actors", value = "actors", paramType = "header", required = true),
            @ApiImplicitParam(name = "up_time", value = "up_time", paramType = "header", required = true),
            @ApiImplicitParam(name = "time", value = "time", paramType = "header", required = true),
            @ApiImplicitParam(name = "num", value = "num", paramType = "header", required = true),
            @ApiImplicitParam(name = "rating", value = "rating", paramType = "header", required = true),
            @ApiImplicitParam(name = "description", value = "description", paramType = "header", required = true),
            @ApiImplicitParam(name = "content", value = "content", paramType = "header", required = true),
            @ApiImplicitParam(name = "unit_coin", value = "unit_coin", paramType = "header", required = true),
            @ApiImplicitParam(name = "vip_num", value = "vip_num", paramType = "header", required = true),
            @ApiImplicitParam(name = "source", value = "source", paramType = "header", required = true),
            @ApiImplicitParam(name = "is_vip", value = "is_vip", paramType = "header", required = true),
            @ApiImplicitParam(name = "state", value = "state", paramType = "header", required = true),
            @ApiImplicitParam(name = "iconNoWord", value = "iconNoWord", paramType = "header", required = true)
    })
    @PostMapping("/add_movie/")
    public AjaxResult addMovie(HttpServletRequest request, @RequestBody MovieDo movieDo) {
        // 调用服务
        return managerService.addMovie(movieDo);

    }


    @ApiOperation(value = "新增短剧视频", notes = "新增短剧视频")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "mid", value = "mid", paramType = "header", required = true),
            @ApiImplicitParam(name = "pic", value = "pic", paramType = "header", required = true),
            @ApiImplicitParam(name = "num", value = "num", paramType = "header", required = true),
            @ApiImplicitParam(name = "url", value = "url", paramType = "header", required = true),
            @ApiImplicitParam(name = "price", value = "price", paramType = "header", required = true),
            @ApiImplicitParam(name = "is_vip", value = "is_vip", paramType = "header", required = true),
            @ApiImplicitParam(name = "state", value = "state", paramType = "header", required = true),
            @ApiImplicitParam(name = "subtitleUrl", value = "subtitleUrl", paramType = "header", required = true),
    })
    @PostMapping("/add_video/")
    public AjaxResult addVideo(HttpServletRequest request, @RequestBody VideoDo movieDo) {
        // 调用服务
        return managerService.addVideo(movieDo);
    }


    @ApiOperation(value = "新增剧集多语言", notes = "新增剧集多语言")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "movieId", value = "movieId", paramType = "header", required = true),
            @ApiImplicitParam(name = "languageCode", value = "languageCode", paramType = "header", required = true),
            @ApiImplicitParam(name = "subtitleUrl", value = "subtitleUrl", paramType = "header", required = true),
    })
    @PostMapping("/add_video_i18n/")
    public AjaxResult addVideoI18n(HttpServletRequest request, @RequestBody VideoDo movieDo) {
        // 调用服务
        return managerService.addVideoI18n(movieDo);
    }

    @ApiOperation(value = "获取所有订阅用户", notes = "获取所有订阅用户")
    @GetMapping("/get_all_sub_users/")
    public AjaxResult getAllSubUsers() {
        return managerService.getAllSubUsers();
    }

    // 本地测试可使用用户id：24718
    @ApiOperation(value = "执行指定用户自动订阅续订", notes = "执行指定用户自动订阅续订")
    @GetMapping("/auto_sub_user/")
    public AjaxResult getAutoSubUser(HttpServletRequest request) throws Exception {
        String uid = request.getHeader("uid");
        return managerService.getAutoSubUser(uid);
    }

    @ApiOperation(value = "执行指定用户自动订阅续订-useepay", notes = "执行指定用户自动订阅续订")
    @GetMapping("/auto_sub_user_useepay/")
    public AjaxResult getAutoSubUserUseePay(HttpServletRequest request) throws Exception {
        String uid = request.getHeader("uid");
        return managerService.getAutoSubUserUseePay(uid);
    }


    @ApiOperation(value = "自动化订阅续费", notes = "自动化订阅续费")
    @GetMapping("/auto_sub")
    public AjaxResult getautoSub(@RequestParam String publicKey) throws InterruptedException {
        return managerService.getautoSub(publicKey);
    }

    @ApiOperation(value = "自动化订阅续费-useepay", notes = "自动化订阅续费")
    @GetMapping("/auto_sub_useepay")
    public AjaxResult getautoSubUseePay(@RequestParam String publicKey) throws InterruptedException {
        return managerService.getautoSubUseePay(publicKey);
    }

    @ApiOperation(value = "自动化订阅续费测试1", notes = "自动化订阅续费测1试")
    @GetMapping("/getTestAllSub")
    public AjaxResult getTestAllSub(@RequestParam String publicKey) throws InterruptedException {
        return managerService.testAllSub(publicKey);
    }

    @ApiOperation(value = "处理封面图", notes = "处理封面图")
    @GetMapping("/updateIconMovie")
    public AjaxResult updateIconMovie(@RequestParam String publicKey) throws InterruptedException {
        return managerService.updateIcon(publicKey);
    }

    @ApiOperation(value = "更改用户有效状态", notes = "更改用户有效状态")
    @GetMapping("/updateValidStatus")
    public AjaxResult updateValidStatus(HttpServletRequest request, @RequestParam String linkId) {
        String uid = request.getHeader("uid");
        return managerService.updateValidStatus(uid, linkId);
    }

    @ApiOperation(value = "获取收件箱域名", notes = "获取收件箱域名")
    @GetMapping("/getForwardEmail")
    public AjaxResult getForwardEmail(HttpServletRequest request) {
        String nid = request.getHeader("nid");
        return managerService.getForwardEmail(nid);
    }

    @ApiOperation(value = "根据appId查询", notes = "根据appId查询")
    @GetMapping("/getByAppId")
    public AjaxResult getByAppId(HttpServletRequest request) {
        String nid = request.getHeader("nid");
        return managerService.getByAppId(nid);
    }

    @ApiOperation("取消订阅")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "id", value = "用户id", dataType = "Integer", dataTypeClass = String.class),
//    })
//    @PreAuthorize("@ss.hasPermi('system:user:add')")
    @Log(title = "取消订阅", businessType = BusinessType.INSERT)
    @PostMapping(value = "/updateUnsub")
    public AjaxResult updateUnsub(HttpServletRequest request) throws Exception {
        String uid = request.getHeader("uid");
        return managerService.updateUnsub(uid);
    }


    @ApiOperation("分批处理")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "id", value = "用户id", dataType = "Integer", dataTypeClass = String.class),
//    })
//    @PreAuthorize("@ss.hasPermi('system:user:add')")
    @Log(title = "分批处理", businessType = BusinessType.INSERT)
    @PostMapping(value = "/startBatchProcessing")
    public String startBatchProcessing(@RequestParam int batches) throws Exception {
        return managerService.startBatchProcessing(batches);
    }


    @ApiOperation("getMovieByLimit")
    @Log(title = "获取最新剧集", businessType = BusinessType.INSERT)
    @GetMapping(value = "/getMovieByLimit")
    public AjaxResult getMovieByLimit(HttpServletRequest request,@RequestParam int limit) throws Exception {
        String nid = request.getHeader("nid");
        return managerService.getMovieByLimit(nid,limit);
    }

    @ApiOperation("清洗订单数据：")
    @Log(title = "清洗订单数据", businessType = BusinessType.INSERT)
    @PostMapping(value = "/cleaningOrderData")
    public AjaxResult cleaningOrderData() throws Exception {
        return managerService.cleaningOrderData();
    }

    @ApiOperation(value = "新续订逻辑-筛选用户", notes = "新续订逻辑-筛选用户")
    @GetMapping("/newAutomaticRenewal")
    public AjaxResult newAutomaticRenewal(@RequestParam String publicKey) throws InterruptedException {
        return managerService.newAutomaticRenewal(publicKey);
    }

    @ApiOperation(value = "新续订逻辑-自动续费", notes = "新续订逻辑-自动续费")
    @GetMapping("/newAutomaticRenewalRun")
    public AjaxResult newAutomaticRenewalRun(@RequestParam String publicKey) throws InterruptedException {
        return managerService.newAutomaticRenewalRun(publicKey);
    }

}



