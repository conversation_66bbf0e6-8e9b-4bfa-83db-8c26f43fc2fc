package com.ruoyi.quartz.task;

import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.domain.ShortOrder;
import com.ruoyi.domain.ShortRunlog;
import com.ruoyi.service.IShortOrderService;
import com.ruoyi.service.IShortRunlogService;
import com.ruoyi.system.domain.BankCardAlert;
import com.ruoyi.system.domain.BankCardStatistics;
import com.ruoyi.system.service.IBankCardAlertService;
import com.ruoyi.system.service.IBankCardStatisticsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 银行卡功能监控任务
 * 用于监控银行卡相关功能的运行状态和数据质量
 *
 * 监控内容：
 * 1. 新订单银行卡提取成功率
 * 2. 统计任务执行状态
 * 3. 预警功能运行状态
 * 4. 数据质量监控
 * 5. 系统性能监控
 *
 * 建议配置为每小时执行一次
 */
@Component("bankCardMonitorTask")
public class BankCardMonitorTask {

    private static final Logger log = LoggerFactory.getLogger(BankCardMonitorTask.class);

    /**
     * 执行银行卡功能全面监控
     */
    public void executeMonitoring() {
        log.info("=== 开始执行银行卡功能监控 ===");

        try {
            // 1. 监控新订单银行卡提取
            monitorNewOrderCardExtraction();

            // 2. 监控统计任务状态
            monitorStatisticsTaskStatus();

            // 3. 监控预警功能
            monitorAlertFunction();

            // 4. 监控数据质量
            monitorDataQuality();

            // 5. 生成监控报告
            generateMonitoringReport();

            log.info("=== 银行卡功能监控完成 ===");

        } catch (Exception e) {
            log.error("银行卡功能监控失败", e);
            // 发送紧急告警
            sendEmergencyAlert("银行卡功能监控任务失败: " + e.getMessage());
        }
    }

    /**
     * 1. 监控新订单银行卡提取成功率
     */
    public void monitorNewOrderCardExtraction() {
        log.info("--- 监控新订单银行卡提取 ---");

        try {
            IShortOrderService orderService = SpringUtils.getBean(IShortOrderService.class);

            // 查询最近1小时的成功订单
            LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(1);
            ShortOrder queryOrder = new ShortOrder();
            queryOrder.setStatus("SUCCEEDED");

            List<ShortOrder> recentOrders = orderService.selectShortOrderList(queryOrder)
                .stream()
                .filter(order -> order.getPayTime() != null)
                .filter(order -> order.getPayTime().toInstant()
                    .isAfter(java.time.Instant.from(oneHourAgo.atZone(java.time.ZoneId.systemDefault()))))
                .collect(java.util.stream.Collectors.toList());

            if (recentOrders.isEmpty()) {
                log.info("✅ 最近1小时无新订单，银行卡提取功能待检验");
                return;
            }

            long totalOrders = recentOrders.size();
            long ordersWithCard = recentOrders.stream()
                .filter(order -> order.getCardNumber() != null && !order.getCardNumber().isEmpty())
                .count();

            double extractionRate = totalOrders > 0 ? (ordersWithCard * 100.0 / totalOrders) : 0;

            log.info("新订单银行卡提取监控:");
            log.info("- 最近1小时订单数: {} 条", totalOrders);
            log.info("- 有银行卡号订单: {} 条", ordersWithCard);
            log.info("- 提取成功率: {:.2f}%", extractionRate);

            // 阈值检查
            if (extractionRate < 80.0) {
                String alertMsg = String.format("⚠️ 银行卡提取成功率异常: %.2f%% (阈值: 80%%)", extractionRate);
                log.warn(alertMsg);
                sendAlert("CARD_EXTRACTION_LOW", alertMsg);
            } else {
                log.info("✅ 银行卡提取成功率正常");
            }

            // 检查是否有格式异常的银行卡号
            long invalidCards = recentOrders.stream()
                .filter(order -> order.getCardNumber() != null)
                .filter(order -> !isValidCardFormat(order.getCardNumber()))
                .count();

            if (invalidCards > 0) {
                String alertMsg = String.format("⚠️ 发现 %d 个格式异常的银行卡号", invalidCards);
                log.warn(alertMsg);
                sendAlert("CARD_FORMAT_ERROR", alertMsg);
            }

        } catch (Exception e) {
            log.error("监控新订单银行卡提取失败", e);
        }
    }

    /**
     * 2. 监控统计任务执行状态
     */
    public void monitorStatisticsTaskStatus() {
        log.info("--- 监控银行卡统计任务状态 ---");

        try {
            IShortRunlogService runlogService = SpringUtils.getBean(IShortRunlogService.class);

            // 检查最近24小时的统计任务执行日志
            LocalDateTime yesterday = LocalDateTime.now().minusHours(24);

            // 这里需要根据实际的runlog查询方式调整
            // 假设统计任务会记录执行日志
            List<ShortRunlog> taskLogs = runlogService.selectShortRunlogList(new ShortRunlog())
                .stream()
                .filter(log -> "BANK_CARD_STATISTICS".equals(log.getType()))
                .filter(log -> log.getCreateTime().toInstant()
                    .isAfter(java.time.Instant.from(yesterday.atZone(java.time.ZoneId.systemDefault()))))
                .collect(java.util.stream.Collectors.toList());

            if (taskLogs.isEmpty()) {
                String alertMsg = "⚠️ 最近24小时未发现银行卡统计任务执行记录";
                log.warn(alertMsg);
                sendAlert("STATISTICS_TASK_MISSING", alertMsg);
            } else {
                // 检查是否有失败的任务
                long failedTasks = taskLogs.stream()
                    .filter(taskLog -> "0".equals(taskLog.getState())) // 假设0表示失败
                    .count();

                if (failedTasks > 0) {
                    String alertMsg = String.format("⚠️ 发现 %d 个失败的银行卡统计任务", failedTasks);
                    log.warn(alertMsg);
                    sendAlert("STATISTICS_TASK_FAILED", alertMsg);
                } else {
                    log.info("✅ 银行卡统计任务执行正常");
                }
            }

            // 检查统计数据更新情况
            IBankCardStatisticsService statisticsService = SpringUtils.getBean(IBankCardStatisticsService.class);

            // 检查今日是否有统计数据更新
            LocalDate today = LocalDate.now();
            List<String> activeCards = statisticsService.getActiveCardNumbers(today.minusDays(1));

            if (activeCards == null || activeCards.isEmpty()) {
                log.info("✅ 昨日无活跃银行卡，统计任务正常跳过");
            } else {
                log.info("✅ 昨日活跃银行卡: {} 张，统计数据正常", activeCards.size());
            }

        } catch (Exception e) {
            log.error("监控银行卡统计任务状态失败", e);
        }
    }

    /**
     * 3. 监控预警功能运行状态
     */
    public void monitorAlertFunction() {
        log.info("--- 监控银行卡预警功能 ---");

        try {
            IBankCardAlertService alertService = SpringUtils.getBean(IBankCardAlertService.class);

            // 检查最近24小时的预警生成情况
            LocalDateTime yesterday = LocalDateTime.now().minusHours(24);

            // 这里需要根据实际的alert查询方式调整
            List<BankCardAlert> recentAlerts = alertService.selectBankCardAlertList(new BankCardAlert())
                .stream()
                .filter(alert -> alert.getCreateTime().toInstant()
                    .isAfter(java.time.Instant.from(yesterday.atZone(java.time.ZoneId.systemDefault()))))
                .collect(java.util.stream.Collectors.toList());

            log.info("预警功能监控:");
            log.info("- 最近24小时预警数: {} 条", recentAlerts.size());

            // 按预警类型统计
            long multiUserAlerts = recentAlerts.stream()
                .filter(alert -> "MULTI_USER".equals(alert.getAlertType()))
                .count();
            long weekOveruseAlerts = recentAlerts.stream()
                .filter(alert -> "WEEK_OVERUSE".equals(alert.getAlertType()))
                .count();
            long monthOveruseAlerts = recentAlerts.stream()
                .filter(alert -> "MONTH_OVERUSE".equals(alert.getAlertType()))
                .count();

            log.info("- 多用户绑定预警: {} 条", multiUserAlerts);
            log.info("- 周消费超限预警: {} 条", weekOveruseAlerts);
            log.info("- 月消费超限预警: {} 条", monthOveruseAlerts);

            // 检查是否有预警爆发（可能表示异常）
            if (recentAlerts.size() > 100) {
                String alertMsg = String.format("⚠️ 预警数量异常: 24小时内生成 %d 条预警", recentAlerts.size());
                log.warn(alertMsg);
                sendAlert("ALERT_BURST", alertMsg);
            } else {
                log.info("✅ 预警生成数量正常");
            }

        } catch (Exception e) {
            log.error("监控银行卡预警功能失败", e);
        }
    }

    /**
     * 4. 监控数据质量
     */
    public void monitorDataQuality() {
        log.info("--- 监控银行卡数据质量 ---");

        try {
            IShortOrderService orderService = SpringUtils.getBean(IShortOrderService.class);

            // 检查数据一致性
            ShortOrder queryOrder = new ShortOrder();
            queryOrder.setStatus("SUCCEEDED");
            List<ShortOrder> successOrders = orderService.selectShortOrderList(queryOrder)
                .stream()
                .limit(1000) // 检查最近1000条记录
                .collect(java.util.stream.Collectors.toList());

            long totalOrders = successOrders.size();
            long ordersWithCard = successOrders.stream()
                .filter(order -> order.getCardNumber() != null && !order.getCardNumber().isEmpty())
                .count();
            long ordersWithRawData = successOrders.stream()
                .filter(order -> order.getRawData() != null && !order.getRawData().isEmpty())
                .count();

            double cardCoverageRate = totalOrders > 0 ? (ordersWithCard * 100.0 / totalOrders) : 0;
            double rawDataCoverageRate = totalOrders > 0 ? (ordersWithRawData * 100.0 / totalOrders) : 0;

            log.info("数据质量监控:");
            log.info("- 检查订单数: {} 条", totalOrders);
            log.info("- 银行卡覆盖率: {:.2f}%", cardCoverageRate);
            log.info("- rawData覆盖率: {:.2f}%", rawDataCoverageRate);

            // 数据质量检查
            if (cardCoverageRate < 70.0 && rawDataCoverageRate > 80.0) {
                String alertMsg = String.format("⚠️ 数据迁移可能不完整: 银行卡覆盖率 %.2f%%, rawData覆盖率 %.2f%%",
                    cardCoverageRate, rawDataCoverageRate);
                log.warn(alertMsg);
                sendAlert("DATA_MIGRATION_INCOMPLETE", alertMsg);
            }

            // 检查银行卡号格式
            long invalidCards = successOrders.stream()
                .filter(order -> order.getCardNumber() != null)
                .filter(order -> !isValidCardFormat(order.getCardNumber()))
                .count();

            if (invalidCards > 0) {
                String alertMsg = String.format("⚠️ 数据质量异常: 发现 %d 个格式错误的银行卡号", invalidCards);
                log.warn(alertMsg);
                sendAlert("DATA_QUALITY_ERROR", alertMsg);
            } else {
                log.info("✅ 银行卡号格式验证通过");
            }

        } catch (Exception e) {
            log.error("监控银行卡数据质量失败", e);
        }
    }

    /**
     * 5. 生成监控报告
     */
    public void generateMonitoringReport() {
        log.info("--- 生成银行卡功能监控报告 ---");

        try {
            IShortOrderService orderService = SpringUtils.getBean(IShortOrderService.class);
            IBankCardStatisticsService statisticsService = SpringUtils.getBean(IBankCardStatisticsService.class);

            // 统计基础数据
            ShortOrder queryOrder = new ShortOrder();
            queryOrder.setStatus("SUCCEEDED");
            List<ShortOrder> successOrders = orderService.selectShortOrderList(queryOrder);

            long totalOrders = successOrders.size();
            long ordersWithCard = successOrders.stream()
                .filter(order -> order.getCardNumber() != null && !order.getCardNumber().isEmpty())
                .count();

            // 统计数据
            List<BankCardStatistics> allStats = statisticsService.selectBankCardStatisticsList(new BankCardStatistics());
            long totalCards = allStats.size();
            long activeCards = allStats.stream()
                .filter(stats -> stats.getTotalCount() > 0)
                .count();

            log.info("=== 银行卡功能监控报告 ===");
            log.info("监控时间: {}", LocalDateTime.now());
            log.info("");
            log.info("基础数据:");
            log.info("- 总成功订单: {} 条", totalOrders);
            log.info("- 有银行卡订单: {} 条", ordersWithCard);
            log.info("- 银行卡覆盖率: {:.2f}%", totalOrders > 0 ? (ordersWithCard * 100.0 / totalOrders) : 0);
            log.info("- 统计中银行卡总数: {} 张", totalCards);
            log.info("- 活跃银行卡数: {} 张", activeCards);
            log.info("");
            log.info("系统状态:");
            log.info("- 银行卡提取: 正常运行");
            log.info("- 统计任务: 正常运行");
            log.info("- 预警功能: 正常运行");
            log.info("- 数据质量: 正常");

        } catch (Exception e) {
            log.error("生成监控报告失败", e);
        }
    }

    /**
     * 发送普通告警
     */
    private void sendAlert(String alertType, String message) {
        try {
            IShortRunlogService runlogService = SpringUtils.getBean(IShortRunlogService.class);

            ShortRunlog runlog = new ShortRunlog();
            runlog.setType("BANK_CARD_MONITOR_ALERT");
            runlog.setState("0"); // 告警状态
            runlog.setContent(String.format("[%s] %s", alertType, message));
            runlog.setCreateTime(new java.util.Date());

            runlogService.insertShortRunlog(runlog);

            // 这里可以添加更多告警方式，如邮件、短信、钉钉等
            log.warn("🚨 发送告警: {}", message);

        } catch (Exception e) {
            log.error("发送告警失败", e);
        }
    }

    /**
     * 发送紧急告警
     */
    private void sendEmergencyAlert(String message) {
        try {
            IShortRunlogService runlogService = SpringUtils.getBean(IShortRunlogService.class);

            ShortRunlog runlog = new ShortRunlog();
            runlog.setType("BANK_CARD_EMERGENCY_ALERT");
            runlog.setState("0"); // 紧急告警状态
            runlog.setContent("🚨 紧急告警: " + message);
            runlog.setCreateTime(new java.util.Date());

            runlogService.insertShortRunlog(runlog);

            // 紧急告警应该通过多种方式发送
            log.error("🚨🚨🚨 紧急告警: {}", message);

        } catch (Exception e) {
            log.error("发送紧急告警失败", e);
        }
    }

    /**
     * 验证银行卡号格式
     */
    private boolean isValidCardFormat(String cardNumber) {
        if (cardNumber == null || cardNumber.isEmpty()) {
            return false;
        }
        return cardNumber.matches("^\\d{4}\\*{4}\\d{4}$") || cardNumber.matches("^\\*{4}\\d{4}$");
    }

    /**
     * 快速健康检查 - 用于高频监控
     */
    public void quickHealthCheck() {
        try {
            log.debug("执行银行卡功能快速健康检查");

            IShortOrderService orderService = SpringUtils.getBean(IShortOrderService.class);

            // 检查最近10分钟的订单
            LocalDateTime tenMinutesAgo = LocalDateTime.now().minusMinutes(10);
            ShortOrder queryOrder = new ShortOrder();
            queryOrder.setStatus("SUCCEEDED");

            List<ShortOrder> recentOrders = orderService.selectShortOrderList(queryOrder)
                .stream()
                .filter(order -> order.getPayTime() != null)
                .filter(order -> order.getPayTime().toInstant()
                    .isAfter(java.time.Instant.from(tenMinutesAgo.atZone(java.time.ZoneId.systemDefault()))))
                .collect(java.util.stream.Collectors.toList());

            if (!recentOrders.isEmpty()) {
                long ordersWithCard = recentOrders.stream()
                    .filter(order -> order.getCardNumber() != null && !order.getCardNumber().isEmpty())
                    .count();

                double extractionRate = (ordersWithCard * 100.0 / recentOrders.size());

                if (extractionRate < 50.0) {
                    sendEmergencyAlert(String.format("银行卡提取成功率严重异常: %.2f%%", extractionRate));
                }
            }

        } catch (Exception e) {
            log.error("快速健康检查失败", e);
        }
    }
}