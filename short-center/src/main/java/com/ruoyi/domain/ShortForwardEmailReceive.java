package com.ruoyi.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 收件箱对象 short_forward_email_receive
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
public class ShortForwardEmailReceive extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 域名 */
    @Excel(name = "域名")
    private String domain;

    /** 0查全部，1.未读，2.已读 */
    @Excel(name = "0查全部，1.未读，2.已读")
    private String type;

    /** appId */
    @Excel(name = "appId")
    private Long appId;

    /** 邮件内容 */
    @Excel(name = "邮件内容")
    private String subject;

    /** 发件日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "发件日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date sentDate;

    /** 发件人 */
    @Excel(name = "发件人")
    private String form;

    /** 发件人邮箱 */
    @Excel(name = "发件人邮箱")
    private String email;

    /** 内容 */
    @Excel(name = "内容")
    private String content;

    /** 附件地址 */
    @Excel(name = "附件地址")
    private String url;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    private Long userId;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }

    public void setDomain(String domain)
    {
        this.domain = domain;
    }

    public String getDomain()
    {
        return domain;
    }

    public void setType(String type)
    {
        this.type = type;
    }

    public String getType()
    {
        return type;
    }

    public void setAppId(Long appId)
    {
        this.appId = appId;
    }

    public Long getAppId()
    {
        return appId;
    }

    public void setSubject(String subject)
    {
        this.subject = subject;
    }

    public String getSubject()
    {
        return subject;
    }

    public void setSentDate(Date sentDate)
    {
        this.sentDate = sentDate;
    }

    public Date getSentDate()
    {
        return sentDate;
    }

    public void setForm(String form)
    {
        this.form = form;
    }

    public String getForm()
    {
        return form;
    }

    public void setEmail(String email)
    {
        this.email = email;
    }

    public String getEmail()
    {
        return email;
    }

    public void setContent(String content)
    {
        this.content = content;
    }

    public String getContent()
    {
        return content;
    }

    public void setUrl(String url)
    {
        this.url = url;
    }

    public String getUrl()
    {
        return url;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("domain", getDomain())
                .append("type", getType())
                .append("appId", getAppId())
                .append("subject", getSubject())
                .append("sentDate", getSentDate())
                .append("form", getForm())
                .append("email", getEmail())
                .append("content", getContent())
                .append("url", getUrl())
                .append("status", getStatus())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .toString();
    }
}
