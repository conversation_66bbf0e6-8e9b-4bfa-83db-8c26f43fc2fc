package com.ruoyi.web.controller.bankcard;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.BankCardStatistics;
import com.ruoyi.system.service.IBankCardStatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 银行卡统计监控Controller
 */
@RestController
@RequestMapping("/bankcard/statistics")
public class BankCardStatisticsController extends BaseController
{
    @Autowired
    private IBankCardStatisticsService bankCardStatisticsService;

    /**
     * 查询银行卡统计列表
     */
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody BankCardStatistics bankCardStatistics)
    {
        startPage();
        List<BankCardStatistics> list = bankCardStatisticsService.selectBankCardStatisticsList(bankCardStatistics);
        return getDataTable(list);
    }

    /**
     * 导出银行卡统计列表
     */
    @Log(title = "银行卡统计", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BankCardStatistics bankCardStatistics)
    {
        List<BankCardStatistics> list = bankCardStatisticsService.selectBankCardStatisticsList(bankCardStatistics);
        ExcelUtil<BankCardStatistics> util = new ExcelUtil<BankCardStatistics>(BankCardStatistics.class);
        util.exportExcel(response, list, "银行卡统计数据");
    }

    /**
     * 新增保存银行卡统计
     */
    @Log(title = "银行卡统计", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody BankCardStatistics bankCardStatistics)
    {
        return toAjax(bankCardStatisticsService.insertBankCardStatistics(bankCardStatistics));
    }

    /**
     * 修改保存银行卡统计
     */
    @Log(title = "银行卡统计", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody BankCardStatistics bankCardStatistics)
    {
        return toAjax(bankCardStatisticsService.updateBankCardStatistics(bankCardStatistics));
    }

    /**
     * 删除银行卡统计
     */
    @Log(title = "银行卡统计", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    public AjaxResult remove(@RequestBody String ids)
    {
        return toAjax(bankCardStatisticsService.deleteBankCardStatisticsByIds(Convert.toLongArray(ids)));
    }

    /**
     * 获取多用户使用的银行卡列表
     */
    @PostMapping("/multiUserCards")
    public TableDataInfo multiUserCards()
    {
        List<BankCardStatistics> list = bankCardStatisticsService.getMultiUserCards();
        return getDataTable(list);
    }

    /**
     * 获取统计概览
     */
    @GetMapping("/overview")
    public AjaxResult overview()
    {
        try {
            // 按状态统计银行卡数量
            List<Object[]> statusCounts = bankCardStatisticsService.countByStatus();

            return AjaxResult.success().put("statusCounts", statusCounts);
        } catch (Exception e) {
            logger.error("获取银行卡统计概览失败", e);
            return AjaxResult.error("获取统计概览失败");
        }
    }

    /**
     * 根据银行卡号查询详细信息
     */
    @GetMapping("/detail/{cardNumber}")
    public AjaxResult getCardDetail(@PathVariable String cardNumber)
    {
        try {
            BankCardStatistics statistics = bankCardStatisticsService.selectBankCardStatisticsByCardNumber(cardNumber);
            if (statistics != null) {
                return AjaxResult.success(statistics);
            } else {
                return AjaxResult.error("银行卡统计信息不存在");
            }
        } catch (Exception e) {
            logger.error("查询银行卡详细信息失败: {}", cardNumber, e);
            return AjaxResult.error("查询失败");
        }
    }

    /**
     * 根据ID查询银行卡统计详细信息
     */
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(bankCardStatisticsService.selectBankCardStatisticsById(id));
    }
}