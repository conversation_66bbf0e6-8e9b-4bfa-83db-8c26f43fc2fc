package com.ruoyi.mapper;

import java.util.List;
import com.ruoyi.domain.ShortUserCollectAndHistory;

/**
 * 【请填写功能名称】Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-09-25
 */
public interface ShortUserCollectAndHistoryMapper 
{
    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public ShortUserCollectAndHistory selectShortUserCollectAndHistoryById(Long id);

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param shortUserCollectAndHistory 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<ShortUserCollectAndHistory> selectShortUserCollectAndHistoryList(ShortUserCollectAndHistory shortUserCollectAndHistory);

    /**
     * 新增【请填写功能名称】
     * 
     * @param shortUserCollectAndHistory 【请填写功能名称】
     * @return 结果
     */
    public int insertShortUserCollectAndHistory(ShortUserCollectAndHistory shortUserCollectAndHistory);

    /**
     * 修改【请填写功能名称】
     * 
     * @param shortUserCollectAndHistory 【请填写功能名称】
     * @return 结果
     */
    public int updateShortUserCollectAndHistory(ShortUserCollectAndHistory shortUserCollectAndHistory);

    /**
     * 删除【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteShortUserCollectAndHistoryById(Long id);

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteShortUserCollectAndHistoryByIds(Long[] ids);
}
