package com.ruoyi.web.controller.short_center.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.domain.ShortBanner;
import com.ruoyi.dto.ShortBannerInDTO;
import com.ruoyi.service.IShortBannerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 轮播图数据Controller
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
@Api("轮播图管理")
@RestController
@RequestMapping("/short_center/banner")
public class ShortBannerController extends BaseController {
    @Autowired
    private IShortBannerService shortBannerService;

    /**
     * 查询轮播图数据列表
     */
    @ApiOperation("查询轮播图数据列表")
    @PreAuthorize("@ss.hasPermi('short_center:banner:list')")
    @GetMapping("/list")
    public TableDataInfo list(ShortBanner shortBanner) {
        startPage();
        List<ShortBanner> list = shortBannerService.selectShortBannerList(shortBanner);
        return getDataTable(list);
    }

    /**
     * 导出轮播图数据列表
     */
    @PreAuthorize("@ss.hasPermi('short_center:banner:export')")
    @Log(title = "轮播图数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ShortBanner shortBanner) {
        List<ShortBanner> list = shortBannerService.selectShortBannerList(shortBanner);
        ExcelUtil<ShortBanner> util = new ExcelUtil<ShortBanner>(ShortBanner.class);
        util.exportExcel(response, list, "轮播图数据数据");
    }

    /**
     * 获取轮播图数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('short_center:banner:query')")
    @GetMapping(value = "/getInfo/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(shortBannerService.selectShortBannerById(id));
    }

    /**
     * 新增轮播图数据
     */
    @ApiOperation("新增轮播图数据")
    @PreAuthorize("@ss.hasPermi('short_center:banner:add')")
    @Log(title = "轮播图数据", businessType = BusinessType.INSERT)
    @PostMapping(value = "/add")
    public AjaxResult add(@RequestBody ShortBanner shortBanner) {
        shortBanner.setCreateBy(getStrUserId());
        return toAjax(shortBannerService.insertShortBanner(shortBanner));
    }

    /**
     * 修改轮播图数据
     */
    @ApiOperation("修改轮播图数据")
    @PreAuthorize("@ss.hasPermi('short_center:banner:edit')")
    @Log(title = "轮播图数据", businessType = BusinessType.UPDATE)
    @PutMapping(value = "/edit")
    public AjaxResult edit(@RequestBody ShortBanner shortBanner) {
        shortBanner.setUpdateBy(getStrUserId());
        return toAjax(shortBannerService.updateShortBanner(shortBanner));
    }

    /**
     * 删除轮播图数据
     */
    @ApiOperation("删除轮播图数据")
    @PreAuthorize("@ss.hasPermi('short_center:banner:remove')")
    @Log(title = "轮播图数据", businessType = BusinessType.DELETE)
    @DeleteMapping("/remove/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(shortBannerService.deleteShortBannerByIds(ids));
    }

    @ApiOperation("保存轮播图数据")
    @PostMapping(value = "/saveBanner")
    public AjaxResult saveBanner(@RequestBody ShortBannerInDTO shortBannerInDTO) {
    	return toAjax(shortBannerService.saveBanner(shortBannerInDTO));
    }


    @ApiOperation("根据movieId删除轮播图")
    @GetMapping("/delete")
    public AjaxResult delete(Long movieId, Long appId) {
        return toAjax(shortBannerService.deleteByMovieId(movieId,appId));
    }
}
