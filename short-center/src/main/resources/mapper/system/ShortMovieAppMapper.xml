<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.ShortMovieAppMapper">

    <resultMap type="ShortMovieApp" id="ShortMovieAppResult">
        <result property="id"    column="id"    />
        <result property="movieId"    column="movie_id"    />
        <result property="appId"    column="app_id"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectShortMovieAppVo">
        select id, movie_id, app_id, status, create_by, create_time, update_by, update_time, remark from short_movie_app
    </sql>

    <select id="selectShortMovieAppList" parameterType="ShortMovieApp" resultMap="ShortMovieAppResult">
        <include refid="selectShortMovieAppVo"/>
        <where>
            <if test="movieId != null "> and movie_id = #{movieId}</if>
            <if test="appId != null "> and app_id = #{appId}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>

    <select id="selectShortMovieAppById" parameterType="Long" resultMap="ShortMovieAppResult">
        <include refid="selectShortMovieAppVo"/>
        where id = #{id}
    </select>
    <select id="selectByMovieIds" resultType="com.ruoyi.domain.ShortMovieApp">
        SELECT
            sma.movie_id id,
            GROUP_CONCAT(DISTINCT sa.`name` ORDER BY sa.`name` SEPARATOR ', ') AS name
        FROM
            short_movie_app sma
                LEFT JOIN short_app sa ON sma.app_id = sa.id
        WHERE
            sma.movie_id IN
        <foreach item="item" index="index" collection="list"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY
            sma.movie_id
    </select>
    <select id="selectByMovieIdAndAppId" resultType="java.lang.Integer">
        SELECT
            COUNT(id)
        FROM
            short_movie_app
        WHERE
            movie_id = #{movieId}
          AND app_id = #{appId}
          and `status` = 0
    </select>

    <insert id="insertShortMovieApp" parameterType="ShortMovieApp">
        insert into short_movie_app
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="movieId != null">movie_id,</if>
            <if test="appId != null">app_id,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="movieId != null">#{movieId},</if>
            <if test="appId != null">#{appId},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>
    <insert id="batchInsert">
        INSERT INTO short_movie_app (
        <trim suffixOverrides=",">
            <if test="list[0].id != null">id,</if>
            <if test="list[0].movieId != null">movie_id,</if>
            <if test="list[0].appId != null">app_id,</if>
            <if test="list[0].status != null">status,</if>
            <if test="list[0].createBy != null">create_by,</if>
            <if test="list[0].createTime != null">create_time,</if>
            <if test="list[0].updateBy != null">update_by,</if>
            <if test="list[0].updateTime != null">update_time,</if>
            <if test="list[0].remark != null">remark,</if>
        </trim>
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            <trim suffixOverrides=",">
                <if test="item.id != null">#{item.id},</if>
                <if test="item.movieId != null">#{item.movieId},</if>
                <if test="item.appId != null">#{item.appId},</if>
                <if test="item.status != null">#{item.status},</if>
                <if test="item.createBy != null">#{item.createBy},</if>
                <if test="item.createTime != null">#{item.createTime},</if>
                <if test="item.updateBy != null">#{item.updateBy},</if>
                <if test="item.updateTime != null">#{item.updateTime},</if>
                <if test="item.remark != null">#{item.remark},</if>
            </trim>
            )
        </foreach>
    </insert>

    <update id="updateShortMovieApp" parameterType="ShortMovieApp">
        update short_movie_app
        <trim prefix="SET" suffixOverrides=",">
            <if test="movieId != null">movie_id = #{movieId},</if>
            <if test="appId != null">app_id = #{appId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteShortMovieAppById" parameterType="Long">
        delete from short_movie_app where id = #{id}
    </delete>

    <delete id="deleteShortMovieAppByIds" parameterType="String">
        delete from short_movie_app where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>