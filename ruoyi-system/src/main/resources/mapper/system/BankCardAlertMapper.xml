<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.BankCardAlertMapper">

    <resultMap type="BankCardAlert" id="BankCardAlertResult">
        <result property="id"    column="id"    />
        <result property="cardNumber"    column="card_number"    />
        <result property="userId"    column="user_id"    />
        <result property="alertType"    column="alert_type"    />
        <result property="alertDetail"    column="alert_detail"    />
        <result property="processedFlag"    column="processed_flag"    />
        <result property="processedAt"    column="processed_at"    />
        <result property="processedBy"    column="processed_by"    />
        <result property="processNote"    column="process_note"    />
        <result property="createTime"    column="created_at"    />
    </resultMap>

    <sql id="selectBankCardAlertVo">
        select id, card_number, user_id, alert_type, alert_detail, processed_flag,
               processed_at, processed_by, process_note, created_at
        from bank_card_alerts
    </sql>

    <select id="selectBankCardAlertList" parameterType="BankCardAlert" resultMap="BankCardAlertResult">
        <include refid="selectBankCardAlertVo"/>
        <where>
            <if test="cardNumber != null  and cardNumber != ''"> and card_number like concat('%', #{cardNumber}, '%')</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="alertType != null  and alertType != ''"> and alert_type = #{alertType}</if>
            <if test="processedFlag != null "> and processed_flag = #{processedFlag}</if>
        </where>
        order by created_at desc
    </select>

    <select id="selectBankCardAlertById" parameterType="Long" resultMap="BankCardAlertResult">
        <include refid="selectBankCardAlertVo"/>
        where id = #{id}
    </select>

    <insert id="insertBankCardAlert" parameterType="BankCardAlert" useGeneratedKeys="true" keyProperty="id">
        insert into bank_card_alerts
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="cardNumber != null and cardNumber != ''">card_number,</if>
            <if test="userId != null">user_id,</if>
            <if test="alertType != null and alertType != ''">alert_type,</if>
            <if test="alertDetail != null">alert_detail,</if>
            <if test="processedFlag != null">processed_flag,</if>
            <if test="processedAt != null">processed_at,</if>
            <if test="processedBy != null">processed_by,</if>
            <if test="processNote != null">process_note,</if>
            <if test="createTime != null">created_at,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="cardNumber != null and cardNumber != ''">#{cardNumber},</if>
            <if test="userId != null">#{userId},</if>
            <if test="alertType != null and alertType != ''">#{alertType},</if>
            <if test="alertDetail != null">#{alertDetail},</if>
            <if test="processedFlag != null">#{processedFlag},</if>
            <if test="processedAt != null">#{processedAt},</if>
            <if test="processedBy != null">#{processedBy},</if>
            <if test="processNote != null">#{processNote},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <update id="updateBankCardAlert" parameterType="BankCardAlert">
        update bank_card_alerts
        <trim prefix="SET" suffixOverrides=",">
            <if test="cardNumber != null and cardNumber != ''">card_number = #{cardNumber},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="alertType != null and alertType != ''">alert_type = #{alertType},</if>
            <if test="alertDetail != null">alert_detail = #{alertDetail},</if>
            <if test="processedFlag != null">processed_flag = #{processedFlag},</if>
            <if test="processedAt != null">processed_at = #{processedAt},</if>
            <if test="processedBy != null">processed_by = #{processedBy},</if>
            <if test="processNote != null">process_note = #{processNote},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBankCardAlertById" parameterType="Long">
        delete from bank_card_alerts where id = #{id}
    </delete>

    <delete id="deleteBankCardAlertByIds" parameterType="String">
        delete from bank_card_alerts where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 业务相关查询 -->
    <select id="countExistingAlerts" resultType="int">
        SELECT COUNT(*) FROM bank_card_alerts
        WHERE card_number = #{cardNumber}
        AND alert_type = #{alertType}
        AND created_at >= #{since}
    </select>

    <select id="selectUnprocessedAlerts" parameterType="Integer" resultMap="BankCardAlertResult">
        <include refid="selectBankCardAlertVo"/>
        WHERE processed_flag = 0
        ORDER BY created_at DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <select id="selectAlertsByCard" resultMap="BankCardAlertResult">
        <include refid="selectBankCardAlertVo"/>
        WHERE card_number = #{cardNumber}
        ORDER BY created_at DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <select id="selectAlertsByUser" resultMap="BankCardAlertResult">
        <include refid="selectBankCardAlertVo"/>
        WHERE user_id = #{userId}
        ORDER BY created_at DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <update id="batchMarkAsProcessed">
        UPDATE bank_card_alerts SET
            processed_flag = 1,
            processed_at = #{processedAt},
            processed_by = #{processedBy},
            process_note = #{processNote}
        WHERE id IN
        <foreach item="alertId" collection="alertIds" open="(" separator="," close=")">
            #{alertId}
        </foreach>
    </update>

    <select id="countByAlertType" resultType="Object">
        SELECT alert_type, COUNT(*) as count
        FROM bank_card_alerts
        WHERE created_at >= #{startDate}
        GROUP BY alert_type
    </select>

    <select id="countRecentAlerts" resultType="int">
        SELECT COUNT(*) FROM bank_card_alerts
        WHERE created_at >= #{since}
    </select>

    <delete id="deleteExpiredAlerts">
        DELETE FROM bank_card_alerts
        WHERE created_at <![CDATA[ < ]]> #{expireTime}
    </delete>

</mapper>