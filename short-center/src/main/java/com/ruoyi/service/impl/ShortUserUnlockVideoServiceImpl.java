package com.ruoyi.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.domain.ShortUserUnlockVideo;
import com.ruoyi.mapper.ShortUserUnlockVideoMapper;
import com.ruoyi.service.IShortUserUnlockVideoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 用户已解锁的视频Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
@Service
public class ShortUserUnlockVideoServiceImpl implements IShortUserUnlockVideoService {
    @Autowired
    private ShortUserUnlockVideoMapper shortUserUnlockVideoMapper;


    /**
     * 查询用户已解锁的视频
     *
     * @param id 用户已解锁的视频主键
     * @return 用户已解锁的视频
     */
    @Override
    public ShortUserUnlockVideo selectShortUserUnlockVideoById(Long id) {
        return shortUserUnlockVideoMapper.selectShortUserUnlockVideoById(id);
    }

    /**
     * 查询用户已解锁的视频列表
     *
     * @param shortUserUnlockVideo 用户已解锁的视频
     * @return 用户已解锁的视频
     */
    @Override
    public List<ShortUserUnlockVideo> selectShortUserUnlockVideoList(ShortUserUnlockVideo shortUserUnlockVideo) {
        return shortUserUnlockVideoMapper.selectShortUserUnlockVideoList(shortUserUnlockVideo);
    }

    /**
     * 新增用户已解锁的视频
     *
     * @param shortUserUnlockVideo 用户已解锁的视频
     * @return 结果
     */
    @Override
    public int insertShortUserUnlockVideo(ShortUserUnlockVideo shortUserUnlockVideo) {
        shortUserUnlockVideo.setCreateTime(DateUtils.getNowDate());
        shortUserUnlockVideo.setUpdateTime(DateUtils.getNowDate());
        return shortUserUnlockVideoMapper.insertShortUserUnlockVideo(shortUserUnlockVideo);
    }

    /**
     * 修改用户已解锁的视频
     *
     * @param shortUserUnlockVideo 用户已解锁的视频
     * @return 结果
     */
    @Override
    public int updateShortUserUnlockVideo(ShortUserUnlockVideo shortUserUnlockVideo) {
        shortUserUnlockVideo.setUpdateTime(DateUtils.getNowDate());
        return shortUserUnlockVideoMapper.updateShortUserUnlockVideo(shortUserUnlockVideo);
    }

    /**
     * 批量删除用户已解锁的视频
     *
     * @param ids 需要删除的用户已解锁的视频主键
     * @return 结果
     */
    @Override
    public int deleteShortUserUnlockVideoByIds(Long[] ids) {
        return shortUserUnlockVideoMapper.deleteShortUserUnlockVideoByIds(ids);
    }

    /**
     * 删除用户已解锁的视频信息
     *
     * @param id 用户已解锁的视频主键
     * @return 结果
     */
    @Override
    public int deleteShortUserUnlockVideoById(Long id) {
        return shortUserUnlockVideoMapper.deleteShortUserUnlockVideoById(id);
    }

    @Override
    public List<Integer> findVideoIdByMovieIdAndUser(Integer movieId, String userId) {
        return shortUserUnlockVideoMapper.findVideoIdByMovieIdAndUser(movieId, userId);
    }

    @Override
    public void insertShortUserUnlockVideoList(List<ShortUserUnlockVideo> unlockVideos) {
        shortUserUnlockVideoMapper.insertShortUserUnlockVideoList(unlockVideos);
    }

    @Override
    public void deleteShortUserUnlockVideoByUserIdAndMovieId(Long userId, Long movieId) {
        shortUserUnlockVideoMapper.deleteShortUserUnlockVideoByUserIdAndMovieId(userId, movieId);
    }
}
