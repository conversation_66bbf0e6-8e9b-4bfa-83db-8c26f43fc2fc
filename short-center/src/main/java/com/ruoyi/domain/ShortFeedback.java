package com.ruoyi.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.List;

/**
 * 用户反馈对象 short_feedback
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
public class ShortFeedback extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 选择app
 */
    @Excel(name = "选择app")
    private Long appId;

    /** 选择用户
 */
    @Excel(name = "选择用户")
    private Long userId;

    /** 反馈内容 */
    @Excel(name = "反馈内容")
    private String content;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    @Excel(name = "用户名称")
    private String userName;

    @Excel(name = "应用名称")
    private String appName;

    @Excel(name = "email")
    private String email;

    @Excel(name = "type")
    private String type;

    @Excel(name = "replyType")
    private Integer replyType;

    @Excel(name = "domain")
    private String domain;

    @Excel(name = "isSubscriber")
    private String isSubscriber;

    public String getIsSubscriber() {
        return isSubscriber;
    }

    public void setIsSubscriber(String isSubscriber) {
        this.isSubscriber = isSubscriber;
    }

    private List<ShortUser> shortUserList;

    private List<ShortOrder> shortOrderList;

    public List<ShortUser> getShortUserList() {
        return shortUserList;
    }

    public void setShortUserList(List<ShortUser> shortUserList) {
        this.shortUserList = shortUserList;
    }

    public List<ShortOrder> getShortOrderList() {
        return shortOrderList;
    }

    public void setShortOrderList(List<ShortOrder> shortOrderList) {
        this.shortOrderList = shortOrderList;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public Integer getReplyType() {
        return replyType;
    }

    public void setReplyType(Integer replyType) {
        this.replyType = replyType;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setAppId(Long appId) 
    {
        this.appId = appId;
    }

    public Long getAppId() 
    {
        return appId;
    }

    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }

    public void setContent(String content) 
    {
        this.content = content;
    }

    public String getContent() 
    {
        return content;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("appId", getAppId())
            .append("userId", getUserId())
            .append("content", getContent())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
