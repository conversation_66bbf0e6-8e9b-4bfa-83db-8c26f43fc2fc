package com.ruoyi.app.api.service.impl;

import brevo.ApiClient;
import brevo.Configuration;
import brevo.auth.ApiKeyAuth;
import brevoApi.TransactionalEmailsApi;
import brevoModel.CreateSmtpEmail;
import brevoModel.SendSmtpEmail;
import brevoModel.SendSmtpEmailSender;
import brevoModel.SendSmtpEmailTo;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.KeyPair;
import com.ruoyi.app.api.dto.InitUserDTO;
import com.ruoyi.app.api.service.BusinessFunctionApiService;
import com.ruoyi.app.api.service.ManagerService;
import com.ruoyi.app.api.strategy.UseepayStrategy;
import com.ruoyi.app.api.utils.GenerateRandomCodeUtil;
import com.ruoyi.app.api.vo.MovieDo;
import com.ruoyi.app.api.vo.UserDo;
import com.ruoyi.app.api.vo.VideoDo;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.dto.EmailDTO;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.domain.*;
import com.ruoyi.mapper.*;
import com.ruoyi.service.*;
import com.ruoyi.service.impl.AESEncryption;
import com.ruoyi.service.impl.ImageProcessor;
import com.ruoyi.vo.RenewUserDo;
import org.apache.tika.Tika;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StreamUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.model.PutObjectResponse;
import software.amazon.awssdk.services.s3.model.S3Exception;

import javax.annotation.Resource;
import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ruoyi.common.utils.Threads.sleep;

/**
 * 管理类信息服务实现
 */
@Service
public class ManagerServiceImpl implements ManagerService {

    private static final Logger log = LoggerFactory.getLogger(ManagerServiceImpl.class);

    // URL锁映射，用于确保同一个URL的并发请求被串行化处理
    private final ConcurrentHashMap<String, Object> urlLocks = new ConcurrentHashMap<>();

    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Autowired
    private IShortMovieService shortMovieService;

    @Autowired
    private IShortVideoService shortVideoService;

    @Autowired
    private IShortUserService shortUserService;

    @Autowired
    private IShortVipService shortVipService;

    @Autowired
    private IShortOrderService shortOrderService;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private IShortRunlogService shortRunlogService;

    @Autowired
    private IShortSemLinkService shortSemLinkService;

    @Autowired
    private IShortExtplatsService shortExtplatsService;

    @Value("${payment.use.dev:false}")
    private boolean useDevApi;

    @Value("${payment.api.dev.url:https://api-demo.airwallex.com}")
    private String devApiUrl;

    @Value("${payment.api.prod.url:https://api.airwallex.com}")
    private String prodApiUrl;

    @Autowired
    private IShortRenewSubscribeDataService shortRenewSubscribeDataService;

    @Resource
    private ShortRenewSubscribeDataMapper shortRenewSubscribeDataMapper;

    @Autowired
    private IShortIntermedStatusService shortIntermedStatusService;

    @Value("${cloud.r2.bucket-name}")
    private String bucketName;

    @Value("${cloud.r2.file-prefix}")
    private String filePrefix;

    @Autowired
    private S3Client s3Client;

    @Autowired
    private ShortVideoMapper shortVideoMapper;

    @Value("${test-pid.temp.id}")
    private String testPidTempId;

    @Value("${test-pid-vip.temp.id}")
    private String testPidTempIdVip;

    @Autowired
    private IShortUserRenewLogService shortUserRenewLogService;

    @Autowired
    private BusinessFunctionApiService businessFunctionApiService;

    @Autowired
    private ShortForwartEmailDomainMapper shortForwartEmailDomainMapper;

    @Autowired
    private ShortVideoI18nMapper shortVideoI18nMapper;

    @Autowired
    private IShortMovieI18nService shortMovieI18nService;

    @Autowired
    private IShortAppService shortAppService;

    @Autowired
    private IShortEmailDomainService shortEmailDomainService;

    @Value("${brevo.api-key}")
    private String brevoApiKey;

    @Value("${brevo.sender.email}")
    private String senderEmail;

    @Value("${brevo.sender.name}")
    private String senderName;

    @Autowired
    private IShortPageService shortPageService;

    @Autowired
    private ShortForwardEmailSendMapper shortForwardEmailSendMapper;


    @Autowired
    private UseepayStrategy useepayStrategy;


    @Autowired
    private IShortRenewalRecordService shortRenewalRecordService;

    @Autowired
    private IShortLinkVipService shortLinkVipService;

    public ManagerServiceImpl() {
    }


    @Override
    public AjaxResult addMovie(MovieDo movieDo) {
        Map<String, Object> map = new HashMap<String, Object>();
        ShortMovie shortMovie = shortMovieService.findByName(movieDo.getName());
        if (null != shortMovie) {
            map.put("id", shortMovie.getId());
            return AjaxResult.error(400, "数据已存在", map);
        } else {
            ShortMovie movie = new ShortMovie();
//            movie.setAppId(Long.valueOf(movieDo.getApp_id()));
            movie.setName(movieDo.getName());
            movie.setOldname(movieDo.getOldname());
            if (null != movieDo.getIcon())
                movie.setIcon(movieDo.getIcon());
            else
                movie.setIcon("/media/none.png");
            if (null != movieDo.getIconNoWord())
                movie.setIconNoWord(movieDo.getIconNoWord());
            else
                movie.setIconNoWord("/media/none.png");
            movie.setDirector(movieDo.getDirector());
            movie.setActors(movieDo.getActors());
            movie.setUpTime(movieDo.getUp_time());
            movie.setTime(String.valueOf(movieDo.getTime()));
            movie.setNum(movieDo.getNum());
            movie.setRating(movieDo.getRating());
            movie.setDescription(movieDo.getName());
            movie.setContent(movieDo.getName());
            movie.setUnitCoin(movieDo.getUnit_coin());
            movie.setVipNum("10");
            movie.setSource(movieDo.getSource());
            movie.setIsVip(false);
            movie.setState("0");
            movie.setUnitCoin(200);
            movie.setCreateTime(DateUtils.getNowDate());
            movie.setUpdatetime(DateUtils.getNowDate());
            shortMovieService.addAppMovie(movie);
            map.put("id", movie.getId());


            new Thread(() -> {
                String res = updateIconMovie(movie.getIcon());
                if (StringUtils.isNotEmpty(res)) {
                    shortMovieService.updateIconById(movie.getId(), res);
                }
                String iconNoWord = updateIconMovie(movie.getIconNoWord());
                if (StringUtils.isNotEmpty(iconNoWord)) {
                    shortMovieService.updateIconNoWordById(movie.getId(), iconNoWord);
                }
            }).start();
        }
        return AjaxResult.success("成功", map);

    }

//    @Override
//    public AjaxResult addVideo(VideoDo videoDo) {
//        ShortVideo shortVideo = shortVideoService.findByUrl(videoDo.getUrl());
//        if (null != shortVideo) {
//            return AjaxResult.error(400, shortVideo.getUrl() + " => 数据已存在");
//        } else {
//            ShortVideo video = new ShortVideo();
//            video.setMovieId(StringUtils.isEmpty(videoDo.getMid()) ? null : Long.valueOf(videoDo.getMid()));
//            video.setPic(videoDo.getPic());
//            video.setUrl(videoDo.getUrl());
//            if (StringUtils.isEmpty(videoDo.getNum()))
//                video.setNum(1);
//            else
//                video.setNum(Integer.valueOf(videoDo.getNum()));
//            video.setCoin(80);
//            video.setIsVip(false);
//            video.setState("0");
//            video.setCreateTime(DateUtils.getNowDate());
//            video.setUpdatetime(DateUtils.getNowDate());
//            video.setAddtime(DateUtils.getNowDate());
//            shortVideoService.addAppVideo(video);
//            if(null != video.getId()){
//                ShortVideoI18n shortVideoI18n = new ShortVideoI18n();
//                shortVideoI18n.setVideoId(video.getId());
//                shortVideoI18n.setLanguageCode(videoDo.getLanguageCode());
//                if(StringUtils.isNotEmpty(videoDo.getSubtitleUrl()) && videoDo.getSubtitleUrl().contains("_"))
//                    shortVideoI18n.setSubtitleUrl(videoDo.getSubtitleUrl());
//                shortVideoI18n.setCreateTime(DateUtils.getNowDate());
//                shortVideoI18n.setUpdateTime(DateUtils.getNowDate());
//                if(StringUtils.isNotEmpty(videoDo.getLanguageCode()) && StringUtils.isNotEmpty(videoDo.getSubtitleUrl()) && videoDo.getSubtitleUrl().contains("_"))
//                    shortVideoI18nMapper.insertShortVideoI18n(shortVideoI18n);
//            }
//        }
//        return AjaxResult.success("添加成功");
//    }


    @Override
    @Transactional
    public AjaxResult addVideo(VideoDo videoDo) {
        try {
            // 步骤1: 获取或创建视频记录（处理并发情况）
            ShortVideo shortVideo = getOrCreateVideo(videoDo);

            // 步骤2: 处理字幕信息（无论是新视频还是已存在的视频）
            processSubtitleInfo(videoDo, shortVideo);

            // 步骤3: 统一返回成功
            return AjaxResult.success("操作成功");
        } catch (Exception e) {
            log.error("添加视频失败: {}", e.getMessage(), e);
            return AjaxResult.error(500, "操作失败: " + e.getMessage());
        }
    }

    /**
     * 处理字幕信息
     */
    private void processSubtitleInfo(VideoDo videoDo, ShortVideo shortVideo) {
        // 检查是否有有效的字幕信息需要处理
        boolean hasValidSubtitleInfo = StringUtils.isNotEmpty(videoDo.getLanguageCode())
                && StringUtils.isNotEmpty(videoDo.getSubtitleUrl())
                && videoDo.getSubtitleUrl().contains("_");

        if (!hasValidSubtitleInfo) {
            return;
        }

        if (shortVideo.getId() == null) {
            throw new RuntimeException("无法获取视频ID，操作失败");
        }

        // 检查该影片的该语言是否已审核通过
        if (isMovieLanguageApproved(shortVideo.getMovieId(), videoDo.getLanguageCode())) {
            log.warn("影片ID: {} 的语言: {} 已审核通过，不允许覆盖更新字幕",
                    shortVideo.getMovieId(), videoDo.getLanguageCode());
            return; // 直接返回，不处理字幕更新
        }

        // 使用组合键锁定字幕操作，避免同一视频的同一语言字幕并发操作
        String subtitleLockKey = shortVideo.getId() + ":" + videoDo.getLanguageCode();
        Object subtitleLock = urlLocks.computeIfAbsent(subtitleLockKey, k -> new Object());

        synchronized (subtitleLock) {
            try {
                // 根据视频ID和语言代码查找是否已存在该语言的字幕
                List<ShortVideoI18n> existingI18nList = shortVideoI18nMapper.findByVideoIdAndLanguageCode(
                        shortVideo.getId(), videoDo.getLanguageCode());

                // 如果存在多条重复记录，清理多余的记录，只保留最新的一条
                if (existingI18nList.size() > 1) {
                    log.warn("发现重复的字幕记录，videoId: {}, languageCode: {}, 记录数: {}",
                            shortVideo.getId(), videoDo.getLanguageCode(), existingI18nList.size());

                    // 保留最新的记录（ID最大的），删除其他记录
                    ShortVideoI18n latestRecord = existingI18nList.get(0); // 已按ID降序排列
                    for (int i = 1; i < existingI18nList.size(); i++) {
                        shortVideoI18nMapper.deleteShortVideoI18nById(existingI18nList.get(i).getId());
                        log.info("删除重复的字幕记录，ID: {}", existingI18nList.get(i).getId());
                    }
                    existingI18nList.clear();
                    existingI18nList.add(latestRecord);
                }

                ShortVideoI18n existingI18n = existingI18nList.isEmpty() ? null : existingI18nList.get(0);

                if (existingI18n == null) {
                    // 该语言的字幕不存在 -> 新增
                    insertNewSubtitle(shortVideo.getId(), videoDo);
                } else {
                    // 该语言的字幕已存在 -> 更新
                    existingI18n.setSubtitleUrl(videoDo.getSubtitleUrl());
                    existingI18n.setUpdateTime(DateUtils.getNowDate());
                    shortVideoI18nMapper.updateShortVideoI18n(existingI18n);
                    log.info("成功更新现有字幕记录: videoId={}, languageCode={}",
                            shortVideo.getId(), videoDo.getLanguageCode());
                }
            } finally {
                // 字幕操作完成后，移除字幕锁
                urlLocks.remove(subtitleLockKey);
            }
        }
    }

    /**
     * 检查影片的指定语言是否已审核通过
     *
     * @param movieId      影片ID
     * @param languageCode 语言代码
     * @return true-已审核通过，false-未审核或未通过
     */
    private boolean isMovieLanguageApproved(Long movieId, String languageCode) {
        if (movieId == null || StringUtils.isEmpty(languageCode)) {
            return false;
        }

        try {
            // 查询该影片的该语言审核状态
            ShortMovieI18n query = new ShortMovieI18n();
            query.setMovieId(movieId);
            query.setLanguageCode(languageCode);
            query.setStatus("1"); // 只查询状态为1（通过）的记录

            List<ShortMovieI18n> approvedList = shortMovieI18nService.selectShortMovieI18nList(query);
            return !approvedList.isEmpty();
        } catch (Exception e) {
            log.error("检查影片语言审核状态失败: movieId={}, languageCode={}, error={}",
                    movieId, languageCode, e.getMessage());
            return false; // 查询失败时，允许更新（保守策略）
        }
    }

    /**
     * 插入新字幕记录
     */
    private void insertNewSubtitle(Long videoId, VideoDo videoDo) {
        try {
            ShortVideoI18n newShortVideoI18n = new ShortVideoI18n();
            newShortVideoI18n.setVideoId(videoId);
            newShortVideoI18n.setLanguageCode(videoDo.getLanguageCode());
            newShortVideoI18n.setSubtitleUrl(videoDo.getSubtitleUrl());
            newShortVideoI18n.setCreateTime(DateUtils.getNowDate());
            newShortVideoI18n.setUpdateTime(DateUtils.getNowDate());
            shortVideoI18nMapper.insertShortVideoI18n(newShortVideoI18n);
            log.info("成功插入字幕记录: videoId={}, languageCode={}", videoId, videoDo.getLanguageCode());
        } catch (Exception e) {
            // 检查是否为唯一约束冲突
            String errorMsg = e.getMessage().toLowerCase();
            if (errorMsg.contains("duplicate") || errorMsg.contains("unique") || errorMsg.contains("重复")) {
                log.info("检测到唯一约束冲突，尝试更新现有字幕记录: videoId={}, languageCode={}",
                        videoId, videoDo.getLanguageCode());

                try {
                    // 重新查询并更新现有记录
                    List<ShortVideoI18n> retryList = shortVideoI18nMapper.findByVideoIdAndLanguageCode(
                            videoId, videoDo.getLanguageCode());
                    if (!retryList.isEmpty()) {
                        ShortVideoI18n retryRecord = retryList.get(0);
                        retryRecord.setSubtitleUrl(videoDo.getSubtitleUrl());
                        retryRecord.setUpdateTime(DateUtils.getNowDate());
                        shortVideoI18nMapper.updateShortVideoI18n(retryRecord);
                        log.info("成功更新现有字幕记录: videoId={}, languageCode={}", videoId, videoDo.getLanguageCode());
                    } else {
                        log.error("唯一约束冲突但未找到现有记录: videoId={}, languageCode={}", videoId, videoDo.getLanguageCode());
                        throw new RuntimeException("字幕数据处理异常：唯一约束冲突但未找到现有记录");
                    }
                } catch (Exception updateEx) {
                    log.error("更新现有字幕记录失败: videoId={}, languageCode={}, error={}",
                            videoId, videoDo.getLanguageCode(), updateEx.getMessage());
                    throw new RuntimeException("字幕数据更新失败: " + updateEx.getMessage(), updateEx);
                }
            } else {
                // 非唯一约束冲突的其他异常
                log.error("插入字幕记录时发生未知错误: videoId={}, languageCode={}, error={}",
                        videoId, videoDo.getLanguageCode(), e.getMessage());
                throw new RuntimeException("字幕数据插入失败: " + e.getMessage(), e);
            }
        }
    }

    /**
     * 获取或创建视频记录
     *
     * @param videoDo 视频数据
     * @return 视频记录
     */
    private ShortVideo getOrCreateVideo(VideoDo videoDo) {
        String url = videoDo.getUrl();
        if (StringUtils.isEmpty(url)) {
            throw new IllegalArgumentException("视频URL不能为空");
        }

        // 使用 intern() 确保相同字符串使用同一个对象作为锁
        String lockKey = url.intern();

        // 使用 URL 本身作为锁对象，而不是从Map中获取
        synchronized (lockKey) {
            // 第一次查询：检查是否已存在
            ShortVideo existingVideo = shortVideoService.findByUrl(url);
            if (existingVideo != null) {
                log.debug("找到已存在的视频记录: url={}, id={}", url, existingVideo.getId());
                return existingVideo;
            }

            // 视频不存在，尝试创建新记录
            try {
                ShortVideo shortVideo = createNewVideo(videoDo);
                log.info("成功创建新视频记录: url={}, id={}", url, shortVideo.getId());
                return shortVideo;
            } catch (Exception e) {
                // 创建失败，可能是并发插入导致的唯一约束冲突
                log.warn("创建视频记录失败，尝试重新查询: url={}, error={}", url, e.getMessage());

                // 第二次查询：可能其他线程已经创建了记录
                ShortVideo retryVideo = shortVideoService.findByUrl(url);
                if (retryVideo != null) {
                    log.info("重新查询到视频记录: url={}, id={}", url, retryVideo.getId());
                    return retryVideo;
                }

                // 如果仍然查询不到，抛出异常
                log.error("创建视频记录失败且重新查询也失败: url={}", url);
                throw new RuntimeException("创建视频记录失败: " + e.getMessage(), e);
            }
        }
    }

    /**
     * 创建新视频记录
     */
    private ShortVideo createNewVideo(VideoDo videoDo) {
        ShortVideo shortVideo = new ShortVideo();
        shortVideo.setMovieId(StringUtils.isEmpty(videoDo.getMid()) ? null : Long.valueOf(videoDo.getMid()));
        shortVideo.setPic(videoDo.getPic());
        shortVideo.setUrl(videoDo.getUrl());
        shortVideo.setNum(StringUtils.isEmpty(videoDo.getNum()) ? 1 : Integer.valueOf(videoDo.getNum()));
        shortVideo.setCoin(200);
        shortVideo.setIsVip(false);
        shortVideo.setState("0");
        shortVideo.setCreateTime(DateUtils.getNowDate());
        shortVideo.setUpdatetime(DateUtils.getNowDate());
        shortVideo.setAddtime(DateUtils.getNowDate());

        // 使用覆盖插入，避免重复键冲突
        insertOrUpdateVideo(shortVideo);

        // 异步更新封面图片
        updateVideoIcon(shortVideo);

        return shortVideo;
    }

    /**
     * 插入或更新视频记录（处理重复键冲突）
     */
    private void insertOrUpdateVideo(ShortVideo shortVideo) {
        try {
            // 尝试插入
            shortVideoService.addAppVideo(shortVideo);
            log.debug("成功插入视频记录: url={}", shortVideo.getUrl());
        } catch (Exception e) {
            // 检查是否为重复键冲突
            String errorMsg = e.getMessage();
            if (errorMsg != null && (errorMsg.contains("Duplicate entry") ||
                    errorMsg.contains("duplicate") ||
                    errorMsg.contains("unique") ||
                    errorMsg.contains("重复"))) {
                log.info("检测到重复键冲突，查找并更新现有记录: url={}", shortVideo.getUrl());

                // 查找现有记录并更新
                ShortVideo existingVideo = shortVideoService.findByUrl(shortVideo.getUrl());
                if (existingVideo != null) {
                    log.info("找到现有视频记录，执行更新: url={}, existingId={}",
                            shortVideo.getUrl(), existingVideo.getId());

                    // 手动设置更新字段，避免调用SecurityUtils
                    existingVideo.setPic(shortVideo.getPic());
                    existingVideo.setNum(shortVideo.getNum());
                    existingVideo.setUpdatetime(DateUtils.getNowDate());
                    existingVideo.setUpdateTime(DateUtils.getNowDate());
                    existingVideo.setUpdateBy("api_system"); // API调用标识

                    // 直接使用Mapper更新，避免Service层的用户认证检查
                    shortVideoMapper.updateShortVideo(existingVideo);

                    // 更新当前对象的ID，确保后续逻辑正常
                    shortVideo.setId(existingVideo.getId());
                    shortVideo.setMovieId(existingVideo.getMovieId()); // 确保movieId也正确设置
                    log.info("成功更新现有视频记录: url={}, id={}", shortVideo.getUrl(), existingVideo.getId());
                } else {
                    // 这种情况很少见，但如果发生，我们重新抛出原始异常
                    log.error("重复键冲突但无法找到现有记录: url={}, 重新抛出原始异常", shortVideo.getUrl());
                    throw new RuntimeException("视频记录创建失败，存在重复键冲突但无法定位现有记录: " + shortVideo.getUrl(), e);
                }
            } else {
                // 非重复键冲突的其他异常，继续抛出
                log.error("插入视频记录时发生非重复键冲突异常: url={}, error={}",
                        shortVideo.getUrl(), errorMsg);
                throw e;
            }
        }
    }

    /**
     * 异步更新视频封面
     */
    private void updateVideoIcon(ShortVideo shortVideo) {
        // 使用线程池而不是直接new Thread，避免创建过多线程
        CompletableFuture.runAsync(() -> {
            try {
                String respic = updateIconMovie(shortVideo.getPic());
                if (StringUtils.isNotEmpty(respic)) {
                    shortVideoMapper.updatePicById(shortVideo.getId(), respic);
                }
            } catch (Exception e) {
                log.error("更新视频封面失败: videoId={}, error={}", shortVideo.getId(), e.getMessage());
            }
        });
    }

    @Override
    public AjaxResult getAllSubUsers() {
//        List<ShortUser> list = shortUserService.getAllSubUsers();
//        List<UserDo> targetList =list.stream()
//                .map(source -> {
//                    UserDo userDo = new UserDo();
//                    userDo.setUser_id(source.getId());
//                    userDo.setPrice(String.valueOf(source.getPrice()));
//                    userDo.setExpire_time(source.getExpireTime());
//                    // 其他属性映射
//                    return userDo;
//                }).collect(Collectors.toList());
        List<ShortUser> distinctOrders = subUser("AIRWALLEX",0);
        List<UserDo> targetList = distinctOrders.stream()
                .map(source -> {
                    UserDo userDo = new UserDo();
                    userDo.setUser_id(source.getId());
                    userDo.setPrice(String.valueOf(source.getPrice()));
                    userDo.setExpire_time(source.getExpireTime());
                    // 其他属性映射
                    return userDo;
                }).collect(Collectors.toList());
        return AjaxResult.success("获取所有订阅用户成功, 满足条件的用户有" + targetList.size() + "个", targetList);
    }

    public List<ShortUser> subUser(String payChannel,int payType) {
        List<ShortUser> list = new ArrayList<>();
        if(payType == 0){
            list = shortUserService.getAllByTypeAndExpireTime("订阅续费",payChannel,payType);
            log.info("满足条件的用户数：" + list.size());

            List<Long> userIds = list.stream().map(ShortUser::getId).collect(Collectors.toList());
            List<Long> userIds51 = shortUserRenewLogService.selectShortUserCodeIs51(userIds);
            list = list.stream()
                    .filter(user -> userIds51.contains(user.getId()))
                    .collect(Collectors.toList());
            log.info("过滤后（51|ok）的用户数：" + list.size());
        }


        List<ShortUser> listD = shortUserService.getAllByTypeAndExpireTime("订阅",payChannel,payType);
        log.info("订阅用户数：" + listD.size());

        list.addAll(listD);
        log.info("订阅+续费用户数：" + list.size());

        // 根据userId分组，并选择每组中updateTime最大的记录
        List<ShortUser> distinctOrders = list.stream()
                .collect(Collectors.toMap(
                        ShortUser::getId,      // 以userId作为key
                        order -> order,        // value为order对象本身
                        (existing, replacement) ->
                                existing.getUpdatetime().compareTo(replacement.getUpdatetime()) > 0
                                        ? existing
                                        : replacement  // 保留updateTime更大的记录
                ))
                .values()
                .stream()
                .collect(Collectors.toList());


        List<ShortUser> resub = shortUserService.getRenewSubscribe();

        // --- 新增代码：提取resub中的所有ID ---
        Set<Long> excludeIds = resub.stream()
                .map(ShortUser::getId)
                .collect(Collectors.toSet());
        // --- 修改原合并逻辑 ---
        List<ShortUser> uniqueList = distinctOrders.stream()
                .filter(user -> !excludeIds.contains(user.getId()))  // 过滤掉distinctOrders中与resub重复的ID
                .collect(Collectors.toList());


        uniqueList.sort(Comparator.comparing(ShortUser::getUpdatetime).reversed());

        return uniqueList;
    }

    @Override
    public AjaxResult getAutoSubUser(String uid) throws Exception {
        // ------------------------------------------------------------------
        if (StringUtils.isNotEmpty(uid)) {
            // 处理uid前缀
            if (uid.length() > 4) {
                uid = uid.substring(4);
            }
        }
        ShortUser user = shortUserService.selectShortUserById(Long.valueOf(uid));
        if (null == user) {
            log.error("用户不存在", uid);
            return AjaxResult.error(400, "用户不存在");
        }

        // 检查请求次数
//        if ((null != user.getPushNum() && user.getPushNum() >= 3) || (null != user.getUnsub() && user.getUnsub() == 1)) {
//            if (null != user.getUnsub() && user.getUnsub() != 1)
//                shortUserService.updateUnsub(user.getId());
//            return AjaxResult.error(400, String.format("用户：%d，自动续订失败，用户请求次数超过三次或取消订阅", user.getId()));
//        }
//        if (null != user.getUnsub() && user.getUnsub() == 1) {
//            return AjaxResult.error(400,String.format("用户：%d，自动续订失败，用户取消订阅", user.getId()));
//        }

//        # 判断用户是否在允许的续订时间范围内
//        # push_num 表示已尝试续订的次数,每次尝试续订后会增加7天的等待期
//        # 例如: push_num=1时需等待7天,push_num=2时需等待14天,以此类推
//        if user.push_num and timezone.now() < user.expire_time + timedelta(days=7 * user.push_num):
//        return JsonResponse({'code': 400, 'msg': f'用户：{user.id}，自动续订失败，用户订阅时间超过{7 * user.push_num}天'})

        if (null == user.getExpireTime()) {
            return AjaxResult.error(400, "用户订阅到期时间为空");
        }
        LocalDateTime localDateTime = user.getExpireTime().toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();

        if (null != user.getLastChargeTime())
            localDateTime = user.getLastChargeTime();
        // 检查续订时间范围
//        if (null != user.getPushNum() && user.getPushNum().intValue()>0 && null != user.getExpireTime() && LocalDateTime.now().isBefore(localDateTime.plus(Duration.ofDays(7 * user.getPushNum())))) {
//            return AjaxResult.error(400,"用户："+user.getId()+"，自动续订失败，用户订阅时间超过"+7 * user.getPushNum()+"天");
//        }
//        if (null != user.getPushNum() && user.getPushNum().intValue()>0 && user.getPushNum().intValue()<=10) {
//            user.setSubIntervalDays(1);
//            shortUserService.updateShortUser(user);
//            return AjaxResult.error(400,"用户："+user.getId()+"，自动续订失败，用户订阅时间超过"+1*user.getPushNum()+"天");
//        }
        if (null != user.getPushNum() && user.getPushNum().intValue() >= 10 && user.getPushNum().intValue() < 15 && LocalDateTime.now().isBefore(localDateTime.plus(Duration.ofDays(3)))) {
            user.setSubIntervalDays(3);
            shortUserService.updateShortUser(user);
            return AjaxResult.error(400, "用户：" + user.getId() + "，自动续订失败，用户订阅时间超过" + 3 * user.getPushNum() + "天");
        }
        if (null != user.getPushNum() && user.getPushNum().intValue() >= 15 && user.getPushNum().intValue() < 19 && LocalDateTime.now().isBefore(localDateTime.plus(Duration.ofDays(7)))) {
            user.setSubIntervalDays(7);
            shortUserService.updateShortUser(user);
            return AjaxResult.error(400, "用户：" + user.getId() + "，自动续订失败，用户订阅时间超过" + 7 * user.getPushNum() + "天");
        }
        if (null != user.getPushNum() && user.getPushNum().intValue() >= 19) {
            shortUserService.updateUnsub(user.getId());
            return AjaxResult.error(400, "用户：" + user.getId() + "，取消订阅");
        }
        user.setLastChargeTime(LocalDateTime.now());
        shortUserService.updateShortUser(user);
        // 获取 Token
        String token = shortExtplatsService.getPaymentToken(user.getAppId() != null ? user.getAppId().toString() : null);
        if (token == null || token.isEmpty()) {
            return AjaxResult.error(405, "没有拿到 token 无法发送订单要求");
        }

        if (null != user.getExpireTime()) {
            if (localDateTime.isAfter(LocalDateTime.now().plusDays(1))) {
                return AjaxResult.error(400, "用户不满足续订条件");
            }
        }
        // ------------------------------------------------------------------

        String isOk = user.getExpireTime() + ", 满足条件";


        // 获取用户的最后一次订阅订单
        ShortOrder originalOrder = null;
        ShortOrder lastSubRe = shortOrderService.findFirstByUserAndPay_typeAndStatusOrderByUpdatedAtDesc(user.getId(), "订阅续费", "SUCCEEDED");
        if (null != lastSubRe) {
            originalOrder = lastSubRe;
        } else {
            ShortOrder lastSub = shortOrderService.findFirstByUserAndPay_typeAndStatusOrderByUpdatedAtDesc(user.getId(), "订阅", "SUCCEEDED");
            if (null != lastSub)
                originalOrder = lastSub;
        }

        if (null == originalOrder) {
            return AjaxResult.error(400, "没有找到订阅订单");
        }

        //定死 营销邮件发送后  续费订阅
        if (originalOrder.getAmount().compareTo(new BigDecimal("0.88")) == 0) {
            originalOrder.setLinkId(Long.valueOf("3666"));
            originalOrder.setVipId(Long.valueOf("190"));
            originalOrder.setAmount(new BigDecimal("14.99"));
        }

        ShortVip shortVipTest = shortVipService.selectShortVipById(originalOrder.getVipId());
        if (null != shortVipTest && testPidTempId.equals(String.valueOf(shortVipTest.getPayTemplateId()))) {
            originalOrder.setVipId(Long.valueOf(testPidTempIdVip));
            originalOrder.setAmount(new BigDecimal("29.99"));
        }
        if(null != originalOrder.getOriginalPrice())
            originalOrder.setAmount(originalOrder.getOriginalPrice());

        ShortRenewSubscribeData shortRenewSubscribeData = new ShortRenewSubscribeData();
        shortRenewSubscribeData.setCreateTime(DateUtils.getNowDate());

        LocalDateTime now = LocalDateTime.now(); // 直接获取当前时间
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String formattedDate = now.format(formatter);
        shortRenewSubscribeData.setLogDate(formattedDate);
        shortRenewSubscribeData.setUserId(user.getId());
        shortRenewSubscribeData.setPhoneVersion(user.getPhoneVersion());
        shortRenewSubscribeData.setRenewAmount(originalOrder.getAmount());

        ShortUserRenewLog shortUserRenewLog = new ShortUserRenewLog();
        shortUserRenewLog.setUserId(user.getId());
        shortUserRenewLog.setAmount(originalOrder.getAmount());

        ShortVip shortVip = shortVipService.selectShortVipById(originalOrder.getVipId());
        if (null != shortVip)
            shortRenewSubscribeData.setRenewType(shortVip.getSubscriptionType());

        InitUserDTO shortBrevo = new InitUserDTO();
        shortBrevo.setReceiveEmail(user.getEmail());
        shortBrevo.setAppId(user.getAppId());
        shortBrevo.setPayAccount(String.valueOf(originalOrder.getAmount()));
        if(null == shortBrevo.getLanguageCode())
            shortBrevo.setLanguageCode("en-US");
        List<ShortMovie> shortMovieList = new ArrayList<>();

        // 解析 pay_info
        try {
            // 获取用户支付信息
            String payInfo = user.getPayInfo();

            if (payInfo == null || payInfo.isEmpty()) {
                return AjaxResult.error(400, "用户ID: {" + user.getId() + "} 没有支付信息");
            }
            // 解析支付信息
            String paymentConsentId = null;
            String customerId = null;

            // payInfo是JSON字符串格式的数组
            ObjectMapper mapper = new ObjectMapper();
            List<Map<String, String>> payInfoList = mapper.readValue(payInfo,
                    mapper.getTypeFactory().constructCollectionType(List.class, Map.class));

            if (!payInfoList.isEmpty()) {
                Map<String, String> firstPayInfo = payInfoList.get(0);
                paymentConsentId = firstPayInfo.get("payment_consent_id");
                customerId = firstPayInfo.get("customer_id");
            }

            if (paymentConsentId == null || customerId == null) {
                return AjaxResult.error(400, "用户ID: {" + user.getId() + "} 支付信息不完整");
            }

            // 生成随机订单号
            String merchantOrderId = "Merchant_Order" + GenerateRandomCodeUtil.generateRandomCode();

            // 创建支付意向
            Map<String, Object> payRequestBody = new HashMap<>();
            payRequestBody.put("request_id", merchantOrderId);
            payRequestBody.put("amount", originalOrder.getAmount());
            payRequestBody.put("currency", "USD");
            payRequestBody.put("merchant_order_id", merchantOrderId);
            payRequestBody.put("customer_id", customerId);

            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + token);

            ShortEmailDomain shortEmailDomain = shortEmailDomainService.getrandomDomain(originalOrder.getAppId());
            if(null != shortEmailDomain)
                payRequestBody.put("descriptor", shortEmailDomain.getAppName());

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(payRequestBody, headers);


            ResponseEntity<Map> payResponse = restTemplate.postForEntity(
                    (useDevApi ? devApiUrl : prodApiUrl) + "/api/v1/pa/payment_intents/create",
                    request,
                    Map.class
            );
            log.info("创建意向支付返回：" + payResponse);
            if (payResponse.getStatusCodeValue() == 201 && payResponse.getBody() != null) {
                Map<String, Object> responseBody = payResponse.getBody();

                String paymentIntentId = (String) responseBody.get("id");
                String clientSecret = (String) responseBody.get("client_secret");
                String currency = (String) responseBody.get("currency");
                String status = (String) responseBody.get("status");


                Long now_link = 0L;
                if (null != originalOrder.getLinkId()) {
                    ShortSemLink shortLink = shortSemLinkService.selectShortSemLinkById(originalOrder.getLinkId());
                    if (null != shortLink) {
                        now_link = shortLink.getId();
                    }
                }

                // 创建订单记录
                ShortOrder order = new ShortOrder();
                order.setUserId(user.getId());
                order.setVipId(originalOrder.getVipId());
                order.setAppId(user.getAppId());
                order.setPayType("订阅续费");
                order.setOrdersn(merchantOrderId);
                order.setMerchantOrderId(merchantOrderId);
                order.setPaymentIntentId(paymentIntentId);
                order.setRequestId((String) payRequestBody.get("request_id"));
                order.setPaymentCurrency("USD");
                order.setPaymentAmount(originalOrder.getAmount());
                order.setAmount(originalOrder.getAmount());
                order.setCurrency(currency);
                order.setStatus(status);
                order.setClientSecret(clientSecret);

                order.setAdid(originalOrder.getAdid());
                order.setLinkId(now_link);
                order.setLinkTime(originalOrder.getLinkTime());
                order.setOther(originalOrder.getOther());

                order.setCreatedAt(DateUtils.getNowDate());
                order.setUpdatedAt(DateUtils.getNowDate());
                order.setCreateTime(DateUtils.getNowDate());
                order.setUpdateTime(DateUtils.getNowDate());
                order.setPixelId(originalOrder.getPixelId());
                order.setMarkType(1);
                order.setSubscriptionType(originalOrder.getSubscriptionType());

                //从用户获取userId
                Long linkId = originalOrder.getLinkId();
                //走深链 判断是否使用自定义付费计划
                List<ShortLinkVip> shortLinkVipList = shortLinkVipService.selectByLinkId(linkId);
                //自定义付费计划有配置 且 vipId 取得是自定义付费计划ID
                if(CollectionUtil.isNotEmpty(shortLinkVipList)){
                    order.setVipType("1");
                }else {
                    order.setVipType("0");
                }


                shortOrderService.insertShortOrder(order);

                log.info("为用户 {} 创建订阅意向订单成功: {}", user.getId(), merchantOrderId);

                // 确认付款意向
                Map<String, Object> confirmRequestBody = new HashMap<>();
                confirmRequestBody.put("request_id", generateRandomCode());
                confirmRequestBody.put("payment_consent_id", paymentConsentId);

                HttpEntity<Map<String, Object>> confirmRequest = new HttpEntity<>(confirmRequestBody, headers);

                ResponseEntity<Map> confirmResponse = restTemplate.postForEntity(
                        (useDevApi ? devApiUrl : prodApiUrl) + "/api/v1/pa/payment_intents/" + paymentIntentId + "/confirm",
                        confirmRequest,
                        Map.class
                );
                // 记录日志并处理结果
                ShortRunlog runlog = new ShortRunlog();
                runlog.setType("自动续订");
                runlog.setCreateTime(DateUtils.getNowDate());
                runlog.setUpdateTime(DateUtils.getNowDate());
//                runlog.setA(LocalDateTime.now());


                if ("SUCCEEDED".equals(confirmResponse.getBody().get("status"))) {
                    runlog.setState("1");
                    runlog.setContent(String.format("用户：" + user.getId() + "，自动续订成功，支付金额：" + confirmResponse.getBody().get("amount") + "，支付方式：" + ((Map) ((Map) confirmResponse.getBody().get("latest_payment_attempt")).get("payment_method")).get("type")));
                    runlog.setNote(confirmResponse.getBody().toString());
                    shortRunlogService.insertShortRunlog(runlog);

                    ShortUser shortUser = new ShortUser();
                    shortUser.setId(user.getId());
                    shortUser.setPushNum(0L);
//                    user.setPushNum(0L);
                    shortUserService.updateShortUser(shortUser);

                    ShortRenewSubscribeData subscribeData = shortRenewSubscribeDataService.selectByUserIdStatus(user.getId(), "");
                    shortRenewSubscribeData.setOrderStatus("SUCCEEDED");
                    if (null == subscribeData) {
                        shortRenewSubscribeData.setRenewCount(1);
                        shortRenewSubscribeData.setPayErrorCount(0);
                        List<ShortRenewSubscribeData> saveList = new ArrayList<>();
                        saveList.add(shortRenewSubscribeData);
                        shortRenewSubscribeDataMapper.insertBatch(saveList);
                    } else {
                        if (!formattedDate.equals(subscribeData.getLogDate())) {
                            shortRenewSubscribeData.setRenewCount(subscribeData.getRenewCount() + 1);
                            shortRenewSubscribeData.setPayErrorCount(0);
                            List<ShortRenewSubscribeData> saveList = new ArrayList<>();
                            saveList.add(shortRenewSubscribeData);
                            shortRenewSubscribeDataMapper.insertBatch(saveList);
                        }

                    }

                    shortUserRenewLog.setType(0L);
                    shortUserRenewLog.setContent(String.format("处理完成，" + token + ", 用户id:" + uid + ", " + isOk + ", 续订详细信息：" + confirmResponse.getBody()));
                    shortUserRenewLog.setCode("ok");
                    shortUserRenewLogService.insertShortUserRenewLog(shortUserRenewLog);

                    return AjaxResult.success(String.format("处理完成，" + token + ", 用户id:" + uid + ", " + isOk + ", 续订详细信息：" + confirmResponse.getBody()));
                } else if ("issuer_declined".equals(confirmResponse.getBody().get("code"))) {
                    runlog.setState("0");
                    runlog.setContent(String.format("用户：" + user.getId() + "，自动续订失败，用户卡余额不足"));
                    runlog.setNote(confirmResponse.getBody().toString());
                    shortRunlogService.insertShortRunlog(runlog);

                    user.setPushNum(user.getPushNum() == null ? 1 : user.getPushNum() + 1);
                    shortUserService.updateShortUser(user);


                    ShortRenewSubscribeData subscribeData = shortRenewSubscribeDataService.selectByUserIdStatus(user.getId(), "");
                    shortRenewSubscribeData.setOrderStatus("REQUIRES_PAYMENT_METHOD");
                    if (null == subscribeData) {
                        shortRenewSubscribeData.setRenewCount(1);
                        shortRenewSubscribeData.setPayErrorCount(0);
                        List<ShortRenewSubscribeData> saveList = new ArrayList<>();
                        saveList.add(shortRenewSubscribeData);
                        shortRenewSubscribeDataMapper.insertBatch(saveList);
                    } else {
                        if (!formattedDate.equals(subscribeData.getLogDate())) {
                            shortRenewSubscribeData.setRenewCount(subscribeData.getRenewCount() + 1);
                            shortRenewSubscribeData.setPayErrorCount(0);
                            List<ShortRenewSubscribeData> saveList = new ArrayList<>();
                            saveList.add(shortRenewSubscribeData);
                            shortRenewSubscribeDataMapper.insertBatch(saveList);
                        }

                    }

                    shortUserRenewLog.setType(1L);
                    shortUserRenewLog.setContent(String.format("用户：" + user.getId() + "，自动续订失败，用户卡余额不足，详细信息：" + confirmResponse.getBody()));
                    shortUserRenewLogService.insertShortUserRenewLog(shortUserRenewLog);
                    if(StringUtils.isNotEmpty(user.getEmail()))
                        businessFunctionApiService.sendEmailForInType(shortBrevo, shortMovieList, "brevo","payFailed");
                    return AjaxResult.error(400, String.format("用户：" + user.getId() + "，自动续订失败，用户卡余额不足，详细信息：" + confirmResponse.getBody()));
                } else {
                    runlog.setState("0");
                    runlog.setContent(String.format("用户：" + user.getId() + "，自动续订失败"));
                    runlog.setNote(confirmResponse.getBody().toString());
                    shortRunlogService.insertShortRunlog(runlog);

                    user.setPushNum(user.getPushNum() == null ? 1 : user.getPushNum() + 1);
                    shortUserService.updateShortUser(user);

                    ShortRenewSubscribeData subscribeData = shortRenewSubscribeDataService.selectByUserIdStatus(user.getId(), "");
                    shortRenewSubscribeData.setOrderStatus("REQUIRES_PAYMENT_METHOD");
                    if (null == subscribeData) {
                        shortRenewSubscribeData.setRenewCount(1);
                        shortRenewSubscribeData.setPayErrorCount(0);
                        List<ShortRenewSubscribeData> saveList = new ArrayList<>();
                        saveList.add(shortRenewSubscribeData);
                        shortRenewSubscribeDataMapper.insertBatch(saveList);
                    } else {
                        if (!formattedDate.equals(subscribeData.getLogDate())) {
                            shortRenewSubscribeData.setRenewCount(subscribeData.getRenewCount() + 1);
                            shortRenewSubscribeData.setPayErrorCount(0);
                            List<ShortRenewSubscribeData> saveList = new ArrayList<>();
                            saveList.add(shortRenewSubscribeData);
                            shortRenewSubscribeDataMapper.insertBatch(saveList);
                        }

                    }

                    shortUserRenewLog.setType(1L);
                    shortUserRenewLog.setContent(String.format("用户：" + user.getId() + "，自动续订失败，详细信息：" + confirmResponse.getBody()));
                    shortUserRenewLogService.insertShortUserRenewLog(shortUserRenewLog);
                    if(StringUtils.isNotEmpty(user.getEmail()))
                        businessFunctionApiService.sendEmailForInType(shortBrevo, shortMovieList, "brevo","payFailed");
                    return AjaxResult.error(400, String.format("用户：" + user.getId() + "，自动续订失败，详细信息：" + confirmResponse.getBody()));
                }

            } else {

                user.setPushNum(user.getPushNum() == null ? 1 : user.getPushNum() + 1);
                shortUserService.updateShortUser(user);

                ShortRenewSubscribeData subscribeData = shortRenewSubscribeDataService.selectByUserIdStatus(user.getId(), "");
                shortRenewSubscribeData.setOrderStatus("REQUIRES_PAYMENT_METHOD");
                if (null == subscribeData) {
                    shortRenewSubscribeData.setRenewCount(1);
                    shortRenewSubscribeData.setPayErrorCount(0);
                    List<ShortRenewSubscribeData> saveList = new ArrayList<>();
                    saveList.add(shortRenewSubscribeData);
                    shortRenewSubscribeDataMapper.insertBatch(saveList);
                } else {
                    if (!formattedDate.equals(subscribeData.getLogDate())) {
                        shortRenewSubscribeData.setRenewCount(subscribeData.getRenewCount() + 1);
                        shortRenewSubscribeData.setPayErrorCount(0);
                        List<ShortRenewSubscribeData> saveList = new ArrayList<>();
                        saveList.add(shortRenewSubscribeData);
                        shortRenewSubscribeDataMapper.insertBatch(saveList);
                    }

                }

                shortUserRenewLog.setType(1L);
                shortUserRenewLog.setContent("创建支付意向失败");
                shortUserRenewLogService.insertShortUserRenewLog(shortUserRenewLog);

                return AjaxResult.error(400, "创建支付意向失败");
            }

        } catch (Exception e) {

            user.setPushNum(user.getPushNum() == null ? 1 : user.getPushNum() + 1);
            shortUserService.updateShortUser(user);

            ShortRenewSubscribeData subscribeData = shortRenewSubscribeDataService.selectByUserIdStatus(user.getId(), "");
            shortRenewSubscribeData.setOrderStatus("REQUIRES_PAYMENT_METHOD");
            if (null == subscribeData) {
                shortRenewSubscribeData.setRenewCount(1);
                shortRenewSubscribeData.setPayErrorCount(0);
                List<ShortRenewSubscribeData> saveList = new ArrayList<>();
                saveList.add(shortRenewSubscribeData);
                shortRenewSubscribeDataMapper.insertBatch(saveList);
            } else {
                if (!formattedDate.equals(subscribeData.getLogDate())) {
                    shortRenewSubscribeData.setRenewCount(subscribeData.getRenewCount() + 1);
                    shortRenewSubscribeData.setPayErrorCount(0);
                    List<ShortRenewSubscribeData> saveList = new ArrayList<>();
                    saveList.add(shortRenewSubscribeData);
                    shortRenewSubscribeDataMapper.insertBatch(saveList);
                }

            }


            log.info("e.getMessage()=================== - {}", e.getMessage());

//            String jsonStr = "{\"code\":\"issuer_declined\",\"message\":\"The card issuer declined this transaction. Please refer to the original response code.\",\"trace_id\":\"ed2e1c5860010d331e0c078ec3db6998\",\"provider_original_response_code\":\"03\",\"details\":{\"card_brand\":\"visa\",\"card_type\":\"PREPAID\",\"is_commercial\":false,\"issuing_bank_name\":\"REGIONS BANK\",\"original_response_code\":\"03\"}}";

            try {
                // 1. 提取 JSON 部分
                String message = e.getMessage();
                int jsonStart = message.indexOf('{');
                int jsonEnd = message.lastIndexOf('}');
                String jsonStr = message.substring(jsonStart, jsonEnd + 1);

                // 2. 解析 JSON
                ObjectMapper mapper = new ObjectMapper();
                JsonNode rootNode = mapper.readTree(jsonStr);

                // 3. 获取 original_response_code
                String originalResponseCode = rootNode
                        .path("details")
                        .path("original_response_code")
                        .asText();

                log.info("解析成功，original_response_code=" + originalResponseCode);

                // 4. 设置到对象（示例）
                if (StringUtils.isNotEmpty(originalResponseCode)) {
                    shortUserRenewLog.setCode(originalResponseCode);
                } else {
                    shortUserRenewLog.setCode("x");
                }
            } catch (Exception ex) {
                log.error("解析失败", ex);
            }
//            try {
//                ObjectMapper mapper = new ObjectMapper();
//                JsonNode rootNode = mapper.readTree(e.getMessage());
//                // 从 details 中获取 original_response_code
//                String originalResponseCode = rootNode.path("details")
//                        .path("original_response_code")
//                        .asText();
//
//                log.info("originalResponseCode=================== - {}", originalResponseCode);
////                System.out.println("Original Response Code: " + originalResponseCode);
//                if(StringUtils.isNotEmpty(originalResponseCode) /*&& originalResponseCode.equals("03")*/){
////                    shortUserService.updateUnsub(user.getId());
//                    shortUserRenewLog.setCode(originalResponseCode);
//                }
//            } catch (Exception e1) {
//                e1.printStackTrace();
//            }


            shortUserRenewLog.setType(1L);
            shortUserRenewLog.setContent(e.getMessage());

            // 过滤错误码
//            List<String> list = Arrays.asList("01", "03", "07", "58", "13", "6P", "15", "93", "N7", "19", "30", "06", "88", "55", "72");
//            if (StringUtils.isNotEmpty(shortUserRenewLog.getCode()) && list.contains(shortUserRenewLog.getCode()))
//                shortUserService.updateUnsub(user.getId());

            shortUserRenewLogService.insertShortUserRenewLog(shortUserRenewLog);
            if(StringUtils.isNotEmpty(user.getEmail()))
                businessFunctionApiService.sendEmailForInType(shortBrevo, shortMovieList, "brevo","payFailed");

            e.printStackTrace();
            return AjaxResult.error(400, e.getMessage());
        }

    }


    public AjaxResult getAutoSubUserUseePay(String uid) throws Exception {
        // ------------------------------------------------------------------
        if (StringUtils.isNotEmpty(uid)) {
            // 处理uid前缀
            if (uid.length() > 4) {
                uid = uid.substring(4);
            }
        }
        ShortUser user = shortUserService.selectShortUserById(Long.valueOf(uid));
        if (null == user) {
            log.error("用户不存在", uid);
            return AjaxResult.error(400, "用户不存在");
        }

        // 检查请求次数
//        if ((null != user.getPushNum() && user.getPushNum() >= 3) || (null != user.getUnsub() && user.getUnsub() == 1)) {
//            if (null != user.getUnsub() && user.getUnsub() != 1)
//                shortUserService.updateUnsub(user.getId());
//            return AjaxResult.error(400, String.format("用户：%d，自动续订失败，用户请求次数超过三次或取消订阅", user.getId()));
//        }

        if (null == user.getExpireTime()) {
            return AjaxResult.error(400, "用户订阅到期时间为空");
        }
        LocalDateTime localDateTime = user.getExpireTime().toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();

        if (null != user.getLastChargeTime())
            localDateTime = user.getLastChargeTime();

        if (null != user.getPushNum() && user.getPushNum().intValue() >= 10 && user.getPushNum().intValue() < 15 && LocalDateTime.now().isBefore(localDateTime.plus(Duration.ofDays(3)))) {
            user.setSubIntervalDays(3);
            shortUserService.updateShortUser(user);
            return AjaxResult.error(400, "用户：" + user.getId() + "，自动续订失败，用户订阅时间超过" + 3 * user.getPushNum() + "天");
        }
        if (null != user.getPushNum() && user.getPushNum().intValue() >= 15 && user.getPushNum().intValue() < 19 && LocalDateTime.now().isBefore(localDateTime.plus(Duration.ofDays(7)))) {
            user.setSubIntervalDays(7);
            shortUserService.updateShortUser(user);
            return AjaxResult.error(400, "用户：" + user.getId() + "，自动续订失败，用户订阅时间超过" + 7 * user.getPushNum() + "天");
        }
        if (null != user.getPushNum() && user.getPushNum().intValue() >= 19) {
            shortUserService.updateUnsub(user.getId());
            return AjaxResult.error(400, "用户：" + user.getId() + "，取消订阅");
        }
        user.setLastChargeTime(LocalDateTime.now());
        shortUserService.updateShortUser(user);
        // 获取 Token
//        String token = shortExtplatsService.getPaymentToken(user.getAppId() != null ? user.getAppId().toString() : null);
//        if (token == null || token.isEmpty()) {
//            return AjaxResult.error(405, "没有拿到 token 无法发送订单要求");
//        }

        if (null != user.getExpireTime()) {
            if (localDateTime.isAfter(LocalDateTime.now().plusDays(1))) {
                return AjaxResult.error(400, "用户不满足续订条件");
            }
        }
        // ------------------------------------------------------------------

        String isOk = user.getExpireTime() + ", 满足条件";


        // 获取用户的最后一次订阅订单
//        ShortOrder originalOrder = null;
//        ShortOrder lastSubRe = shortOrderService.findFirstByUserAndPay_typeAndStatusOrderByUpdatedAtDesc(user.getId(), "订阅续费", "SUCCEEDED",null);
//        if (null != lastSubRe) {
//            originalOrder = lastSubRe;
//        } else {
        ShortOrder originalOrder = shortOrderService.findFirstByUserAndPay_typeAndStatusOrderByUpdatedAtDesc(user.getId(), "订阅", "SUCCEEDED");
//        if (null != lastSub)
//            originalOrder = lastSub;
//        }

        if (null == originalOrder) {
            return AjaxResult.error(400, "没有找到订阅订单");
        }

        //定死 营销邮件发送后  续费订阅
        if (originalOrder.getAmount().compareTo(new BigDecimal("0.88")) == 0) {
            originalOrder.setLinkId(Long.valueOf("3666"));
            originalOrder.setVipId(Long.valueOf("190"));
            originalOrder.setAmount(new BigDecimal("14.99"));
        }

        ShortVip shortVipTest = shortVipService.selectShortVipById(originalOrder.getVipId());
        if (null != shortVipTest && testPidTempId.equals(String.valueOf(shortVipTest.getPayTemplateId()))) {
            originalOrder.setVipId(Long.valueOf(testPidTempIdVip));
            originalOrder.setAmount(new BigDecimal("29.99"));
        }
        if(null != originalOrder.getOriginalPrice())
            originalOrder.setAmount(originalOrder.getOriginalPrice());

        ShortRenewSubscribeData shortRenewSubscribeData = new ShortRenewSubscribeData();
        shortRenewSubscribeData.setCreateTime(DateUtils.getNowDate());

        LocalDateTime now = LocalDateTime.now(); // 直接获取当前时间
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String formattedDate = now.format(formatter);
        shortRenewSubscribeData.setLogDate(formattedDate);
        shortRenewSubscribeData.setUserId(user.getId());
        shortRenewSubscribeData.setPhoneVersion(user.getPhoneVersion());
        shortRenewSubscribeData.setRenewAmount(originalOrder.getAmount());

        ShortUserRenewLog shortUserRenewLog = new ShortUserRenewLog();
        shortUserRenewLog.setUserId(user.getId());
        shortUserRenewLog.setAmount(originalOrder.getAmount());

        ShortVip shortVip = shortVipService.selectShortVipById(originalOrder.getVipId());
        if (null != shortVip)
            shortRenewSubscribeData.setRenewType(shortVip.getSubscriptionType());





//        // 记录日志并处理结果
//        ShortRunlog runlog = new ShortRunlog();
//        runlog.setType("自动续订");
//        runlog.setCreateTime(DateUtils.getNowDate());
//        runlog.setUpdateTime(DateUtils.getNowDate());

        Boolean subscribeOrder = useepayStrategy.createSubscribeOrder(originalOrder);
        return AjaxResult.success(subscribeOrder);
        // 解析 pay_info
//        try {
            // 获取用户支付信息
//            String payInfo = user.getPayInfo();

//            if (payInfo == null || payInfo.isEmpty()) {
//                return AjaxResult.error(400, "用户ID: {" + user.getId() + "} 没有支付信息");
//            }
            // 解析支付信息
//            String paymentConsentId = null;
//            String customerId = null;

            // payInfo是JSON字符串格式的数组
//            ObjectMapper mapper = new ObjectMapper();
//            List<Map<String, String>> payInfoList = mapper.readValue(payInfo,
//                    mapper.getTypeFactory().constructCollectionType(List.class, Map.class));
//
//            if (!payInfoList.isEmpty()) {
//                Map<String, String> firstPayInfo = payInfoList.get(0);
//                paymentConsentId = firstPayInfo.get("payment_consent_id");
//                customerId = firstPayInfo.get("customer_id");
//            }
//
//            if (paymentConsentId == null || customerId == null) {
//                return AjaxResult.error(400, "用户ID: {" + user.getId() + "} 支付信息不完整");
//            }

//            // 生成随机订单号
//            String merchantOrderId = "Merchant_Order" + GenerateRandomCodeUtil.generateRandomCode();

//            // 创建支付意向
//            Map<String, Object> payRequestBody = new HashMap<>();
//            payRequestBody.put("request_id", merchantOrderId);
//            payRequestBody.put("amount", originalOrder.getAmount());
//            payRequestBody.put("currency", "USD");
//            payRequestBody.put("merchant_order_id", merchantOrderId);
//            payRequestBody.put("customer_id", customerId);
//
//            HttpHeaders headers = new HttpHeaders();
//            headers.set("Authorization", "Bearer " + token);
//
//            HttpEntity<Map<String, Object>> request = new HttpEntity<>(payRequestBody, headers);
//
//
//            ResponseEntity<Map> payResponse = restTemplate.postForEntity(
//                    (useDevApi ? devApiUrl : prodApiUrl) + "/api/v1/pa/payment_intents/create",
//                    request,
//                    Map.class
//            );
//            log.info("创建意向支付返回：" + payResponse);
//            if (payResponse.getStatusCodeValue() == 201 && payResponse.getBody() != null) {
//                Map<String, Object> responseBody = payResponse.getBody();
//
//                String paymentIntentId = (String) responseBody.get("id");
//                String clientSecret = (String) responseBody.get("client_secret");
//                String currency = (String) responseBody.get("currency");
//                String status = (String) responseBody.get("status");
//
//
//                Long now_link = 0L;
//                if (null != originalOrder.getLinkId()) {
//                    ShortSemLink shortLink = shortSemLinkService.selectShortSemLinkById(originalOrder.getLinkId());
//                    if (null != shortLink) {
//                        now_link = shortLink.getId();
//                    }
//                }

                // 创建订单记录
//                ShortOrder order = new ShortOrder();
//                order.setUserId(user.getId());
//                order.setVipId(originalOrder.getVipId());
//                order.setAppId(user.getAppId());
//                order.setPayType("订阅续费");
//                order.setOrdersn(merchantOrderId);
//                order.setMerchantOrderId(merchantOrderId);
//                order.setPaymentIntentId(paymentIntentId);
//                order.setRequestId((String) payRequestBody.get("request_id"));
//                order.setPaymentCurrency("USD");
//                order.setPaymentAmount(originalOrder.getAmount());
//                order.setAmount(originalOrder.getAmount());
//                order.setCurrency(currency);
//                order.setStatus(status);
//                order.setClientSecret(clientSecret);
//
//                order.setAdid(originalOrder.getAdid());
//                order.setLinkId(now_link);
//                order.setLinkTime(originalOrder.getLinkTime());
//                order.setOther(originalOrder.getOther());
//
//                order.setCreatedAt(DateUtils.getNowDate());
//                order.setUpdatedAt(DateUtils.getNowDate());
//                order.setCreateTime(DateUtils.getNowDate());
//                order.setUpdateTime(DateUtils.getNowDate());
//                order.setPixelId(originalOrder.getPixelId());
//                shortOrderService.insertShortOrder(order);

//                log.info("为用户 {} 创建订阅意向订单成功: {}", user.getId(), merchantOrderId);

                // 确认付款意向
//                Map<String, Object> confirmRequestBody = new HashMap<>();
//                confirmRequestBody.put("request_id", generateRandomCode());
//                confirmRequestBody.put("payment_consent_id", paymentConsentId);
//
//                HttpEntity<Map<String, Object>> confirmRequest = new HttpEntity<>(confirmRequestBody, headers);
//
//                ResponseEntity<Map> confirmResponse = restTemplate.postForEntity(
//                        (useDevApi ? devApiUrl : prodApiUrl) + "/api/v1/pa/payment_intents/" + paymentIntentId + "/confirm",
//                        confirmRequest,
//                        Map.class
//                );


//                if ("SUCCEEDED".equals(confirmResponse.getBody().get("status"))) {
//                    runlog.setState("1");
//                    runlog.setContent(String.format("用户：" + user.getId() + "，自动续订成功，支付金额：" + confirmResponse.getBody().get("amount") + "，支付方式：" + ((Map) ((Map) confirmResponse.getBody().get("latest_payment_attempt")).get("payment_method")).get("type")));
//                    runlog.setNote(confirmResponse.getBody().toString());
//                    shortRunlogService.insertShortRunlog(runlog);
//
//                    ShortUser shortUser = new ShortUser();
//                    shortUser.setId(user.getId());
//                    shortUser.setPushNum(0L);
//                    shortUserService.updateShortUser(shortUser);
//
//                    ShortRenewSubscribeData subscribeData = shortRenewSubscribeDataService.selectByUserIdStatus(user.getId(), "");
//                    shortRenewSubscribeData.setOrderStatus("SUCCEEDED");
//                    if (null == subscribeData) {
//                        shortRenewSubscribeData.setRenewCount(1);
//                        shortRenewSubscribeData.setPayErrorCount(0);
//                        List<ShortRenewSubscribeData> saveList = new ArrayList<>();
//                        saveList.add(shortRenewSubscribeData);
//                        shortRenewSubscribeDataMapper.insertBatch(saveList);
//                    } else {
//                        if (!formattedDate.equals(subscribeData.getLogDate())) {
//                            shortRenewSubscribeData.setRenewCount(subscribeData.getRenewCount() + 1);
//                            shortRenewSubscribeData.setPayErrorCount(0);
//                            List<ShortRenewSubscribeData> saveList = new ArrayList<>();
//                            saveList.add(shortRenewSubscribeData);
//                            shortRenewSubscribeDataMapper.insertBatch(saveList);
//                        }
//
//                    }
//
//                    shortUserRenewLog.setType(0L);
//                    shortUserRenewLog.setContent(String.format("处理完成，" + token + ", 用户id:" + uid + ", " + isOk + ", 续订详细信息：" + confirmResponse.getBody()));
//                    shortUserRenewLog.setCode("ok");
//                    shortUserRenewLogService.insertShortUserRenewLog(shortUserRenewLog);
//
//                    return AjaxResult.success(String.format("处理完成，" + token + ", 用户id:" + uid + ", " + isOk + ", 续订详细信息：" + confirmResponse.getBody()));
//                } else if ("issuer_declined".equals(confirmResponse.getBody().get("code"))) {
//                    runlog.setState("0");
//                    runlog.setContent(String.format("用户：" + user.getId() + "，自动续订失败，用户卡余额不足"));
//                    runlog.setNote(confirmResponse.getBody().toString());
//                    shortRunlogService.insertShortRunlog(runlog);
//
//                    user.setPushNum(user.getPushNum() == null ? 1 : user.getPushNum() + 1);
//                    shortUserService.updateShortUser(user);
//
//
//                    ShortRenewSubscribeData subscribeData = shortRenewSubscribeDataService.selectByUserIdStatus(user.getId(), "");
//                    shortRenewSubscribeData.setOrderStatus("REQUIRES_PAYMENT_METHOD");
//                    if (null == subscribeData) {
//                        shortRenewSubscribeData.setRenewCount(1);
//                        shortRenewSubscribeData.setPayErrorCount(0);
//                        List<ShortRenewSubscribeData> saveList = new ArrayList<>();
//                        saveList.add(shortRenewSubscribeData);
//                        shortRenewSubscribeDataMapper.insertBatch(saveList);
//                    } else {
//                        if (!formattedDate.equals(subscribeData.getLogDate())) {
//                            shortRenewSubscribeData.setRenewCount(subscribeData.getRenewCount() + 1);
//                            shortRenewSubscribeData.setPayErrorCount(0);
//                            List<ShortRenewSubscribeData> saveList = new ArrayList<>();
//                            saveList.add(shortRenewSubscribeData);
//                            shortRenewSubscribeDataMapper.insertBatch(saveList);
//                        }
//
//                    }
//
//                    shortUserRenewLog.setType(1L);
//                    shortUserRenewLog.setContent(String.format("用户：" + user.getId() + "，自动续订失败，用户卡余额不足，详细信息：" + confirmResponse.getBody()));
//                    shortUserRenewLogService.insertShortUserRenewLog(shortUserRenewLog);
//
//                    return AjaxResult.error(400, String.format("用户：" + user.getId() + "，自动续订失败，用户卡余额不足，详细信息：" + confirmResponse.getBody()));
//                } else {
//                    runlog.setState("0");
//                    runlog.setContent(String.format("用户：" + user.getId() + "，自动续订失败"));
//                    runlog.setNote(confirmResponse.getBody().toString());
//                    shortRunlogService.insertShortRunlog(runlog);
//
//                    user.setPushNum(user.getPushNum() == null ? 1 : user.getPushNum() + 1);
//                    shortUserService.updateShortUser(user);
//
//                    ShortRenewSubscribeData subscribeData = shortRenewSubscribeDataService.selectByUserIdStatus(user.getId(), "");
//                    shortRenewSubscribeData.setOrderStatus("REQUIRES_PAYMENT_METHOD");
//                    if (null == subscribeData) {
//                        shortRenewSubscribeData.setRenewCount(1);
//                        shortRenewSubscribeData.setPayErrorCount(0);
//                        List<ShortRenewSubscribeData> saveList = new ArrayList<>();
//                        saveList.add(shortRenewSubscribeData);
//                        shortRenewSubscribeDataMapper.insertBatch(saveList);
//                    } else {
//                        if (!formattedDate.equals(subscribeData.getLogDate())) {
//                            shortRenewSubscribeData.setRenewCount(subscribeData.getRenewCount() + 1);
//                            shortRenewSubscribeData.setPayErrorCount(0);
//                            List<ShortRenewSubscribeData> saveList = new ArrayList<>();
//                            saveList.add(shortRenewSubscribeData);
//                            shortRenewSubscribeDataMapper.insertBatch(saveList);
//                        }
//
//                    }
//
//                    shortUserRenewLog.setType(1L);
//                    shortUserRenewLog.setContent(String.format("用户：" + user.getId() + "，自动续订失败，详细信息：" + confirmResponse.getBody()));
//                    shortUserRenewLogService.insertShortUserRenewLog(shortUserRenewLog);
//
//                    return AjaxResult.error(400, String.format("用户：" + user.getId() + "，自动续订失败，详细信息：" + confirmResponse.getBody()));
//                }
//
//            } else {
//
//                user.setPushNum(user.getPushNum() == null ? 1 : user.getPushNum() + 1);
//                shortUserService.updateShortUser(user);
//
//                ShortRenewSubscribeData subscribeData = shortRenewSubscribeDataService.selectByUserIdStatus(user.getId(), "");
//                shortRenewSubscribeData.setOrderStatus("REQUIRES_PAYMENT_METHOD");
//                if (null == subscribeData) {
//                    shortRenewSubscribeData.setRenewCount(1);
//                    shortRenewSubscribeData.setPayErrorCount(0);
//                    List<ShortRenewSubscribeData> saveList = new ArrayList<>();
//                    saveList.add(shortRenewSubscribeData);
//                    shortRenewSubscribeDataMapper.insertBatch(saveList);
//                } else {
//                    if (!formattedDate.equals(subscribeData.getLogDate())) {
//                        shortRenewSubscribeData.setRenewCount(subscribeData.getRenewCount() + 1);
//                        shortRenewSubscribeData.setPayErrorCount(0);
//                        List<ShortRenewSubscribeData> saveList = new ArrayList<>();
//                        saveList.add(shortRenewSubscribeData);
//                        shortRenewSubscribeDataMapper.insertBatch(saveList);
//                    }
//
//                }
//
//                shortUserRenewLog.setType(1L);
//                shortUserRenewLog.setContent("创建支付意向失败");
//                shortUserRenewLogService.insertShortUserRenewLog(shortUserRenewLog);
//
//                return AjaxResult.error(400, "创建支付意向失败");
//            }
//
//        } catch (Exception e) {
//
//            user.setPushNum(user.getPushNum() == null ? 1 : user.getPushNum() + 1);
//            shortUserService.updateShortUser(user);
//
//            ShortRenewSubscribeData subscribeData = shortRenewSubscribeDataService.selectByUserIdStatus(user.getId(), "");
//            shortRenewSubscribeData.setOrderStatus("REQUIRES_PAYMENT_METHOD");
//            if (null == subscribeData) {
//                shortRenewSubscribeData.setRenewCount(1);
//                shortRenewSubscribeData.setPayErrorCount(0);
//                List<ShortRenewSubscribeData> saveList = new ArrayList<>();
//                saveList.add(shortRenewSubscribeData);
//                shortRenewSubscribeDataMapper.insertBatch(saveList);
//            } else {
//                if (!formattedDate.equals(subscribeData.getLogDate())) {
//                    shortRenewSubscribeData.setRenewCount(subscribeData.getRenewCount() + 1);
//                    shortRenewSubscribeData.setPayErrorCount(0);
//                    List<ShortRenewSubscribeData> saveList = new ArrayList<>();
//                    saveList.add(shortRenewSubscribeData);
//                    shortRenewSubscribeDataMapper.insertBatch(saveList);
//                }

//            }


//            log.info("e.getMessage()=================== - {}", e.getMessage());

//            String jsonStr = "{\"code\":\"issuer_declined\",\"message\":\"The card issuer declined this transaction. Please refer to the original response code.\",\"trace_id\":\"ed2e1c5860010d331e0c078ec3db6998\",\"provider_original_response_code\":\"03\",\"details\":{\"card_brand\":\"visa\",\"card_type\":\"PREPAID\",\"is_commercial\":false,\"issuing_bank_name\":\"REGIONS BANK\",\"original_response_code\":\"03\"}}";

//            try {
//                // 1. 提取 JSON 部分
//                String message = e.getMessage();
//                int jsonStart = message.indexOf('{');
//                int jsonEnd = message.lastIndexOf('}');
//                String jsonStr = message.substring(jsonStart, jsonEnd + 1);
//
//                // 2. 解析 JSON
//                ObjectMapper mapper = new ObjectMapper();
//                JsonNode rootNode = mapper.readTree(jsonStr);
//
//                // 3. 获取 original_response_code
//                String originalResponseCode = rootNode
//                        .path("details")
//                        .path("original_response_code")
//                        .asText();
//
//                log.info("解析成功，original_response_code=" + originalResponseCode);
//
//                // 4. 设置到对象（示例）
//                if (StringUtils.isNotEmpty(originalResponseCode)) {
//                    shortUserRenewLog.setCode(originalResponseCode);
//                } else {
//                    shortUserRenewLog.setCode("x");
//                }
//            } catch (Exception ex) {
//                log.error("解析失败", ex);
//            }
//            try {
//                ObjectMapper mapper = new ObjectMapper();
//                JsonNode rootNode = mapper.readTree(e.getMessage());
//                // 从 details 中获取 original_response_code
//                String originalResponseCode = rootNode.path("details")
//                        .path("original_response_code")
//                        .asText();
//
//                log.info("originalResponseCode=================== - {}", originalResponseCode);
////                System.out.println("Original Response Code: " + originalResponseCode);
//                if(StringUtils.isNotEmpty(originalResponseCode) /*&& originalResponseCode.equals("03")*/){
////                    shortUserService.updateUnsub(user.getId());
//                    shortUserRenewLog.setCode(originalResponseCode);
//                }
//            } catch (Exception e1) {
//                e1.printStackTrace();
//            }


//            shortUserRenewLog.setType(1L);
//            shortUserRenewLog.setContent(e.getMessage());
//
//            // 过滤错误码
//            List<String> list = Arrays.asList("01", "03", "07", "58", "13", "6P", "15", "93", "N7", "19", "30", "06", "88", "55", "72");
//            if (StringUtils.isNotEmpty(shortUserRenewLog.getCode()) && list.contains(shortUserRenewLog.getCode()))
//                shortUserService.updateUnsub(user.getId());
//
//            shortUserRenewLogService.insertShortUserRenewLog(shortUserRenewLog);
//            e.printStackTrace();
//            return AjaxResult.error(400, e.getMessage());
//        }

    }

    @Override
    public AjaxResult getautoSub(String publicKey) throws InterruptedException {

        int count = shortIntermedStatusService.countByName("auto_sub");
        if (count > 0)
            return AjaxResult.success("自动化订阅续费执行中，请勿重复操作");

        ShortIntermedStatus shortIntermedStatus = new ShortIntermedStatus();
        shortIntermedStatus.setName("auto_sub");
        shortIntermedStatusService.insertShortIntermedStatus(shortIntermedStatus);


        try {
            String privateKey = "-----BEGIN OPENSSH PRIVATE KEY-----\n" +
                    "b3BlbnNzaC1rZXktdjEAAAAABG5vbmUAAAAEbm9uZQAAAAAAAAABAAAAMwAAAAtzc2gtZW\n" +
                    "QyNTUxOQAAACDdx+eInOus2G8pGSS+paM8QV5SkQdAXGh/e0Z7pRaDRwAAAJiIp66uiKeu\n" +
                    "rgAAAAtzc2gtZWQyNTUxOQAAACDdx+eInOus2G8pGSS+paM8QV5SkQdAXGh/e0Z7pRaDRw\n" +
                    "AAAEDJW6rFB140gGyRJZM+mqoIFj8HAGrGpnuWj+sCJa/06N3H54ic66zYbykZJL6lozxB\n" +
                    "XlKRB0BcaH97RnulFoNHAAAAE2Zlbmd4aW41MjIwQDE2My5jb20BAg==\n" +
                    "-----END OPENSSH PRIVATE KEY-----";

            // 2. 加载密钥对
            KeyPair keyPair = KeyPair.load(new JSch(), privateKey.getBytes(), null);
            byte[] derivedPublicKeyBlob = keyPair.getPublicKeyBlob();

            // 3. 解析用户公钥（按空格分割并解码Base64）
            String[] publicKeyParts = publicKey.split(" ");
            if (publicKeyParts.length < 2) {
                throw new IllegalArgumentException("Invalid public key format");
            }
            byte[] userPublicKeyBlob = Base64.getDecoder().decode(publicKeyParts[1]);

            // 4. 安全比对（避免时序攻击）
            boolean isMatch = MessageDigest.isEqual(derivedPublicKeyBlob, userPublicKeyBlob);
            if (isMatch) {
                log.info("验证密钥正确");

//                //改完放开注释
                List<ShortUser> list = subUser("AIRWALLEX",0);
                //执行更新设置同步状态为 1
                // shortUserService.batchUpdateSynStatus(list);

                List<UserDo> targetList = list.stream()
                        .map(source -> {
                            UserDo userDo = new UserDo();
                            userDo.setUser_id(source.getId());
                            userDo.setPrice(String.valueOf(source.getPrice()));
                            userDo.setExpire_time(source.getExpireTime());
                            userDo.setApp_id(source.getAppId());
                            userDo.setEmail(source.getEmail());
                            // 其他属性映射
                            return userDo;
                        }).collect(Collectors.toList());

                log.info("自动订阅获取到的所有数据：" + targetList.size());
//                List<Long> userIds = targetList.stream().map(UserDo::getUser_id).collect(Collectors.toList());

                List<UserDo> newtargetList  = new ArrayList<>();
                //邮箱为空的   --直接加入到新的list中
                List<Long> emailUserIds = targetList.stream()
                        .filter(user -> user.getEmail() != null && user.getEmail().isEmpty())
                        .map(UserDo::getUser_id)
                        .collect(Collectors.toList());

                List<Long> emailUserIds1 = targetList.stream()
                        .filter(user -> user.getEmail() == null)
                        .map(UserDo::getUser_id)
                        .collect(Collectors.toList());


                List<UserDo> matchedUsers = targetList.stream()
                        .filter(user -> emailUserIds.contains(user.getUser_id()))
                        .collect(Collectors.toList());
                newtargetList.addAll(matchedUsers);
                List<UserDo> matchedUsers1 = targetList.stream()
                        .filter(user -> emailUserIds1.contains(user.getUser_id()))
                        .collect(Collectors.toList());
                newtargetList.addAll(matchedUsers1);


//              邮箱不为空的---
                List<Long> emailnotnullUserIds = targetList.stream()
                        .filter(user -> user.getEmail() != null && !user.getEmail().isEmpty()) // 过滤email不为空的UserDo
                        .map(UserDo::getUser_id) // 提取user_id
                        .collect(Collectors.toList());
                //
//                System.out.println(emailnotnullUserIds);
                //这里是需要加入到新的list中的---筛选掉 同邮箱 续订时间未到的
                List<Long> resList =  shortUserService.getResList(emailnotnullUserIds);
//                System.out.println(resList);

                List<UserDo> matchedUsers2 = targetList.stream()
                        .filter(user -> resList.contains(user.getUser_id()))
                        .collect(Collectors.toList());
                newtargetList.addAll(matchedUsers2);
//                System.out.println(newtargetList);

                // 主动分片 10个一组
                List<List<UserDo>> partition = Lists.partition(newtargetList, 10);
                CountDownLatch latch = new CountDownLatch(partition.size());
                for (List<UserDo> userDos : partition) {
                    threadPoolTaskExecutor.execute(() -> {
                        try {
                            for (int i = 0; i < userDos.size(); i++) {
                                log.info("总条数【"+ newtargetList.size() +"】|【"+ Thread.currentThread().getName() +"】线程 | 执行id：【" + userDos.get(i).getUser_id() + "】| 当前线程条数:【" + userDos.size() + "】| i=====:" + i);
                                Thread.sleep(2000);
                                getAutoSubUser(1111 + String.valueOf(userDos.get(i).getUser_id()));
                            }
                        } catch (Exception e) {
                            log.error("执行续订异常：", e);
                        } finally {
                            latch.countDown();
                        }
                    });
                }
                latch.await();
            }

            shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
            return AjaxResult.success("操作成功");

        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("status", "error");
            error.put("message", "Validation failed: " + e.getMessage());
            shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
            return AjaxResult.error("操作失败");
        }


//        return AjaxResult.success("操作成功");
    }

    public AjaxResult getautoSubUseePay(String publicKey) throws InterruptedException {

        int count = shortIntermedStatusService.countByName("getautoSubUseePay");
        if (count > 0)
            return AjaxResult.success("自动化订阅续费执行中，请勿重复操作");

        ShortIntermedStatus shortIntermedStatus = new ShortIntermedStatus();
        shortIntermedStatus.setName("getautoSubUseePay");
        shortIntermedStatusService.insertShortIntermedStatus(shortIntermedStatus);


        try {
            String privateKey = "-----BEGIN OPENSSH PRIVATE KEY-----\n" +
                    "b3BlbnNzaC1rZXktdjEAAAAABG5vbmUAAAAEbm9uZQAAAAAAAAABAAAAMwAAAAtzc2gtZW\n" +
                    "QyNTUxOQAAACDdx+eInOus2G8pGSS+paM8QV5SkQdAXGh/e0Z7pRaDRwAAAJiIp66uiKeu\n" +
                    "rgAAAAtzc2gtZWQyNTUxOQAAACDdx+eInOus2G8pGSS+paM8QV5SkQdAXGh/e0Z7pRaDRw\n" +
                    "AAAEDJW6rFB140gGyRJZM+mqoIFj8HAGrGpnuWj+sCJa/06N3H54ic66zYbykZJL6lozxB\n" +
                    "XlKRB0BcaH97RnulFoNHAAAAE2Zlbmd4aW41MjIwQDE2My5jb20BAg==\n" +
                    "-----END OPENSSH PRIVATE KEY-----";

            // 2. 加载密钥对
            KeyPair keyPair = KeyPair.load(new JSch(), privateKey.getBytes(), null);
            byte[] derivedPublicKeyBlob = keyPair.getPublicKeyBlob();

            // 3. 解析用户公钥（按空格分割并解码Base64）
            String[] publicKeyParts = publicKey.split(" ");
            if (publicKeyParts.length < 2) {
                throw new IllegalArgumentException("Invalid public key format");
            }
            byte[] userPublicKeyBlob = Base64.getDecoder().decode(publicKeyParts[1]);

            // 4. 安全比对（避免时序攻击）
            boolean isMatch = MessageDigest.isEqual(derivedPublicKeyBlob, userPublicKeyBlob);
            if (isMatch) {
                log.info("验证密钥正确");

//                //改完放开注释
                List<ShortUser> list = subUser("USEEPAY",1);
                //执行更新设置同步状态为 1
                // shortUserService.batchUpdateSynStatus(list);

                List<UserDo> targetList = list.stream()
                        .map(source -> {
                            UserDo userDo = new UserDo();
                            userDo.setUser_id(source.getId());
                            userDo.setPrice(String.valueOf(source.getPrice()));
                            userDo.setExpire_time(source.getExpireTime());
                            userDo.setApp_id(source.getAppId());
                            userDo.setEmail(source.getEmail());
                            // 其他属性映射
                            return userDo;
                        }).collect(Collectors.toList());

                log.info("自动订阅获取到的所有数据：" + targetList.size());

//                List<UserDo> newtargetList  = new ArrayList<>();
//                //邮箱为空的   --直接加入到新的list中
//                List<Long> emailUserIds = targetList.stream()
//                        .filter(user -> user.getEmail() != null && user.getEmail().isEmpty())
//                        .map(UserDo::getUser_id)
//                        .collect(Collectors.toList());
//
//                List<Long> emailUserIds1 = targetList.stream()
//                        .filter(user -> user.getEmail() == null)
//                        .map(UserDo::getUser_id)
//                        .collect(Collectors.toList());
//                List<UserDo> matchedUsers = targetList.stream()
//                        .filter(user -> emailUserIds.contains(user.getUser_id()))
//                        .collect(Collectors.toList());
//                newtargetList.addAll(matchedUsers);
//                List<UserDo> matchedUsers1 = targetList.stream()
//                        .filter(user -> emailUserIds1.contains(user.getUser_id()))
//                        .collect(Collectors.toList());
//                newtargetList.addAll(matchedUsers1);
////              邮箱不为空的---
//                List<Long> emailnotnullUserIds = targetList.stream()
//                        .filter(user -> user.getEmail() != null && !user.getEmail().isEmpty()) // 过滤email不为空的UserDo
//                        .map(UserDo::getUser_id) // 提取user_id
//                        .collect(Collectors.toList());
//                //
//                //这里是需要加入到新的list中的---筛选掉 同邮箱 续订时间未到的
//                List<Long> resList =  shortUserService.getResList(emailnotnullUserIds);
//
//                List<UserDo> matchedUsers2 = targetList.stream()
//                        .filter(user -> resList.contains(user.getUser_id()))
//                        .collect(Collectors.toList());
//                newtargetList.addAll(matchedUsers2);

                // 主动分片 10个一组
                List<List<UserDo>> partition = Lists.partition(targetList, 10);
                CountDownLatch latch = new CountDownLatch(partition.size());
                for (List<UserDo> userDos : partition) {
                    threadPoolTaskExecutor.execute(() -> {
                        try {
                            for (int i = 0; i < userDos.size(); i++) {
                                log.info("总条数【"+ targetList.size() +"】|【"+ Thread.currentThread().getName() +"】线程 | 执行id：【" + userDos.get(i).getUser_id() + "】| 当前线程条数:【" + userDos.size() + "】| i=====:" + i);
                                Thread.sleep(2000);
                                getAutoSubUserUseePay(1111 + String.valueOf(userDos.get(i).getUser_id()));
                            }
                        } catch (Exception e) {
                            log.error("执行续订异常：", e);
                        } finally {
                            latch.countDown();
                        }
                    });
                }
                latch.await();
            }

            shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
            return AjaxResult.success("操作成功");

        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("status", "error");
            error.put("message", "Validation failed: " + e.getMessage());
            shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
            return AjaxResult.error("操作失败");
        }


//        return AjaxResult.success("操作成功");
    }

    private List<RenewUserDo> filterOtherConditions(List<UserDo> targetList) throws InterruptedException {
        List<RenewUserDo> list = new ArrayList<>();

        List<List<UserDo>> partition = Lists.partition(targetList, 10);
        CountDownLatch latch = new CountDownLatch(partition.size());
        for (List<UserDo> userDos : partition) {
            threadPoolTaskExecutor.execute(() -> {
                try {
                    for (int i = 0; i < userDos.size(); i++) {
                        String uid = 1111 + String.valueOf(userDos.get(i).getUser_id());
                        if (StringUtils.isNotEmpty(uid)) {
                            // 处理uid前缀
                            if (uid.length() > 4) {
                                uid = uid.substring(4);
                            }
                        }
                        ShortUser user = shortUserService.selectShortUserById(Long.valueOf(uid));
                        if (null == user) {
                            log.error("用户不存在", uid);
                            return;
                        }
                        // 检查请求次数
                        if ((null != user.getUnsub() && user.getUnsub() == 1)) {
                            if (null != user.getUnsub() && user.getUnsub() != 1) {
                                shortUserService.updateUnsub(user.getId());
                            }
                            log.error("用户：" + user.getId() + "，自动续订失败，用户请求次数超过三次或取消订阅");
                            return;
                        }
                        if (null == user.getExpireTime()) {
                            log.error("用户：" + user.getId() + "，自动续订失败，用户订阅到期时间为空");
                            return;
                        }
                        LocalDateTime localDateTime = user.getExpireTime().toInstant()
                                .atZone(ZoneId.systemDefault())
                                .toLocalDateTime();
                        if (null != user.getLastChargeTime()) {
                            localDateTime = user.getLastChargeTime();
                        }
                        if (null != user.getPushNum() && user.getPushNum().intValue() >= 10 && user.getPushNum().intValue() < 15 && LocalDateTime.now().isBefore(localDateTime.plus(Duration.ofDays(3)))) {
                            user.setSubIntervalDays(3);
                            shortUserService.updateShortUser(user);
                            log.error("用户：" + user.getId() + "，自动续订失败，用户订阅时间超过" + 3 * user.getPushNum() + "天");
                            return;
                        }
                        if (null != user.getPushNum() && user.getPushNum().intValue() >= 15 && user.getPushNum().intValue() < 19 && LocalDateTime.now().isBefore(localDateTime.plus(Duration.ofDays(7)))) {
                            user.setSubIntervalDays(7);
                            shortUserService.updateShortUser(user);
                            log.error("用户：" + user.getId() + "，自动续订失败，用户订阅时间超过" + 7 * user.getPushNum() + "天");
                            return;
                        }
                        if (null != user.getPushNum() && user.getPushNum().intValue() >= 19) {
                            shortUserService.updateUnsub(user.getId());
                            log.error("用户：" + user.getId() + "，取消订阅");
                            return;
                        }
                        String token = shortExtplatsService.getPaymentToken(user.getAppId() != null ? user.getAppId().toString() : null);
                        if (token == null || token.isEmpty()) {
                            log.error("没有拿到 token 无法发送订单要求");
                            return;
                        }
                        if (null != user.getExpireTime()) {
                            if (localDateTime.isAfter(LocalDateTime.now().plusDays(1))) {
                                log.error("用户：" + user.getId() + "，自动续订失败，用户订阅时间未到期");
                                return;
                            }
                        }
                        // 获取续订用户code
                        RenewUserDo renewUserDo = new RenewUserDo();
                        renewUserDo.setUserId(userDos.get(i).getUser_id());
                        renewUserDo.setPrice(userDos.get(i).getPrice());
                        renewUserDo.setExpireTime(userDos.get(i).getExpire_time());
                        String code = shortUserRenewLogService.selectShortUserCodeByUserId(user.getId());
                        if(StringUtils.isNotEmpty(code)){
                            renewUserDo.setCode(code);
                        }else {
                            renewUserDo.setCode("UNKNOW");
                        }
                        renewUserDo.setExecuteDate(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(LocalDateTime.now().withMinute(0).withSecond(0).withNano(0)));
                        renewUserDo.setCreateTime(String.valueOf(LocalDateTime.now()));
                        list.add(renewUserDo);
                    }
                } catch (Exception e) {
                    log.error("过滤异常：", e);
                } finally {
                    latch.countDown();
                }
            });
        }
        latch.await();
        return list;
    }

    @Override
    public AjaxResult testAllSub(String publicKey) {

        int count = shortIntermedStatusService.countByName("auto_sub");
        if (count > 0)
            return AjaxResult.success("自动化订阅续费执行中，请勿重复操作");

        ShortIntermedStatus shortIntermedStatus = new ShortIntermedStatus();
        shortIntermedStatus.setName("auto_sub");
        shortIntermedStatusService.insertShortIntermedStatus(shortIntermedStatus);


        try {
            String privateKey = "-----BEGIN OPENSSH PRIVATE KEY-----\n" +
                    "b3BlbnNzaC1rZXktdjEAAAAABG5vbmUAAAAEbm9uZQAAAAAAAAABAAAAMwAAAAtzc2gtZW\n" +
                    "QyNTUxOQAAACDdx+eInOus2G8pGSS+paM8QV5SkQdAXGh/e0Z7pRaDRwAAAJiIp66uiKeu\n" +
                    "rgAAAAtzc2gtZWQyNTUxOQAAACDdx+eInOus2G8pGSS+paM8QV5SkQdAXGh/e0Z7pRaDRw\n" +
                    "AAAEDJW6rFB140gGyRJZM+mqoIFj8HAGrGpnuWj+sCJa/06N3H54ic66zYbykZJL6lozxB\n" +
                    "XlKRB0BcaH97RnulFoNHAAAAE2Zlbmd4aW41MjIwQDE2My5jb20BAg==\n" +
                    "-----END OPENSSH PRIVATE KEY-----";

            // 2. 加载密钥对
            KeyPair keyPair = KeyPair.load(new JSch(), privateKey.getBytes(), null);
            byte[] derivedPublicKeyBlob = keyPair.getPublicKeyBlob();

            // 3. 解析用户公钥（按空格分割并解码Base64）
            String[] publicKeyParts = publicKey.split(" ");
            if (publicKeyParts.length < 2) {
                throw new IllegalArgumentException("Invalid public key format");
            }
            byte[] userPublicKeyBlob = Base64.getDecoder().decode(publicKeyParts[1]);

            // 4. 安全比对（避免时序攻击）
            boolean isMatch = MessageDigest.isEqual(derivedPublicKeyBlob, userPublicKeyBlob);
            if (isMatch) {
                log.info("验证密钥正确");
                sleep(8000);
                List<ShortUser> distinctOrders = subUser("AIRWALLEX",0);
                List<UserDo> targetList = distinctOrders.stream()
                        .map(source -> {
                            UserDo userDo = new UserDo();
                            userDo.setUser_id(source.getId());
                            userDo.setPrice(String.valueOf(source.getPrice()));
                            userDo.setExpire_time(source.getExpireTime());
                            // 其他属性映射
                            return userDo;
                        }).collect(Collectors.toList());

                shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
                return AjaxResult.success("操作成功=" + targetList);
            }
            shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
            return AjaxResult.success("操作成功");

        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("status", "error");
            error.put("message", "Validation failed: " + e.getMessage());
            shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
            return AjaxResult.error("操作失败");
        }
    }

    @Override
    public AjaxResult updateIcon(String publicKey) {
        int count = shortIntermedStatusService.countByName("updateIcon");
        if (count > 0)
            return AjaxResult.success("处理封面图，请勿重复操作");

        ShortIntermedStatus shortIntermedStatus = new ShortIntermedStatus();
        shortIntermedStatus.setName("updateIcon");
        shortIntermedStatusService.insertShortIntermedStatus(shortIntermedStatus);


        try {
            String privateKey = "-----BEGIN OPENSSH PRIVATE KEY-----\n" +
                    "b3BlbnNzaC1rZXktdjEAAAAABG5vbmUAAAAEbm9uZQAAAAAAAAABAAAAMwAAAAtzc2gtZW\n" +
                    "QyNTUxOQAAACDdx+eInOus2G8pGSS+paM8QV5SkQdAXGh/e0Z7pRaDRwAAAJiIp66uiKeu\n" +
                    "rgAAAAtzc2gtZWQyNTUxOQAAACDdx+eInOus2G8pGSS+paM8QV5SkQdAXGh/e0Z7pRaDRw\n" +
                    "AAAEDJW6rFB140gGyRJZM+mqoIFj8HAGrGpnuWj+sCJa/06N3H54ic66zYbykZJL6lozxB\n" +
                    "XlKRB0BcaH97RnulFoNHAAAAE2Zlbmd4aW41MjIwQDE2My5jb20BAg==\n" +
                    "-----END OPENSSH PRIVATE KEY-----";

            // 2. 加载密钥对
            KeyPair keyPair = KeyPair.load(new JSch(), privateKey.getBytes(), null);
            byte[] derivedPublicKeyBlob = keyPair.getPublicKeyBlob();

            // 3. 解析用户公钥（按空格分割并解码Base64）
            String[] publicKeyParts = publicKey.split(" ");
            if (publicKeyParts.length < 2) {
                throw new IllegalArgumentException("Invalid public key format");
            }
            byte[] userPublicKeyBlob = Base64.getDecoder().decode(publicKeyParts[1]);

            // 4. 安全比对（避免时序攻击）
            boolean isMatch = MessageDigest.isEqual(derivedPublicKeyBlob, userPublicKeyBlob);
            if (isMatch) {
                log.info("验证密钥正确-处理封面图");

//                ShortMovie shortMovie = shortMovieService.selectShortMovieById(32L);
                List<ShortMovie> shortMovieList = shortMovieService.getAll();
                for (ShortMovie shortMovie : shortMovieList) {
                    String res = updateIconMovie(shortMovie.getIcon());
                    if (StringUtils.isNotBlank(res)) {
                        shortMovieService.updateIconById(shortMovie.getId(), res);

                        List<ShortVideo> shortVideoList = shortVideoMapper.findByMovieId(shortMovie.getId());
                        for (ShortVideo shortVideo : shortVideoList) {
                            String respic = updateIconMovie(shortVideo.getPic());
                            if (StringUtils.isNotBlank(respic)) {
                                shortVideoMapper.updatePicById(shortVideo.getId(), respic);
                            }
                        }

                    }
                }


                shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
                return AjaxResult.success("操作成功=");
            }
            shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
            return AjaxResult.success("操作成功");

        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("status", "error");
            error.put("message", "Validation failed: " + e.getMessage());
            shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
            return AjaxResult.error("操作失败");
        }
    }

    @Override
    public AjaxResult updateValidStatus(String uid, String linkId) {
        if (StringUtils.isNotEmpty(uid)) {
            // 处理uid前缀
            if (uid.length() > 4) {
                uid = uid.substring(4);
            }
        }
        ShortUser user = shortUserService.selectShortUserById(Long.valueOf(uid));
        if (null != user) {
            if (StrUtil.isNotEmpty(linkId)) {
                if (Objects.isNull(user.getEmailBindDate())) {
                    user.setEmailBindDate(LocalDate.now());
                }
                if (Objects.isNull(user.getEmailBindLink())) {
                    user.setEmailBindLink(Long.parseLong(linkId));
                }
            }

            // 检查用户原来的validStatus状态
            Integer previousValidStatus = user.getValidStatus();

            // 设置用户为正式注册用户
            user.setValidStatus(1);
            shortUserService.updateShortUser(user);

            log.info("用户validStatus已更新: userId={}, 原状态={}, 新状态=1",
                    user.getId(), previousValidStatus);

            // 如果用户之前不是正式注册用户(validStatus != 1)，现在变成正式用户，则触发像素回传
            if (previousValidStatus == null || previousValidStatus != 1) {
                // 触发Facebook像素回传（相当于正式注册）
                triggerRegistrationPixel(user);
                log.info("用户首次成为正式注册用户，触发Facebook像素回传: userId={}", user.getId());
            } else {
                log.info("用户已经是正式注册用户，无需重复触发像素回传: userId={}", user.getId());
            }
        }
        return AjaxResult.success("成功");
    }

    @Override
    public AjaxResult getForwardEmail(String nid) {
        ShortForwartEmailDomain shortForwartEmailDomain = new ShortForwartEmailDomain();
        shortForwartEmailDomain.setAppId(Long.valueOf(nid));
        List<ShortForwartEmailDomain> list = shortForwartEmailDomainMapper.selectShortForwartEmailDomainList(shortForwartEmailDomain);
        if (list != null && !list.isEmpty()) {
            for (ShortForwartEmailDomain shortForwartEmailDomain1 : list) {
                shortForwartEmailDomain1.setPassword(null);
            }
        }
        return AjaxResult.success(list);
    }

    @Override
    public AjaxResult addVideoI18n(VideoDo videoDo) {
        ShortVideoI18n shortVideoI18n = shortVideoI18nMapper.findByUrl(videoDo.getSubtitleUrl());
        if (null != shortVideoI18n) {
            return AjaxResult.error(400, shortVideoI18n.getSubtitleUrl() + " => 数据已存在");
        } else {
            if (StringUtils.isEmpty(videoDo.getSubtitleUrl()))
                return AjaxResult.error(400, "字幕url不能为空");

            if (videoDo.getSubtitleUrl().contains("_")) {
                String[] parts = videoDo.getSubtitleUrl().substring(videoDo.getSubtitleUrl().lastIndexOf("/") + 1).split("[_.]");  // 按 _ 或 . 分割
                String number = parts[1];  // 提取中间的数字部分

                ShortVideo shortVideo = new ShortVideo();
                shortVideo.setNum(Integer.parseInt(number));
                shortVideo.setMovieId(Long.valueOf(videoDo.getMovieId()));
                List<ShortVideo> shortVideoList = shortVideoMapper.selectShortVideoList(shortVideo);
                if (!shortVideoList.isEmpty()) {
                    // 检查该影片的该语言是否已审核通过
                    if (isMovieLanguageApproved(Long.valueOf(videoDo.getMovieId()), videoDo.getLanguageCode())) {
                        log.warn("影片ID: {} 的语言: {} 已审核通过，不允许添加字幕",
                                videoDo.getMovieId(), videoDo.getLanguageCode());
                        return AjaxResult.error(400, String.format("影片ID: %s 的语言: %s 已审核通过，不允许覆盖更新字幕数据",
                                videoDo.getMovieId(), videoDo.getLanguageCode()));
                    }

                    ShortVideoI18n newShortVideoI18n = new ShortVideoI18n();
                    newShortVideoI18n.setVideoId(shortVideoList.get(0).getId());
                    newShortVideoI18n.setLanguageCode(videoDo.getLanguageCode());
                    newShortVideoI18n.setSubtitleUrl(videoDo.getSubtitleUrl());
                    newShortVideoI18n.setCreateTime(DateUtils.getNowDate());
                    newShortVideoI18n.setUpdateTime(DateUtils.getNowDate());
                    shortVideoI18nMapper.insertShortVideoI18n(newShortVideoI18n);
                }
            }

        }
        return AjaxResult.success("添加成功");
    }

    @Override
    public AjaxResult getByAppId(String nid) {
        return AjaxResult.success(shortAppService.getByAppId(nid));
    }

    @Override
    public AjaxResult updateUnsub(String uid) throws Exception {
        if (StringUtils.isNotEmpty(uid)) {
            // 处理uid前缀
            if (uid.length() > 4) {
                uid = uid.substring(4);
            }
        }
        shortUserService.updateUnsubSource(Long.valueOf(uid),"订阅记录取消");
        int res = shortUserService.updateUnsub(Long.valueOf(uid));

//        ShortUser user = shortUserService.selectShortUserById(Long.valueOf(uid));
//        InitUserDTO shortBrevo = new InitUserDTO();
//        shortBrevo.setReceiveEmail(user.getEmail());
//        shortBrevo.setAppId(user.getAppId());
//        if(null == shortBrevo.getLanguageCode())
//            shortBrevo.setLanguageCode("en-US");
//        if(null != user.getLinkidId()){
//            ShortSemLink shortSemLink = shortSemLinkService.selectShortSemLinkById(user.getLinkidId());
//            if(null != shortSemLink && null != shortSemLink.getLanguage())
//                shortBrevo.setLanguageCode(shortSemLink.getLanguage());
//        }
//        shortBrevo.setUserId(String.valueOf(user.getId()));
//        shortBrevo.setUniqueId(user.getUniqueId());
//        shortBrevo.setExpireTime(user.getExpireTime());
//        ShortMovie shortMovie = shortMovieService.selectShortMovieById(59L);
        return AjaxResult.success(res);
    }

    /**
     * 触发用户注册的Facebook像素回传
     *
     * @param user 用户信息
     */
    private void triggerRegistrationPixel(ShortUser user) {
        try {
            log.info("开始触发用户注册像素回传: userId={}, source={}", user.getId(), user.getSource());

            // 调用BusinessFunctionApiService中的voFacebook方法
            if (businessFunctionApiService != null) {
                businessFunctionApiService.triggerFacebookPixel(user);
                log.info("Facebook像素回传触发完成: userId={}", user.getId());
            } else {
                log.error("BusinessFunctionApiService未正确注入，无法触发像素回传: userId={}", user.getId());
            }

        } catch (Exception e) {
            log.error("触发用户注册像素回传失败: userId={}, error={}", user.getId(), e.getMessage(), e);

            // 记录失败日志
            ShortRunlog runlog = new ShortRunlog();
            runlog.setType("PIXEL回传");
            runlog.setState("0");
            runlog.setContent("用户：" + user.getId() + "，注册像素回传触发失败");
            runlog.setNote(e.getMessage());
            shortRunlogService.insertShortRunlog(runlog);
        }
    }

    private String updateIconMovie(String icon) {
        String responseUrl = null;
        try {

            MultipartFile file = getMultipartFileFromUrl(icon);

            // 现在可以使用multipartFile了
            //objectKey 对象键（文件在 S3 中的路径和名称）
            String objectKey = "uploads/" + UUID.randomUUID().toString() + "_" + file.getOriginalFilename();

            // 检查图片格式
            String imageType = detectImageType(file);
            if (imageType == null) {
                throw new Exception("仅支持JPG/PNG/JPEG/WEBP/GIF格式");
            }

            // 2. 处理图片
            byte[] processedImage = ImageProcessor.processImage(file.getBytes(), imageType);

            try {
                PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                        .bucket(bucketName)
                        .key(objectKey)
                        .contentType(imageType)
                        .contentDisposition("inline")            // ✅ 指定为 inline，防止下载
                        .build();

                PutObjectResponse response = s3Client.putObject(
                        putObjectRequest,
                        RequestBody.fromBytes(processedImage)
                );
                //判断文件是否上传成功
                if (response.sdkHttpResponse().isSuccessful()) {
                    log.debug("文件上传成功，构建访问链接");
                    responseUrl = filePrefix + objectKey;
                    System.out.println("responseUrl============" + responseUrl);
                }
                return responseUrl;
            } catch (S3Exception e) {
//                        throw new RuntimeException("Failed to read the file", e);
            }

        } catch (Exception e) {
//            return "";
        }
        return responseUrl;
    }

    /**
     * 生成随机码
     *
     * @return 随机字符串
     */
    private String generateRandomCode() {
        // 生成15位数字
        StringBuilder numbers = new StringBuilder();
        Random random = new Random();
        for (int i = 0; i < 15; i++) {
            numbers.append(random.nextInt(10));
        }

        // 生成3位字母
        StringBuilder letters = new StringBuilder();
        String alphabet = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
        for (int i = 0; i < 3; i++) {
            letters.append(alphabet.charAt(random.nextInt(alphabet.length())));
        }

        // 组合返回
        return numbers.toString() + letters.toString();
    }

    // 字节数组转十六进制（辅助方法）
    private static String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }

    public static MultipartFile getMultipartFileFromUrl(String imageUrl) throws Exception {
        try {
            URL url = new URL(imageUrl);
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

            // 添加User-Agent等请求头避免403
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestProperty("User-Agent", "Mozilla/5.0");

            try (InputStream inputStream = connection.getInputStream()) {
                byte[] buffer = new byte[4096];
                int bytesRead;

                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
            }

            // 获取文件名
            String fileName = imageUrl.substring(imageUrl.lastIndexOf('/') + 1);

            return new MockMultipartFile(
                    "file",
                    fileName,
                    URLConnection.guessContentTypeFromName(fileName),
                    outputStream.toByteArray()
            );

        } catch (Exception e) {
            throw new RuntimeException("获取远程图片失败: " + e.getMessage());
        }
    }

    public String detectImageType(MultipartFile file) throws IOException {
        Tika tika = new Tika();
        String mimeType = tika.detect(file.getInputStream());

        switch (mimeType) {
            case "image/jpeg":
                return "jpg";
            case "image/png":
                return "png";
            case "image/gif":
                return "gif";
            case "image/webp":
                return "webp";
            default:
                return null;
        }
    }




    private final AtomicInteger processedCount = new AtomicInteger(0);
    private final AtomicInteger batchCount = new AtomicInteger(0);
    private int totalBatches = 0;
    private int batchSize = 0;
    private boolean jobStarted = false;

    @Override
    public String startBatchProcessing(int batches) throws Exception {

        if (batches <= 0) {
            return null;
        }

        if (jobStarted) {
            return null;
        }

        List<ShortUser> shortUserList = shortUserService.get816();
        // 初始化任务参数
        totalBatches = batches;
        batchSize = shortUserList.size() / batches;
        processedCount.set(0);
        batchCount.set(0);
        jobStarted = true;

        // 立即执行第一次处理
        processBatch();

        return String.format("已启动分批处理任务，共%d批，每批处理%d条数据，每小时执行一批",
                totalBatches, batchSize);

    }

    @Override
    public AjaxResult getMovieByLimit(String nid, int limit) {
        return AjaxResult.success(shortMovieService.getByRand10(Long.valueOf(nid),limit));
    }

    @Override
    public AjaxResult cleaningOrderData() {
//        1.整体根据银行卡筛，排查是否同时存在邮箱和无邮箱账户
//        2.重新标记无邮箱的，已标记订单
//        3.筛51码和ok码，挑选出可续订


//        0.直接修改非成功的订单,订单状态为充值，把mark_type标记为0
        //执行sql
//        UPDATE short_order SET mark_type = null;
//        UPDATE short_order SET mark_type = 0 WHERE `status` != 'SUCCEEDED';
//        UPDATE short_order SET mark_type = 0 WHERE pay_type = '充值';
//        shortOrderService.updateMarkTypeNotStatus();
//        1.查询所有站邮箱为空的用户，根据用户id循环查询订单，修改最新一条成功的订单mark_type=1,其余订单=0
        List<Long> nullEmailListIds = shortUserService.getNullEmailListIds();
        for (Long id :nullEmailListIds){
            List<Long> ids = new ArrayList<>();
            ids.add(id);
            shortOrderService.updateMarkTypeInUserIds(ids);
        }
//        2.查询全站邮箱唯一的用户，根据用户id循环查询订单，修改最新一条成功的订单mark_type=1,其余订单=0
        List<Long> emailListIds = shortUserService.getEmailListIds();
        for (Long id :emailListIds){
            List<Long> ids = new ArrayList<>();
            ids.add(id);
            shortOrderService.updateMarkTypeInUserIds(ids);
        }
//        3.查询25号站多邮箱用户，然后通过多个用户id，修改最新一条成功的订单mark_type=1,其余订单=0
        List<String> emailList = shortUserService.getEmailList(25L);
        for (String email:emailList){
            List<Long> ids = shortUserService.getByEmail(email,25L,0);
            if(!ids.isEmpty())
                shortOrderService.updateMarkTypeInUserIds(ids);
        }
//        补充：25号站单邮箱且同时在其他站存在的数据，把25号站最新一条成功的订单mark_type=1,其余订单=0
        List<String> email25AndOtherList = shortUserService.getEmail25AndOtherList();
        for (String email:email25AndOtherList){
            List<Long> ids = shortUserService.getByEmail(email,25L,0);
            if(!ids.isEmpty())
                shortOrderService.updateMarkTypeInUserIds(ids);
        }

//        4.验证25号站，所有的订单是否全部标记过
//        标记25号站有邮箱且标记过的，其他站的该邮箱下订单mark_type=0
        List<String> emailMarkTypeIds = shortUserService.getEmailMarkTypeIds();
        for (String email:emailMarkTypeIds){
            List<Long> ids = shortUserService.getByEmail(email,25L,1);
            if(!ids.isEmpty())
                shortOrderService.updateMarkTypeInUserIds0(ids);
        }
//        5.筛选掉25号站，已经标记的数据，然后通过每个邮箱找对影响的所有账户id，再根据这些用户id，修改最新一条成功的订单mark_type=1,其余订单=0
        List<String> emailNotApp25Ids = shortUserService.getEmailNotApp25Ids();
        for (String email:emailNotApp25Ids){
            List<Long> ids = shortUserService.getByEmail(email,25L,1);
            if(!ids.isEmpty())
                shortOrderService.updateMarkTypeInUserIds(ids);
        }
        return AjaxResult.success();
    }

    @Override
    public AjaxResult newAutomaticRenewal(String publicKey) {
        int count = shortIntermedStatusService.countByName("newAutomaticRenewal");
        if (count > 0)
            return AjaxResult.success("自动化订阅续费执行中，请勿重复操作");

        ShortIntermedStatus shortIntermedStatus = new ShortIntermedStatus();
        shortIntermedStatus.setName("newAutomaticRenewal");
        shortIntermedStatusService.insertShortIntermedStatus(shortIntermedStatus);


        try {
            String privateKey = "-----BEGIN OPENSSH PRIVATE KEY-----\n" +
                    "b3BlbnNzaC1rZXktdjEAAAAABG5vbmUAAAAEbm9uZQAAAAAAAAABAAAAMwAAAAtzc2gtZW\n" +
                    "QyNTUxOQAAACDdx+eInOus2G8pGSS+paM8QV5SkQdAXGh/e0Z7pRaDRwAAAJiIp66uiKeu\n" +
                    "rgAAAAtzc2gtZWQyNTUxOQAAACDdx+eInOus2G8pGSS+paM8QV5SkQdAXGh/e0Z7pRaDRw\n" +
                    "AAAEDJW6rFB140gGyRJZM+mqoIFj8HAGrGpnuWj+sCJa/06N3H54ic66zYbykZJL6lozxB\n" +
                    "XlKRB0BcaH97RnulFoNHAAAAE2Zlbmd4aW41MjIwQDE2My5jb20BAg==\n" +
                    "-----END OPENSSH PRIVATE KEY-----";

            // 2. 加载密钥对
            KeyPair keyPair = KeyPair.load(new JSch(), privateKey.getBytes(), null);
            byte[] derivedPublicKeyBlob = keyPair.getPublicKeyBlob();

            // 3. 解析用户公钥（按空格分割并解码Base64）
            String[] publicKeyParts = publicKey.split(" ");
            if (publicKeyParts.length < 2) {
                throw new IllegalArgumentException("Invalid public key format");
            }
            byte[] userPublicKeyBlob = Base64.getDecoder().decode(publicKeyParts[1]);

            // 4. 安全比对（避免时序攻击）
            boolean isMatch = MessageDigest.isEqual(derivedPublicKeyBlob, userPublicKeyBlob);
            if (isMatch) {
                log.info("验证密钥正确");

//                //改完放开注释
//                0.筛选最后一次状态码为ok，连续三次51码关闭取消订阅的用户
//                    a.查询出取消续订的用户，
    //                b.查询出连续三次码最后关闭的
//                1.筛选周期性重置的数据，
                List<ShortUser> cycleList = shortUserService.getCycleList();

                for (ShortUser shortUser:cycleList){
                    if(shortUser.getCycleNum()>10) {
                        shortUserService.updateUnsubSource(shortUser.getId(), "周期性重置10次取消订阅");
                        continue;
                    }
                    shortUserService.updateCodeAndCycleNum(shortUser.getId(),shortUser.getCycleNum()+1);
                }
//                2.查询全站没有邮箱且可续订的用户
                List<Long> noEmailList = shortUserService.getNoEmailList();

//                3.查询全站有邮箱且唯一账户的用户
                List<Long> emailList1 = shortUserService.getEmailList1();

//                4.查询25站邮箱非唯一的用户，取出可以续订的用户
                List<Long> emailAll25 = shortUserService.getEmailAll25();

                List<Long> allNotEmailListByAll25 = new ArrayList<>();
                if(emailAll25.size() > 0){
//                5.查询除25站，邮箱非唯一的用户，取出可续订的用户，排除邮箱在25站存在可续订的用户
                    List<String> emailListByAll25 = shortUserService.getEmailListByAll25(emailAll25);

                    allNotEmailListByAll25 = shortUserService.getAllNotEmailListByAll25(emailListByAll25);
                }

                //过滤当天已经续订过的
                List<ShortUser> resub = shortUserService.getRenewSubscribe();

                // --- 新增代码：提取resub中的所有ID ---
                Set<Long> excludeIds = resub.stream()
                        .map(ShortUser::getId)
                        .collect(Collectors.toSet());

                List<Long> mergedList = Stream.of(noEmailList, emailList1, emailAll25, allNotEmailListByAll25,excludeIds)
                        .flatMap(Collection::stream)
                        .distinct()
                        .collect(Collectors.toList());

                LocalDateTime now = LocalDateTime.now(); // 直接获取当前时间
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                String formattedDate = now.format(formatter);

                ShortRenewalRecord oldRecord = new ShortRenewalRecord();
                oldRecord.setLogDate(formattedDate);
                List<ShortRenewalRecord> oldRenewalRecordList = shortRenewalRecordService.selectShortRenewalRecordList(oldRecord);
                Set<Long> existingUserIds = oldRenewalRecordList.stream()
                        .map(ShortRenewalRecord::getUserId)
                        .collect(Collectors.toSet());
                List<Long> filteredList = mergedList.stream()
                        .filter(userId -> !existingUserIds.contains(userId))
                        .collect(Collectors.toList());


                List<Long> lastList = new ArrayList<>();
                for (Long id:filteredList){
                    String code =  shortUserRenewLogService.getLastCodeByUserId(id);
                    if(StringUtils.isEmpty(code) || code.equals("51") ||code.equals("ok") )
                        lastList.add(id);
                }
                System.out.println("==========================================================");
                System.out.println("lastList="+lastList);
                System.out.println("==========================================================");

                if(!lastList.isEmpty()){
                    List<Long> onlyCardList = shortOrderService.getOnlyCardList(lastList);
                    if(!onlyCardList.isEmpty()){
                        List<ShortRenewalRecord> list = new ArrayList<>();
                        List<ShortUser> shortUserList = shortUserService.getIds(onlyCardList);
                        for (ShortUser shortUser:shortUserList){

                            ShortRenewalRecord shortRenewalRecord = new ShortRenewalRecord();
                            shortRenewalRecord.setUserId(shortUser.getId());
                            shortRenewalRecord.setLogDate(formattedDate);
                            shortRenewalRecord.setAppId(shortUser.getAppId());
                            shortRenewalRecord.setType(0L);
                            shortRenewalRecord.setCreateTime(DateUtils.getNowDate());
                            shortRenewalRecord.setEmailType(0L);
                            list.add(shortRenewalRecord);
                        }

                        if(!list.isEmpty())
                            shortRenewalRecordService.insertBatch(list);
                    }

                }


                //新扣续订接口
//                getAutoSubUserNew(1111 + String.valueOf(userDos.get(i).getUser_id()));



                // --- 修改原合并逻辑 ---
//                List<ShortUser> uniqueList = list.stream()
//                        .filter(user -> !excludeIds.contains(user.getId()))  // 过滤掉list中与resub重复的ID
//                        .collect(Collectors.toList());

//                uniqueList.sort(Comparator.comparing(ShortUser::getUpdatetime).reversed());

                // 主动分片 10个一组
//                List<List<UserDo>> partition = Lists.partition(newtargetList, 10);
//                CountDownLatch latch = new CountDownLatch(partition.size());
//                for (List<UserDo> userDos : partition) {
//                    threadPoolTaskExecutor.execute(() -> {
//                        try {
//                            for (int i = 0; i < userDos.size(); i++) {
//                                log.info("总条数【"+ newtargetList.size() +"】|【"+ Thread.currentThread().getName() +"】线程 | 执行id：【" + userDos.get(i).getUser_id() + "】| 当前线程条数:【" + userDos.size() + "】| i=====:" + i);
//                                Thread.sleep(2000);
//                                getAutoSubUser(1111 + String.valueOf(userDos.get(i).getUser_id()));
//                            }
//                        } catch (Exception e) {
//                            log.error("执行续订异常：", e);
//                        } finally {
//                            latch.countDown();
//                        }
//                    });
//                }
//                latch.await();
            }

            shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
            return AjaxResult.success("操作成功");

        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("status", "error");
            error.put("message", "Validation failed: " + e.getMessage());
            shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
            return AjaxResult.error("操作失败");
        }
    }

    @Override
    public AjaxResult newAutomaticRenewalRun(String publicKey) {
        int count = shortIntermedStatusService.countByName("newAutomaticRenewalRun");
        if (count > 0)
            return AjaxResult.success("自动化订阅续费执行中，请勿重复操作");

        ShortIntermedStatus shortIntermedStatus = new ShortIntermedStatus();
        shortIntermedStatus.setName("newAutomaticRenewalRun");
        shortIntermedStatusService.insertShortIntermedStatus(shortIntermedStatus);


        try {
            String privateKey = "-----BEGIN OPENSSH PRIVATE KEY-----\n" +
                    "b3BlbnNzaC1rZXktdjEAAAAABG5vbmUAAAAEbm9uZQAAAAAAAAABAAAAMwAAAAtzc2gtZW\n" +
                    "QyNTUxOQAAACDdx+eInOus2G8pGSS+paM8QV5SkQdAXGh/e0Z7pRaDRwAAAJiIp66uiKeu\n" +
                    "rgAAAAtzc2gtZWQyNTUxOQAAACDdx+eInOus2G8pGSS+paM8QV5SkQdAXGh/e0Z7pRaDRw\n" +
                    "AAAEDJW6rFB140gGyRJZM+mqoIFj8HAGrGpnuWj+sCJa/06N3H54ic66zYbykZJL6lozxB\n" +
                    "XlKRB0BcaH97RnulFoNHAAAAE2Zlbmd4aW41MjIwQDE2My5jb20BAg==\n" +
                    "-----END OPENSSH PRIVATE KEY-----";

            // 2. 加载密钥对
            KeyPair keyPair = KeyPair.load(new JSch(), privateKey.getBytes(), null);
            byte[] derivedPublicKeyBlob = keyPair.getPublicKeyBlob();

            // 3. 解析用户公钥（按空格分割并解码Base64）
            String[] publicKeyParts = publicKey.split(" ");
            if (publicKeyParts.length < 2) {
                throw new IllegalArgumentException("Invalid public key format");
            }
            byte[] userPublicKeyBlob = Base64.getDecoder().decode(publicKeyParts[1]);

            // 4. 安全比对（避免时序攻击）
            boolean isMatch = MessageDigest.isEqual(derivedPublicKeyBlob, userPublicKeyBlob);
            if (isMatch) {
                log.info("验证密钥正确");


                ShortRenewalRecord shortRenewalRecord = new ShortRenewalRecord();
                LocalDateTime now = LocalDateTime.now().minusDays(1); // 获取当前时间并减一天
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                String formattedDate = now.format(formatter);
                shortRenewalRecord.setLogDate(formattedDate);
                shortRenewalRecord.setType(0L);
                List<ShortRenewalRecord> renewalRecordList = shortRenewalRecordService.selectShortRenewalRecordList(shortRenewalRecord);
                for (ShortRenewalRecord renewalRecord:renewalRecordList){
                    renewalRecord.setType(1L);
                    shortRenewalRecordService.updateShortRenewalRecord(renewalRecord);

                    Thread.sleep(2000);
                    System.out.println(renewalRecord.getUserId());
                    getAutoSubUser1(1111 + String.valueOf(renewalRecord.getUserId()));
                }

                // 主动分片 10个一组
//                List<List<ShortRenewalRecord>> partition = Lists.partition(renewalRecordList, 10);
//                CountDownLatch latch = new CountDownLatch(partition.size());
//                for (List<ShortRenewalRecord> userDos : partition) {
//                    threadPoolTaskExecutor.execute(() -> {
//                        try {
//                            for (int i = 0; i < userDos.size(); i++) {
//                                userDos.get(i).setType(1L);
//                                shortRenewalRecordService.updateShortRenewalRecord(userDos.get(i));
//                                log.info("总条数【"+ renewalRecordList.size() +"】|【"+ Thread.currentThread().getName() +"】线程 | 执行id：【" + userDos.get(i).getUserId() + "】| 当前线程条数:【" + userDos.size() + "】| i=====:" + i);
//                                Thread.sleep(2000);
//                                getAutoSubUserNew(1111 + String.valueOf(userDos.get(i).getUserId()));
//                            }
//                        } catch (Exception e) {
//                            log.error("执行续订异常：", e);
//                        } finally {
//                            latch.countDown();
//                        }
//                    });
//                }
//                latch.await();

            }

            shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
            return AjaxResult.success("操作成功");

        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("status", "error");
            error.put("message", "Validation failed: " + e.getMessage());
            shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
            return AjaxResult.error("操作失败");
        }
    }

    /**
     * 每小时执行一次的批处理任务
     */
    @Scheduled(fixedRate = 60 * 60 * 1000) // 每小时执行一次
    @Async
    public void scheduledBatchProcessing() throws Exception {
        if (jobStarted && batchCount.get() < totalBatches) {
            processBatch();
        }
    }

    /**
     * 处理单个批次的数据
     */
    private void processBatch() throws Exception {
        int currentBatch = batchCount.incrementAndGet();
        if (currentBatch > totalBatches) {
            return;
        }

        List<ShortUser> shortUserList = shortUserService.get816();
        int start = (currentBatch - 1) * batchSize;
        int end = (currentBatch == totalBatches) ? shortUserList.size() : currentBatch * batchSize;

        // 模拟数据处理
        System.out.println(LocalDateTime.now() + " - 开始处理批次 " + currentBatch +
                "，处理数据范围: " + start + " - " + (end - 1));


        // 这里添加实际的数据处理逻辑
        // processData(start, end);
        List<ShortUser> batchData = shortUserList.subList(start, end);


        List<ShortUser> resub = shortUserService.getRenewSubscribe1();

        // --- 新增代码：提取resub中的所有ID ---
        Set<Long> excludeIds = resub.stream()
                .map(ShortUser::getId)
                .collect(Collectors.toSet());
        // --- 修改原合并逻辑 ---
        List<ShortUser> uniqueList = batchData.stream()
                .filter(user -> !excludeIds.contains(user.getId()))  // 过滤掉distinctOrders中与resub重复的ID
                .collect(Collectors.toList());


        processData(uniqueList);



        processedCount.addAndGet(end - start);
        System.out.println(LocalDateTime.now() + " - 批次 " + currentBatch + " 处理完成");

        if (currentBatch == totalBatches) {
            jobStarted = false;
            System.out.println("所有批次处理完成，共处理 " + processedCount.get() + " 条数据");
        }
    }

    private void processData(List<ShortUser> batchData) throws Exception {
//        getAutoSubUser1(1111 + String.valueOf(841035));
        for (int i = 0; i < batchData.size(); i++) {
//            System.out.println(batchData.get(i).getId());
//            int minSeconds = 120; // 2分钟 = 120秒
//            int maxSeconds = 240; // 5分钟 = 300秒
//            // 生成 [120, 300] 的随机秒数
//            int randomSeconds = ThreadLocalRandom.current().nextInt(minSeconds, maxSeconds + 1);
//            Thread.sleep(randomSeconds * 1000L);

            Thread.sleep(2000);
            System.out.println(batchData.get(i).getId());
            getAutoSubUser1(1111 + String.valueOf(batchData.get(i).getId()));
        }
    }



    public AjaxResult getAutoSubUser1(String uid) throws Exception {
        // ------------------------------------------------------------------
        if (StringUtils.isNotEmpty(uid)) {
            // 处理uid前缀
            if (uid.length() > 4) {
                uid = uid.substring(4);
            }
        }
        ShortUser user = shortUserService.selectShortUserById(Long.valueOf(uid));
        if (null == user) {
            log.error("用户不存在", uid);
            return AjaxResult.error(400, "用户不存在");
        }

        // 检查请求次数
//        if ((null != user.getPushNum() && user.getPushNum() >= 3) || (null != user.getUnsub() && user.getUnsub() == 1)) {
//            if (null != user.getUnsub() && user.getUnsub() != 1)
//                shortUserService.updateUnsub(user.getId());
//            return AjaxResult.error(400, String.format("用户：%d，自动续订失败，用户请求次数超过三次或取消订阅", user.getId()));
//        }
//        if (null != user.getUnsub() && user.getUnsub() == 1) {
//            return AjaxResult.error(400,String.format("用户：%d，自动续订失败，用户取消订阅", user.getId()));
//        }

//        # 判断用户是否在允许的续订时间范围内
//        # push_num 表示已尝试续订的次数,每次尝试续订后会增加7天的等待期
//        # 例如: push_num=1时需等待7天,push_num=2时需等待14天,以此类推
//        if user.push_num and timezone.now() < user.expire_time + timedelta(days=7 * user.push_num):
//        return JsonResponse({'code': 400, 'msg': f'用户：{user.id}，自动续订失败，用户订阅时间超过{7 * user.push_num}天'})

        if (null == user.getExpireTime()) {
            return AjaxResult.error(400, "用户订阅到期时间为空");
        }
        LocalDateTime localDateTime = user.getExpireTime().toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();

        if (null != user.getLastChargeTime())
            localDateTime = user.getLastChargeTime();
        // 检查续订时间范围
//        if (null != user.getPushNum() && user.getPushNum().intValue()>0 && null != user.getExpireTime() && LocalDateTime.now().isBefore(localDateTime.plus(Duration.ofDays(7 * user.getPushNum())))) {
//            return AjaxResult.error(400,"用户："+user.getId()+"，自动续订失败，用户订阅时间超过"+7 * user.getPushNum()+"天");
//        }
//        if (null != user.getPushNum() && user.getPushNum().intValue()>0 && user.getPushNum().intValue()<=10) {
//            user.setSubIntervalDays(1);
//            shortUserService.updateShortUser(user);
//            return AjaxResult.error(400,"用户："+user.getId()+"，自动续订失败，用户订阅时间超过"+1*user.getPushNum()+"天");
//        }
        if (null != user.getPushNum() && user.getPushNum().intValue() >= 10 && user.getPushNum().intValue() < 15 && LocalDateTime.now().isBefore(localDateTime.plus(Duration.ofDays(3)))) {
            user.setSubIntervalDays(3);
            shortUserService.updateShortUser(user);
            return AjaxResult.error(400, "用户：" + user.getId() + "，自动续订失败，用户订阅时间超过" + 3 * user.getPushNum() + "天");
        }
        if (null != user.getPushNum() && user.getPushNum().intValue() >= 15 && user.getPushNum().intValue() < 19 && LocalDateTime.now().isBefore(localDateTime.plus(Duration.ofDays(7)))) {
            user.setSubIntervalDays(7);
            shortUserService.updateShortUser(user);
            return AjaxResult.error(400, "用户：" + user.getId() + "，自动续订失败，用户订阅时间超过" + 7 * user.getPushNum() + "天");
        }
        if (null != user.getPushNum() && user.getPushNum().intValue() >= 19) {
            shortUserService.updateUnsub(user.getId());
            return AjaxResult.error(400, "用户：" + user.getId() + "，取消订阅");
        }
        user.setLastChargeTime(LocalDateTime.now());
        shortUserService.updateShortUser(user);
        // 获取 Token
        String token = shortExtplatsService.getPaymentToken(user.getAppId() != null ? user.getAppId().toString() : null);
        if (token == null || token.isEmpty()) {
            return AjaxResult.error(405, "没有拿到 token 无法发送订单要求");
        }

        if (null != user.getExpireTime()) {
            if (localDateTime.isAfter(LocalDateTime.now().plusDays(1))) {
                return AjaxResult.error(400, "用户不满足续订条件");
            }
        }
        // ------------------------------------------------------------------

        String isOk = user.getExpireTime() + ", 满足条件";


        // 获取用户的最后一次订阅订单
        ShortOrder originalOrder = null;
        ShortOrder lastSubRe = shortOrderService.findFirstByUserAndPay_typeAndStatusOrderByUpdatedAtDesc(user.getId(), "订阅续费", "SUCCEEDED");
        if (null != lastSubRe) {
            originalOrder = lastSubRe;
        } else {
            ShortOrder lastSub = shortOrderService.findFirstByUserAndPay_typeAndStatusOrderByUpdatedAtDesc(user.getId(), "订阅", "SUCCEEDED");
            if (null != lastSub)
                originalOrder = lastSub;
        }

        if (null == originalOrder) {
            return AjaxResult.error(400, "没有找到订阅订单");
        }

        //定死 营销邮件发送后  续费订阅
        if (originalOrder.getAmount().compareTo(new BigDecimal("0.88")) == 0) {
            originalOrder.setLinkId(Long.valueOf("3666"));
            originalOrder.setVipId(Long.valueOf("190"));
            originalOrder.setAmount(new BigDecimal("14.99"));
        }

        ShortVip shortVipTest = shortVipService.selectShortVipById(originalOrder.getVipId());
        if (null != shortVipTest && testPidTempId.equals(String.valueOf(shortVipTest.getPayTemplateId()))) {
            originalOrder.setVipId(Long.valueOf(testPidTempIdVip));
            originalOrder.setAmount(new BigDecimal("29.99"));
        }

        if(null != originalOrder.getOriginalPrice())
            originalOrder.setAmount(originalOrder.getOriginalPrice());

        ShortRenewSubscribeData shortRenewSubscribeData = new ShortRenewSubscribeData();
        shortRenewSubscribeData.setCreateTime(DateUtils.getNowDate());

        LocalDateTime now = LocalDateTime.now(); // 直接获取当前时间
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String formattedDate = now.format(formatter);
        shortRenewSubscribeData.setLogDate(formattedDate);
        shortRenewSubscribeData.setUserId(user.getId());
        shortRenewSubscribeData.setPhoneVersion(user.getPhoneVersion());
        shortRenewSubscribeData.setRenewAmount(originalOrder.getAmount());

        ShortUserRenewLog shortUserRenewLog = new ShortUserRenewLog();
        shortUserRenewLog.setUserId(user.getId());
        shortUserRenewLog.setAmount(originalOrder.getAmount());

        ShortVip shortVip = shortVipService.selectShortVipById(originalOrder.getVipId());
        if (null != shortVip)
            shortRenewSubscribeData.setRenewType(shortVip.getSubscriptionType());
        InitUserDTO shortBrevo = new InitUserDTO();
        shortBrevo.setReceiveEmail(user.getEmail());
        shortBrevo.setAppId(user.getAppId());
        shortBrevo.setPayAccount(String.valueOf(originalOrder.getAmount()));
        if(null == shortBrevo.getLanguageCode())
            shortBrevo.setLanguageCode("en-US");
        List<ShortMovie> shortMovieList = new ArrayList<>();

        // 解析 pay_info
        try {
            // 获取用户支付信息
            String payInfo = user.getPayInfo();

            if (payInfo == null || payInfo.isEmpty()) {
                return AjaxResult.error(400, "用户ID: {" + user.getId() + "} 没有支付信息");
            }
            // 解析支付信息
            String paymentConsentId = null;
            String customerId = null;

            // payInfo是JSON字符串格式的数组
            ObjectMapper mapper = new ObjectMapper();
            List<Map<String, String>> payInfoList = mapper.readValue(payInfo,
                    mapper.getTypeFactory().constructCollectionType(List.class, Map.class));

            if (!payInfoList.isEmpty()) {
                Map<String, String> firstPayInfo = payInfoList.get(0);
                paymentConsentId = firstPayInfo.get("payment_consent_id");
                customerId = firstPayInfo.get("customer_id");
            }

            if (paymentConsentId == null || customerId == null) {
                return AjaxResult.error(400, "用户ID: {" + user.getId() + "} 支付信息不完整");
            }

            // 生成随机订单号
            String merchantOrderId = "Merchant_Order" + GenerateRandomCodeUtil.generateRandomCode();

            // 创建支付意向
            Map<String, Object> payRequestBody = new HashMap<>();
            payRequestBody.put("request_id", merchantOrderId);
            payRequestBody.put("amount", originalOrder.getAmount());
            payRequestBody.put("currency", "USD");
            payRequestBody.put("merchant_order_id", merchantOrderId);
            payRequestBody.put("customer_id", customerId);

            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + token);

            ShortEmailDomain shortEmailDomain = shortEmailDomainService.getrandomDomain(originalOrder.getAppId());
            if(null != shortEmailDomain)
                payRequestBody.put("descriptor", shortEmailDomain.getAppName());

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(payRequestBody, headers);


            ResponseEntity<Map> payResponse = restTemplate.postForEntity(
                    (useDevApi ? devApiUrl : prodApiUrl) + "/api/v1/pa/payment_intents/create",
                    request,
                    Map.class
            );
            log.info("创建意向支付返回：" + payResponse);
            if (payResponse.getStatusCodeValue() == 201 && payResponse.getBody() != null) {
                Map<String, Object> responseBody = payResponse.getBody();

                String paymentIntentId = (String) responseBody.get("id");
                String clientSecret = (String) responseBody.get("client_secret");
                String currency = (String) responseBody.get("currency");
                String status = (String) responseBody.get("status");


                Long now_link = 0L;
                if (null != originalOrder.getLinkId()) {
                    ShortSemLink shortLink = shortSemLinkService.selectShortSemLinkById(originalOrder.getLinkId());
                    if (null != shortLink) {
                        now_link = shortLink.getId();
                    }
                }

                // 创建订单记录
                ShortOrder order = new ShortOrder();
                order.setUserId(user.getId());
                order.setVipId(originalOrder.getVipId());
                order.setAppId(user.getAppId());
                order.setPayType("订阅续费");
                order.setOrdersn(merchantOrderId);
                order.setMerchantOrderId(merchantOrderId);
                order.setPaymentIntentId(paymentIntentId);
                order.setRequestId((String) payRequestBody.get("request_id"));
                order.setPaymentCurrency("USD");
                order.setPaymentAmount(originalOrder.getAmount());
                order.setAmount(originalOrder.getAmount());
                order.setCurrency(currency);
                order.setStatus(status);
                order.setClientSecret(clientSecret);

                order.setAdid(originalOrder.getAdid());
                order.setLinkId(now_link);
                order.setLinkTime(originalOrder.getLinkTime());
                order.setOther(originalOrder.getOther());

                order.setCreatedAt(DateUtils.getNowDate());
                order.setUpdatedAt(DateUtils.getNowDate());
                order.setCreateTime(DateUtils.getNowDate());
                order.setUpdateTime(DateUtils.getNowDate());
                order.setPixelId(originalOrder.getPixelId());
                order.setMarkType(1);
                order.setSubscriptionType(originalOrder.getSubscriptionType());
                //从用户获取userId
                Long linkId = originalOrder.getLinkId();
                //走深链 判断是否使用自定义付费计划
                List<ShortLinkVip> shortLinkVipList = shortLinkVipService.selectByLinkId(linkId);
                //自定义付费计划有配置 且 vipId 取得是自定义付费计划ID
                if(CollectionUtil.isNotEmpty(shortLinkVipList)){
                    order.setVipType("1");
                }else {
                    order.setVipType("0");
                }

                shortOrderService.insertShortOrder(order);

                log.info("为用户 {} 创建订阅意向订单成功: {}", user.getId(), merchantOrderId);

                // 确认付款意向
                Map<String, Object> confirmRequestBody = new HashMap<>();
                confirmRequestBody.put("request_id", generateRandomCode());
                confirmRequestBody.put("payment_consent_id", paymentConsentId);

                HttpEntity<Map<String, Object>> confirmRequest = new HttpEntity<>(confirmRequestBody, headers);

                ResponseEntity<Map> confirmResponse = restTemplate.postForEntity(
                        (useDevApi ? devApiUrl : prodApiUrl) + "/api/v1/pa/payment_intents/" + paymentIntentId + "/confirm",
                        confirmRequest,
                        Map.class
                );
                // 记录日志并处理结果
                ShortRunlog runlog = new ShortRunlog();
                runlog.setType("自动续订");
                runlog.setCreateTime(DateUtils.getNowDate());
                runlog.setUpdateTime(DateUtils.getNowDate());
//                runlog.setA(LocalDateTime.now());


                if ("SUCCEEDED".equals(confirmResponse.getBody().get("status"))) {
                    runlog.setState("1");
                    runlog.setContent(String.format("用户：" + user.getId() + "，自动续订成功，支付金额：" + confirmResponse.getBody().get("amount") + "，支付方式：" + ((Map) ((Map) confirmResponse.getBody().get("latest_payment_attempt")).get("payment_method")).get("type")));
                    runlog.setNote(confirmResponse.getBody().toString());
                    shortRunlogService.insertShortRunlog(runlog);

                    ShortUser shortUser = new ShortUser();
                    shortUser.setId(user.getId());
                    shortUser.setPushNum(0L);
//                    user.setPushNum(0L);
                    shortUserService.updateShortUser(shortUser);

                    ShortRenewSubscribeData subscribeData = shortRenewSubscribeDataService.selectByUserIdStatus(user.getId(), "");
                    shortRenewSubscribeData.setOrderStatus("SUCCEEDED");
                    if (null == subscribeData) {
                        shortRenewSubscribeData.setRenewCount(1);
                        shortRenewSubscribeData.setPayErrorCount(0);
                        List<ShortRenewSubscribeData> saveList = new ArrayList<>();
                        saveList.add(shortRenewSubscribeData);
                        shortRenewSubscribeDataMapper.insertBatch(saveList);
                    } else {
                        if (!formattedDate.equals(subscribeData.getLogDate())) {
                            shortRenewSubscribeData.setRenewCount(subscribeData.getRenewCount() + 1);
                            shortRenewSubscribeData.setPayErrorCount(0);
                            List<ShortRenewSubscribeData> saveList = new ArrayList<>();
                            saveList.add(shortRenewSubscribeData);
                            shortRenewSubscribeDataMapper.insertBatch(saveList);
                        }

                    }

                    shortUserRenewLog.setType(0L);
                    shortUserRenewLog.setContent(String.format("处理完成，" + token + ", 用户id:" + uid + ", " + isOk + ", 续订详细信息：" + confirmResponse.getBody()));
                    shortUserRenewLog.setCode("ok");
                    shortUserRenewLogService.insertShortUserRenewLog(shortUserRenewLog);




//                    InitUserDTO shortBrevo = new InitUserDTO();
//                    //先固定英语
//                    shortBrevo.setLanguageCode("en-US");
//                    shortBrevo.setUserId(String.valueOf(user.getId()));
//                    shortBrevo.setUniqueId(user.getUniqueId());
//                    Long vipId = order.getVipId();
                    // 获取订阅类型
//                    String subscriptionType = "";
//                    //判断是否自定义付费计划
//                    if(null != order.getVipType() && order.getVipType().equals("1")){
//                        subscriptionType = order.getSubscriptionType();
//                    }else {
//                        ShortVip vip = vipService.selectShortVipById(vipId);
//                        subscriptionType = vip.getSubscriptionType();
//                    }
                    // 动态计算天数
//                    int daysToAdd = Integer.parseInt(subscriptionType);
//                    Date newExpireTime = null;
//                    if ("1".equals(user.getIsSubscriber()) && user.getExpireTime() != null &&
//                            user.getExpireTime().after(DateUtils.getNowDate())) {
//                        // 在现有过期时间基础上添加天数
//                        Calendar calendar = Calendar.getInstance();
//                        calendar.setTime(user.getExpireTime());
//                        calendar.add(Calendar.DAY_OF_YEAR, daysToAdd);
//                        newExpireTime = calendar.getTime();
//                    } else {
//                        // 从当前时间开始计算
//                        Calendar calendar = Calendar.getInstance();
//                        calendar.add(Calendar.DAY_OF_YEAR, daysToAdd);
//                        newExpireTime = calendar.getTime();
//                    }
//                    String exTime = formatToEnglishUTC(newExpireTime);
//                    if(StringUtils.isNotEmpty(user.getEmail())){
//                        shortBrevo.setReceiveEmail(user.getEmail());
//                        shortBrevo.setAppId(user.getAppId());
//                        sendBatchXDSessed(shortBrevo,"brevo",exTime);
//                    }

                    return AjaxResult.success(String.format("处理完成，" + token + ", 用户id:" + uid + ", " + isOk + ", 续订详细信息：" + confirmResponse.getBody()));
                } else if ("issuer_declined".equals(confirmResponse.getBody().get("code"))) {
                    runlog.setState("0");
                    runlog.setContent(String.format("用户：" + user.getId() + "，自动续订失败，用户卡余额不足"));
                    runlog.setNote(confirmResponse.getBody().toString());
                    shortRunlogService.insertShortRunlog(runlog);

                    user.setPushNum(user.getPushNum() == null ? 1 : user.getPushNum() + 1);
                    shortUserService.updateShortUser(user);


                    ShortRenewSubscribeData subscribeData = shortRenewSubscribeDataService.selectByUserIdStatus(user.getId(), "");
                    shortRenewSubscribeData.setOrderStatus("REQUIRES_PAYMENT_METHOD");
                    if (null == subscribeData) {
                        shortRenewSubscribeData.setRenewCount(1);
                        shortRenewSubscribeData.setPayErrorCount(0);
                        List<ShortRenewSubscribeData> saveList = new ArrayList<>();
                        saveList.add(shortRenewSubscribeData);
                        shortRenewSubscribeDataMapper.insertBatch(saveList);
                    } else {
                        if (!formattedDate.equals(subscribeData.getLogDate())) {
                            shortRenewSubscribeData.setRenewCount(subscribeData.getRenewCount() + 1);
                            shortRenewSubscribeData.setPayErrorCount(0);
                            List<ShortRenewSubscribeData> saveList = new ArrayList<>();
                            saveList.add(shortRenewSubscribeData);
                            shortRenewSubscribeDataMapper.insertBatch(saveList);
                        }

                    }

                    shortUserRenewLog.setType(1L);
                    shortUserRenewLog.setContent(String.format("用户：" + user.getId() + "，自动续订失败，用户卡余额不足，详细信息：" + confirmResponse.getBody()));
                    shortUserRenewLogService.insertShortUserRenewLog(shortUserRenewLog);
                    if(StringUtils.isNotEmpty(user.getEmail()))
                        businessFunctionApiService.sendEmailForInType(shortBrevo, shortMovieList, "brevo","payFailed");
                    return AjaxResult.error(400, String.format("用户：" + user.getId() + "，自动续订失败，用户卡余额不足，详细信息：" + confirmResponse.getBody()));
                } else {
                    runlog.setState("0");
                    runlog.setContent(String.format("用户：" + user.getId() + "，自动续订失败"));
                    runlog.setNote(confirmResponse.getBody().toString());
                    shortRunlogService.insertShortRunlog(runlog);

                    user.setPushNum(user.getPushNum() == null ? 1 : user.getPushNum() + 1);
                    shortUserService.updateShortUser(user);

                    ShortRenewSubscribeData subscribeData = shortRenewSubscribeDataService.selectByUserIdStatus(user.getId(), "");
                    shortRenewSubscribeData.setOrderStatus("REQUIRES_PAYMENT_METHOD");
                    if (null == subscribeData) {
                        shortRenewSubscribeData.setRenewCount(1);
                        shortRenewSubscribeData.setPayErrorCount(0);
                        List<ShortRenewSubscribeData> saveList = new ArrayList<>();
                        saveList.add(shortRenewSubscribeData);
                        shortRenewSubscribeDataMapper.insertBatch(saveList);
                    } else {
                        if (!formattedDate.equals(subscribeData.getLogDate())) {
                            shortRenewSubscribeData.setRenewCount(subscribeData.getRenewCount() + 1);
                            shortRenewSubscribeData.setPayErrorCount(0);
                            List<ShortRenewSubscribeData> saveList = new ArrayList<>();
                            saveList.add(shortRenewSubscribeData);
                            shortRenewSubscribeDataMapper.insertBatch(saveList);
                        }

                    }

                    shortUserRenewLog.setType(1L);
                    shortUserRenewLog.setContent(String.format("用户：" + user.getId() + "，自动续订失败，详细信息：" + confirmResponse.getBody()));
                    shortUserRenewLogService.insertShortUserRenewLog(shortUserRenewLog);
                    if(StringUtils.isNotEmpty(user.getEmail()))
                        businessFunctionApiService.sendEmailForInType(shortBrevo, shortMovieList, "brevo","payFailed");
                    return AjaxResult.error(400, String.format("用户：" + user.getId() + "，自动续订失败，详细信息：" + confirmResponse.getBody()));
                }

            } else {

                user.setPushNum(user.getPushNum() == null ? 1 : user.getPushNum() + 1);
                shortUserService.updateShortUser(user);

                ShortRenewSubscribeData subscribeData = shortRenewSubscribeDataService.selectByUserIdStatus(user.getId(), "");
                shortRenewSubscribeData.setOrderStatus("REQUIRES_PAYMENT_METHOD");
                if (null == subscribeData) {
                    shortRenewSubscribeData.setRenewCount(1);
                    shortRenewSubscribeData.setPayErrorCount(0);
                    List<ShortRenewSubscribeData> saveList = new ArrayList<>();
                    saveList.add(shortRenewSubscribeData);
                    shortRenewSubscribeDataMapper.insertBatch(saveList);
                } else {
                    if (!formattedDate.equals(subscribeData.getLogDate())) {
                        shortRenewSubscribeData.setRenewCount(subscribeData.getRenewCount() + 1);
                        shortRenewSubscribeData.setPayErrorCount(0);
                        List<ShortRenewSubscribeData> saveList = new ArrayList<>();
                        saveList.add(shortRenewSubscribeData);
                        shortRenewSubscribeDataMapper.insertBatch(saveList);
                    }

                }

                shortUserRenewLog.setType(1L);
                shortUserRenewLog.setContent("创建支付意向失败");
                shortUserRenewLogService.insertShortUserRenewLog(shortUserRenewLog);

                return AjaxResult.error(400, "创建支付意向失败");
            }

        } catch (Exception e) {

            user.setPushNum(user.getPushNum() == null ? 1 : user.getPushNum() + 1);
            shortUserService.updateShortUser(user);

            ShortRenewSubscribeData subscribeData = shortRenewSubscribeDataService.selectByUserIdStatus(user.getId(), "");
            shortRenewSubscribeData.setOrderStatus("REQUIRES_PAYMENT_METHOD");
            if (null == subscribeData) {
                shortRenewSubscribeData.setRenewCount(1);
                shortRenewSubscribeData.setPayErrorCount(0);
                List<ShortRenewSubscribeData> saveList = new ArrayList<>();
                saveList.add(shortRenewSubscribeData);
                shortRenewSubscribeDataMapper.insertBatch(saveList);
            } else {
                if (!formattedDate.equals(subscribeData.getLogDate())) {
                    shortRenewSubscribeData.setRenewCount(subscribeData.getRenewCount() + 1);
                    shortRenewSubscribeData.setPayErrorCount(0);
                    List<ShortRenewSubscribeData> saveList = new ArrayList<>();
                    saveList.add(shortRenewSubscribeData);
                    shortRenewSubscribeDataMapper.insertBatch(saveList);
                }

            }


            log.info("e.getMessage()=================== - {}", e.getMessage());

//            String jsonStr = "{\"code\":\"issuer_declined\",\"message\":\"The card issuer declined this transaction. Please refer to the original response code.\",\"trace_id\":\"ed2e1c5860010d331e0c078ec3db6998\",\"provider_original_response_code\":\"03\",\"details\":{\"card_brand\":\"visa\",\"card_type\":\"PREPAID\",\"is_commercial\":false,\"issuing_bank_name\":\"REGIONS BANK\",\"original_response_code\":\"03\"}}";

            try {
                // 1. 提取 JSON 部分
                String message = e.getMessage();
                int jsonStart = message.indexOf('{');
                int jsonEnd = message.lastIndexOf('}');
                String jsonStr = message.substring(jsonStart, jsonEnd + 1);

                // 2. 解析 JSON
                ObjectMapper mapper = new ObjectMapper();
                JsonNode rootNode = mapper.readTree(jsonStr);

                // 3. 获取 original_response_code
                String originalResponseCode = rootNode
                        .path("details")
                        .path("original_response_code")
                        .asText();

                log.info("解析成功，original_response_code=" + originalResponseCode);

                // 4. 设置到对象（示例）
                if (StringUtils.isNotEmpty(originalResponseCode)) {
                    shortUserRenewLog.setCode(originalResponseCode);
                } else {
                    shortUserRenewLog.setCode("x");
                }
            } catch (Exception ex) {
                log.error("解析失败", ex);
            }
//            try {
//                ObjectMapper mapper = new ObjectMapper();
//                JsonNode rootNode = mapper.readTree(e.getMessage());
//                // 从 details 中获取 original_response_code
//                String originalResponseCode = rootNode.path("details")
//                        .path("original_response_code")
//                        .asText();
//
//                log.info("originalResponseCode=================== - {}", originalResponseCode);
////                System.out.println("Original Response Code: " + originalResponseCode);
//                if(StringUtils.isNotEmpty(originalResponseCode) /*&& originalResponseCode.equals("03")*/){
////                    shortUserService.updateUnsub(user.getId());
//                    shortUserRenewLog.setCode(originalResponseCode);
//                }
//            } catch (Exception e1) {
//                e1.printStackTrace();
//            }


            shortUserRenewLog.setType(1L);
            shortUserRenewLog.setContent(e.getMessage());

            // 过滤错误码
//            List<String> list = Arrays.asList("01", "03", "07", "58", "13", "6P", "15", "93", "N7", "19", "30", "06", "88", "55", "72");
//            if (StringUtils.isNotEmpty(shortUserRenewLog.getCode()) && list.contains(shortUserRenewLog.getCode()))
//                shortUserService.updateUnsub(user.getId());

            shortUserRenewLogService.insertShortUserRenewLog(shortUserRenewLog);

            if(StringUtils.isNotEmpty(user.getEmail()))
                businessFunctionApiService.sendEmailForInType(shortBrevo, shortMovieList, "brevo","payFailed");
            e.printStackTrace();
            return AjaxResult.error(400, e.getMessage());
        }

    }




    public void sendBatchXDSessed(InitUserDTO shortBrevo, String type,String exTime) throws Exception {
        SendSmtpEmail email = new SendSmtpEmail();
        TransactionalEmailsApi apiInstance = new TransactionalEmailsApi();
        EmailDTO emailDTO = new EmailDTO();
        ShortForwardEmailSend shortForwardEmailSend = new ShortForwardEmailSend();
        shortForwardEmailSend.setReEmail(shortBrevo.getReceiveEmail());
        Message message = null;
        if(type.equals("forward")){

            shortForwardEmailSend.setCreateTime(DateUtils.getNowDate());
            shortForwardEmailSend.setSentDate(DateUtils.getNowDate());
            shortForwardEmailSend.setAppId(shortBrevo.getAppId());


            ShortForwartEmailDomain shortForwartEmailDomain = new ShortForwartEmailDomain();
            shortForwartEmailDomain.setAppId(shortBrevo.getAppId());
            List<ShortForwartEmailDomain> list = shortForwartEmailDomainMapper.selectShortForwartEmailDomainList(shortForwartEmailDomain);
            if(!list.isEmpty()){

                String username = list.get(0).getDomain(); // 你的Forward Email地址
                String password = AESEncryption.decrypt(list.get(0).getPassword()); // 你的密码
                shortForwardEmailSend.setDomain(username);


                String host = "smtp.forwardemail.net"; // Forward Email SMTP服务器
                Properties props = new Properties();
                props.put("mail.smtp.host", host);
                props.put("mail.smtp.port", "465");
                props.put("mail.smtp.ssl.enable", "true");
                props.put("mail.smtp.auth", "true");


                try {
                    // 创建会话
                    Session session = Session.getInstance(props,
                            new Authenticator() {
                                protected PasswordAuthentication getPasswordAuthentication() {
                                    return new PasswordAuthentication(username, password);
                                }
                            });

                    // 创建邮件

                    message = new MimeMessage(session);
                    message.setFrom(new InternetAddress(username));
                    message.setRecipients(Message.RecipientType.TO,
                            InternetAddress.parse(shortForwardEmailSend.getReEmail()));

                } catch (MessagingException e) {}
            }


        }

        // 3. 设置发件人
        SendSmtpEmailSender sender = new SendSmtpEmailSender();
        ShortEmailDomain shortEmailDomain = shortEmailDomainService.getrandomDomain(shortBrevo.getAppId());

        if (type.equals("brevo")) {
            // 2. 初始化邮件API
            ApiClient defaultClient = Configuration.getDefaultApiClient();
            ApiKeyAuth apiKey = (ApiKeyAuth) defaultClient.getAuthentication("api-key");
            apiKey.setApiKey(brevoApiKey); // 替换为你的Brevo API密钥

            if(null != shortEmailDomain){
                sender.setEmail(shortEmailDomain.getDomain()); // 必须已验证的邮箱
                sender.setName(shortEmailDomain.getAppName());
            }else{
                sender.setEmail(senderEmail); // 必须已验证的邮箱
                sender.setName(senderName);
            }


            List<SendSmtpEmailTo> toList = new ArrayList<>();
            // 4. 设置收件人
            SendSmtpEmailTo recipient = new SendSmtpEmailTo();
            recipient.setEmail(null != shortBrevo.getReceiveEmail() ? shortBrevo.getReceiveEmail() : null);
            recipient.setName(null != shortBrevo.getReceiveName() ? shortBrevo.getReceiveName() : null);
            toList.add(recipient);
            email.setSender(sender);
            email.setTo(toList);
        }else if(type.equals("aws")){
            emailDTO.setReceiveEmail(ListUtil.toList(shortBrevo.getReceiveEmail()));
            emailDTO.setSendChannel("aws");
            emailDTO.setServiceType("awsSimpl");
            emailDTO.setTitle("Kushort");
        }
        // 记录日志并处理结果
        ShortRunlog runlog = new ShortRunlog();
        runlog.setType("发送订阅续费成功邮件");
        runlog.setCreateTime(DateUtils.getNowDate());
        runlog.setUpdateTime(DateUtils.getNowDate());

        try {
            // 5. 从HTML文件读取内容
            String htmlName = "";

            if(null == shortBrevo.getLanguageCode() || shortBrevo.getLanguageCode().equals("en-US")){
                if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                    htmlName = "sub2.html";
                else
                    htmlName = shortBrevo.getHtmlName();
            }else if(shortBrevo.getLanguageCode().equals("es")){
                if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                    htmlName = "es-sub2.html";
                else
                    htmlName = shortBrevo.getHtmlName();
            }else if(shortBrevo.getLanguageCode().equals("pt")){
                if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                    htmlName = "pt-sub2.html";
                else
                    htmlName = shortBrevo.getHtmlName();
            }else if(shortBrevo.getLanguageCode().equals("zh-TW")){
                if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                    htmlName = "zh-TW-sub2.html";
                else
                    htmlName = shortBrevo.getHtmlName();
            }else if(shortBrevo.getLanguageCode().equals("ja")){
                if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                    htmlName = "ja-sub2.html";
                else
                    htmlName = shortBrevo.getHtmlName();
            }else if(shortBrevo.getLanguageCode().equals("de")){
                if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                    htmlName = "de-sub2.html";
                else
                    htmlName = shortBrevo.getHtmlName();
            }


            ClassPathResource resource = new ClassPathResource("templates/" + htmlName);
            String htmlContent = StreamUtils.copyToString(
                    resource.getInputStream(),
                    StandardCharsets.UTF_8
            );
//            String subject = getSubject(shortBrevo);
            String subject =shortEmailDomain.getAppName()+ " Renewal Reminder";



            String appDomain = null != shortEmailDomain.getAppDomain() ? shortEmailDomain.getAppDomain() : "";

            String randomNumApp1 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
            String randomNumApp2 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
//            ?userId=前六   + +  后六&uniqueId=
            ShortUser shortUser = shortUserService.selectShortUserById(Long.valueOf(shortBrevo.getUserId()));

            String unSubUrl = appDomain+"/subscription-records/"+"?userId="+randomNumApp1+shortUser.getHashId()+randomNumApp2+"&uniqueId="+shortBrevo.getUniqueId()+"&language="+shortBrevo.getLanguageCode()+"&noLogin=true";
            if(null != shortUser)
                appDomain = appDomain+"?userId="+randomNumApp1+shortUser.getHashId()+randomNumApp2+"&uniqueId="+shortBrevo.getUniqueId()+"&language="+shortBrevo.getLanguageCode();


            ShortPage shortPage = new ShortPage();
            shortPage.setAppId(String.valueOf(shortUser.getAppId()));
            shortPage.setCname("投诉和反馈");
            List<ShortPage> shortPageList = shortPageService.selectShortPageList(shortPage);




//
//            String dayType = order.getSubscriptionType();
//            Date payTime = order.getPayTime();
//            BigDecimal buyPrice = order.getPaymentAmount();
//            BigDecimal renewalPrice = order.getPaymentAmount();
//            http://localhost:3000/subscription-records/

            List<ShortMovie> shortMovieList = shortMovieService.getByRand4(shortUser.getAppId());



            int randomFourDigit1 = ThreadLocalRandom.current().nextInt(1000, 10000);
            int randomFourDigit2 = ThreadLocalRandom.current().nextInt(1000, 10000);
            int randomFourDigit3 = ThreadLocalRandom.current().nextInt(1000, 10000);
            String randomNum = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
            String randomNum1 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);

            String movieUrl1 = shortEmailDomain.getAppDomain() + "/video/?id=" + randomFourDigit1 + shortMovieList.get(0).getId() + "&userId=" + randomNum + shortUser.getHashId() + randomNum1 + "&uniqueId=" + shortUser.getUniqueId();

            String randomNum2 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
            String randomNum3 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
            String movieUrl2 = shortEmailDomain.getAppDomain() + "/video/?id=" + randomFourDigit2 + shortMovieList.get(1).getId() + "&userId=" + randomNum2 + shortUser.getHashId() + randomNum3 + "&uniqueId=" + shortUser.getUniqueId();


            String randomNum4 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
            String randomNum5 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
            String movieUrl3 = shortEmailDomain.getAppDomain() + "/video/?id=" + randomFourDigit3 + shortMovieList.get(2).getId() + "&userId=" + randomNum4 + shortUser.getHashId() + randomNum5 + "&uniqueId=" + shortUser.getUniqueId();

            String appFeedback = shortEmailDomain.getAppDomain() + "/complaint-and-feedback/?userId=" + randomNum4 + shortUser.getHashId() + randomNum5 + "&uniqueId=" + shortUser.getUniqueId();





            // 使用更安全的占位符格式
            htmlContent = htmlContent
                    .replace("${user_name}", null != shortBrevo.getUser_name() ? shortBrevo.getUser_name() : "")

                    .replace("${appName}", null != shortEmailDomain.getAppName() ? shortEmailDomain.getAppName() : "")
                    .replace("{{appName}}", null != shortEmailDomain.getAppName() ? shortEmailDomain.getAppName() : "")

                    .replace("${appDomain}", appDomain)
                    .replace("{{appDomain}}", appDomain)
                    .replace("${appFeedback}", appFeedback)
                    .replace("{{appFeedback}}", appFeedback)

                    .replace("${expireTime}",exTime )
                    .replace("{{expireTime}}", exTime)


                    .replace("${jumpUrl}", shortPageList.get(0).getUrl()+"?selevt=3&email="+shortBrevo.getReceiveEmail())
                    .replace("{{jumpUrl}}", shortPageList.get(0).getUrl()+"?selevt=3&email="+shortBrevo.getReceiveEmail())


                    .replace("${unSubUrl}", unSubUrl)
                    .replace("{{unSubUrl}}", unSubUrl)

                    .replace("${movieIcon1}", null != shortMovieList.get(0).getIcon() ? shortMovieList.get(0).getIcon() : "")
                    .replace("{{movieIcon1}}", null != shortMovieList.get(0).getIcon() ? shortMovieList.get(0).getIcon() : "")

                    .replace("${movieIcon2}", null != shortMovieList.get(1).getIcon() ? shortMovieList.get(1).getIcon() : "")
                    .replace("{{movieIcon2}}", null != shortMovieList.get(1).getIcon() ? shortMovieList.get(1).getIcon() : "")

                    .replace("${movieIcon3}", null != shortMovieList.get(2).getIcon() ? shortMovieList.get(2).getIcon() : "")
                    .replace("{{movieIcon3}}", null != shortMovieList.get(2).getIcon() ? shortMovieList.get(2).getIcon() : "1")



                    .replace("${movieName1}", null != shortMovieList.get(0).getName() ? shortMovieList.get(0).getName() : "")
                    .replace("{{movieName1}}", null != shortMovieList.get(0).getName() ? shortMovieList.get(0).getName() : "")

                    .replace("${movieName2}", null != shortMovieList.get(1).getName() ? shortMovieList.get(1).getName() : "")
                    .replace("{{movieName2}}", null != shortMovieList.get(1).getName() ? shortMovieList.get(1).getName() : "")

                    .replace("${movieName3}", null != shortMovieList.get(2).getName() ? shortMovieList.get(2).getName() : "")
                    .replace("{{movieName3}}", null != shortMovieList.get(2).getName() ? shortMovieList.get(2).getName() : "")

                    .replace("${movieUrl1}", movieUrl1)
                    .replace("{{movieUrl1}}", movieUrl1)

                    .replace("${movieUrl2}", movieUrl2)
                    .replace("{{movieUrl2}}", movieUrl2)

                    .replace("${movieUrl3}", movieUrl3)
                    .replace("{{movieUrl3}}", movieUrl3)


                    .replace("{{user_name}}", null != shortBrevo.getUser_name() ? shortBrevo.getUser_name() : ""); // 兼容旧格式

            if (type.equals("brevo")) {
                // 6. 创建邮件内容
                email.setSubject(subject);
                email.setHtmlContent(htmlContent); // 使用HTML内容
            }else if(type.equals("forward")){

                message.setSubject(subject);
                shortForwardEmailSend.setSubject(subject);

                // 创建邮件正文部分
                MimeBodyPart textPart = new MimeBodyPart();

                // 组合 HTML 内容
                textPart.setContent(htmlContent, "text/html; charset=utf-8");

                // 组合各部分
                Multipart multipart = new MimeMultipart();
                multipart.addBodyPart(textPart);

                message.setContent(multipart);
                shortForwardEmailSend.setContent(htmlContent);


            }else{
                emailDTO.setSubject(subject);
                emailDTO.setContent(htmlContent);
            }

            Message finalMessage = message;
            new Thread(() -> {
                try {
                    runlog.setState("1");
                    String content = "";
                    if (type.equals("brevo")) {
                        // 7. 发送邮件
                        CreateSmtpEmail response = apiInstance.sendTransacEmail(email);
                        content = String.format(",邮件发送成功！消息ID: " + response.getMessageId() + "用户Id：" + shortBrevo.getUserId() +
                                ",邮件主题：" + shortBrevo.getSubject() + ",收件人名称：" + shortBrevo.getReceiveName() +
                                ",收件人邮件：" + shortBrevo.getReceiveEmail()+",邮件服务商:"+type);
                        runlog.setNote(response.toString());
                    } else if (type.equals("aws")) {
//                        emailServiceContext.getService(emailDTO.getServiceType()).sendEmail(emailDTO);
                        content = String.format(",邮件发送成功！用户Id：" + shortBrevo.getUserId() +
                                ",邮件主题：" + shortBrevo.getSubject() +
                                ",收件人邮件：" + shortBrevo.getReceiveEmail()+
                                ",邮件服务商:"+type);
                        runlog.setNote(type);
                    }else if (type.equals("forward")){
                        // 发送邮件
                        Transport.send(finalMessage);
                        content = String.format(",邮件发送成功！" +  "用户Id：" + shortBrevo.getUserId() +
                                ",邮件主题：" + shortBrevo.getSubject() + ",收件人名称：" + shortBrevo.getReceiveName() +
                                ",收件人邮件：" + shortBrevo.getReceiveEmail()+",邮件服务商:"+type);

                        shortForwardEmailSend.setType(1L);
                        shortForwardEmailSendMapper.insertShortForwardEmailSend(shortForwardEmailSend);

                    }
                    runlog.setContent(content);
                    shortRunlogService.insertShortRunlog(runlog);
                } catch (Exception e) {
                    runlog.setState("0");
                    runlog.setContent(String.format(",发送失败：" + e.getMessage() + ",邮件主题：" + shortBrevo.getSubject() +
                            ",收件人名称：" + shortBrevo.getReceiveName() + ",收件人邮件：" + shortBrevo.getReceiveEmail())+",邮件服务商:"+type);
                    runlog.setNote(e.toString());
                    shortRunlogService.insertShortRunlog(runlog);
                }
            }).start();

//            String randomNum = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 4);
//            Map<String, String> vars = new HashMap<>();
//            vars.put("uId", randomNum + shortBrevo.getUserId());
//            vars.put("uniqueId", shortBrevo.getUniqueId());
//            return AjaxResult.success(vars);
        } catch (IOException e) {
            System.err.println("读取HTML文件失败: " + e.getMessage());
            runlog.setState("0");
            runlog.setContent(String.format(",读取HTML文件失败：" + e.getMessage() + ",邮件主题：" + shortBrevo.getSubject() + ",收件人名称：" + shortBrevo.getReceiveName() + ",收件人邮件：" + shortBrevo.getReceiveEmail()));
            runlog.setNote(e.toString());
            shortRunlogService.insertShortRunlog(runlog);

//            return AjaxResult.error("读取HTML文件失败: " + e.getMessage());
        } catch (Exception e) {
            System.err.println("发送失败: " + e.getMessage());
            runlog.setState("0");
            runlog.setContent(String.format(",发送失败：" + e.getMessage() + ",邮件主题：" + shortBrevo.getSubject() + ",收件人名称：" + shortBrevo.getReceiveName() + ",收件人邮件：" + shortBrevo.getReceiveEmail()));
            runlog.setNote(e.toString());
            shortRunlogService.insertShortRunlog(runlog);
//            return AjaxResult.error("发送失败: " + e.getMessage());
        }
    }


    public static String formatToEnglishUTC(Object time) {
        // 定义纯英文格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(
                "EEE MMM dd HH:mm:ss 'UTC' yyyy",
                Locale.ENGLISH
        );

        // 处理不同类型的时间对象
        if (time instanceof Instant) {
            return ((Instant) time).atZone(ZoneOffset.UTC).format(formatter);
        } else if (time instanceof ZonedDateTime) {
            return ((ZonedDateTime) time).withZoneSameInstant(ZoneOffset.UTC).format(formatter);
        } else if (time instanceof LocalDateTime) {
            return ((LocalDateTime) time).atZone(ZoneOffset.UTC).format(formatter);
        } else if (time instanceof Date) {
            return ((Date) time).toInstant().atZone(ZoneOffset.UTC).format(formatter);
        } else {
            throw new IllegalArgumentException("Unsupported time type: " + time.getClass());
        }
    }

}