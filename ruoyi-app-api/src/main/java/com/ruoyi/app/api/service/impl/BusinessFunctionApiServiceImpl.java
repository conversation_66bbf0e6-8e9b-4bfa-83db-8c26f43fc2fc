package com.ruoyi.app.api.service.impl;

import brevo.ApiClient;
import brevo.Configuration;
import brevo.auth.ApiKeyAuth;
import brevoApi.TransactionalEmailsApi;
import brevoModel.CreateSmtpEmail;
import brevoModel.SendSmtpEmail;
import brevoModel.SendSmtpEmailSender;
import brevoModel.SendSmtpEmailTo;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.KeyPair;
import com.ruoyi.app.api.dto.EmailLoginDTO;
import com.ruoyi.app.api.dto.InitUserDTO;
import com.ruoyi.app.api.service.BusinessFunctionApiService;
import com.ruoyi.app.api.service.IpInfoService;
import com.ruoyi.app.api.utils.DeviceUtils;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.dto.EmailDTO;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.PayChannel;
import com.ruoyi.common.exception.base.BaseException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.SystemVersionComparator;
import com.ruoyi.domain.*;
import com.ruoyi.mapper.ShortEmailSendLogMapper;
import com.ruoyi.mapper.ShortForwardEmailSendMapper;
import com.ruoyi.mapper.ShortForwartEmailDomainMapper;
import com.ruoyi.service.*;
import com.ruoyi.service.impl.AESEncryption;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.StreamUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.ruoyi.common.utils.Threads.sleep;

/**
 * 业务功能API服务实现类
 */
@Slf4j
@Service
public class BusinessFunctionApiServiceImpl implements BusinessFunctionApiService {

    @Autowired
    private IShortMovieI18nService shortMovieI18nService;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private IShortFeedbackService shortFeedbackService;

    @Autowired
    private IShortEventService shortEventService;

    @Autowired
    private IShortUserService shortUserService;

    @Autowired
    private IShortUserEventRecordService shortUserEventRecordService;

    @Autowired
    private IShortAppService shortAppService;

    @Autowired
    private IShortFcmService shortFcmService;

    @Autowired
    private IShortRunlogService shortRunlogService;

    @Autowired
    private IShortUserLinkRecordService shortUserLinkRecordService;

    @Autowired
    private IShortUserActivityLogService shortUserActivityLogService;

    // 添加支付平台服务依赖
    @Autowired
    private IShortExtplatsService shortExtplatsService;

    // 支付API URL
    @Value("${payment.use.dev:false}")
    private boolean useDevApi;

    @Value("${payment.api.dev.url:https://api-demo.airwallex.com}")
    private String devApiUrl;

    @Value("${payment.api.prod.url:https://api.airwallex.com}")
    private String prodApiUrl;
    @Value("${pay.temp.id}")
    private Long payTempId;

    // 是否启用支付预加载
    private boolean renderingAdvance = true;

    // 添加RestTemplate用于HTTP请求
    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private IpInfoService ipInfoService;

    // 为异步任务创建线程池
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(5);

    @Autowired
    private IShortSemLinkService shortSemLinkService;

    @Autowired
    private IShortMovieService shortMovieService;

    @Autowired
    private IShortOrderService shortOrderService;

    @Autowired
    private IShortFacebookPixelService shortFacebookPixelService;

    @Autowired
    private IShortEmailSendLogService shortEmailSendLogService;

    @Autowired
    private IShortIntermedStatusService shortIntermedStatusService;

    @Value("${brevo.api-key}")
    private String brevoApiKey;

    @Value("${brevo.sender.email}")
    private String senderEmail;

    @Value("${brevo.sender.name}")
    private String senderName;

    // 默认Facebook访问令牌和像素ID
    @Value("${facebook.pixel.access_token:EAAaiPzZC9gDoBOxeP4CWxt8r0G50PZCD1ldqTYeknZBNbzFvZBj4k6t0rf4dGsJ6J41vqttlwZCuclceh4S9jimJ3jttlArXXTNuGlIPyZAQYZCCEpt1iYoNwjTBhFTN5tZC3feDFaPb7SszwbPbDJbfmmMz8ZCrNA0FJZBIJZAYZBIvSsGrxbQXDkd07hFrl6ZCDZCy6teQZDZD}")
    private String defaultFacebookAccessToken;

    @Value("${facebook.pixel.id:613126348222290}")
    private String defaultFacebookPixelId;

    @Value("${facebook.api.version:v18.0}")
    private String facebookApiVersion;
    @Resource
    private ShortEmailSendLogMapper shortEmailSendLogMapper;
    @Resource
    private EmailServiceContext emailServiceContext;

    @Autowired
    private IShortUserEamilService shortUserEamilService;

    @Autowired
    private IShortEmailDomainService shortEmailDomainService;
    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Resource
    private RedisCache redisCache;
    @Autowired
    private IShortPageService shortPageService;

    @Autowired
    private ShortForwartEmailDomainMapper shortForwartEmailDomainMapper;

    @Autowired
    private ShortForwardEmailSendMapper shortForwardEmailSendMapper;

    @Autowired
    private IShortForwardEmailReceiveService shortForwardEmailReceiveService;

    @Value("${useepay.domain}")
    private String useepayDomain;

    @Value("${useepay.createCustomerUrl}")
    private String useepayCreateCustomerUrl;

    @Value("${useepay.createInvoiceUrl}")
    private String useepayCreateInvoiceUrl;

    @Value("${useepay.createPaymentIntentsUrl}")
    private String useepayCreatePaymentIntentsUrl;

    public static final List<Long> h5ResolveUaAppList = new ArrayList<>();


    public static final Map<String, JSONObject> appPixelRuleMap = new HashMap<>();

    private boolean isPrivateOrLoopbackIp(String ipAddress) {
        try {
            InetAddress inetAddress = InetAddress.getByName(ipAddress);
            return inetAddress.isSiteLocalAddress() || inetAddress.isLoopbackAddress();
        } catch (UnknownHostException e) {
            log.warn("检查IP地址是否为私有/回环地址时遇到格式错误: {}. 错误信息: {}", ipAddress, e.getMessage());
            return false;
        }
    }


    @Override
    public Map<String, Object> addFeedback(String token, String appId, Map<String, Object> feedbackData) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 获取请求参数
            Object userId = feedbackData.get("user");
            String content = (String) feedbackData.get("content");

            // 验证参数
            if (userId == null || StringUtils.isEmpty(content) || StringUtils.isEmpty(appId)) {
                result.put("code", 400);
                result.put("msg", "参数不完整");
                return result;
            }

            // 处理用户ID
            String userIdStr = userId.toString();
            if (userIdStr.length() > 4) {
                userIdStr = userIdStr.substring(4);
            }

            // 将反馈信息保存到数据库
            ShortFeedback feedback = new ShortFeedback();
            feedback.setAppId(Long.parseLong(appId));
            feedback.setUserId(Long.parseLong(userIdStr));
            feedback.setContent(content);
            feedback.setStatus("0"); // 正常状态

            // 保存到数据库
            int result1 = shortFeedbackService.insertShortFeedback(feedback);

            if (result1 > 0) {
                log.info("添加反馈成功: appId={}, userId={}, content={}", appId, userIdStr, content);
                result.put("code", 200);
                result.put("msg", "添加成功");
            } else {
                log.error("添加反馈失败: appId={}, userId={}, content={}", appId, userIdStr, content);
                result.put("code", 400);
                result.put("msg", "添加失败");
            }

            return result;
        } catch (Exception e) {
            log.error("添加反馈失败", e);
            result.put("code", 400);
            result.put("msg", "添加失败: " + e.getMessage());
            return result;
        }
    }

    @Override
    public Map<String, Object> getDailySign(String token, String appId, String uid, Integer activity) {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("获取每日签到活动: appId={}, activity={}, token={}, uid={}", appId, activity, token, uid);

            // 查询活动信息
            ShortEvent shortEvent = shortEventService.selectShortEventById(Long.valueOf(activity));
            System.out.println(shortEvent);
            // 检查活动状态是否为1（已发布）
            if (shortEvent == null || !"1".equals(shortEvent.getState())) {
                result.put("code", 400);
                result.put("msg", "获取每日签到赠送金币的活动失败-2");
                return result;
            }

            log.info("活动: {}", shortEvent.getName());
            log.info("活动规则: {}", shortEvent.getRule());

            // 解析活动规则JSON - 修复JSON解析问题
            Map<String, Object> ruleDict;
            try {
                String ruleJson = shortEvent.getRule();
                // 处理外层多余的引号
                if (ruleJson.startsWith("\"") && ruleJson.endsWith("\"")) {
                    ruleJson = ruleJson.substring(1, ruleJson.length() - 1);
                    // 处理转义字符
                    ruleJson = ruleJson.replace("\\r\\n", "")
                            .replace("\\\"", "\"")
                            .replace("\\\\", "\\");
                }
                ruleDict = objectMapper.readValue(ruleJson, new TypeReference<Map<String, Object>>() {
                });
            } catch (Exception e) {
                log.error("解析活动规则失败", e);
                ruleDict = new HashMap<>();
            }

            // 获取每日奖励金币数
            Map<String, Object> rewards = (Map<String, Object>) ruleDict.getOrDefault("rewards", new HashMap<>());
            Integer dailyReward = (Integer) rewards.getOrDefault("daily", 0);
            log.info("奖励的金币数: {}", dailyReward);

            // 查找用户信息
            ShortUser user = null;
            if (StringUtils.isNotEmpty(token)) {
                ShortUser queryUser = new ShortUser();
                queryUser.setToken(token);
                queryUser.setAppId(Long.valueOf(appId));
                queryUser.setStatus("0"); // 正常状态

                // 查询用户列表并取第一个
                user = shortUserService.selectShortUserList(queryUser).stream().findFirst().orElse(null);
            } else if (StringUtils.isNotEmpty(uid)) {
                // 通过uid查找用户
                ShortUser queryUser = new ShortUser();
                queryUser.setId(Long.valueOf(uid));
                queryUser.setAppId(Long.valueOf(appId));
                queryUser.setStatus("0"); // 正常状态

                // 查询用户列表并取第一个
                user = shortUserService.selectShortUserList(queryUser).stream().findFirst().orElse(null);
            }

            if (user != null) {
                log.info("找到用户: userId={}", user.getId());

                // 查询用户最新的签到记录
                ShortUserEventRecord queryRecord = new ShortUserEventRecord();
                queryRecord.setUserId(user.getId());
                queryRecord.setEventId(shortEvent.getId());
                queryRecord.setEventType("Daily_Login");

                // 获取用户的签到记录列表并找到最新的一条 - 修复空指针异常
                ShortUserEventRecord latestRecord = shortUserEventRecordService.selectShortUserEventRecordList(queryRecord)
                        .stream()
                        .filter(r -> r.getCreateTime() != null) // 过滤掉createTime为null的记录
                        .sorted((r1, r2) -> {
                            // 添加null安全比较
                            if (r1.getCreateTime() == null) return 1;
                            if (r2.getCreateTime() == null) return -1;
                            return r2.getCreateTime().compareTo(r1.getCreateTime());
                        }) // 按创建时间降序排序
                        .findFirst()
                        .orElse(null);

                Date now = new Date();

                // 检查是否在24小时内签到过
                if (latestRecord == null ||
                        (now.getTime() - latestRecord.getCreateTime().getTime()) >= 24 * 3600 * 1000) {

                    log.info("用户未在24小时内签到，创建新的签到记录");

                    // 创建新的签到记录
                    ShortUserEventRecord newRecord = new ShortUserEventRecord();
                    newRecord.setUserId(user.getId());
                    newRecord.setEventId(shortEvent.getId());
                    newRecord.setEventType("Daily_Login");
                    newRecord.setState("1");
                    newRecord.setNote("每日签到奖励" + dailyReward + "金币");

                    // 保存签到记录
                    shortUserEventRecordService.insertShortUserEventRecord(newRecord);

                    // 更新用户的金币
                    if (user.getCoin() == null) {
                        user.setCoin(0L);
                    }
                    user.setCoin(user.getCoin() + dailyReward);
                    shortUserService.updateShortUser(user);

                    log.info("用户签到成功，增加{}金币", dailyReward);
                } else {
                    log.info("用户已在24小时内签到过，不再增加金币");
                }
            } else {
                log.info("未找到用户");
            }

            // 构建活动数据响应
            Map<String, Object> activityData = new HashMap<>();
            activityData.put("id", shortEvent.getId());
            activityData.put("state", shortEvent.getState());
            activityData.put("name", shortEvent.getName());
            activityData.put("rule", shortEvent.getRule());

            // 返回成功结果
            result.put("code", 200);
            result.put("msg", "success");
            result.put("data", activityData);

            return result;
        } catch (Exception e) {
            log.error("获取每日签到活动失败", e);
            result.put("code", 400);
            result.put("msg", "获取每日签到赠送金币的活动失败: " + e.getMessage());
            return result;
        }
    }

    @Override
    public Map<String, Object> getVersion(String appId, String type, Map<String, Object> versionData) {
        Map<String, Object> resultMap = new HashMap<>();

        String linkidid = MapUtils.getString(versionData, "linkid");
        if (StringUtils.isEmpty(linkidid) && "null".equals(linkidid) && "none".equals(linkidid)) {
            sleep(800);
        }

        try {
            log.info("获取应用版本信息: appId={}, type={}", appId, type);

            // 获取请求参数
            String version = MapUtils.getString(versionData, "ver");
            String fcmToken = MapUtils.getString(versionData, "fcm_token");
            String source = MapUtils.getString(versionData, "source");
            String phoneVersion = MapUtils.getString(versionData, "phone_version");
            String systemVersion = MapUtils.getString(versionData, "system_version");
            String language = MapUtils.getString(versionData, "language");
            String appVersion = MapUtils.getString(versionData, "app_version");
            String accountType = MapUtils.getString(versionData, "account_type");
            String uniqueId = MapUtils.getString(versionData, "unique_id");
            String ip = MapUtils.getString(versionData, "ip");
            String ua = MapUtils.getString(versionData, "ua");
            String fbc = MapUtils.getString(versionData, "fbc");
            String fbp = MapUtils.getString(versionData, "fbp");
            String linkid = MapUtils.getString(versionData, "linkid");
            String campaignName = MapUtils.getString(versionData, "campaignName");
            String campaignId = MapUtils.getString(versionData, "campaignId");
            String adsetName = MapUtils.getString(versionData, "adsetName");
            String adsetId = MapUtils.getString(versionData, "adsetId");
            String adName = MapUtils.getString(versionData, "adName");
            String adId = MapUtils.getString(versionData, "adId");
            String deepid = MapUtils.getString(versionData, "deepid");
            String userType = MapUtils.getString(versionData, "user_type");

            // 深链信息
            String movie = MapUtils.getString(versionData, "movie");
            String pid = MapUtils.getString(versionData, "pid");
            String kid = MapUtils.getString(versionData, "kid");
            String pixelId = MapUtils.getString(versionData, "pixel_id");

            // 过滤掉本地局域网IP地址
            if (StringUtils.isNotEmpty(ip) && !"null".equals(ip) && !"none".equals(ip)) {
                if (isPrivateOrLoopbackIp(ip)) {
                    versionData.put("ip", "");
                    ip = "";
                }
            }

            // 打印关键日志信息
            log.info("版本号: {}, 来源: {}, APP版本: {}, 系统版本: {}, 推广链接ID: {}",
                    version, source, appVersion, systemVersion, linkid);

            boolean appSwitch = false;
            boolean isRenderingAdvance = renderingAdvance;

            log.info("开始处理用户信息");

            // 为用户创建一个临时账户
            ShortUser tempUser = null;

            // 根据 linkid 获取 ShortSemLink 信息
            ShortSemLink shortSemLink = null;
            if (StringUtils.isNotEmpty(linkid) && !"null".equals(linkid) && !"none".equals(linkid)) {
                try {
                    shortSemLink = shortSemLinkService.selectShortSemLinkById(Long.valueOf(linkid));
                    if (shortSemLink != null) {
                        log.info("根据linkid={}, 查询到深链信息={}", linkid, shortSemLink);
                    }
                } catch (Exception e) {
                    log.error("查询深链信息失败: linkid={}, error={}", linkid, e.getMessage());
                }
            }
            ShortApp appInfo = shortAppService.selectShortAppById(Long.valueOf(appId));
            if (null == appInfo) {
                throw new RuntimeException("appId不存在:" + appId);
            }
            String appType = appInfo.getType();
            // 先检查是否存在具有相同 unique_id 的用户
            ShortUser queryUser = new ShortUser();
            queryUser.setUniqueId(uniqueId);
            queryUser.setAppId(Long.valueOf(appId));

            // pixel_id 和 appId　反查　short_facebook_pixel　确定记录ID
//            ShortSemLink shortSemLink = null;
//            if(StringUtils.isNotEmpty(linkid)){
//                shortSemLink = shortSemLinkService.selectShortSemLinkById(Long.valueOf(linkid));
//                log.info("根据pixelId={},appId={},查询深链信息={}",  pixelId, appId, shortSemLink);
//            }
            List<ShortUser> existingUsers = shortUserService.selectShortUserList(queryUser);
            ShortUser existingUser = existingUsers.isEmpty() ? null : existingUsers.get(0);

            // 情况1: 用户已存在，更新用户信息
            if (existingUser != null) {
                log.info("找到已存在用户: id={}, uniqueId={}", existingUser.getId(), uniqueId);

                // 设置像素ID
                Long newPixelId = null;
                if (shortSemLink != null) {
                    newPixelId = shortSemLink.getPixelId();
                    existingUser.setPixelId(newPixelId);
                }

                // 记录用户多链接进入的数据，使用新的pixelId
                if (StringUtils.isNotEmpty(linkid) && !"null".equals(linkid) && !"none".equals(linkid) &&
                        (existingUser.getLinkidId() == null || !Long.valueOf(linkid).equals(existingUser.getLinkidId()))) {
                    ShortUserLinkRecord record = new ShortUserLinkRecord();
                    record.setUserId(existingUser.getId());
                    record.setLinkId(Long.valueOf(linkid));
                    record.setPixelId(newPixelId); // 直接使用深链的pixelId
                    shortUserLinkRecordService.insertShortUserLinkRecord(record);
                    log.info("记录用户多链接进入: userId={}, linkId={}, pixelId={}", existingUser.getId(), linkid, newPixelId);
                }

                // 使用该用户
                tempUser = existingUser;

                // 更新用户的一些信息
                if (StringUtils.isNotEmpty(source) && !"null".equals(source) && !"none".equals(source)) {
                    existingUser.setSource(source);  // 当有来源的时候 更新来源
                }
                existingUser.setPhoneVersion(phoneVersion);
                existingUser.setSystemVersion(systemVersion);
                existingUser.setLanguage(language);
                existingUser.setAppVersion(appVersion);
                if (StringUtils.isNotEmpty(adId) && !"null".equals(adId)) {
                    existingUser.setAdId(adId);
                }
                //只解析Android手机型号
                if (StringUtils.isNotEmpty(phoneVersion) && !"Apple".equals(phoneVersion)
                        && StringUtils.isNotEmpty(ua) && !"null".equals(ua)) {
                    String deviceName = DeviceUtils.getDeviceName(ua);
                    if (null != deviceName) {
                        existingUser.setPhoneVersion(deviceName);
                    }
                }
                //设置app类型和移动设备型号
                existingUser.setAppType(appType);
                if (StringUtils.isNotEmpty(existingUser.getPhoneVersion())) {
                    existingUser.setDeviceType("Apple".equals(existingUser.getPhoneVersion()) ? "0" : "1");
                }
                //解析ua 设置具体手机型号
                if (StringUtils.isNotEmpty(deepid) && !"null".equals(deepid) && !"none".equals(deepid)) {
                    existingUser.setNote(deepid);
                }
                if (StringUtils.isNotEmpty(ip)) {
                    existingUser.setIp(ip);
                }
                if (StringUtils.isNotEmpty(linkid) && !"null".equals(linkid) && !"none".equals(linkid)) {
                    if (null == existingUser.getLinkidId() || !String.valueOf(existingUser.getLinkidId()).equals(linkid)) {
                        existingUser.setLinkTime(DateUtils.getNowDate());
                    }
                    existingUser.setLinkidId(Long.valueOf(linkid));
                }
                if (StringUtils.isNotEmpty(userType) && !"null".equals(userType) && !"none".equals(userType)) {
                    existingUser.setUserType(userType);
                }

                // 使用Meta分级策略更新现有用户other信息
                log.info("ua========================: ua={}", ua);
                String existingUserOtherJson = buildOtherJsonWithGradedStrategy(fbc, fbp, ua, campaignName, campaignId,
                        adsetName, adsetId, adName, adId);
                if (existingUserOtherJson != null) {
                    existingUser.setOther(existingUserOtherJson);
                    log.info("现有用户分级策略更新other成功");
                }
                log.info("existingUser============: id={}", existingUser);
                shortUserService.updateShortUser(existingUser);
                log.info("更新用户信息成功: id={}", existingUser.getId());
            }
            // 情况2: 用户不存在，创建新用户
            else {
                log.info("未找到现有用户，准备创建新用户: uniqueId={}", uniqueId);

                // 创建新用户对象
                ShortUser newUser = new ShortUser();
                newUser.setUniqueId(uniqueId);
                newUser.setAppId(Long.valueOf(appId));
                newUser.setUsername("temp_user");
                newUser.setPassword("temp_user");
                newUser.setAvatar("/media/none.png");
                newUser.setState("1");
                newUser.setSource(source);
                newUser.setEmail("");
                newUser.setPhoneVersion(phoneVersion);
                newUser.setSystemVersion(systemVersion);
                newUser.setLanguage(language);
                newUser.setAppVersion(appVersion);
                newUser.setAccountType(accountType);
                newUser.setIp(ip);
                newUser.setSubId("");
                newUser.setToken("");
                newUser.setCoin(0L);
                newUser.setSignInDays(1L);
                newUser.setValidStatus(1);
                newUser.setChooseLanguage("en");
                if (StringUtils.isNotEmpty(adId) && !"null".equals(adId)) {
                    newUser.setAdId(adId);
                }
                //只解析Android手机型号
                if (StringUtils.isNotEmpty(phoneVersion) && !"Apple".equals(phoneVersion)
                        && StringUtils.isNotEmpty(ua) && !"null".equals(ua)) {
                    String deviceName = DeviceUtils.getDeviceName(ua);
                    if (null != deviceName) {
                        newUser.setPhoneVersion(deviceName);
                    }
                }
                //设置APP类型和移动设备型号
                newUser.setAppType(appType);
                if (StringUtils.isNotEmpty(newUser.getPhoneVersion())) {
                    newUser.setDeviceType("Apple".equals(newUser.getPhoneVersion()) ? "0" : "1");
                }
                // 设置像素ID (如果有)
                if (shortSemLink != null) {
                    newUser.setPixelId(shortSemLink.getPixelId());
                }

                // 设置国家信息
                Map<String, String> countryMap = ipInfoService.getCountryByIp(ip);
                newUser.setCountry(countryMap.get("countryName"));

                // 设置链接ID和时间
                if (StringUtils.isNotEmpty(linkid) && !"null".equals(linkid) && !"none".equals(linkid)) {
                    newUser.setLinkidId(Long.valueOf(linkid));
                    newUser.setLinkTime(DateUtils.getNowDate());
                }

                // 使用Meta分级策略设置新用户广告相关信息
                String newUserOtherJson = buildOtherJsonWithGradedStrategy(fbc, fbp, ua, campaignName, campaignId,
                        adsetName, adsetId, adName, adId);
                if (newUserOtherJson != null) {
                    newUser.setOther(newUserOtherJson);
                    log.info("新用户分级策略保存other成功");
                }
                newUser.setNote(deepid);

                // 设置用户类型
                if (StringUtils.isNotEmpty(userType) && !"null".equals(userType) && !"none".equals(userType)) {
                    newUser.setUserType(userType);
                }

                try {
                    // 尝试插入新用户 - 可能触发唯一约束异常
                    shortUserService.insertShortUser(newUser);
                    log.info("创建新用户成功: id={}", newUser.getId());

                    // 生成随机哈希ID
                    String randomNum = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 4);
                    newUser.setHashId(randomNum + newUser.getId());
                    shortUserService.updateShortUser(newUser);

                    // 记录用户链接进入
                    if (StringUtils.isNotEmpty(linkid) && !"null".equals(linkid) && !"none".equals(linkid)) {
                        ShortUserLinkRecord record = new ShortUserLinkRecord();
                        record.setUserId(newUser.getId());
                        record.setLinkId(Long.valueOf(linkid));
                        if (shortSemLink != null) {
                            record.setPixelId(shortSemLink.getPixelId());
                        }
                        shortUserLinkRecordService.insertShortUserLinkRecord(record);
                        log.info("记录新用户链接进入: userId={}, linkId={}", newUser.getId(), linkid);
                    }

                    tempUser = newUser;
                } catch (Exception e) {
                    // 捕获可能的唯一约束冲突 - 说明在尝试插入的同时，其他请求已经创建了相同的用户
                    log.warn("插入用户时发生冲突，尝试查询并更新现有用户: uniqueId={}, error={}", uniqueId, e.getMessage());
                    log.warn("tempUser这里边是什么===================", tempUser);

                    // 查询是否存在（其他线程刚刚创建的）用户
                    // 等待短暂时间，确保其他事务已完成
                    Thread.sleep(100);

                    List<ShortUser> retryUsers = shortUserService.selectShortUserList(queryUser);
                    if (retryUsers.isEmpty()) {
                        // 仍然没有找到，这是不可预期的错误
                        log.error("创建用户失败且未找到现有用户: uniqueId={}", uniqueId);
                        throw new BaseException("创建用户失败，请重试");
                    }

                    // 找到并使用现有用户，更新信息
                    ShortUser existingUserRetry = retryUsers.get(0);
                    log.info("找到已存在用户(处理冲突): id={}, uniqueId={}", existingUserRetry.getId(), uniqueId);

                    // 使用newUser中准备好的数据来更新existingUserRetry
                    existingUserRetry.setPhoneVersion(phoneVersion);
                    existingUserRetry.setSystemVersion(systemVersion);
                    existingUserRetry.setLanguage(language);
                    existingUserRetry.setAppVersion(appVersion);
                    existingUserRetry.setIp(ip);

                    if (StringUtils.isNotEmpty(source) && !"null".equals(source) && !"none".equals(source)) {
                        existingUserRetry.setSource(source);
                    }

                    if (StringUtils.isNotEmpty(deepid) && !"null".equals(deepid) && !"none".equals(deepid)) {
                        existingUserRetry.setNote(deepid);
                    }

                    if (StringUtils.isNotEmpty(linkid) && !"null".equals(linkid) && !"none".equals(linkid)) {
                        existingUserRetry.setLinkidId(Long.valueOf(linkid));
                        existingUserRetry.setLinkTime(DateUtils.getNowDate());
                    }

                    if (StringUtils.isNotEmpty(userType) && !"null".equals(userType) && !"none".equals(userType)) {
                        existingUserRetry.setUserType(userType);
                    }

                    if (shortSemLink != null) {
                        existingUserRetry.setPixelId(shortSemLink.getPixelId());
                    }

                    // 使用Meta分级策略处理并发冲突情况
                    log.info("并发处理检查: fbc={}, fbp={}", fbc, fbp);
                    String retryUserOtherJson = buildOtherJsonWithGradedStrategy(fbc, fbp, ua, campaignName, campaignId,
                            adsetName, adsetId, adName, adId);
                    if (retryUserOtherJson != null) {
                        existingUserRetry.setOther(retryUserOtherJson);
                        log.info("并发处理分级策略更新other成功");
                    }

                    shortUserService.updateShortUser(existingUserRetry);
                    log.info("更新用户信息成功(处理冲突): id={}", existingUserRetry.getId());

                    // 记录用户多链接进入
                    if (StringUtils.isNotEmpty(linkid) && !"null".equals(linkid) && !"none".equals(linkid) &&
                            (existingUserRetry.getLinkidId() == null || !Long.valueOf(linkid).equals(existingUserRetry.getLinkidId()))) {
                        ShortUserLinkRecord record = new ShortUserLinkRecord();
                        record.setUserId(existingUserRetry.getId());
                        record.setLinkId(Long.valueOf(linkid));
                        record.setPixelId(existingUserRetry.getPixelId());
                        shortUserLinkRecordService.insertShortUserLinkRecord(record);
                        log.info("记录用户多链接进入(处理冲突): userId={}, linkId={}", existingUserRetry.getId(), linkid);
                    }

                    tempUser = existingUserRetry;
                }
            }

            // 新增一条FCM推送对象
            if (StringUtils.isNotEmpty(fcmToken) && tempUser != null) {
                try {
                    // 检查是否已存在相同token的记录
                    ShortFcm queryFcm = new ShortFcm();
                    queryFcm.setUserId(tempUser.getId());
                    List<ShortFcm> existingFcms = shortFcmService.selectShortFcmList(queryFcm);

                    if (existingFcms.isEmpty()) {
                        log.info("创建新的FCM推送对象: userId={}", tempUser.getId());
                        ShortFcm fcm = new ShortFcm();
                        fcm.setUserId(tempUser.getId());
                        fcm.setAppId(Long.valueOf(appId));
                        fcm.setToken(fcmToken);
                        shortFcmService.insertShortFcm(fcm);
                    } else {
                        log.info("更新现有FCM推送对象: userId={}", tempUser.getId());
                        ShortFcm existingFcm = existingFcms.get(0);
                        existingFcm.setToken(fcmToken);
                        shortFcmService.updateShortFcm(existingFcm);
                    }
                } catch (Exception e) {
                    log.error("处理FCM推送对象失败", e);
                }
            }

            // 判断是否为新用户(pixelStatus为空或0)并处理异步任务
            if (tempUser.getPixelStatus() == null || tempUser.getPixelStatus() == 0L) {
                log.info("检测到新用户(push_num={}),开始执行异步处理: userId={}", tempUser.getPushNum(), tempUser.getId());
                final ShortUser finalTempUser = tempUser;

                // 异步处理以不阻塞主流程，增加5秒延迟确保用户完全创建成功后再执行
                scheduler.schedule(() -> {
                    try {
                        // 重新查询用户获取最新数据
                        ShortUser latestUser = shortUserService.selectShortUserById(finalTempUser.getId());
                        if (latestUser != null) {
                            log.info("延迟5秒后查询到用户最新信息: userId={}, source={}", latestUser.getId(), latestUser.getSource());

                            // 处理支付Token和客户ID
                            processPaymentInfo(latestUser, appId);

                            // 处理Facebook事件
                            voFacebook(latestUser);

                            // 设置pixelStatus为1，标记已处理过
                            latestUser.setPixelStatus(1);
                            shortUserService.updateShortUser(latestUser);
                            log.info("新用户异步处理完成并更新pixelStatus=1: userId={}", latestUser.getId());
                        } else {
                            log.error("延迟执行任务后未找到用户: userId={}", finalTempUser.getId());
                        }
                    } catch (Exception e) {
                        log.error("异步处理新用户附加信息失败: userId={}, error={}",
                                finalTempUser.getId(), e.getMessage(), e);
                    }
                }, 5, TimeUnit.SECONDS);
            }

            // 上报用户行为日志
            if (tempUser != null) {
                try {
                    recordUserActivityLog(tempUser, "login", versionData, appId, linkid, movie, pid, kid, pixelId, adId);
                } catch (Exception e) {
                    log.error("上报用户行为日志失败", e);
                    // 不影响主流程，这里仅记录错误日志
                }
            }

            // 拼接用户ID并返回结果
            String uid = tempUser != null ? tempUser.getHashId() : "";

            resultMap.put("code", 200);
            resultMap.put("msg", "success");

            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("state", appSwitch);
            dataMap.put("source", tempUser != null ? tempUser.getSource() : "");
            dataMap.put("user_id", uid);
            dataMap.put("choose_language", tempUser != null ? tempUser.getChooseLanguage() : "en");
            dataMap.put("isRenderingAdvance", isRenderingAdvance);
            //设置APP发布状态
            ShortApp shortApp = shortAppService.selectShortAppById(Long.valueOf(appId));
            if (null != shortApp) {
                dataMap.put("publishStatus", shortApp.getPublishStatus());
                dataMap.put("app_version", shortApp.getAppVersion());
            }
            resultMap.put("data", dataMap);

        } catch (Exception e) {
            log.error("获取版本信息失败：", e);

            // 上报异常
            ShortRunlog runlog = new ShortRunlog();
            runlog.setType("版本号错误");
            runlog.setState("0");
            runlog.setContent("版本号错误 - 新增失败：" + e.getMessage());
            runlog.setNote(e.getMessage());
            shortRunlogService.insertShortRunlog(runlog);

            // 尝试记录用户行为日志（异常情况）
            try {
                recordErrorActivityLog(versionData, appId, e.getMessage());
            } catch (Exception ex) {
                log.error("记录异常活动日志失败", ex);
            }

            resultMap.put("code", 200);
            resultMap.put("msg", "版本号错误 - 新增失败：" + e.getMessage());

            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("state", false);
            dataMap.put("source", "");
            dataMap.put("user_id", "");
            dataMap.put("isRenderingAdvance", renderingAdvance);

            resultMap.put("data", dataMap);
        }

        return resultMap;
    }

    /**
     * 处理用户支付信息
     *
     * @param user  用户对象
     * @param appId 应用ID
     */
    private void processPaymentInfo(ShortUser user, String appId) {
        try {
            // 获取支付Token，传入appId
            String token = shortExtplatsService.getPaymentToken(appId);
            if (token != null) {
                // 创建唯一请求ID
                String requestId = UUID.randomUUID().toString();

                // 构建请求头
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);
                headers.set("Authorization", "Bearer " + token);

                // 构建请求体
                Map<String, String> customerRequest = new HashMap<>();
                customerRequest.put("request_id", requestId);
                customerRequest.put("merchant_customer_id", "merchant_" + user.getHashId());
                customerRequest.put("email", user.getEmail());

                HttpEntity<Map<String, String>> request = new HttpEntity<>(customerRequest, headers);

                // 发送创建客户请求
                ResponseEntity<Map> response = restTemplate.postForEntity(
                        (useDevApi ? devApiUrl : prodApiUrl) + "/api/v1/pa/customers/create",
                        request,
                        Map.class);

                if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                    // 提取客户信息
                    String customerId = (String) response.getBody().get("id");
                    String clientSecret = (String) response.getBody().get("client_secret");

                    if (customerId != null && !"null".equals(customerId)) {
                        // 创建支付信息JSON
                        ArrayNode payInfoArray = objectMapper.createArrayNode();
                        ObjectNode payInfo = objectMapper.createObjectNode();
                        payInfo.put("type", "airwallex");
                        payInfo.put("customer_id", customerId);
                        payInfo.put("client_secret", clientSecret);
                        payInfoArray.add(payInfo);

                        // 保存支付信息到用户
                        user.setPayInfo(objectMapper.writeValueAsString(payInfoArray));
                        shortUserService.updateShortUser(user);

                        // 记录日志
                        ShortRunlog runlog = new ShortRunlog();
                        runlog.setType("支付customer_id");
                        runlog.setState("1");
                        runlog.setContent("用户：" + user.getId() + "，创建支付customer_id：" + objectMapper.writeValueAsString(response.getBody()));
                        runlog.setNote("");
                        shortRunlogService.insertShortRunlog(runlog);
                    }
                }
            }
        } catch (Exception e) {
            // 记录异常日志
            ShortRunlog runlog = new ShortRunlog();
            runlog.setType("支付customer_id");
            runlog.setState("0");
            runlog.setContent("用户：" + user.getId() + "，创建支付customer_id失败：" + e.getMessage());
            runlog.setNote(e.getMessage());
            shortRunlogService.insertShortRunlog(runlog);
            log.error("创建支付customer_id失败", e);
        }
    }

    /**
     * 记录用户活动日志
     *
     * @param user        用户对象
     * @param eventType   事件类型
     * @param versionData 版本数据
     * @param appId       应用ID
     * @param linkid      链接ID
     * @param movie       电影ID
     * @param pid         支付模板ID
     * @param kid         视频金币计划ID
     * @param pixelId     像素ID
     * @param adId        广告ID
     */
    private void recordUserActivityLog(ShortUser user, String eventType, Map<String, Object> versionData,
                                       String appId, String linkid, String movie, String pid,
                                       String kid, String pixelId, String adId) throws JsonProcessingException {
        // 上报用户登录事件
        ShortUserEventRecord eventRecord = new ShortUserEventRecord();
        eventRecord.setUserId(user.getId());
        eventRecord.setEventType(eventType);
        eventRecord.setNote(objectMapper.writeValueAsString(versionData));
        eventRecord.setState("1");
        shortUserEventRecordService.insertShortUserEventRecord(eventRecord);

        // 构建内容消息
        StringBuilder contentBuilder = new StringBuilder();
        contentBuilder.append("用户：").append(user.getId()).append("，登录成功");

        if (StringUtils.isNotEmpty(linkid)) {
            contentBuilder.append("，通过链接 ").append(linkid);
        }

        contentBuilder.append("，进入APP");

        // 添加深链信息
        StringBuilder deepLinkInfo = new StringBuilder();
        if (StringUtils.isNotEmpty(movie) || StringUtils.isNotEmpty(pid) ||
                StringUtils.isNotEmpty(kid) || StringUtils.isNotEmpty(pixelId) ||
                StringUtils.isNotEmpty(adId) || StringUtils.isNotEmpty(appId)) {

            deepLinkInfo.append("，深链信息携带的信息（");

            boolean hasAddedInfo = false;

            if (StringUtils.isNotEmpty(appId)) {
                deepLinkInfo.append("APP_ID：").append(appId);
                hasAddedInfo = true;
            }

            if (StringUtils.isNotEmpty(adId)) {
                if (hasAddedInfo) deepLinkInfo.append("，");
                deepLinkInfo.append("ADID：").append(adId);
                hasAddedInfo = true;
            }

            if (StringUtils.isNotEmpty(movie)) {
                if (hasAddedInfo) deepLinkInfo.append("，");
                deepLinkInfo.append("短剧：").append(movie);
                hasAddedInfo = true;
            }

            if (StringUtils.isNotEmpty(pid)) {
                if (hasAddedInfo) deepLinkInfo.append("，");
                deepLinkInfo.append("充值面板：").append(pid);
                hasAddedInfo = true;
            }

            if (StringUtils.isNotEmpty(kid)) {
                if (hasAddedInfo) deepLinkInfo.append("，");
                deepLinkInfo.append("扣费面板：").append(kid);
                hasAddedInfo = true;
            }

            if (StringUtils.isNotEmpty(pixelId)) {
                if (hasAddedInfo) deepLinkInfo.append("，");
                deepLinkInfo.append("PIXEL_ID：").append(pixelId);
            }

            deepLinkInfo.append("）");
            contentBuilder.append(deepLinkInfo);
        }

        // 使用活动日志服务记录用户登录
        ShortUserActivityLog activityLog = new ShortUserActivityLog();
        activityLog.setUserId(user.getId());
        activityLog.setAppId(Long.valueOf(appId));
        activityLog.setState("1"); // 成功状态
        activityLog.setContent(contentBuilder.toString());
        activityLog.setNote(StringUtils.isNotEmpty(user.getSource()) ? user.getSource() : "常规用户");

        shortUserActivityLogService.insertShortUserActivityLog(activityLog);
        log.info("记录用户活动日志成功: userId={}", user.getId());
    }

    /**
     * 记录错误活动日志
     *
     * @param versionData 版本数据
     * @param appId       应用ID
     * @param errorMsg    错误信息
     */
    private void recordErrorActivityLog(Map<String, Object> versionData, String appId, String errorMsg)
            throws JsonProcessingException {
        // 获取部分信息
        String adId = MapUtils.getString(versionData, "adId");
        String movie = MapUtils.getString(versionData, "movie");
        String pid = MapUtils.getString(versionData, "pid");
        String kid = MapUtils.getString(versionData, "kid");
        String pixelId = MapUtils.getString(versionData, "pixel_id");
        String linkid = MapUtils.getString(versionData, "linkid");

        // 构建错误消息
        StringBuilder contentBuilder = new StringBuilder();
        contentBuilder.append("错误：").append(errorMsg);

        if (StringUtils.isNotEmpty(linkid)) {
            contentBuilder.append("，通过链接 ").append(linkid);
        }

        contentBuilder.append("，进入APP");

        // 添加深链信息
        StringBuilder deepLinkInfo = new StringBuilder();
        if (StringUtils.isNotEmpty(movie) || StringUtils.isNotEmpty(pid) ||
                StringUtils.isNotEmpty(kid) || StringUtils.isNotEmpty(pixelId) ||
                StringUtils.isNotEmpty(adId) || StringUtils.isNotEmpty(appId)) {

            deepLinkInfo.append("，深链信息携带的信息（");

            boolean hasAddedInfo = false;

            if (StringUtils.isNotEmpty(appId)) {
                deepLinkInfo.append("APP_ID：").append(appId);
                hasAddedInfo = true;
            }

            if (StringUtils.isNotEmpty(adId)) {
                if (hasAddedInfo) deepLinkInfo.append("，");
                deepLinkInfo.append("ADID：").append(adId);
                hasAddedInfo = true;
            }

            if (StringUtils.isNotEmpty(movie)) {
                if (hasAddedInfo) deepLinkInfo.append("，");
                deepLinkInfo.append("短剧：").append(movie);
                hasAddedInfo = true;
            }

            if (StringUtils.isNotEmpty(pid)) {
                if (hasAddedInfo) deepLinkInfo.append("，");
                deepLinkInfo.append("充值面板：").append(pid);
                hasAddedInfo = true;
            }

            if (StringUtils.isNotEmpty(kid)) {
                if (hasAddedInfo) deepLinkInfo.append("，");
                deepLinkInfo.append("扣费面板：").append(kid);
                hasAddedInfo = true;
            }

            if (StringUtils.isNotEmpty(pixelId)) {
                if (hasAddedInfo) deepLinkInfo.append("，");
                deepLinkInfo.append("PIXEL_ID：").append(pixelId);
            }

            deepLinkInfo.append("）");
            contentBuilder.append(deepLinkInfo);
        }

        // 创建用户活动日志记录
        ShortUserActivityLog activityLog = new ShortUserActivityLog();
        activityLog.setAppId(Long.valueOf(appId));
        activityLog.setState("0"); // 失败状态
        activityLog.setContent(contentBuilder.toString());
        activityLog.setNote("系统异常");

        shortUserActivityLogService.insertShortUserActivityLog(activityLog);
        log.info("记录异常活动日志成功");
    }

    @Override
    public Map<String, Object> storeDeviceInfo(String platform, String screenWidth, String screenHeight,
                                               String systemTimeZone, String ip, String linkUrl, String appId) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 验证必要参数
            if (StringUtils.isEmpty(platform) || StringUtils.isEmpty(screenWidth) ||
                    StringUtils.isEmpty(screenHeight) || StringUtils.isEmpty(systemTimeZone) ||
                    StringUtils.isEmpty(linkUrl)) {
                result.put("code", 400);
                result.put("msg", "缺少必要参数");
                return result;
            }

            // 生成设备标识
            String deviceKey;
            // 0417新版添加nid
            if (StringUtils.isNotEmpty(appId)) {
                deviceKey = StringUtils.isNotEmpty(ip) ?
                        String.format("device:nid:%s:%s:%s:%s:%s:%s", appId, platform, screenWidth, screenHeight, systemTimeZone, ip) :
                        String.format("device:nid:%s:%s:%s:%s:%s", appId, platform, screenWidth, screenHeight, systemTimeZone);
            } else {
                // 兼容旧格式
                deviceKey = StringUtils.isNotEmpty(ip) ?
                        String.format("device:%s:%s:%s:%s:%s", platform, screenWidth, screenHeight, systemTimeZone, ip) :
                        String.format("device:%s:%s:%s:%s", platform, screenWidth, screenHeight, systemTimeZone);
            }

            // 设备数据
            Map<String, Object> deviceData = new HashMap<>();
            deviceData.put("platform", platform);
            deviceData.put("screenWidth", screenWidth);
            deviceData.put("screenHeight", screenHeight);
            deviceData.put("systemTimeZone", systemTimeZone);
            deviceData.put("ip", ip);
            deviceData.put("linkUrl", linkUrl);
            deviceData.put("nid", appId);

            // 将设备数据转换为JSON字符串
            String deviceDataJson = objectMapper.writeValueAsString(deviceData);

            // 记录存储信息，方便排查
            log.info("存储设备信息到Redis，键: {}", deviceKey);

            // 存储到Redis，设置2小时过期
            redisTemplate.opsForValue().set(deviceKey, deviceDataJson, 2, TimeUnit.HOURS);

            // 记录日志
            Map<String, Object> logData = new HashMap<>();
            logData.put("type", "设备链接存储");
            logData.put("state", 1);
            logData.put("content", "设备信息已缓存: " + deviceKey);
            logData.put("note", "链接地址: " + linkUrl + ", nid: " + appId + ", 数据: " + deviceDataJson);
            log.info("设备信息已缓存: {}, 链接地址: {}, nid: {}, 数据: {}",
                    deviceKey, linkUrl, appId, deviceDataJson);

            // 记录到数据库
            ShortRunlog runLog = new ShortRunlog();
            runLog.setType("设备链接存储");
            runLog.setState("1");
            runLog.setContent("设备信息已缓存: " + deviceKey);
            runLog.setNote("链接地址: " + linkUrl + ", nid: " + appId);
            runLog.setStatus("0"); // 0表示正常状态
            shortRunlogService.insertShortRunlog(runLog);

            // 构建响应数据
            Map<String, Object> data = new HashMap<>();
            data.put("device_key", deviceKey);

            result.put("code", 200);
            result.put("msg", "success");
            result.put("data", data);

            return result;
        } catch (Exception e) {
            log.error("存储设备信息失败", e);
            result.put("code", 400);
            result.put("msg", "存储设备信息失败: " + e.getMessage());
            return result;
        }
    }

    @Override
    public Map<String, Object> matchDeviceInfo(String platform, String screenWidth, String screenHeight,
                                               String systemTimeZone, String ip, String appId) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 验证必要参数
            if (StringUtils.isEmpty(platform) || StringUtils.isEmpty(screenWidth) ||
                    StringUtils.isEmpty(screenHeight) || StringUtils.isEmpty(systemTimeZone)) {
                result.put("code", 400);
                result.put("msg", "缺少必要参数");
                return result;
            }


            // 尝试多种可能的设备标识格式，优先使用与当前线上格式一致的key
            List<String> deviceKeys = new ArrayList<>();

            // 新版本key格式（优先尝试带nid的key）
            if (StringUtils.isNotEmpty(appId)) {
                deviceKeys.add(String.format("device:nid:%s:%s:%s:%s:%s", appId, platform, screenWidth, screenHeight, systemTimeZone));
                if (StringUtils.isNotEmpty(ip)) {
                    deviceKeys.add(String.format("device:nid:%s:%s:%s:%s:%s:%s", appId, platform, screenWidth, screenHeight, systemTimeZone, ip));
                }
            }

            // 然后尝试旧版key格式
            deviceKeys.add(String.format("device:%s:%s:%s:%s", platform, screenWidth, screenHeight, systemTimeZone));
            if (StringUtils.isNotEmpty(ip)) {
                deviceKeys.add(String.format("device:%s:%s:%s:%s:%s", platform, screenWidth, screenHeight, systemTimeZone, ip));
            }

            // 从Redis获取数据，按优先级尝试各种key
            String deviceDataJson = null;
            String matchedKey = null;

            for (String key : deviceKeys) {
                if (key != null) {
                    log.debug("尝试匹配Redis键: {}", key);
                    try {
                        deviceDataJson = redisTemplate.opsForValue().get(key);
                        if (deviceDataJson != null) {
                            matchedKey = key;
                            log.info("成功匹配Redis键: {}, 值: {}", key, deviceDataJson);
                            break;
                        }
                    } catch (Exception e) {
                        log.error("从Redis获取键 {} 时出错: {}", key, e.getMessage());
                    }
                }
            }

            if (deviceDataJson == null) {
                // 记录未找到匹配的日志
                String keysStr = String.join(", ", deviceKeys);
                log.info("未找到匹配: {}", keysStr);

                // 使用日志记录
                Map<String, Object> logData = new HashMap<>();
                logData.put("type", "设备链接匹配");
                logData.put("state", 0);
                logData.put("content", "未找到匹配: " + String.join(", ", deviceKeys));
                // 记录到数据库
                ShortRunlog runLog = new ShortRunlog();
                runLog.setType("设备链接匹配");
                runLog.setState("0");
                runLog.setContent("未找到匹配: " + String.join(", ", deviceKeys));
                runLog.setStatus("0"); // 0表示正常状态
                shortRunlogService.insertShortRunlog(runLog);

                // 记录更详细的诊断信息
                try {
                    Set<String> allDeviceKeys = redisTemplate.keys("device:*");
                    if (allDeviceKeys != null && !allDeviceKeys.isEmpty()) {
                        runLog = new ShortRunlog();
                        runLog.setType("设备链接诊断");
                        runLog.setState("0");
                        runLog.setContent("Redis中存在 " + allDeviceKeys.size() + " 个设备键，但未找到匹配");
                        runLog.setNote("请求参数: platform=" + platform + ", screenWidth=" + screenWidth +
                                ", screenHeight=" + screenHeight + ", systemTimeZone=" + systemTimeZone +
                                ", ip=" + ip + ", appId=" + appId);
                        shortRunlogService.insertShortRunlog(runLog);
                    }
                } catch (Exception e) {
                    log.error("诊断设备键时出错", e);
                }

                result.put("code", 200);
                result.put("msg", "未找到匹配的设备信息");
                result.put("data", null);
                return result;
            }

            // 解析设备数据
            Map<String, Object> deviceData;
            try {
                deviceData = objectMapper.readValue(deviceDataJson, Map.class);
            } catch (JsonProcessingException e) {
                log.error("解析设备数据失败", e);
                result.put("code", 200);
                result.put("msg", "解析设备数据失败");
                result.put("data", null);
                return result;
            }

            String linkUrl = (String) deviceData.get("linkUrl");
            String cachedNid = (String) deviceData.get("nid");

            // 兼容性处理：如果请求和缓存都有nid且不匹配，则认为不是同一个应用
            if (StringUtils.isNotEmpty(appId) && StringUtils.isNotEmpty(cachedNid) && !appId.equals(cachedNid)) {
                log.info("应用ID不匹配: 请求nid={}, 缓存nid={}, 设备标识: {}", appId, cachedNid, matchedKey);

                // 使用日志记录
                Map<String, Object> logData = new HashMap<>();
                logData.put("type", "设备链接匹配");
                logData.put("state", 0);
                logData.put("content", "应用ID不匹配: 请求nid=" + appId + ", 缓存nid=" + cachedNid);
                logData.put("note", "设备标识: " + matchedKey);
                // 记录到数据库
                ShortRunlog runLog = new ShortRunlog();
                runLog.setType("设备链接匹配");
                runLog.setState("0");
                runLog.setContent("应用ID不匹配: 请求nid=" + appId + ", 缓存nid=" + cachedNid);
                runLog.setNote("设备标识: " + matchedKey);
                runLog.setStatus("0"); // 0表示正常状态
                shortRunlogService.insertShortRunlog(runLog);

                result.put("code", 200);
                result.put("msg", "未找到匹配的设备信息");
                result.put("data", null);
                return result;
            }

            // 匹配成功后删除Redis缓存
            redisTemplate.delete(matchedKey);

            // 记录设备匹配成功日志
            log.info("设备匹配成功: {}, 链接地址: {}", matchedKey, linkUrl);

            // 使用日志记录
            Map<String, Object> logData = new HashMap<>();
            logData.put("type", "设备链接匹配");
            logData.put("state", 1);
            logData.put("content", "设备匹配成功: " + matchedKey);
            logData.put("note", "链接地址: " + linkUrl);
            // 记录到数据库
            ShortRunlog runLog = new ShortRunlog();
            runLog.setType("设备链接匹配");
            runLog.setState("1");
            runLog.setContent("设备匹配成功: " + matchedKey);
            runLog.setNote("链接地址: " + linkUrl);
            runLog.setStatus("0"); // 0表示正常状态
            shortRunlogService.insertShortRunlog(runLog);

            Map<String, Object> data = new HashMap<>();
            data.put("linkUrl", linkUrl);

            result.put("code", 200);
            result.put("msg", "success");
            result.put("data", data);
            return result;
        } catch (Exception e) {
            log.error("匹配设备信息失败", e);
            result.put("code", 200);
            result.put("msg", "匹配设备信息失败: " + e.getMessage());
            result.put("data", null);
            return result;
        }
    }


    /**
     * 发送Facebook事件，支持多种事件类型
     *
     * @param userId         用户ID
     * @param eventName      事件名称(CompleteRegistration/Purchase等)
     * @param orderId        订单ID (仅在Purchase事件时相关)
     * @param async          是否异步执行
     * @param isManualUpload 是否为手动回传（手动回传时绕过已回传和首单检查）
     * @return 是否成功发送
     */
    public boolean sendFacebookEvent(Long userId, String eventName, Long orderId, boolean async, boolean isManualUpload) {
        if (async) {
            // 异步执行
            new Thread(() -> {
                try {
                    doSendFacebookEvent(userId, eventName, orderId, isManualUpload);
                } catch (Exception e) {
                    log.error("异步发送Facebook事件失败: userId={}, event={}, orderId={}, error={}",
                            userId, eventName, orderId, e.getMessage(), e);

                    // 记录失败日志
                    try {
                        ShortRunlog errorLog = new ShortRunlog();
                        errorLog.setType("PIXEL回传");
                        errorLog.setState("0");
                        errorLog.setContent("用户：" + userId + "，事件：" + eventName + (orderId != null ? ", 订单：" + orderId : "") + "，异步回传失败");
                        errorLog.setNote(e.getMessage());
                        shortRunlogService.insertShortRunlog(errorLog);
                    } catch (Exception logEx) {
                        log.error("记录事件错误日志失败", logEx);
                    }
                }
            }).start();
            return true; // 异步情况下返回启动成功
        } else {
            // 同步执行
            return doSendFacebookEvent(userId, eventName, orderId, isManualUpload);
        }
    }

    public void initAppPixelRuleConfig() {
        if (appPixelRuleMap.isEmpty()) {
            //获取全局配置 app 对应的回传规则
            String key = CacheConstants.SYS_CONFIG_KEY + "sys.app.pixelRule";
            Object str = redisCache.getCacheObject(key);
            String appConfig = JSONUtil.toJsonStr(str);
            log.info("获取配置={}", appConfig);
            if (StringUtils.isNotEmpty(appConfig)) {
                JSONArray array = JSONUtil.parseArray(appConfig);
                for (int i = 0; i < array.size(); i++) {
                    JSONObject jsonObject = array.getJSONObject(i);
                    String appId = jsonObject.getStr("appId");
                    appPixelRuleMap.put(appId, jsonObject);
                }
            }
        }
    }

    /**
     * 实际执行Facebook事件发送的方法
     *
     * @param userId         用户ID
     * @param eventName      事件名称
     * @param orderId        订单ID (仅在Purchase事件时相关)
     * @param isManualUpload 是否为手动回传（手动回传时绕过已回传和首单检查）
     * @return 是否成功发送
     */
    private boolean doSendFacebookEvent(Long userId, String eventName, Long orderId, boolean isManualUpload) {
        ShortOrder successOrder = null;  // 声明在最外层以便于在catch块中使用
        Map<String, Boolean> adInfoStatus = null;  // 广告信息完整性状态
        String errorDetail = null;  // 详细错误信息
        String pixelId = null; // 声明 pixelId 在方法作用域顶部并初始化为 null
        try {
            log.info("开始发送Facebook事件: userId={}, event={}, orderId={}, isManualUpload={}", userId, eventName, orderId, isManualUpload);

            // 查询用户信息
            ShortUser user = shortUserService.selectShortUserById(userId);
            if (user == null) {
                log.error("用户不存在，无法发送Facebook事件: userId={}", userId);
                return false;
            }
            try {
                //初始化配置
                initAppPixelRuleConfig();
                Long appId = user.getAppId();
                //有配置才算 而且当前用户的手机系统版本不为空
                if (appPixelRuleMap.containsKey(appId.toString()) && StringUtils.isNotEmpty(user.getPhoneVersion())) {
                    List<String> exceptSystem = Arrays.asList("Windows", "Linux", "Ubuntu");
                    JSONObject pixelRule = appPixelRuleMap.get(appId.toString());
                    String android = pixelRule.getStr("android");
                    String ios = pixelRule.getStr("ios");
                    ShortRunlog alreadyLog = new ShortRunlog();
                    alreadyLog.setType("PIXEL回传跳过");
                    alreadyLog.setState("1");
                    // 判断当前用户 系统版本是否满足要求
                    if ("Apple".equals(user.getPhoneVersion()) && StringUtils.isNotEmpty(user.getSystemVersion())) {
                        int i = SystemVersionComparator.compareVersions(user.getSystemVersion(), ios);
                        if (i < 0) {
                            String content = "IOS版本低于要求，跳过发送Facebook事件: 用户：" + userId;
                            alreadyLog.setContent(content);
                            shortRunlogService.insertShortRunlog(alreadyLog);
                            log.info("Apple用户版本低于要求，无法发送Facebook事件: userId={}, appId={}", userId, appId);
                            return false;
                        }
                    } else if (!exceptSystem.contains(user.getPhoneVersion()) && StringUtils.isNotEmpty(user.getSystemVersion())) {
                        //安卓版本
                        int i = SystemVersionComparator.compareVersions(user.getSystemVersion(), android);
                        if (i < 0) {
                            String content = "Android版本低于要求，跳过发送Facebook事件: 用户：" + userId;
                            alreadyLog.setContent(content);
                            shortRunlogService.insertShortRunlog(alreadyLog);
                            log.error("Android用户版本低于要求，无法发送Facebook事件: userId={}, appId={}", userId, appId);
                            return false;
                        }
                    } else {
                        String content = "当前用户系统版本不支持回传，无法发送Facebook事件，跳过发送Facebook事件: 用户：" + userId;
                        alreadyLog.setContent(content);
                        shortRunlogService.insertShortRunlog(alreadyLog);
                        log.info("当前用户系统版本不支持回传，无法发送Facebook事件: userId={}, appId={}", userId, appId);
                        return false;
                    }
                }
            } catch (Exception e) {
                log.error("执行facebook过滤规则异常", e);
            }
            // 获取应用信息
            ShortApp app = null;
            if (user.getAppId() != null) {
                app = shortAppService.selectShortAppById(user.getAppId());
            }

            if (app == null) {
                log.error("应用不存在，无法发送Facebook事件: userId={}, appId={}", userId, user.getAppId());
                return false;
            }

            // 特殊处理Purchase事件
            if ("Purchase".equals(eventName)) {
                if (orderId == null) {
                    log.error("Purchase事件回传错误: orderId未提供. userId={}", userId);
                    ShortRunlog errorLog = new ShortRunlog();
                    errorLog.setType("PIXEL回传错误");
                    errorLog.setState("0");
                    errorLog.setContent("用户：" + userId + "，Purchase事件回传错误: orderId未提供。");
                    shortRunlogService.insertShortRunlog(errorLog);
                    return false;
                }

                successOrder = shortOrderService.selectShortOrderById(orderId);

                if (successOrder == null) {
                    log.error("Purchase事件回传错误: 订单ID {} 未找到. userId={}", orderId, userId);
                    ShortRunlog errorLog = new ShortRunlog();
                    errorLog.setType("PIXEL回传错误");
                    errorLog.setState("0");
                    errorLog.setContent("用户：" + userId + "，订单ID " + orderId + " 未找到。");
                    shortRunlogService.insertShortRunlog(errorLog);
                    return false;
                }

                if (!successOrder.getUserId().equals(userId)) {
                    log.error("Purchase事件回传错误: 订单ID {} (用户 {}) 与当前用户 {} 不匹配。", orderId, successOrder.getUserId(), userId);
                    ShortRunlog errorLog = new ShortRunlog();
                    errorLog.setType("PIXEL回传错误");
                    errorLog.setState("0");
                    errorLog.setContent("用户：" + userId + "，订单ID " + orderId + " (用户 " + successOrder.getUserId() + ") 与当前用户 " + userId + " 不匹配。");
                    shortRunlogService.insertShortRunlog(errorLog);
                    return false;
                }

                // 检查订单状态（手动回传时也需要检查）
                if (!"SUCCEEDED".equals(successOrder.getStatus())) {
                    log.warn("Purchase事件: 订单ID {} 状态为 {} (非SUCCEEDED)，跳过回传。", orderId, successOrder.getStatus());
                    ShortRunlog skipLog = new ShortRunlog();
                    skipLog.setType("PIXEL回传跳过");
                    skipLog.setState("1");
                    skipLog.setContent("用户：" + userId + "，订单ID " + orderId + " 状态为 " + successOrder.getStatus() + " (非SUCCEEDED)，跳过回传。");
                    shortRunlogService.insertShortRunlog(skipLog);
                    updateOrderPixelStatus(successOrder, 3, "订单状态非 SUCCEEDED 状态: " + successOrder.getStatus());
                    return false;
                }

                // 检查订单是否已回传 - 手动回传时跳过此检查
                if (!isManualUpload && successOrder.getPixelStatus() != null &&
                        (successOrder.getPixelStatus() == 1 || successOrder.getPixelStatus() == 2)) {
                    log.info("订单已回传过，不重复回传: orderId={}, pixelStatus={}",
                            successOrder.getId(), successOrder.getPixelStatus());

                    ShortRunlog alreadyLog = new ShortRunlog();
                    alreadyLog.setType("PIXEL回传跳过");
                    alreadyLog.setState("1");
                    alreadyLog.setContent("用户：" + userId + "，订单：" + successOrder.getId() + "，已回传过，状态: " +
                            (successOrder.getPixelStatus() == 1 ? "自动回传" : "手动回传"));
                    shortRunlogService.insertShortRunlog(alreadyLog);
                    return true; // 已回传视为成功
                }

                // 判断是否为针对此 PIXEL ID 的首单
                Long pixelIdForCheck = successOrder.getPixelId() != null ? successOrder.getPixelId() : (user.getPixelId() != null ? user.getPixelId() : null);
                if (pixelIdForCheck == null) {
                    // 如果无法确定Pixel ID，尝试从默认配置获取
                    ShortFacebookPixel defaultFbPixel = shortFacebookPixelService.selectShortFacebookPixelByFacebookPixelId(defaultFacebookPixelId);
                    if (defaultFbPixel != null) {
                        pixelIdForCheck = defaultFbPixel.getId();
                        log.warn("订单和用户均未关联Pixel ID，尝试使用系统默认Pixel ID进行首单判断: {}, 对应DB ID: {}", defaultFacebookPixelId, pixelIdForCheck);
                    } else {
                        log.warn("无法确定Pixel ID进行首单判断（包括默认值 {} 也找不到对应记录），将按非首单处理或跳过: orderId={}", defaultFacebookPixelId, successOrder.getId());

                        // 无法确定Pixel ID时，无法回传
                        ShortRunlog skipLog = new ShortRunlog();
                        skipLog.setType("PIXEL回传跳过");
                        skipLog.setState("1");
                        skipLog.setContent("用户：" + userId + "，订单：" + successOrder.getId() + "，无法确定Pixel ID进行首单判断（包括默认值），跳过回传。");
                        shortRunlogService.insertShortRunlog(skipLog);
                        updateOrderPixelStatus(successOrder, 3, "无法确定Pixel ID进行首单判断（包括默认值）");
                        return false;
                    }
                }

                // 检查是否为首单 - 手动回传时跳过此检查
                if (!isManualUpload && pixelIdForCheck != null) {
                    // 修改首单检查逻辑：基于pixel_id + link_id组合进行更精确的首单判断
                    Integer previousOrdersCountForPixelAndLink = shortOrderService.countSuccessfulOrdersByPixelIdAndLinkIdBeforeTime(
                            userId,
                            pixelIdForCheck,
                            successOrder.getLinkId(), // 新增link_id参数，确保每个链接的首单都能回传
                            successOrder.getPayTime() != null ? successOrder.getPayTime() : successOrder.getUpdateTime(), // 使用支付时间或更新时间
                            successOrder.getId() // 排除当前订单
                    );

                    if (previousOrdersCountForPixelAndLink != null && previousOrdersCountForPixelAndLink > 0) {
                        log.info("针对Pixel ID {} + Link ID {}，用户在此订单前已有 {} 个成功订单，此单非首单，跳过回传: userId={}, orderId={}",
                                pixelIdForCheck, successOrder.getLinkId(), previousOrdersCountForPixelAndLink, userId, successOrder.getId());
                        ShortRunlog skipLog = new ShortRunlog();
                        skipLog.setType("PIXEL回传跳过");
                        skipLog.setState("1");
                        skipLog.setContent("用户：" + userId + "，订单：" + successOrder.getId() + "，针对Pixel ID " + pixelIdForCheck + " + Link ID " + successOrder.getLinkId() + " 非首单（之前有 " + previousOrdersCountForPixelAndLink + " 单），跳过回传");
                        shortRunlogService.insertShortRunlog(skipLog);
                        // 将订单的 pixelStatus 标记为非首单
                        updateOrderPixelStatus(successOrder, 4, "针对Pixel ID " + pixelIdForCheck + " + Link ID " + successOrder.getLinkId() + " 非首单");
                        return false; // 非首单，不回传
                    }
                }
                // 如果是首单，则在发送前将状态标记为0 (未回传，但准备处理)
                successOrder.setPixelStatus(0);
                shortOrderService.updateShortOrder(successOrder);
            }

            // 按优先级获取Facebook像素ID和访问令牌
            String accessToken = null;
            ShortFacebookPixel facebookPixel = null;

            log.info("开始按优先级查找Facebook像素ID");

            // 1. 优先使用用户关联的像素ID
            if (user.getPixelId() != null) {
                facebookPixel = shortFacebookPixelService.selectShortFacebookPixelById(user.getPixelId());
                if (facebookPixel != null) {
                    log.info("使用用户关联的Facebook像素: pixelId={}, 备注={}",
                            facebookPixel.getFacebookPixelId(), facebookPixel.getRemark());
                }
            }

            // 2. 如果是购买事件且订单有关联的像素ID，使用订单关联的像素ID
            if (facebookPixel == null && "Purchase".equals(eventName) && successOrder != null && successOrder.getPixelId() != null) {
                facebookPixel = shortFacebookPixelService.selectShortFacebookPixelById(successOrder.getPixelId());
                if (facebookPixel != null) {
                    log.info("使用订单关联的Facebook像素: pixelId={}, 备注={}",
                            facebookPixel.getFacebookPixelId(), facebookPixel.getRemark());
                }
            }

            // 3. 如果用户/订单都没有关联有效的像素ID，直接使用系统默认值
            if (facebookPixel == null) {
                pixelId = defaultFacebookPixelId;
                accessToken = defaultFacebookAccessToken;
                log.info("未找到用户/订单关联的Facebook像素ID，使用系统默认值: {}", pixelId);
            } else {
                // 使用从ShortFacebookPixel获取的像素ID和访问令牌
                pixelId = facebookPixel.getFacebookPixelId();
                accessToken = facebookPixel.getFacebookAccessToken();

                // 如果表中没有存储访问令牌，则使用默认值
                if (StringUtils.isEmpty(accessToken)) {
                    accessToken = defaultFacebookAccessToken;
                    log.info("像素记录中无访问令牌，使用默认访问令牌");
                }
            }

            // 从配置中获取Facebook API版本
            String apiVersion = facebookApiVersion; // 使用注入的API版本

            // 从用户数据中获取其他参数
            Map<String, Object> otherMap = new HashMap<>();
            if (StringUtils.isNotEmpty(user.getOther())) {
                try {
                    otherMap = objectMapper.readValue(user.getOther(), Map.class);
                } catch (Exception e) {
                    log.error("解析用户other信息失败: {}", e.getMessage());
                }
            }

            // 检查用户广告信息的完整性
            adInfoStatus = checkAdInfoCompleteness(user, otherMap);
            if (!adInfoStatus.get("is_complete")) {
                log.warn("用户广告信息不完整，可能影响回传效果: userId={}", userId);

                // 记录日志但不阻止回传
                StringBuilder missingInfo = new StringBuilder("缺失信息: ");
                for (Map.Entry<String, Boolean> entry : adInfoStatus.entrySet()) {
                    if (!entry.getValue() && !entry.getKey().equals("is_complete")) {
                        missingInfo.append(entry.getKey().replace("_present", "")).append(", ");
                    }
                }

                ShortRunlog infoLog = new ShortRunlog();
                infoLog.setType("PIXEL回传-信息不完整");
                infoLog.setState("1"); // 警告但非错误
                infoLog.setContent("用户：" + userId + ", PixelID: " + pixelId + ", " + missingInfo);
                shortRunlogService.insertShortRunlog(infoLog);
            }

            // 验证令牌是否存在 - 这部分逻辑可以保留，增加安全检查
            if (StringUtils.isEmpty(accessToken)) {
                String errMsg = "无法获取Facebook访问令牌，即使尝试使用默认值";
                log.error(errMsg + ": userId={}, appId={}", userId, app.getId());

                // 如果是购买事件，更新订单状态为失败(3)
                if (successOrder != null) {
                    updateOrderPixelStatus(successOrder, 3, errMsg);
                }

                return false;
            }

            log.info("成功获取Facebook访问令牌和像素ID，准备发送事件: pixelId={}", pixelId);

            // 构建请求参数
            String fbApiUrl = String.format("https://graph.facebook.com/%s/%s/events", apiVersion, pixelId);

            // 生成唯一的事件ID用于去重
            String eventId = "website_" + eventName.toLowerCase() + "_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().replace("-", "").substring(0, 8);

            // 获取并清理FBC数据
            String fbc = cleanFbcData(MapUtils.getString(otherMap, "fbc", ""));

            // 获取FBP
            String fbp = MapUtils.getString(otherMap, "fbp", "");

            // 实施分级回传策略
            boolean hasFbc = StringUtils.isNotEmpty(fbc) && !"null".equalsIgnoreCase(fbc) && !"none".equalsIgnoreCase(fbc);
            boolean hasFbp = StringUtils.isNotEmpty(fbp) && !"null".equalsIgnoreCase(fbp) && !"none".equalsIgnoreCase(fbp);

            log.info("Facebook像素数据检查: userId={}, hasFbc={}, hasFbp={}", userId, hasFbc, hasFbp);

            // 基于Meta官方2024年优先级调整的分级回传策略
            // Meta优先级：FBC (高优先级) > FBP (中优先级)等
            String strategyLevel = "";
            String finalFbc = fbc;  // 保存原始值用于API发送
            String finalFbp = fbp;  // 保存原始值用于API发送

            if (hasFbc && hasFbp) {
                // 最优情况：有FBC+FBP，Meta EMQ得分最高
                strategyLevel = "最优";
                log.debug("【最优】FBC+FBP完整: 用户拥有完整跟踪数据，EMQ得分最高, userId={}", userId);

            } else if (hasFbc) {
                // 良好情况：有FBC（高优先级参数），Meta EMQ良好
                strategyLevel = "良好";
                finalFbp = ""; // 清空无效FBP，避免发送无效数据
                log.debug("【良好】仅FBC: 用户有高优先级FBC数据，Meta EMQ良好, userId={}", userId);

            } else if (hasFbp) {
                // 次优情况：只有FBP（中优先级），缺少高优先级FBC
                strategyLevel = "次优";
                finalFbc = ""; // 清空无效FBC，避免发送无效数据
                log.warn("【次优】仅FBP: 缺少高优先级FBC，仅有中优先级FBP数据, userId={}", userId);

                // 记录警告日志，强调FBC的重要性
                ShortRunlog warningLog = new ShortRunlog();
                warningLog.setType("PIXEL回传警告");
                warningLog.setState("1");
                warningLog.setContent(String.format("用户：%d，事件：%s，PixelID: %s，缺少高优先级FBC，仅有FBP继续回传", userId, eventName, pixelId));
                warningLog.setNote("FBC是Meta高优先级参数，建议检查前端Facebook Click ID获取逻辑以提升EMQ得分");
                try {
                    shortRunlogService.insertShortRunlog(warningLog);
                } catch (Exception logEx) {
                    log.error("记录FBC缺失警告日志失败", logEx);
                }

            } else {
                // 最差情况：FBC和FBP都没有，才报错中止
                String errorMessage = "FBC和FBP数据均缺失，无法进行有效回传";
                log.error("Facebook事件发送中止：{}。userId={}, eventName={}", errorMessage, userId, eventName);

                // 记录错误日志
                ShortRunlog fbcErrorLog = new ShortRunlog();
                fbcErrorLog.setType("PIXEL回传错误");
                fbcErrorLog.setState("0");
                fbcErrorLog.setContent(String.format("用户：%d，事件：%s，PixelID: %s，错误：%s", userId, eventName, pixelId, errorMessage));
                fbcErrorLog.setNote("FBC和FBP数据均缺失，无法进行有效的Facebook像素回传。");
                try {
                    shortRunlogService.insertShortRunlog(fbcErrorLog);
                } catch (Exception logEx) {
                    log.error("记录数据缺失错误日志失败", logEx);
                }

                // 如果是Purchase事件，更新订单状态为失败
                if ("Purchase".equals(eventName)) {
                    updateOrderPixelStatus(successOrder, 3, errorMessage);
                }
                return false; // 提前返回
            }

            // 记录最终使用的FBC和FBP数据及EMQ预期
            log.info("Meta分级回传策略完成: userId={}, FBC={}, FBP={}, EMQ预期={}",
                    userId, StringUtils.isNotEmpty(finalFbc) ? "✓" : "✗", StringUtils.isNotEmpty(finalFbp) ? "✓" : "✗", strategyLevel);

            // 获取用户代理
            String userAgent = MapUtils.getString(otherMap, "ua", "");

            // 添加用户数据
            Map<String, Object> userData = new HashMap<>();
            userData.put("client_user_agent", userAgent);
            userData.put("client_ip_address", user.getIp());

            // 添加FBC (如果有效) - 在此处才进行Facebook API要求的格式化
            if (StringUtils.isNotEmpty(finalFbc)) {
                // 按Facebook要求格式化FBC: fb.1.{timestamp}.{fbclid}
                // 只在发送API前格式化，不修改存储的原始数据
                long timestamp = System.currentTimeMillis();
                String formattedFbc = String.format("fb.1.%d.%s", timestamp, finalFbc);
                userData.put("fbc", formattedFbc);
                log.debug("FBC格式化完成: 原始={}, 格式化={}", finalFbc, formattedFbc);
            }

            if (StringUtils.isNotEmpty(finalFbp)) {
                userData.put("fbp", finalFbp);
            }

            // 添加external_id (如果有adId)
            String adId = MapUtils.getString(otherMap, "adId", "");
            if (StringUtils.isNotEmpty(adId) && !"null".equals(adId)) {
                userData.put("external_id", hashData(adId));
            }

            // 添加匿名ID
            userData.put("anon_id", hashData("匿名用户ID_" + userId));

            // 构建事件数据
            Map<String, Object> eventData = new HashMap<>();
            eventData.put("event_name", eventName);
            eventData.put("event_time", System.currentTimeMillis() / 1000);
            eventData.put("action_source", "website");
            eventData.put("event_id", eventId);
            eventData.put("user_data", userData);

            // 对于购买事件添加自定义数据
            if ("Purchase".equals(eventName)) {
                // 如果前面已经有订单对象，直接使用，否则再次查询
                if (successOrder == null) {
                    // 查询用户订单
                    ShortOrder queryOrder = new ShortOrder();
                    queryOrder.setUserId(userId);
                    queryOrder.setStatus("SUCCEEDED"); // 成功状态
                    List<ShortOrder> orders = shortOrderService.selectShortOrderList(queryOrder);
                    successOrder = orders.get(0); // 前面已经检查过列表大小为1
                }

                // 检查是否为邮件营销活动订单，如果是则跳过回传(即使是手动回传)
                if (successOrder.getIsEmailMarketingOrder() != null && successOrder.getIsEmailMarketingOrder() == 1) {
                    log.info("跳过Facebook事件回传: 邮件营销活动订单, orderId={}", successOrder.getId());

                    // 记录日志
                    ShortRunlog skipLog = new ShortRunlog();
                    skipLog.setType("PIXEL回传-跳过");
                    skipLog.setState("1");
                    skipLog.setContent("用户：" + userId + "，事件：" + eventName + ", 订单：" + successOrder.getId() + "，邮件营销订单不回传");
                    shortRunlogService.insertShortRunlog(skipLog);

                    // 更新订单像素状态为已处理但跳过(4表示跳过回传)
                    updateOrderPixelStatus(successOrder, 4, "邮件营销订单，跳过回传");

                    return true; // 返回true表示处理成功(跳过回传)
                }

                // 获取货币和金额信息 - 修复版本
                String currencyCode = "USD"; // 默认货币
                Number value = 0.01;  // 修改：使用Number类型而不是String，设置最小值避免Facebook报错

                if (successOrder.getAmount() != null) {
                    // 修改：确保金额是有效的数字类型
                    try {
                        BigDecimal amount = successOrder.getAmount();

                        // 验证金额合理性
                        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
                            log.warn("订单金额无效或为0: orderId={}, amount={}", successOrder.getId(), amount);
                            // 设置最小金额为0.01避免Facebook报错
                            value = 0.01;
                        } else if (amount.compareTo(new BigDecimal("999999999")) > 0) {
                            log.warn("订单金额过大: orderId={}, amount={}", successOrder.getId(), amount);
                            // 设置合理的最大金额
                            value = 999999999;
                        } else {
                            // 修改：保留两位小数并转换为double类型
                            value = amount.setScale(2, RoundingMode.HALF_UP).doubleValue();
                        }

                        // 验证和设置货币代码
                        if (StringUtils.isNotEmpty(successOrder.getCurrency())) {
                            String orderCurrency = successOrder.getCurrency().toUpperCase();
                            // 验证货币代码格式（3位字母）
                            if (orderCurrency.matches("^[A-Z]{3}$")) {
                                currencyCode = orderCurrency;
                            } else {
                                log.warn("订单货币代码格式无效: orderId={}, currency={}, 使用默认USD",
                                        successOrder.getId(), successOrder.getCurrency());
                                currencyCode = "USD";
                            }
                        }
                    } catch (Exception e) {
                        log.error("处理订单金额时出错: orderId={}, amount={}, error={}",
                                successOrder.getId(), successOrder.getAmount(), e.getMessage());
                        // 出错时使用默认值
                        value = 0.01;
                        currencyCode = "USD";
                    }
                } else {
                    log.warn("订单金额为空: orderId={}", successOrder.getId());
                    // 金额为空时设置最小值
                    value = 0.01;
                }

                // 记录订单查询结果
                ShortRunlog orderLog = new ShortRunlog();
                orderLog.setType("购买：PIXEL回传");
                orderLog.setState("1");
                orderLog.setContent("用户：" + userId + "，查询订单金额：" + value + " " + currencyCode +
                        " (原始金额: " + successOrder.getAmount() + ")");
                orderLog.setNote("订单ID: " + successOrder.getId());
                shortRunlogService.insertShortRunlog(orderLog);

                log.info("查询用户购买订单: userId={}, 订单金额={} {}, 数据类型={}",
                        userId, value, currencyCode, value.getClass().getSimpleName());

                // 修改：构建符合Facebook API要求的custom_data
                Map<String, Object> customData = new HashMap<>();
                customData.put("value", value);  // 修改：确保是Number类型
                customData.put("currency", currencyCode);
                eventData.put("custom_data", customData);

                // 记录发送给Facebook的详细数据用于调试
                log.info("Facebook Purchase事件custom_data: {}", customData);
            }

            // 构建整体请求
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("data", Collections.singletonList(eventData));
            requestBody.put("access_token", accessToken);

            // 记录详细请求日志
            String pixelEvent = "CompleteRegistration".equals(eventName) ? "注册" : eventName;
            ShortRunlog detailLog = new ShortRunlog();
            detailLog.setType(pixelEvent + "：PIXEL回传, 详细信息");
            detailLog.setState("1");
            detailLog.setContent("用户：" + userId + "，PixelID: " + pixelId + "，详细：" + objectMapper.writeValueAsString(requestBody));
            detailLog.setNote("");
            shortRunlogService.insertShortRunlog(detailLog);

            log.info("发送Facebook事件请求: {}", fbApiUrl);

            // 发送HTTP请求
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            // 创建带超时设置的RestTemplate
            RestTemplate customRestTemplate = new RestTemplate();
            customRestTemplate.setRequestFactory(new SimpleClientHttpRequestFactory());
            ((SimpleClientHttpRequestFactory) customRestTemplate.getRequestFactory()).setConnectTimeout(10000); // 10秒连接超时
            ((SimpleClientHttpRequestFactory) customRestTemplate.getRequestFactory()).setReadTimeout(10000); // 10秒读取超时

            // 发送请求并捕获响应
            ResponseEntity<Map> response = customRestTemplate.postForEntity(
                    fbApiUrl,
                    entity,
                    Map.class
            );

            // 记录详细响应日志
            ShortRunlog responseLog = new ShortRunlog();
            responseLog.setType(pixelEvent + "：PIXEL回传, 返回信息");
            responseLog.setState(response.getStatusCode().is2xxSuccessful() ? "1" : "0");
            responseLog.setContent("用户：" + userId + "，PixelID: " + pixelId + "，详细：" + objectMapper.writeValueAsString(response.getBody()));
            responseLog.setNote("");
            shortRunlogService.insertShortRunlog(responseLog);

            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("Facebook事件发送成功: userId={}, event={}, orderId={}, response={}",
                        userId, eventName, successOrder != null ? successOrder.getId() : null, response.getBody());

                // 如果是购买事件，更新订单状态为成功(1-自动回传 或 2-手动回传)
                if (successOrder != null) {
                    // 记录回传前的状态
                    Integer beforeStatus = successOrder.getPixelStatus();

                    // 根据是否为手动回传设置不同的状态值
                    int newStatus = isManualUpload ? 2 : 1;
                    log.info("准备更新订单回传状态: orderId={}, isManual={}, 新状态={}",
                            successOrder.getId(), isManualUpload, newStatus);

                    // 更新状态
                    updateOrderPixelStatus(successOrder, newStatus, null);

                    // 确认状态已更新
                    try {
                        // 短暂延迟确保数据库更新完成
                        Thread.sleep(50);

                        // 重新查询订单验证状态
                        ShortOrder verifyOrder = shortOrderService.selectShortOrderById(successOrder.getId());
                        if (verifyOrder != null) {
                            if (!Objects.equals(verifyOrder.getPixelStatus(), newStatus)) {
                                log.warn("回传后订单状态可能未正确更新: orderId={}, 原状态={}, 期望状态={}, 当前状态={}",
                                        successOrder.getId(), beforeStatus, newStatus, verifyOrder.getPixelStatus());

                                // 再次尝试更新状态
                                verifyOrder.setPixelStatus(newStatus);
                                shortOrderService.updateShortOrder(verifyOrder);
                                log.info("重新尝试更新订单状态: orderId={}, 状态={}", verifyOrder.getId(), newStatus);
                            } else {
                                log.info("回传后订单状态已正确更新: orderId={}, 状态={}", verifyOrder.getId(), newStatus);
                            }
                        }
                    } catch (Exception e) {
                        log.error("验证回传状态更新时出错: {}", e.getMessage());
                    }
                }

                return true;
            } else {
                // 提取错误信息
                errorDetail = extractErrorMessage(response);
                log.error("Facebook事件发送失败: userId={}, event={}, statusCode={}, 详细错误: {}",
                        userId, eventName, response.getStatusCode(), errorDetail);

                // 如果是购买事件，更新订单状态为失败(3)
                if (successOrder != null) {
                    updateOrderPixelStatus(successOrder, 3, errorDetail);
                }

                return false;
            }
        } catch (Exception e) {
            errorDetail = analyzeErrorType(e) + ": " + e.getMessage();
            log.error("发送Facebook事件异常: userId={}, event={}, error={}",
                    userId, eventName, errorDetail, e);

            // 记录异常日志
            try {
                // 详细的异常信息记录
                ShortRunlog errorLog = new ShortRunlog();
                errorLog.setType("PIXEL回传错误");
                errorLog.setState("0");

                // 添加广告信息完整性状态
                StringBuilder errorContent = new StringBuilder("用户：" + userId + "，事件：" + eventName + "，PixelID: " + pixelId + "，错误：" + e.getMessage());
                if (adInfoStatus != null) {
                    errorContent.append("，广告信息完整性检查：");
                    for (Map.Entry<String, Boolean> entry : adInfoStatus.entrySet()) {
                        if (!entry.getKey().equals("is_complete")) {
                            errorContent.append(entry.getKey()).append("=").append(entry.getValue()).append(", ");
                        }
                    }
                }

                errorLog.setContent(errorContent.toString());
                errorLog.setNote(e.getMessage());
                shortRunlogService.insertShortRunlog(errorLog);

                // 如果是购买事件，更新订单状态为失败(3)
                if (successOrder != null) {
                    updateOrderPixelStatus(successOrder, 3, errorDetail);
                }
            } catch (Exception logEx) {
                log.error("记录事件错误日志失败", logEx);
            }
            return false;
        }
    }

    @Override
    public AjaxResult sendEmail(String publicKey) {
        int count = shortIntermedStatusService.countByName("sendEmail");
        if (count > 0)
            return AjaxResult.success("自动统计首页数据，请勿重复操作");

        ShortIntermedStatus shortIntermedStatus = new ShortIntermedStatus();
        shortIntermedStatus.setName("sendEmail");
        shortIntermedStatusService.insertShortIntermedStatus(shortIntermedStatus);


        try {
            String privateKey = "-----BEGIN OPENSSH PRIVATE KEY-----\n" +
                    "b3BlbnNzaC1rZXktdjEAAAAABG5vbmUAAAAEbm9uZQAAAAAAAAABAAAAMwAAAAtzc2gtZW\n" +
                    "QyNTUxOQAAACDdx+eInOus2G8pGSS+paM8QV5SkQdAXGh/e0Z7pRaDRwAAAJiIp66uiKeu\n" +
                    "rgAAAAtzc2gtZWQyNTUxOQAAACDdx+eInOus2G8pGSS+paM8QV5SkQdAXGh/e0Z7pRaDRw\n" +
                    "AAAEDJW6rFB140gGyRJZM+mqoIFj8HAGrGpnuWj+sCJa/06N3H54ic66zYbykZJL6lozxB\n" +
                    "********************************************************\n" +
                    "-----END OPENSSH PRIVATE KEY-----";

            // 2. 加载密钥对
            KeyPair keyPair = KeyPair.load(new JSch(), privateKey.getBytes(), null);
            byte[] derivedPublicKeyBlob = keyPair.getPublicKeyBlob();

            // 3. 解析用户公钥（按空格分割并解码Base64）
            String[] publicKeyParts = publicKey.split(" ");
            if (publicKeyParts.length < 2) {
                throw new IllegalArgumentException("Invalid public key format");
            }
            byte[] userPublicKeyBlob = Base64.getDecoder().decode(publicKeyParts[1]);

            // 4. 安全比对（避免时序攻击）
            boolean isMatch = MessageDigest.isEqual(derivedPublicKeyBlob, userPublicKeyBlob);
            if (isMatch) {
                log.info("验证密钥正确------定时发邮件");
//================================
                List<ShortEmailSendLog> list = shortEmailSendLogService.getListByEndTime();
                for (ShortEmailSendLog shortEmailSendLog : list) {

                    int subInt = shortEmailSendLog.getNum().intValue();

                    shortEmailSendLog.setNum(shortEmailSendLog.getNum() + 1);
                    shortEmailSendLog.setEndTime(DateUtil.offsetMinute(shortEmailSendLog.getEndTime(), 10));
                    shortEmailSendLogService.updateShortEmailSendLog(shortEmailSendLog);
                    ShortUser shortUser = shortUserService.selectShortUserById(shortEmailSendLog.getUserId());
                    if (null == shortUser)
                        continue;

                    // 1. 配置API密钥
                    ApiClient defaultClient = Configuration.getDefaultApiClient();
                    ApiKeyAuth apiKey = (ApiKeyAuth) defaultClient.getAuthentication("api-key");
                    apiKey.setApiKey(brevoApiKey); // 替换为你的Brevo API密钥

                    // 2. 初始化邮件API
                    TransactionalEmailsApi apiInstance = new TransactionalEmailsApi();

                    // 3. 设置发件人
                    SendSmtpEmailSender sender = new SendSmtpEmailSender();

                    ShortEmailDomain shortEmailDomain = shortEmailDomainService.getrandomDomain(shortUser.getAppId());
                    if (null == shortEmailDomain) {
                        continue;
                    }


                    sender.setEmail(shortEmailDomain.getDomain()); // 必须已验证的邮箱
                    sender.setName(shortEmailDomain.getAppName());

                    // 4. 设置收件人
                    List<SendSmtpEmailTo> toList = new ArrayList<>();
                    SendSmtpEmailTo recipient = new SendSmtpEmailTo();
                    recipient.setEmail(null != shortEmailSendLog.getEmail() ? shortEmailSendLog.getEmail() : null);
                    recipient.setName("dearuser");
                    toList.add(recipient);

                    // 记录日志并处理结果
                    ShortRunlog runlog = new ShortRunlog();
                    runlog.setType("营销邮件");
                    runlog.setCreateTime(DateUtils.getNowDate());
                    runlog.setUpdateTime(DateUtils.getNowDate());

                    try {
                        // 5. 从HTML文件读取内容
                        // 修正资源加载代码
                        String htmlName = "KuShortEmail.html";

                        String icon = shortUserService.getIconByUserId(shortEmailSendLog.getUserId());

                        String enterUrl = "";
                        String subject = "";
                        String movieName = "";
                        String display1 = "none";
                        String display2 = "none";
                        String display3 = "none";
                        String movieName1 = "";
                        String movieName2 = "";
                        String movieName3 = "";
                        String movieUrl1 = "";
                        String movieUrl2 = "";
                        String movieUrl3 = "";

                        if (StringUtils.isEmpty(enterUrl)) {
                            ShortUserActivityLog shortUserActivityLog = shortUserActivityLogService.getNoteByUserId(shortEmailSendLog.getUserId());

                            if (null != shortUserActivityLog && StringUtils.isNotEmpty(shortUserActivityLog.getNote())) {
                                ShortMovie shortMovie = shortMovieService.selectShortMovieById(Long.valueOf(shortUserActivityLog.getNote()));
                                if (null != shortMovie) {
                                    String[] domains = {
                                            "file.flareshort.com",
                                            "file.veryshortvideos.com",
                                            "file.shortplayer.vip",
                                            "file.god-mod.net"
                                    };

                                    // 随机选择一个域名
                                    Random randomFile = new Random();
                                    String newDomain = domains[randomFile.nextInt(domains.length)];
                                    // 替换域名
                                    String newIcon = shortMovie.getIcon().replace("file.flareshort.com", newDomain);

                                    // 使用新URL
                                    icon = newIcon;
                                    subject = shortMovie.getName();
                                    movieName = shortMovie.getName();
                                    if (shortMovie.getLangId().intValue() == 4)
                                        htmlName = "EsKuShortEmail.html";
                                }
                            }

                            String str = shortUserActivityLog.getContent();
                            String separator = "url：";

                            // 查找分隔符位置
                            int index = str.indexOf(separator);

                            // 截取分隔符后的内容（需确保分隔符存在）
                            enterUrl = (index != -1) ? str.substring(index + separator.length()) : "";

                            String str1 = enterUrl;
                            String separator1 = "，";

                            int index1 = str1.indexOf(separator1);
                            enterUrl = (index1 != -1) ? str1.substring(0, index1) : str1;

                            String randomNum = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                            String randomNum1 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);


                            enterUrl = enterUrl + "&user_id=" + randomNum + shortUser.getHashId() + randomNum1 + "&unique_id=" + shortUser.getUniqueId() + "&is_email=true&link_app_id=3&email_server=brevo";


                            List<ShortUserActivityLog> shortUserActivityLogList = shortUserActivityLogService.getNoteByUserIdLimit3(shortEmailSendLog.getUserId(), shortUserActivityLog.getNote());
                            for (int i = 0; i < shortUserActivityLogList.size(); i++) {
                                String eurl = "";
                                ShortMovie shortMovie = shortMovieService.selectShortMovieById(Long.valueOf(shortUserActivityLogList.get(i).getNote()));

                                String str2 = shortUserActivityLogList.get(i).getContent();
                                String separator2 = "url：";

                                // 查找分隔符位置
                                int index2 = str2.indexOf(separator2);

                                // 截取分隔符后的内容（需确保分隔符存在）
                                eurl = (index2 != -1) ? str2.substring(index2 + separator2.length()) : "";

                                String str12 = eurl;
                                String separator12 = "，";

                                int index12 = str12.indexOf(separator12);
                                eurl = (index12 != -1) ? str12.substring(0, index12) : str12;

                                String randomNum2 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                                String randomNum12 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);


                                eurl = eurl + "&user_id=" + randomNum2 + shortUser.getHashId() + randomNum12 + "&unique_id=" + shortUser.getUniqueId() + "&is_email=true&link_app_id=3&email_server=brevo";
                                if (i == 0) {
                                    display1 = "block";
                                    movieName1 = shortMovie.getName();
                                    movieUrl1 = eurl;
                                }
                                if (i == 1) {
                                    display2 = "block";
                                    movieName2 = shortMovie.getName();
                                    movieUrl2 = eurl;
                                }
                                if (i == 2) {
                                    display3 = "block";
                                    movieName3 = shortMovie.getName();
                                    movieUrl3 = eurl;
                                }

                            }

                        }


                        ClassPathResource resource = new ClassPathResource("templates/" + htmlName);
                        String htmlContent = StreamUtils.copyToString(
                                resource.getInputStream(),
                                StandardCharsets.UTF_8
                        );


                        if (StringUtils.isEmpty(icon)) {
                            continue;
                        }

                        htmlContent = htmlContent
                                .replace("${enterUrl}", enterUrl)
                                .replace("${icon}", icon)

                                .replace("${movieName}", movieName)
                                .replace("{{movieName}}", movieName)

                                .replace("${appName}", shortEmailDomain.getAppName())
                                .replace("{{appName}}", shortEmailDomain.getAppName())

                                .replace("${appDomain}", shortEmailDomain.getAppDomain())
                                .replace("{{appDomain}}", shortEmailDomain.getAppDomain())

                                .replace("${display1}", display1)
                                .replace("{{display1}}", display1)
                                .replace("${display2}", display2)
                                .replace("{{display2}}", display2)
                                .replace("${display3}", display3)
                                .replace("{{display3}}", display3)
                                .replace("${movieUrl1}", movieUrl1)
                                .replace("{{movieUrl1}}", movieUrl1)
                                .replace("${movieUrl2}", movieUrl2)
                                .replace("{{movieUrl2}}", movieUrl2)
                                .replace("${movieUrl3}", movieUrl3)
                                .replace("{{movieUrl3}}", movieUrl3)
                                .replace("${movieName1}", movieName1)
                                .replace("{{movieName1}}", movieName1)
                                .replace("${movieName2}", movieName2)
                                .replace("{{movieName2}}", movieName2)
                                .replace("${movieName3}", movieName3)
                                .replace("{{movieName3}}", movieName3)


                                .replace("{{enterUrl}}", enterUrl)
                                .replace("{{icon}}", icon);

                        // 6. 创建邮件内容
                        SendSmtpEmail email = new SendSmtpEmail();
                        email.setSender(sender);
                        email.setTo(toList);
                        subject = subject + " Only $0.88 to Unlock";
                        email.setSubject(subject);
                        email.setHtmlContent(htmlContent); // 使用HTML内容

                        new Thread(() -> {
                            try {
                                // 7. 发送邮件
                                CreateSmtpEmail response = apiInstance.sendTransacEmail(email);
                                System.out.println("邮件发送成功！消息ID: " + response.getMessageId());
                                runlog.setState("1");
                                runlog.setContent(String.format(",营销召回邮件发送成功！消息ID: " + response.getMessageId() + "用户Id：" + shortEmailSendLog.getUserId() +
                                        ",邮件主题：" + "Special Notice: Best Limited Time Bonus Program for You" +
                                        ",收件人邮件：" + shortEmailSendLog.getEmail()));
                                runlog.setNote(response.toString());
                                shortRunlogService.insertShortRunlog(runlog);
                            } catch (Exception e) {
                                runlog.setState("0");
                                runlog.setContent(String.format(",发送失败：" + e.getMessage() + ",邮件主题：" + "Special Notice: Best Limited Time Bonus Program for You" + ",收件人邮件：" + shortEmailSendLog.getEmail()));
                                runlog.setNote(e.toString());
                                shortRunlogService.insertShortRunlog(runlog);
                            }
                        }).start();

                    } catch (IOException e) {
                        System.err.println("读取HTML文件失败: " + e.getMessage());
                        runlog.setState("0");
                        runlog.setContent(String.format(",读取HTML文件失败：" + e.getMessage() + ",邮件主题：Special Notice: Best Limited Time Bonus Program for You" + ",收件人邮件：" + shortEmailSendLog.getEmail()));
                        runlog.setNote(e.toString());
                        shortRunlogService.insertShortRunlog(runlog);
                        shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
                    } catch (Exception e) {
                        System.err.println("发送失败: " + e.getMessage());
                        runlog.setState("0");
                        runlog.setContent(String.format(",发送失败：" + e.getMessage() + ",邮件主题：Special Notice: Best Limited Time Bonus Program for You" + ",收件人邮件：" + shortEmailSendLog.getEmail()));
                        runlog.setNote(e.toString());
                        shortRunlogService.insertShortRunlog(runlog);
                        shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
                    }
                }


                //================================
                shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
                return AjaxResult.success("操作成功=");
            }
            shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
            return AjaxResult.success("操作成功");

        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("status", "error");
            error.put("message", "Validation failed: " + e.getMessage());
            shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
            return AjaxResult.error("操作失败");
        }
    }

    @Override
    public AjaxResult sendAwsEmail(EmailDTO emailDTO) {
        int count = shortIntermedStatusService.countByName("sendAwsEmail");
        if (count > 0)
            return AjaxResult.success("自动统计首页数据，请勿重复操作");

        ShortIntermedStatus shortIntermedStatus = new ShortIntermedStatus();
        shortIntermedStatus.setName("sendAwsEmail");
        shortIntermedStatusService.insertShortIntermedStatus(shortIntermedStatus);
        try {
            String privateKey = "-----BEGIN OPENSSH PRIVATE KEY-----\n" +
                    "b3BlbnNzaC1rZXktdjEAAAAABG5vbmUAAAAEbm9uZQAAAAAAAAABAAAAMwAAAAtzc2gtZW\n" +
                    "QyNTUxOQAAACDdx+eInOus2G8pGSS+paM8QV5SkQdAXGh/e0Z7pRaDRwAAAJiIp66uiKeu\n" +
                    "rgAAAAtzc2gtZWQyNTUxOQAAACDdx+eInOus2G8pGSS+paM8QV5SkQdAXGh/e0Z7pRaDRw\n" +
                    "AAAEDJW6rFB140gGyRJZM+mqoIFj8HAGrGpnuWj+sCJa/06N3H54ic66zYbykZJL6lozxB\n" +
                    "********************************************************\n" +
                    "-----END OPENSSH PRIVATE KEY-----";

            // 2. 加载密钥对
            KeyPair keyPair = KeyPair.load(new JSch(), privateKey.getBytes(), null);
            byte[] derivedPublicKeyBlob = keyPair.getPublicKeyBlob();

            // 3. 解析用户公钥（按空格分割并解码Base64）
            String[] publicKeyParts = emailDTO.getPublicKey().split(" ");
            if (publicKeyParts.length < 2) {
                throw new IllegalArgumentException("Invalid public key format");
            }
            byte[] userPublicKeyBlob = Base64.getDecoder().decode(publicKeyParts[1]);

            // 4. 安全比对（避免时序攻击）
            boolean isMatch = MessageDigest.isEqual(derivedPublicKeyBlob, userPublicKeyBlob);
            if (isMatch) {
                log.info("验证密钥正确------定时发邮件");

                List<ShortEmailSendLog> list = shortEmailSendLogService.getListByEndTime();
                for (ShortEmailSendLog shortEmailSendLog : list) {
                    //aws 免费的发送数量有限制，间隔250毫秒避免发送失败
                    Thread.sleep(250);
                    // 4. 设置收件人
                    emailDTO.setReceiveEmail(ListUtil.toList(shortEmailSendLog.getEmail()));
                    emailDTO.setLogId(ListUtil.toList(shortEmailSendLog.getId()));
                    // 记录日志并处理结果
                    ShortRunlog runlog = new ShortRunlog();
                    runlog.setType("欢迎邮件");
                    runlog.setCreateTime(DateUtils.getNowDate());
                    runlog.setUpdateTime(DateUtils.getNowDate());
                    try {


                        // 5. 从HTML文件读取内容
                        // 修正资源加载代码
                        String htmlName = "reg.html";

                        // 使用更安全的占位符格式
                        //剧集图片
                        ShortUser shortUser = shortUserService.selectShortUserById(shortEmailSendLog.getUserId());
                        String icon = shortUserService.getIconByUserId(shortEmailSendLog.getUserId());


                        if (StringUtils.isEmpty(icon)) {
                            continue;
                        }

                        Map<String, Object> aws = buildParamMap(shortEmailSendLog.getUserId(), shortUser, "aws");
                        htmlName = aws.get("htmlName") != null ? aws.get("htmlName").toString() : htmlName;
                        String htmlContent = buildHtmlContent(htmlName, aws);
                        String movieName = aws.get("movieName") != null ? aws.get("movieName").toString() : "";
                        // 6. 创建邮件内容

                        String subject = movieName + " Only $0.88 to Unlock";
                        emailDTO.setSubject(subject);
                        emailDTO.setContent(htmlContent); // 使用HTML内容
                        emailDTO.setTitle("Kushort");
                        try {
                            emailDTO.setParamMap(aws);
                            // 7. 发送邮件
                            R r = emailServiceContext.getService(emailDTO.getServiceType()).sendEmail(emailDTO);
                            shortEmailSendLog.setNum(shortEmailSendLog.getNum() + 1);
                            shortEmailSendLog.setEndTime(DateUtil.offsetMinute(shortEmailSendLog.getEndTime(), 10));
                            shortEmailSendLogService.updateShortEmailSendLog(shortEmailSendLog);
                            log.info("发送成功：" + r);
                            runlog.setState("1");
                            runlog.setContent(String.format("用户Id：" + shortEmailSendLog.getUserId() +
                                    ",邮件主题：" + subject + ",邮箱服务商:AWS" +
                                    ",收件人邮件：" + shortEmailSendLog.getEmail()));
                            runlog.setNote("aws");
                            shortRunlogService.insertShortRunlog(runlog);
                        } catch (Exception e) {
                            runlog.setState("0");
                            runlog.setContent(String.format(",发送失败：" + e.getMessage() + ",邮件主题：" + "Special Notice: Best Limited Time Bonus Program for You" + ",收件人邮件：" + shortEmailSendLog.getEmail()));
                            runlog.setNote(e.toString());
                            shortRunlogService.insertShortRunlog(runlog);
                        }

                    } catch (IOException e) {
                        runlog.setState("0");
                        runlog.setContent(String.format(",读取HTML文件失败：" + e.getMessage() + ",邮箱服务商:AWS" + ",收件人邮件：" + shortEmailSendLog.getEmail()));
                        runlog.setNote(e.toString());
                        shortRunlogService.insertShortRunlog(runlog);
                        shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
                    } catch (Exception e) {
                        runlog.setState("0");
                        runlog.setContent(String.format(",发送失败：" + e.getMessage() + ",邮箱服务商:AWS" + ",收件人邮件：" + shortEmailSendLog.getEmail()));
                        runlog.setNote(e.toString());
                        shortRunlogService.insertShortRunlog(runlog);
                        shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
                    }
                }

                shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
                return AjaxResult.success("操作成功=");
            }
            shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
            return AjaxResult.success("操作成功");

        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("status", "error");
            error.put("message", "Validation failed: " + e.getMessage());
            shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
            return AjaxResult.error("操作失败");
        }
    }

    private String buildHtmlContent(String htmlName, Map<String, Object> data) throws IOException {
        ClassPathResource resource = new ClassPathResource("templates/" + htmlName);
        String htmlContent = StreamUtils.copyToString(
                resource.getInputStream(),
                StandardCharsets.UTF_8
        );
        return StrUtil.format(htmlContent, data);

    }

    private Map<String, Object> buildParamMap(Long userId, ShortUser shortUser, String type) {
        Map<String, Object> paramMap = new HashMap<>();
        ShortUserActivityLog shortUserActivityLog = shortUserActivityLogService.getNoteByUserId(userId);

        if (null != shortUserActivityLog && StringUtils.isNotEmpty(shortUserActivityLog.getNote())) {
            ShortMovie shortMovie = shortMovieService.selectShortMovieById(Long.valueOf(shortUserActivityLog.getNote()));
            if (null != shortMovie) {
                String[] domains = {
                        "file.flareshort.com",
                        "file.veryshortvideos.com",
                        "file.shortplayer.vip",
                        "file.god-mod.net"
                };

                // 随机选择一个域名
                Random randomFile = new Random();
                String newDomain = domains[randomFile.nextInt(domains.length)];
                // 替换域名
                String newIcon = shortMovie.getIcon().replace("file.flareshort.com", newDomain);
                paramMap.put("icon", newIcon);
                paramMap.put("movieName", shortMovie.getName());
                if (shortMovie.getLangId().intValue() == 4)
                    paramMap.put("htmlName", "EsKuShortEmail.html");
            }
        }

        String str = shortUserActivityLog.getContent();
        String separator = "url：";

        // 查找分隔符位置
        int index = str.indexOf(separator);

        // 截取分隔符后的内容（需确保分隔符存在）
        String enterUrl = (index != -1) ? str.substring(index + separator.length()) : "";
        paramMap.put("enterUrl", enterUrl);
        String str1 = enterUrl;
        String separator1 = "，";

        int index1 = str1.indexOf(separator1);
        enterUrl = (index1 != -1) ? str1.substring(0, index1) : str1;

        String randomNum = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
        String randomNum1 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);


        enterUrl = enterUrl + "&user_id=" + randomNum + shortUser.getHashId() + randomNum1 + "&unique_id=" + shortUser.getUniqueId() + "&is_email=true&link_app_id=3&email_server=" + type;

        paramMap.put("enterUrl", enterUrl);
        String display1 = "none", display2 = "none", display3 = "none";
        String movieName1 = "", movieName2 = "", movieName3 = "";
        String movieUrl1 = "", movieUrl2 = "", movieUrl3 = "";
        List<ShortUserActivityLog> shortUserActivityLogList = shortUserActivityLogService.getNoteByUserIdLimit3(userId, shortUserActivityLog.getNote());
        for (int i = 0; i < shortUserActivityLogList.size(); i++) {
            String eurl = "";
            ShortMovie shortMovie = shortMovieService.selectShortMovieById(Long.valueOf(shortUserActivityLogList.get(i).getNote()));

            String str2 = shortUserActivityLogList.get(i).getContent();
            String separator2 = "url：";

            // 查找分隔符位置
            int index2 = str2.indexOf(separator2);

            // 截取分隔符后的内容（需确保分隔符存在）
            eurl = (index2 != -1) ? str2.substring(index2 + separator2.length()) : "";

            String str12 = eurl;
            String separator12 = "，";

            int index12 = str12.indexOf(separator12);
            eurl = (index12 != -1) ? str12.substring(0, index12) : str12;

            String randomNum2 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
            String randomNum12 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
            eurl = eurl + "&user_id=" + randomNum2 + shortUser.getHashId() + randomNum12 + "&unique_id=" + shortUser.getUniqueId() + "&is_email=true&link_app_id=3&email_server=" + type;
            if (i == 0) {
                display1 = "block";

                movieName1 = shortMovie.getName();
                movieUrl1 = eurl;
            }
            if (i == 1) {
                display2 = "block";
                movieName2 = shortMovie.getName();
                movieUrl2 = eurl;
            }
            if (i == 2) {
                display3 = "block";
                movieName3 = shortMovie.getName();
                movieUrl3 = eurl;
            }

        }
        paramMap.put("display1", display1);
        paramMap.put("display2", display2);
        paramMap.put("display3", display3);
        paramMap.put("movieUrl1", movieUrl1);
        paramMap.put("movieUrl2", movieUrl2);
        paramMap.put("movieUrl3", movieUrl3);
        paramMap.put("movieName1", movieName1);
        paramMap.put("movieName2", movieName2);
        paramMap.put("movieName3", movieName3);
        return paramMap;
    }

    // 添加辅助方法：对数据进行哈希处理
    private String hashData(String data) {
        if (StringUtils.isEmpty(data)) {
            return "";
        }
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(data.getBytes(StandardCharsets.UTF_8));
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) hexString.append('0');
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (Exception e) {
            log.error("哈希数据失败: {}", e.getMessage());
            return "";
        }
    }

    // 在服务关闭时关闭线程池
    @PreDestroy
    public void destroy() {
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
        }
    }

    public void initH5ResolveUaConfig() {
        if (CollectionUtil.isEmpty(h5ResolveUaAppList)) {
            //从redis中获取
            String key = CacheConstants.SYS_CONFIG_KEY + "sys.app.resolveAppIds";
            Object str = redisCache.getCacheObject(key);
            String appConfig = JSONUtil.toJsonStr(str);
            //转化为 LONG集合
            if (StringUtils.isNotEmpty(appConfig)) {
                List<Long> list = JSONUtil.toList(appConfig, Long.class);
                h5ResolveUaAppList.addAll(list);
            }
        }
    }


    @Override
    public Map<String, Object> initUser(String appId, InitUserDTO initUserDTO) {
        Map<String, Object> resultMap = new HashMap<>();
        log.info("H5执行初始化用户=={}", JSONUtil.toJsonStr(initUserDTO));
        try {
            log.info("初始化H5用户: appId={}", appId);

            // 根据linkid获取并填充DTO --------
            String linkIdStr = initUserDTO.getLinkid();
            setLinkIdStr(linkIdStr, initUserDTO);

            String ip = initUserDTO.getIp();

            // 过滤掉本地局域网IP地址
            if (StringUtils.isNotEmpty(ip) && !"null".equals(ip) && !"none".equals(ip)) {
                if (isPrivateOrLoopbackIp(ip)) {
                    initUserDTO.setIp("");
                }
            }

            // 打印关键日志信息
            log.info("来源: {}, 语言: {}, IP: {}, 推广链接ID: {}", initUserDTO.getSource(), initUserDTO.getLanguage(), initUserDTO.getIp(), initUserDTO.getLinkid());

            boolean appSwitch = false;
            boolean isRenderingAdvance = renderingAdvance;

            log.info("开始处理用户信息");

            // 为用户创建一个临时账户
            ShortUser tempUser = null;

            //根据 appId 和 pixelId 查找关联记录
            //ShortFacebookPixel shortFacebookPixel = shortFacebookPixelService.selectByAppIdAndFacebookPixelId(initUserDTO.getPixelId(), appId);
            ShortSemLink shortSemLink = null;
            if (StringUtils.isNotEmpty(initUserDTO.getLinkid())) {
                shortSemLink = shortSemLinkService.selectShortSemLinkById(Long.valueOf(initUserDTO.getLinkid()));
                log.info("appId=>{},facebookPixelId=>{},查询深链信息=>{},", appId, initUserDTO.getPixelId(), shortSemLink);
            }
            if (null != shortSemLink) {
                initUserDTO.setSource(shortSemLink.getPlatform());
            }

            // 先检查是否存在具有相同 unique_id 的用户
            ShortUser queryUser = new ShortUser();
            queryUser.setUniqueId(initUserDTO.getUniqueId());
            queryUser.setAppId(Long.valueOf(appId));
            //查询App信息
            ShortApp shortApp = shortAppService.selectShortAppById(Long.valueOf(appId));
            if (null == shortApp) {
                throw new RuntimeException("appId不存在:" + appId);
            }
            String appType = shortApp.getType();
            List<ShortUser> existingUsers = shortUserService.selectShortUserList(queryUser);
            ShortUser existingUser = existingUsers.isEmpty() ? null : existingUsers.get(0);
            //初始化H5解析用户UA配置
            //initH5ResolveUaConfig();
            if (existingUser != null) {
                log.info("找到已存在用户: id={}, uniqueId={}", existingUser.getId(), initUserDTO.getUniqueId());
                if (null != shortSemLink) {
                    existingUser.setPixelId(shortSemLink.getPixelId());
                    initUserDTO.setSource(shortSemLink.getPlatform());
                }
                linkRecord(existingUser, initUserDTO);

                // 如果存在相同 unique_id 的用户，使用该用户
                tempUser = existingUser;
                existingUser.setAppType(appType);
                if (StringUtils.isNotEmpty(existingUser.getPhoneVersion())) {
                    existingUser.setDeviceType("Apple".equals(existingUser.getPhoneVersion()) ? "0" : "1");
                }
                setUpdateUser(existingUser, initUserDTO);

            } else {
                log.info("未找到现有用户，创建新用户: uniqueId={}", initUserDTO.getUniqueId());
                String uuid = UUID.randomUUID().toString();
                redisTemplate.opsForValue().set(initUserDTO.getUniqueId() + "-" + appId, uuid, 2, TimeUnit.MINUTES);
                // 如果不存在，创建新用户
                ShortUser newUser = new ShortUser();
                if (initUserDTO.getPayByEmailFlag()) {
                    newUser.setPayByEmailFlag(Boolean.FALSE);
                }

                newUser.setUniqueId(initUserDTO.getUniqueId());
                newUser.setAppId(Long.valueOf(appId));
                newUser.setUsername("temp_user");
                newUser.setPassword("temp_user");
                newUser.setAvatar("/media/none.png");
                newUser.setState("1");
                newUser.setSource(initUserDTO.getSource());
                newUser.setEmail("");
                newUser.setLanguage(initUserDTO.getLanguage());
                newUser.setAppVersion(initUserDTO.getAppVersion());
                newUser.setIp(initUserDTO.getIp());
                newUser.setSubId("");
                newUser.setToken("");
                newUser.setCoin(0L);
                newUser.setSignInDays(1L);
                if (null != shortSemLink) {
                    newUser.setPixelId(shortSemLink.getPixelId());
                    newUser.setSource(shortSemLink.getPlatform());
                }
//                newUser.setCountry(initUserDTO.getCountry());
//                newUser.setRegion(initUserDTO.getRegion());
//                newUser.setCity(initUserDTO.getCity());
                Map<String, String> countryMap = ipInfoService.getCountryByIp(ip);
                newUser.setCountry(countryMap.get("countryName"));
                //非苹果设备 从手机model映射表查询具体手机品牌+型号
                try {
                    if (StringUtils.isNotEmpty(initUserDTO.getPhoneVersion()) && !"Apple".equals(initUserDTO.getPhoneVersion())) {
                        String deviceName = DeviceUtils.getDeviceName(initUserDTO.getUa());
                        if (null != deviceName) {
                            newUser.setPhoneVersion(deviceName);
                        } else {
                            newUser.setPhoneVersion(initUserDTO.getPhoneVersion());
                        }
                    } else {
                        newUser.setPhoneVersion(initUserDTO.getPhoneVersion());
                    }
                } catch (Exception e) {
                    newUser.setPhoneVersion(initUserDTO.getPhoneVersion());
                    log.error("获取手机型号失败: {}", e.getMessage());
                }
                newUser.setSystemVersion(initUserDTO.getSystemVersion());
                newUser.setAppVersion(initUserDTO.getAppVersion());
                newUser.setAppType(appType);
                if (StringUtils.isNotEmpty(newUser.getPhoneVersion())) {
                    newUser.setDeviceType("Apple".equals(newUser.getPhoneVersion()) ? "0" : "1");
                }
                if (StringUtils.isNotEmpty(initUserDTO.getLinkid()) && !"null".equals(initUserDTO.getLinkid()) && !"none".equals(initUserDTO.getLinkid())) {
                    newUser.setLinkidId(Long.valueOf(initUserDTO.getLinkid()));
                    newUser.setLinkTime(DateUtils.getNowDate());
                }
                //设置广告ID
                if (StringUtils.isNotEmpty(initUserDTO.getAdId()) && !"null".equals(initUserDTO.getAdId())) {
                    newUser.setAdId(initUserDTO.getAdId());
                }
                Map<String, Object> otherMap = new HashMap<>();
                otherMap.put("ua", initUserDTO.getUa());
                otherMap.put("fbc", initUserDTO.getFbc());
                otherMap.put("fbp", initUserDTO.getFbp());
                otherMap.put("campaignName", initUserDTO.getCampaignName());
                otherMap.put("campaignId", initUserDTO.getCampaignId());
                otherMap.put("adsetName", initUserDTO.getAdsetName());
                otherMap.put("adsetId", initUserDTO.getAdsetId());
                otherMap.put("adName", initUserDTO.getAdName());
                otherMap.put("adId", initUserDTO.getAdId());

                newUser.setOther(objectMapper.writeValueAsString(otherMap));

                shortUserService.insertShortUser(newUser);
                log.info("创建新用户成功: id={}", newUser.getId());

                // 生成4位随机数字 混淆用户ID
                // 使用UUID的hashCode值转为字符串并取前4位
                String randomNum = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 4);
                newUser.setHashId(randomNum + newUser.getId());
                shortUserService.updateShortUser(newUser);

                // 记录用户多链接进入的数据，方便溯源
                if (StringUtils.isNotEmpty(initUserDTO.getLinkid()) && !"null".equals(initUserDTO.getLinkid()) && !"none".equals(initUserDTO.getLinkid())) {
                    ShortUserLinkRecord record = new ShortUserLinkRecord();
                    record.setUserId(newUser.getId());
                    record.setLinkId(Long.valueOf(initUserDTO.getLinkid()));
                    if (null != shortSemLink) {
                        record.setPixelId(shortSemLink.getPixelId());
                    }
                    shortUserLinkRecordService.insertShortUserLinkRecord(record);
                    log.info("记录新用户链接进入: userId={}, linkId={}", newUser.getId(), initUserDTO.getLinkid());
                }

                tempUser = newUser;

                // 异步发送应用内事件，不阻塞主进程
//                if (tempUser != null) {
//                    // 处理支付Token和客户ID
//                    try {
//                        //判断使用 空中云汇 还是 useepay
//                        ShortExtplats shortExtplats = shortExtplatsService.selectShortExtplatsById(shortApp.getExtplatsId());
//                        if(null == shortExtplats){
//                            log.error("未找到该app的支付接口");
//                            return resultMap;
//                        }
//                        String payChannel = shortExtplats.getPayChannel();
//                        if(PayChannel.USEEPAY.getCode().equals(payChannel)){
//                            createUseepayCustomer(tempUser, shortApp);
//                        }else if(PayChannel.AIRWALLEX.getCode().equals(payChannel)){
//                            createAirwallexCustomer(tempUser, shortApp);
//                        }else {
//                            log.error("未找到该app的支付接口");
//                            return resultMap;
//                        }
//                    } catch (Exception e) {
//                        // 记录异常日志
//                        ShortRunlog runlog = new ShortRunlog();
//                        runlog.setType("支付customer_id");
//                        runlog.setState("0");
//                        runlog.setContent("用户：" + tempUser.getId() + "，创建支付customer_id失败：" + e.getMessage());
//                        runlog.setNote(e.getMessage());
//                        shortRunlogService.insertShortRunlog(runlog);
//                        log.error("创建支付customer_id失败", e);
//                    }
//                }
            }

            // 上报用户行为日志
            try {
                // 上报用户登录事件
                ShortUserEventRecord eventRecord = new ShortUserEventRecord();
                eventRecord.setUserId(tempUser.getId());
                eventRecord.setEventType("h5_login");

                Map<String, Object> eventDataMap = new HashMap<>();
                eventDataMap.put("source", initUserDTO.getSource());
                eventDataMap.put("language", initUserDTO.getLanguage());
                eventDataMap.put("ip", initUserDTO.getIp());
                eventDataMap.put("ua", initUserDTO.getUa());
                eventDataMap.put("fbc", initUserDTO.getFbc());
                eventDataMap.put("fbp", initUserDTO.getFbp());
                eventDataMap.put("linkid", initUserDTO.getLinkid());
                eventDataMap.put("campaignName", initUserDTO.getCampaignName());
                eventDataMap.put("campaignId", initUserDTO.getCampaignId());
                eventDataMap.put("adsetName", initUserDTO.getAdsetName());
                eventDataMap.put("adsetId", initUserDTO.getAdsetId());
                eventDataMap.put("adName", initUserDTO.getAdName());
                eventDataMap.put("adId", initUserDTO.getAdId());
                eventDataMap.put("deepid", initUserDTO.getDeepid());
                eventDataMap.put("movie", initUserDTO.getMovie());
                eventDataMap.put("pid", initUserDTO.getPid());
                eventDataMap.put("kid", initUserDTO.getKid());
                eventDataMap.put("pixel_id", initUserDTO.getPixelId());

                eventRecord.setNote(objectMapper.writeValueAsString(eventDataMap));
                eventRecord.setState("1");
                shortUserEventRecordService.insertShortUserEventRecord(eventRecord);

                // 构建内容消息
                StringBuilder contentBuilder = new StringBuilder();
                contentBuilder.append("用户：").append(tempUser.getId()).append("，H5登录成功");

                if (StringUtils.isNotEmpty(initUserDTO.getLinkid())) {
                    contentBuilder.append("，通过链接 ").append(initUserDTO.getLinkid());
                }

                contentBuilder.append("，进入H5页面");

                // 添加深链信息
                StringBuilder deepLinkInfo = new StringBuilder();
                if (StringUtils.isNotEmpty(initUserDTO.getMovie()) || StringUtils.isNotEmpty(initUserDTO.getPid()) ||
                        StringUtils.isNotEmpty(initUserDTO.getKid()) || StringUtils.isNotEmpty(initUserDTO.getPixelId()) ||
                        StringUtils.isNotEmpty(initUserDTO.getAdId()) || StringUtils.isNotEmpty(appId)) {

                    deepLinkInfo.append("，深链信息携带的信息（");

                    boolean hasAddedInfo = false;

                    if (StringUtils.isNotEmpty(appId)) {
                        deepLinkInfo.append("APP_ID：").append(appId);
                        hasAddedInfo = true;
                    }
                    if (StringUtils.isNotEmpty(initUserDTO.getEnterUrl())) {
                        deepLinkInfo.append("进入Url：").append(initUserDTO.getEnterUrl());
                        hasAddedInfo = true;
                    }

                    if (StringUtils.isNotEmpty(initUserDTO.getAdId())) {
                        if (hasAddedInfo) deepLinkInfo.append("，");
                        deepLinkInfo.append("ADID：").append(initUserDTO.getAdId());
                        hasAddedInfo = true;
                    }

                    if (StringUtils.isNotEmpty(initUserDTO.getMovie())) {
                        if (hasAddedInfo) deepLinkInfo.append("，");
                        deepLinkInfo.append("短剧：").append(initUserDTO.getMovie());
                        hasAddedInfo = true;
                    }

                    if (StringUtils.isNotEmpty(initUserDTO.getPid())) {
                        if (hasAddedInfo) deepLinkInfo.append("，");
                        deepLinkInfo.append("充值面板：").append(initUserDTO.getPid());
                        hasAddedInfo = true;
                    }

                    if (StringUtils.isNotEmpty(initUserDTO.getKid())) {
                        if (hasAddedInfo) deepLinkInfo.append("，");
                        deepLinkInfo.append("扣费面板：").append(initUserDTO.getKid());
                        hasAddedInfo = true;
                    }

                    if (StringUtils.isNotEmpty(initUserDTO.getPixelId())) {
                        if (hasAddedInfo) deepLinkInfo.append("，");
                        deepLinkInfo.append("PIXEL_ID：").append(initUserDTO.getPixelId());
                    }

                    deepLinkInfo.append("）");
                    contentBuilder.append(deepLinkInfo);
                    contentBuilder.append("，");
                    contentBuilder.append("缓存刷新时间：").append(initUserDTO.getCacheRefreshTime());
                }

                // 使用活动日志服务记录用户登录
                ShortUserActivityLog activityLog = new ShortUserActivityLog();
                activityLog.setUserId(tempUser.getId());
                activityLog.setAppId(Long.valueOf(appId));
                activityLog.setState("1"); // 成功状态
                activityLog.setContent(contentBuilder.toString());
                activityLog.setNote(StringUtils.isNotEmpty(tempUser.getSource()) ? tempUser.getSource() : "H5用户");

                shortUserActivityLogService.insertShortUserActivityLog(activityLog);
                log.info("记录用户活动日志成功: userId={}", tempUser.getId());
            } catch (Exception e) {
                log.error("上报用户行为日志失败", e);
                ShortUser finalTempUser = tempUser;
                threadPoolTaskExecutor.execute(() -> {
                    ShortUserActivityLog shortUserActivityLog = new ShortUserActivityLog();
                    shortUserActivityLog.setUserId(finalTempUser.getId());
                    shortUserActivityLog.setAppId(Long.valueOf(appId));
                    shortUserActivityLog.setState("0");
                    shortUserActivityLog.setContent("用户：" + finalTempUser.getId() + "，H5登录失败");
                    shortUserActivityLog.setNote("用户行为日志上报失败");
                    shortUserActivityLogService.insertShortUserActivityLog(shortUserActivityLog);
                });
                // 不影响主流程，这里仅记录错误日志
            }

            // 拼接用户ID
            String uid = tempUser.getHashId();

            resultMap.put("code", 200);
            resultMap.put("msg", "success");

            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("state", appSwitch);
            dataMap.put("source", tempUser.getSource());
            dataMap.put("user_id", uid);
            dataMap.put("isRenderingAdvance", isRenderingAdvance);

            resultMap.put("data", dataMap);

        } catch (Exception e) {
            log.error("初始化用户失败：", e);

            // 上报异常
            ShortRunlog runlog = new ShortRunlog();
            runlog.setType("初始化用户错误");
            runlog.setState("0");
            runlog.setContent("初始化用户 - 失败：" + e.getMessage());
            runlog.setNote(e.getMessage());
            shortRunlogService.insertShortRunlog(runlog);

            // 尝试记录用户行为日志（异常情况）
            try {
                // 构建错误消息
                StringBuilder contentBuilder = new StringBuilder();
                contentBuilder.append("错误：").append(e.getMessage());

                if (StringUtils.isNotEmpty(initUserDTO.getLinkid()) && !"null".equals(initUserDTO.getLinkid()) && !"none".equals(initUserDTO.getLinkid())) {
                    contentBuilder.append("，通过链接 ").append(initUserDTO.getLinkid());
                }

                contentBuilder.append("，进入H5页面");

                // 添加深链信息
                StringBuilder deepLinkInfo = new StringBuilder();
                if (StringUtils.isNotEmpty(initUserDTO.getMovie()) || StringUtils.isNotEmpty(initUserDTO.getPid()) ||
                        StringUtils.isNotEmpty(initUserDTO.getKid()) || StringUtils.isNotEmpty(initUserDTO.getPixelId()) ||
                        StringUtils.isNotEmpty(initUserDTO.getAdId()) || StringUtils.isNotEmpty(appId)) {

                    deepLinkInfo.append("，深链信息携带的信息（");

                    boolean hasAddedInfo = false;

                    if (StringUtils.isNotEmpty(appId)) {
                        deepLinkInfo.append("APP_ID：").append(appId);
                        hasAddedInfo = true;
                    }

                    if (StringUtils.isNotEmpty(initUserDTO.getAdId())) {
                        if (hasAddedInfo) deepLinkInfo.append("，");
                        deepLinkInfo.append("ADID：").append(initUserDTO.getAdId());
                        hasAddedInfo = true;
                    }

                    if (StringUtils.isNotEmpty(initUserDTO.getMovie())) {
                        if (hasAddedInfo) deepLinkInfo.append("，");
                        deepLinkInfo.append("短剧：").append(initUserDTO.getMovie());
                        hasAddedInfo = true;
                    }

                    if (StringUtils.isNotEmpty(initUserDTO.getPid())) {
                        if (hasAddedInfo) deepLinkInfo.append("，");
                        deepLinkInfo.append("充值面板：").append(initUserDTO.getPid());
                        hasAddedInfo = true;
                    }

                    if (StringUtils.isNotEmpty(initUserDTO.getKid())) {
                        if (hasAddedInfo) deepLinkInfo.append("，");
                        deepLinkInfo.append("扣费面板：").append(initUserDTO.getKid());
                        hasAddedInfo = true;
                    }

                    if (StringUtils.isNotEmpty(initUserDTO.getPixelId())) {
                        if (hasAddedInfo) deepLinkInfo.append("，");
                        deepLinkInfo.append("PIXEL_ID：").append(initUserDTO.getPixelId());
                    }

                    deepLinkInfo.append("）");
                    contentBuilder.append(deepLinkInfo);
                }

                // 创建用户活动日志记录
                ShortUserActivityLog activityLog = new ShortUserActivityLog();
                activityLog.setAppId(Long.valueOf(appId));
                activityLog.setState("0"); // 失败状态
                activityLog.setContent(contentBuilder.toString());
                activityLog.setNote("H5系统异常");

                shortUserActivityLogService.insertShortUserActivityLog(activityLog);
                log.info("记录异常活动日志成功");

            } catch (Exception ex) {
                log.error("记录异常活动日志失败", ex);
            }

            resultMap.put("code", 200);
            resultMap.put("msg", "初始化用户失败：" + e.getMessage());

            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("state", false);
            dataMap.put("source", "");
            dataMap.put("user_id", "");
            dataMap.put("isRenderingAdvance", renderingAdvance);

            resultMap.put("data", dataMap);
        }

        return resultMap;
    }

    private void createUseepayCustomer(ShortUser tempUser, ShortApp shortApp) throws Exception {
        log.info("走useepay 创建客户");
        if (StringUtils.isNotEmpty(tempUser.getPayInfo())) {
            log.info("老用户,已创建支付身份");
            return;
        }
        //根据app配置的支付渠道
        Long extplatsId = shortApp.getExtplatsId();
        ShortExtplats shortExtplats = shortExtplatsService.selectShortExtplatsById(extplatsId);
        String merchantNo = shortExtplats.getToken();
        String xAppId = shortExtplats.getKey();
        String clientSecret = shortExtplats.getClientSecret();
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("x-merchant-no", merchantNo);
        headerMap.put("x-app-id", xAppId);
        headerMap.put("x-api-key", clientSecret);
        headerMap.put("Content-Type", "application/json");
        //设置入参
        Map<String, Object> customerRequest = new HashMap<>();
        customerRequest.put("name", tempUser.getUsername());
        customerRequest.put("email", StringUtils.isEmpty(tempUser.getEmail()) ? "<EMAIL>" : tempUser.getEmail());
        customerRequest.put("merchant_customer_id", tempUser.getId().toString());
        Map<String, Object> addressMap = new HashMap<>();
        addressMap.put("country", "US");
        addressMap.put("line1", "New York");
        customerRequest.put("address", addressMap);
        cn.hutool.http.HttpResponse resp = HttpRequest.post(useepayDomain + useepayCreateCustomerUrl)
                .addHeaders(headerMap)
                .body(JSONUtil.toJsonStr(customerRequest))
                .execute();
        if (resp.getStatus() != 200) {
            throw new Exception("创建用户失败：" + resp.body());
        }
        log.info("创建useepay用户成功：" + resp.body());
        JSONObject jsonObject = JSONUtil.parseObj(resp.body());
        String customerId = jsonObject.getStr("id");
        tempUser.setPayInfo(customerId);
        shortUserService.updateShortUser(tempUser);
        // 记录日志
        ShortRunlog runlog = new ShortRunlog();
        runlog.setType("USEEPAY支付customer_id");
        runlog.setState("1");
        runlog.setContent("用户：" + tempUser.getId() + "，创建支付customer_id：" + customerId);
        runlog.setNote("");
        shortRunlogService.insertShortRunlog(runlog);
    }

    private void createAirwallexCustomer(ShortUser tempUser, ShortApp shortApp) throws Exception {
        // 获取支付Token，传入appId
        String token = shortExtplatsService.getPaymentToken(shortApp.getId().toString());
        if (token != null) {
            // 创建唯一请求ID
            String requestId = UUID.randomUUID().toString();

            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + token);

            // 构建请求体
            Map<String, String> customerRequest = new HashMap<>();
            customerRequest.put("request_id", requestId);
            customerRequest.put("merchant_customer_id", "merchant_" + tempUser.getHashId());

            customerRequest.put("email", tempUser.getEmail());


            HttpEntity<Map<String, String>> request = new HttpEntity<>(customerRequest, headers);

            // 发送创建客户请求
            ResponseEntity<Map> response = restTemplate.postForEntity(
                    (useDevApi ? devApiUrl : prodApiUrl) + "/api/v1/pa/customers/create",
                    request,
                    Map.class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                // 提取客户信息
                String customerId = (String) response.getBody().get("id");
                String clientSecret = (String) response.getBody().get("client_secret");

                if (customerId != null && !"null".equals(customerId)) {
                    // 创建支付信息JSON
                    ArrayNode payInfoArray = objectMapper.createArrayNode();
                    ObjectNode payInfo = objectMapper.createObjectNode();
                    payInfo.put("type", "airwallex");
                    payInfo.put("customer_id", customerId);
                    payInfo.put("client_secret", clientSecret);
                    payInfoArray.add(payInfo);

                    // 保存支付信息到用户
                    tempUser.setPayInfo(objectMapper.writeValueAsString(payInfoArray));
                    shortUserService.updateShortUser(tempUser);

                    // 记录日志
                    ShortRunlog runlog = new ShortRunlog();
                    runlog.setType("支付customer_id");
                    runlog.setState("1");
                    runlog.setContent("用户：" + tempUser.getId() + "，创建支付customer_id：" + objectMapper.writeValueAsString(response.getBody()));
                    runlog.setNote("");
                    shortRunlogService.insertShortRunlog(runlog);
                }
            }
        }
    }

    @Override
    public Map<String, Object> getLinkVideos(String appId, String hashId) {
        Map<String, Object> resultMap = new HashMap<>();
        long startTime = System.currentTimeMillis();

        try {
            log.info("查询用户深链关联电影: appId={}, hashId={}", appId, hashId);

            if (StringUtils.isEmpty(hashId)) {
                throw new BaseException("用户ID不能为空");
            }

            // 通过用户临时ID查询真实用户
            ShortUser queryUser = new ShortUser();
            queryUser.setHashId(hashId);
            queryUser.setAppId(Long.valueOf(appId)); // 增加appId条件
            List<ShortUser> users = shortUserService.selectShortUserList(queryUser);

            if (users.isEmpty()) {
                throw new BaseException("未找到用户信息");
            }

            ShortUser user = users.get(0);
            log.info("找到用户: id={}, hashId={}", user.getId(), user.getHashId());

            // 查询该用户所有的链接记录
            ShortUserLinkRecord linkRecord = new ShortUserLinkRecord();
            linkRecord.setUserId(user.getId());
            List<ShortUserLinkRecord> linkRecords = shortUserLinkRecordService.selectShortUserLinkRecordList(linkRecord);

            // 记录查询日志
            ShortRunlog runlog = new ShortRunlog();
            runlog.setType("查询深链电影");
            runlog.setState("1");
            runlog.setContent("用户：" + user.getId() + "，hashId：" + hashId + "，appId: " + appId + "，查询关联电影，共找到链接记录：" + linkRecords.size() + "条");
            runlog.setNote("");
            shortRunlogService.insertShortRunlog(runlog);

            // 确保包含用户当前的linkidId
            if (user.getLinkidId() != null && linkRecords.stream().noneMatch(record -> record.getLinkId().equals(user.getLinkidId()))) {
                ShortUserLinkRecord currentRecord = new ShortUserLinkRecord();
                currentRecord.setUserId(user.getId());
                currentRecord.setLinkId(user.getLinkidId());
                linkRecords.add(currentRecord);
                log.info("添加用户当前linkidId到记录中: linkidId={}", user.getLinkidId());
            }

            // 收集不重复的链接ID
            Set<Long> linkIdSet = new HashSet<>();
            for (ShortUserLinkRecord record : linkRecords) {
                if (record.getLinkId() != null) {
                    linkIdSet.add(record.getLinkId());
                }
            }
            List<Long> linkIds = new ArrayList<>(linkIdSet);

            // 存储处理数据的映射
            Map<Long, ShortSemLink> semLinkMap = new HashMap<>();
            Map<Long, List<Long>> movieLinkMap = new HashMap<>();
            Map<Long, ShortMovie> movieMap = new HashMap<>();

            // 批量查询所有链接信息
            if (!linkIds.isEmpty()) {
                log.info("开始查询用户关联的 {} 个链接信息", linkIds.size());
                for (Long linkId : linkIds) {
                    try {
                        ShortSemLink semLink = shortSemLinkService.selectShortSemLinkById(linkId);
                        if (semLink != null && semLink.getMovieId() != null) {
                            semLinkMap.put(linkId, semLink);

                            // 将电影ID与链接ID的关联关系存入映射
                            Long movieId = semLink.getMovieId();
                            movieLinkMap.computeIfAbsent(movieId, k -> new ArrayList<>()).add(linkId);

                            log.info("处理链接成功: id={}, name={}, movieId={}", linkId, semLink.getName(), movieId);
                        } else {
                            log.info("链接 {} 不存在或无关联电影ID", linkId);
                        }
                    } catch (Exception e) {
                        log.error("处理链接 {} 时出错: {}", linkId, e.getMessage());
                    }
                }
                log.info("共关联了 {} 个电影", movieLinkMap.size());
            } else {
                log.info("用户无关联链接记录");
            }

            // 查询所有相关电影的信息
            List<Long> movieIds = new ArrayList<>(movieLinkMap.keySet());
            List<ShortMovie> movieList = new ArrayList<>();

            if (!movieIds.isEmpty()) {
                try {
                    log.info("开始查询 {} 个电影信息", movieIds.size());
                    // 优化：按照应用ID查询所有电影，然后过滤
                    ShortMovie queryMovie = new ShortMovie();
                    queryMovie.setAppId(Long.valueOf(appId));
                    List<ShortMovie> allMovies = shortMovieService.selectShortMovieList(queryMovie);
                    log.info("应用 {} 下共有 {} 个电影", appId, allMovies.size());

                    // 过滤出需要的电影ID并且状态为"1"的电影
                    for (ShortMovie movie : allMovies) {
                        if (movieIds.contains(movie.getId()) && "1".equals(movie.getState())) {
                            movieList.add(movie);
                            movieMap.put(movie.getId(), movie);
                        }
                    }

                    log.info("找到符合条件的电影数量: {}", movieList.size());
                } catch (Exception e) {
                    log.error("查询电影信息时出错: {}", e.getMessage());
                }
            }

            // 构建返回结果，每部电影只返回一次，包含所有关联链接
            List<Map<String, Object>> resultList = new ArrayList<>();

            // 每个电影只处理一次
            for (Long movieId : movieMap.keySet()) {
                ShortMovie movie = movieMap.get(movieId);
                if (movie == null) {
                    continue;
                }

                // 获取此电影关联的所有链接
                List<Long> relatedLinkIds = movieLinkMap.get(movieId);
                if (relatedLinkIds == null || relatedLinkIds.isEmpty()) {
                    continue;
                }

                try {
                    // 构建链接信息列表
                    List<Map<String, Object>> linkInfoList = new ArrayList<>();
                    for (Long linkId : relatedLinkIds) {
                        ShortSemLink semLink = semLinkMap.get(linkId);
                        if (semLink != null) {
                            Map<String, Object> linkInfo = new HashMap<>();
                            linkInfo.put("link_id", linkId);
                            linkInfo.put("link_name", semLink.getName());
                            linkInfoList.add(linkInfo);

                            log.info("为电影ID={} 添加链接信息: linkId={}, linkName={}",
                                    movie.getId(), linkId, semLink.getName());
                        }
                    }

                    // 返回电影属性及其所有关联链接
                    Map<String, Object> movieData = new HashMap<>();
                    movieData.put("id", movie.getId());
                    movieData.put("name", movie.getName());
                    movieData.put("oldname", movie.getOldname());
                    movieData.put("description", movie.getDescription());
                    movieData.put("icon", movie.getIcon());
                    movieData.put("director", movie.getDirector());
                    movieData.put("actors", movie.getActors());
                    movieData.put("up_time", movie.getUpTime());
                    movieData.put("time", movie.getTime());
                    movieData.put("num", movie.getNum());
                    movieData.put("rating", movie.getRating());
                    movieData.put("content", movie.getContent());
                    movieData.put("vip_num", movie.getVipNum());
                    movieData.put("is_vip", movie.getIsVip());
                    movieData.put("source", movie.getSource());
                    movieData.put("rec", movie.getRec());
                    movieData.put("state", movie.getState());
                    movieData.put("addtime", movie.getAddtime());
                    movieData.put("updatetime", movie.getUpdatetime());
                    movieData.put("cate_id", movie.getCateId());
                    movieData.put("lang_id", movie.getLangId());
                    movieData.put("app_id", movie.getAppId());
                    movieData.put("language_name", movie.getLanguageName());
                    movieData.put("unit_coin", movie.getUnitCoin());
                    movieData.put("sum_video_count", movie.getSumVideoCount());
                    movieData.put("movie_id", movie.getId());

                    // 使用第一个链接作为主链接
                    if (!linkInfoList.isEmpty()) {
                        Map<String, Object> firstLink = linkInfoList.get(0);
                        movieData.put("link_id", firstLink.get("link_id"));
                        movieData.put("link_name", firstLink.get("link_name"));
                    }

                    // 添加所有关联链接列表
                    movieData.put("links", linkInfoList);

                    resultList.add(movieData);
                    log.info("添加电影数据: ID={}, 名称={}, 关联链接数={}",
                            movie.getId(), movie.getName(), linkInfoList.size());
                } catch (Exception e) {
                    log.error("处理电影ID {} 时出错: {}", movieId, e.getMessage());
                }
            }

            // 记录用户查询事件
            ShortUserEventRecord eventRecord = new ShortUserEventRecord();
            eventRecord.setUserId(user.getId());
            eventRecord.setEventType("query_link_movies");
            eventRecord.setNote("查询深链关联电影，结果数量：" + resultList.size() + "，应用ID: " + appId);
            eventRecord.setState("1");
            shortUserEventRecordService.insertShortUserEventRecord(eventRecord);

            resultMap.put("code", 200);
            resultMap.put("msg", "success");

            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("movie_list", resultList);
            dataMap.put("total", resultList.size());

            resultMap.put("data", dataMap);

            long duration = System.currentTimeMillis() - startTime;
            log.info("查询用户深链关联电影完成: appId={}, hashId={}, 结果数量={}, 耗时={}ms",
                    appId, hashId, resultList.size(), duration);

        } catch (Exception e) {
            log.error("查询用户深链关联电影失败：", e);

            // 上报异常
            ShortRunlog runlog = new ShortRunlog();
            runlog.setType("查询深链电影错误");
            runlog.setState("0");
            runlog.setContent("查询深链电影错误：" + e.getMessage());
            runlog.setNote(e.getMessage());
            shortRunlogService.insertShortRunlog(runlog);

            resultMap.put("code", 500);
            resultMap.put("msg", "查询失败：" + e.getMessage());
            resultMap.put("data", new HashMap<>());
        }

        return resultMap;
    }

    @Override
    public Map<String, Object> getMovieIdByLinkId(String appId, Long linkId, boolean emailFlag, Long userId) {
        Map<String, Object> resultMap = new HashMap<>();
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("movie_id", null);
        dataMap.put("qd", null);
        dataMap.put("pid", null);
        dataMap.put("kid", null);
        dataMap.put("pixel_id", null);
        dataMap.put("link_app_id", null);

        try {
            log.info("根据推广链接ID获取参数: appId={}, linkId={}", appId, linkId);

            if (linkId == null) {
                resultMap.put("code", 400);
                resultMap.put("msg", "linkId 不能为空");
                resultMap.put("data", dataMap);
                return resultMap;
            }

            // 使用appId和linkId查询
            ShortSemLink semLink = shortSemLinkService.selectShortSemLinkByIdAndAppId(linkId, appId);
            Long pid = 0L;
            if (semLink != null) {
                log.info("找到推广链接 {} ", linkId);
                dataMap.put("movie_id", semLink.getMovieId());
                if (emailFlag) {
                    if (Objects.nonNull(userId)) {
                        String sub = StrUtil.sub(userId.toString(), 4, userId.toString().length());
                        log.info("用户ID: {}", sub);
                        ShortUser shortUser = shortUserService.selectShortUserById(Long.parseLong(sub));
                        if (Objects.isNull(shortUser)) {
                            log.error("用户不存在 userId={}", userId);
                            throw new RuntimeException("用户不存在");
                        }
                        if (Objects.nonNull(shortUser.getPayByEmailFlag()) && !shortUser.getPayByEmailFlag()) {
                            pid = payTempId;
                        } else {
                            pid = semLink.getPayTemplateId();
                        }
                    }

                } else {
                    pid = semLink.getPayTemplateId();
                }
                dataMap.put("pid", pid);
                dataMap.put("kid", semLink.getVideoCoinPlanId());
                dataMap.put("link_app_id", semLink.getLinkAppId());

                // 解析 pdata 获取 qd
                if (StringUtils.isNotEmpty(semLink.getPdata())) {
                    String[] pdataParams = semLink.getPdata().replaceFirst("^\\?", "").split("&");
                    for (String param : pdataParams) {
                        String[] keyValue = param.split("=", 2);
                        if (keyValue.length == 2 && "qd".equals(keyValue[0])) {
                            dataMap.put("qd", keyValue[1]);
                            log.info("从pdata中解析到 qd: {}", keyValue[1]);
                            break;
                        }
                    }
                }

                // 获取 pixelId (来自 ShortFacebookPixel)
                if (semLink.getPixelId() != null) {
                    ShortFacebookPixel shortFacebookPixel = shortFacebookPixelService.selectShortFacebookPixelById(semLink.getPixelId());
                    if (shortFacebookPixel != null) {
                        dataMap.put("pixel_id", shortFacebookPixel.getFacebookPixelId());
                        log.info("从ShortFacebookPixel {} 中获取到 Pixel ID: {}", shortFacebookPixel.getId(), shortFacebookPixel.getFacebookPixelId());
                    } else {
                        log.warn("未找到pixelId={}对应的像素配置", semLink.getPixelId());
                    }
                } else {
                    log.warn("推广链接 linkid={} 没有关联PixelId信息", linkId);
                }

                resultMap.put("code", 200);
                resultMap.put("msg", "success");
            } else {
                log.warn("未找到 linkId={} 对应的推广链接信息或与应用appId={}不匹配", linkId, appId);
                resultMap.put("code", 200); // 仍然返回200，表示请求成功但无有效数据
                resultMap.put("msg", "未找到推广链接信息");
            }

            resultMap.put("data", dataMap);

        } catch (Exception e) {
            log.error("根据linkId={} 获取参数失败: {}", linkId, e.getMessage(), e);
            resultMap.put("code", 500);
            resultMap.put("msg", "查询失败: " + e.getMessage());
            resultMap.put("data", dataMap);
        }

        return resultMap;
    }

    @Override
    public ShortUser filterUser(InitUserDTO initUserDTO) throws JsonProcessingException {
        // 根据linkid获取并填充DTO --------
        String linkIdStr = initUserDTO.getLinkid();
        setLinkIdStr(linkIdStr, initUserDTO);

        ShortUser queryUser = new ShortUser();
        queryUser.setEmail(initUserDTO.getReceiveEmail());
        queryUser.setAppId(initUserDTO.getAppId());
        //处理用户邮件发送记录
        saveOrUpdateEmailLog(initUserDTO);
        List<ShortUser> existingUsers = shortUserService.selectShortUserList(queryUser);
        ShortUser existingUser = existingUsers.isEmpty() ? null : existingUsers.get(0);
        if (null != existingUser) {
            initUserDTO.setSendType(0);
            String resUniqueId = "";
            ShortUser resShortUser = new ShortUser();

            if (initUserDTO.getIfApp() > 0) {
                resUniqueId = existingUser.getUniqueId();
                resShortUser = existingUser;
            } else {
                ShortUser newShortUser = shortUserService.selectShortUserById(Long.valueOf(initUserDTO.getUserId()));
                if (null != newShortUser && null != newShortUser.getExpireTime()) {
//                existingUser.setExpireTime(newShortUser.getExpireTime());
//                shortUserService.updateShortUser(existingUser);


                    if (StrUtil.isNotEmpty(initUserDTO.getLinkid())) {
                        if (Objects.isNull(newShortUser.getEmailBindDate())) {
                            newShortUser.setEmailBindDate(LocalDate.now());
                        }
                        if (Objects.isNull(newShortUser.getEmailBindLink())) {
                            newShortUser.setEmailBindLink(Long.parseLong(initUserDTO.getLinkid()));
                        }
                    }
                    newShortUser.setAccountType("h5邮箱登录");
                    newShortUser.setEmail(existingUser.getEmail());
                    shortUserService.updateShortUser(newShortUser);

                    //写关联关系
                    ShortUserEamil shortUserEamil = new ShortUserEamil();
                    shortUserEamil.setUserId(newShortUser.getId());
                    shortUserEamil.setEmail(existingUser.getEmail());
//                shortUserEamil.setEmailBindLink();
                    if (StrUtil.isNotEmpty(initUserDTO.getLinkid())) {
                        shortUserEamil.setEmailBindDate(LocalDate.now());
                        shortUserEamil.setEmailBindLink(Long.parseLong(initUserDTO.getLinkid()));
                    }
                    shortUserEamilService.insertShortUserEamil(shortUserEamil);

                    //写关联关系
                    ShortUserEamil shortUserEamil1 = new ShortUserEamil();
                    shortUserEamil1.setUserId(existingUser.getId());
                    shortUserEamil1.setEmail(existingUser.getEmail());
//                shortUserEamil.setEmailBindLink();
                    if (StrUtil.isNotEmpty(initUserDTO.getLinkid())) {
                        shortUserEamil1.setEmailBindDate(LocalDate.now());
                        shortUserEamil1.setEmailBindLink(Long.parseLong(initUserDTO.getLinkid()));
                    }
                    shortUserEamilService.insertShortUserEamil(shortUserEamil1);
                }
                resUniqueId = newShortUser.getUniqueId();
                resShortUser = newShortUser;
            }


            linkRecord(existingUser, initUserDTO);

            existingUser.setEmail(initUserDTO.getReceiveEmail());
            if (StringUtils.isNotEmpty(existingUser.getEmail())) {
                existingUser.setPayByEmailFlag(Boolean.FALSE);
                //不为空说明从深链进入
//                if (StrUtil.isNotEmpty(initUserDTO.getLinkid())) {
//                    if (Objects.isNull(existingUser.getEmailBindDate())) {
//                        existingUser.setEmailBindDate(LocalDate.now());
//                    }
//                    if (Objects.isNull(existingUser.getEmailBindLink())) {
//                        existingUser.setEmailBindLink(Long.parseLong(initUserDTO.getLinkid()));
//                    }
//                }
            }
            if (StringUtils.isNotEmpty(initUserDTO.getLinkid()) && !"null".equals(initUserDTO.getLinkid()) && !"none".equals(initUserDTO.getLinkid())) {
                ShortSemLink shortSemLink = shortSemLinkService.selectShortSemLinkById(Long.valueOf(initUserDTO.getLinkid()));
                if (null != shortSemLink) {
                    initUserDTO.setSource(shortSemLink.getPlatform());

                }
            }
            initUserDTO.setPayByEmailFlag(Boolean.FALSE);
            setUpdateUser(existingUser, initUserDTO);

//            shortUserService.updateState(initUserDTO.getUserId(),0);
            ShortEmailSendLog shortEmailSendLog = shortEmailSendLogMapper.getByEmail(existingUser.getEmail());
            if (null != shortEmailSendLog) {
                shortEmailSendLog.setUserId(existingUser.getId());
                shortEmailSendLogMapper.updateShortEmailSendLog(shortEmailSendLog);
            }
            initUserDTO.setUniqueId(resUniqueId);
            return resShortUser;
        } else {
            ShortUser shortUser = shortUserService.selectShortUserById(Long.valueOf(initUserDTO.getUserId()));

            linkRecord(shortUser, initUserDTO);

            shortUser.setEmail(initUserDTO.getReceiveEmail());
            shortUser.setAccountType("h5邮箱登录");
            shortUser.setPayByEmailFlag(Boolean.FALSE);

            if (StringUtils.isNotEmpty(initUserDTO.getLinkid()) && !"null".equals(initUserDTO.getLinkid()) && !"none".equals(initUserDTO.getLinkid())) {
                ShortSemLink shortSemLink = shortSemLinkService.selectShortSemLinkById(Long.valueOf(initUserDTO.getLinkid()));
                if (null != shortSemLink) {
                    shortUser.setSource(shortSemLink.getPlatform());
                }
            }
            if (StringUtils.isNotEmpty(shortUser.getEmail())) {
                shortUser.setPayByEmailFlag(Boolean.FALSE);
                if (StrUtil.isNotEmpty(initUserDTO.getLinkid())) {
                    if (Objects.isNull(shortUser.getEmailBindDate())) {
                        shortUser.setEmailBindDate(LocalDate.now());
                    }
                    if (Objects.isNull(shortUser.getEmailBindLink())) {
                        shortUser.setEmailBindLink(Long.parseLong(initUserDTO.getLinkid()));
                    }
                }
            }

            setUpdateUser(shortUser, initUserDTO);

            initUserDTO.setUniqueId(shortUser.getUniqueId());


            if (shortUser != null) {
                createCustomers(shortUser);
            }

            return shortUser;
        }


    }

    private void saveOrUpdateEmailLog(InitUserDTO initUserDTO) {
        ShortEmailSendLog shortEmailSendLog = new ShortEmailSendLog();
        shortEmailSendLog.setEmail(initUserDTO.getReceiveEmail());
        List<ShortEmailSendLog> shortEmailSendLogs = shortEmailSendLogMapper.selectShortEmailSendLogList(shortEmailSendLog);
        if (CollUtil.isEmpty(shortEmailSendLogs)) {
            shortEmailSendLog.setEmail(initUserDTO.getReceiveEmail());
            shortEmailSendLog.setUserId(Long.valueOf(initUserDTO.getUserId()));
            shortEmailSendLog.setNum(0L);
            Date date = new Date();
            shortEmailSendLog.setCreateTime(date);
//            shortEmailSendLog.setEndTime(DateUtil.offsetHour(date,1));
            shortEmailSendLog.setEndTime(DateUtil.offsetMinute(date, 30));
            shortEmailSendLog.setStatus("0");
            shortEmailSendLog.setCreateBy(initUserDTO.getUserId());
            shortEmailSendLogMapper.insertShortEmailSendLog(shortEmailSendLog);
        }
    }

    public void setLinkIdStr(String linkIdStr, InitUserDTO initUserDTO) {
        if (StringUtils.isNotEmpty(linkIdStr) && !"null".equals(linkIdStr) && !"none".equals(linkIdStr)) {
            try {
                Long linkId = Long.parseLong(linkIdStr);
                ShortSemLink semLink = shortSemLinkService.selectShortSemLinkById(linkId);
                if (semLink != null) {
                    log.info("根据linkid={} 找到推广链接: {}", linkId, semLink.getName());

                    // 1. 解析 pdata 获取 qd
                    if (StringUtils.isNotEmpty(semLink.getPdata())) {
                        String[] pdataParams = semLink.getPdata().replaceFirst("^\\?", "").split("&");
                        for (String param : pdataParams) {
                            String[] keyValue = param.split("=", 2);
                            if (keyValue.length == 2 && "qd".equals(keyValue[0])) {
                                initUserDTO.setQd(keyValue[1]);
                                log.info("从pdata中解析到 qd: {}", keyValue[1]);
                                break;
                            }
                        }
                    }

                    // 2. 获取 movieId, payTemplateId, videoCoinPlanId
                    if (semLink.getMovieId() != null) {
                        initUserDTO.setMovie(String.valueOf(semLink.getMovieId()));
                    }
                    if (semLink.getPayTemplateId() != null) {
                        initUserDTO.setPid(String.valueOf(semLink.getPayTemplateId()));
                    }
                    if (semLink.getVideoCoinPlanId() != null) {
                        initUserDTO.setKid(String.valueOf(semLink.getVideoCoinPlanId()));
                    }
                    if (semLink.getPlatform() != null) {
                        initUserDTO.setSource(String.valueOf(semLink.getPlatform()));
                    }

                    // 3. 获取 deepLink 和 pixelId
                    if (semLink.getAppId() != null) {
                        ShortApp linkedApp = shortAppService.selectShortAppById(semLink.getAppId());
                        ShortFacebookPixel shortFacebookPixel = null;

                        // 通过主键ID查询像素配置
                        if (semLink.getPixelId() != null) {
                            shortFacebookPixel = shortFacebookPixelService.selectShortFacebookPixelById(semLink.getPixelId());
                        }
                        if (linkedApp != null) {
                            log.info("找到关联App: id={}, name={}", linkedApp.getId(), linkedApp.getName());
                            if (StringUtils.isNotEmpty(linkedApp.getDeepLink())) {
                                initUserDTO.setDeepLink(linkedApp.getDeepLink());
                            }
                            // 使用App中的Pixel ID覆盖DTO中的值 (与renderTemplate逻辑保持一致)
                            if (shortFacebookPixel != null && StringUtils.isNotEmpty(shortFacebookPixel.getFacebookPixelId())) {
                                initUserDTO.setPixelId(shortFacebookPixel.getFacebookPixelId());
                                log.info("从shortFacebookPixel中获取并设置 Pixel ID: {}", shortFacebookPixel.getFacebookPixelId());
                            }
                        } else {
                            log.warn("未找到linkid={}关联的App信息: appId={}", linkId, semLink.getAppId());
                        }
                    } else {
                        log.warn("推广链接 linkid={} 没有关联AppId", linkId);
                    }
                } else {
                    log.warn("未找到 linkid={} 对应的推广链接信息", linkId);
                }
            } catch (NumberFormatException e) {
                log.error("解析 linkid 失败: linkid='{}'", linkIdStr, e);
            } catch (Exception e) {
                log.error("处理 linkid={} 相关信息时出错", linkIdStr, e);
            }
        }
    }

    public void linkRecord(ShortUser existingUser, InitUserDTO initUserDTO) {
        // 记录用户多链接进入的数据，方便溯源
        if (StringUtils.isNotEmpty(initUserDTO.getLinkid()) && !"null".equals(initUserDTO.getLinkid()) && !"none".equals(initUserDTO.getLinkid()) &&
                (existingUser.getLinkidId() == null || !Long.valueOf(initUserDTO.getLinkid()).equals(existingUser.getLinkidId()))) {
            ShortUserLinkRecord record = new ShortUserLinkRecord();
            record.setUserId(existingUser.getId());
            record.setLinkId(Long.valueOf(initUserDTO.getLinkid()));
            record.setPixelId(existingUser.getPixelId());
            shortUserLinkRecordService.insertShortUserLinkRecord(record);
            log.info("记录用户多链接进入: userId={}, linkId={}", existingUser.getId(), initUserDTO.getLinkid());
        }
    }

    public void setUpdateUser(ShortUser existingUser, InitUserDTO initUserDTO) throws JsonProcessingException {
        // 更新用户的一些信息
        if (StringUtils.isNotEmpty(initUserDTO.getSource()) && !"null".equals(initUserDTO.getSource()) && !"none".equals(initUserDTO.getSource())) {
            existingUser.setSource(initUserDTO.getSource());  // 当有来源的时候 更新来源
        }
        //非苹果设备 从手机model映射表查询具体手机品牌+型号
        try {
            if (!"Apple".equals(initUserDTO.getPhoneVersion())) {
                String deviceName = DeviceUtils.getDeviceName(initUserDTO.getUa());
                if (null != deviceName) {
                    existingUser.setPhoneVersion(deviceName);
                } else {
                    existingUser.setPhoneVersion(initUserDTO.getPhoneVersion());
                }
            } else {
                existingUser.setPhoneVersion(initUserDTO.getPhoneVersion());
            }
        } catch (Exception e) {
            existingUser.setPhoneVersion(initUserDTO.getPhoneVersion());
            log.error("获取用户设备具体型号失败: {}", e.getMessage());
        }
        //设置广告ID
        if (StringUtils.isNotEmpty(initUserDTO.getAdId()) && !"null".equals(initUserDTO.getAdId())) {
            existingUser.setAdId(initUserDTO.getAdId());
        }
        existingUser.setSystemVersion(initUserDTO.getSystemVersion());
        existingUser.setLanguage(initUserDTO.getLanguage());
        existingUser.setAppVersion(initUserDTO.getAppVersion());


//        existingUser.setAccountType("h5邮箱登录"); // 写死为h5邮箱登录
        if (StringUtils.isNotEmpty(initUserDTO.getDeepid()) && !"null".equals(initUserDTO.getDeepid()) && !"none".equals(initUserDTO.getDeepid())) {
            existingUser.setNote(initUserDTO.getDeepid());
        }
        if (StringUtils.isNotEmpty(initUserDTO.getIp()) && !"null".equals(initUserDTO.getIp()) && !"none".equals(initUserDTO.getIp())) {
            existingUser.setIp(initUserDTO.getIp());
        }
        if (StringUtils.isNotEmpty(initUserDTO.getLinkid()) && !"null".equals(initUserDTO.getLinkid()) && !"none".equals(initUserDTO.getLinkid())) {
            Long newLinkId = Long.valueOf(initUserDTO.getLinkid());

            // 更新linkTime
            existingUser.setLinkTime(DateUtils.getNowDate());
            // 更新linkId
            existingUser.setLinkidId(newLinkId);
            
            // 更新pixelId - 从深链获取最新的pixelId
            ShortSemLink shortSemLink = shortSemLinkService.selectShortSemLinkById(newLinkId);
            if (shortSemLink != null && shortSemLink.getPixelId() != null) {
                existingUser.setPixelId(shortSemLink.getPixelId());
            }
        }
        if (StringUtils.isNotEmpty(initUserDTO.getUserType()) && !"null".equals(initUserDTO.getUserType()) && !"none".equals(initUserDTO.getUserType())) {
            existingUser.setUserType(initUserDTO.getUserType());
        }

        // 使用Meta分级策略更新用户other信息
        String initUserOtherJson = buildOtherJsonWithGradedStrategy(initUserDTO);
        if (initUserOtherJson != null) {
            existingUser.setOther(initUserOtherJson);
            log.info("initUser分级策略更新other成功");
        }
        shortUserService.updateShortUser(existingUser);
        log.info("更新用户信息成功: id={}", existingUser.getId());
    }


    public void voFacebook(ShortUser tempUser) {
        // 处理Facebook事件回传逻辑
        if ("Facebook".equals(tempUser.getSource())) {
            try {
                // 对于新用户，发送注册事件
                final Long userId = tempUser.getId();
                final String nowEvent = "CompleteRegistration";

                // 使用线程池实现延迟执行
                scheduler.schedule(() -> {
                    try {
                        // 延迟后重新查询用户信息，获取最新的source值
                        ShortUser currentUser = shortUserService.selectShortUserById(userId);

                        // 判断来源是否为Facebook
                        if (currentUser != null && "Facebook".equals(currentUser.getSource())) {
                            // 记录回传前的状态
                            Integer beforeStatus = currentUser.getPixelStatus();

                            // 执行回传
                            boolean success = sendFacebookEvent(userId, nowEvent, null, false, false); // CompleteRegistration不涉及orderId
                            log.info("用户来源为Facebook，已执行事件: {}, 结果: {}", nowEvent, success ? "成功" : "失败");

                            // 回传成功后，确保更新用户pixelStatus状态
                            if (success) {
                                // 更新pixelStatus为1（已回传成功）- 修正为Integer类型
                                currentUser.setPixelStatus(1); // 使用Integer类型而不是1L
                                int rows = shortUserService.updateShortUser(currentUser);

                                // 验证状态是否成功更新
                                if (rows > 0) {
                                    log.info("用户注册事件回传状态已更新: userId={}, pixelStatus={}",
                                            userId, currentUser.getPixelStatus());

                                    // 验证更新是否生效
                                    ShortUser verifyUser = shortUserService.selectShortUserById(userId);
                                    if (verifyUser != null && verifyUser.getPixelStatus() != null && verifyUser.getPixelStatus() == 1) {
                                        log.info("用户状态验证成功: userId={}, pixelStatus=1", userId);
                                    } else {
                                        log.warn("用户状态验证失败: userId={}, 期望状态=1, 实际状态={}",
                                                userId, verifyUser != null ? verifyUser.getPixelStatus() : "null");

                                        // 再次尝试更新
                                        if (verifyUser != null) {
                                            verifyUser.setPixelStatus(1); // 使用Integer类型
                                            shortUserService.updateShortUser(verifyUser);
                                            log.info("再次尝试更新用户回传状态: userId={}", userId);
                                        }
                                    }
                                } else {
                                    log.error("更新用户回传状态失败: userId={}", userId);
                                }
                            }

                            ShortRunlog pixelLog = new ShortRunlog();
                            pixelLog.setType("PIXEL回传");
                            pixelLog.setState("1");
                            pixelLog.setContent("用户：" + userId + "，注册成功回传");
                            pixelLog.setNote("");
                            shortRunlogService.insertShortRunlog(pixelLog);
                        } else {
                            ShortRunlog pixelLog = new ShortRunlog();
                            pixelLog.setType("PIXEL回传");
                            pixelLog.setState("1");
                            pixelLog.setContent("用户：" + userId + "，非FACEBOOK来源，不执行事件");
                            pixelLog.setNote("");
                            shortRunlogService.insertShortRunlog(pixelLog);
                            log.info("用户来源不是Facebook (是 {})，不执行事件",
                                    currentUser != null ? currentUser.getSource() : "null");
                        }
                    } catch (Exception e) {
                        log.error("重新查询用户信息失败", e);
                    }
                }, 10, TimeUnit.SECONDS); // 延迟10秒执行

                ShortRunlog initLog = new ShortRunlog();
                initLog.setType("PIXEL回传");
                initLog.setState("1");
                initLog.setContent("用户：" + userId + "，注册成功回传任务已创建");
                initLog.setNote("10秒后检查用户来源并执行回传");
                shortRunlogService.insertShortRunlog(initLog);
                log.info("异步发送应用内事件: {} (10秒后检查最新条件再执行)", nowEvent);

            } catch (Exception e) {
                ShortRunlog runlog = new ShortRunlog();
                runlog.setType("PIXEL回传");
                runlog.setState("0");
                runlog.setContent("用户：" + tempUser.getId() + "，注册成功回传失败");
                runlog.setNote(e.getMessage());
                shortRunlogService.insertShortRunlog(runlog);
                log.error("注册成功回传失败", e);
            }
        } else {
            log.info("用户来源不是Facebook，不创建事件回传任务");
        }
    }

    /**
     * 更新订单的Facebook像素回传状态
     *
     * @param order    订单对象
     * @param status   状态码(0: 待回传/处理中,1: 自动回传成功,2: 手动回传成功,3: 回传失败,4: 跳过回传(非首单/邮件营销订单))
     * @param errorMsg 错误信息(回传失败时使用)
     */
    private void updateOrderPixelStatus(ShortOrder order, int status, String errorMsg) {
        if (order == null) {
            return;
        }

        try {
            // 记录状态变更详细日志
            log.info("订单像素状态更新开始: orderId={}, 旧状态={}, 新状态={}",
                    order.getId(),
                    order.getPixelStatus() != null ? order.getPixelStatus() : "null",
                    status);

            // 更新像素状态
            order.setPixelStatus(status);

            // 保存订单更新
            int rows = shortOrderService.updateShortOrder(order);

            if (rows > 0) {
                log.info("订单像素回传状态已更新成功: orderId={}, pixelStatus={}", order.getId(), status);

                // 再次查询验证状态是否正确更新
                ShortOrder updatedOrder = shortOrderService.selectShortOrderById(order.getId());
                if (updatedOrder != null) {
                    if (Objects.equals(updatedOrder.getPixelStatus(), status)) {
                        log.info("订单状态验证成功: orderId={}, pixelStatus={}", order.getId(), updatedOrder.getPixelStatus());
                    } else {
                        log.warn("订单状态验证失败: orderId={}, 期望状态={}, 实际状态={}",
                                order.getId(), status, updatedOrder.getPixelStatus());
                    }
                }
            } else {
                log.error("订单像素回传状态更新失败: orderId={}, 影响行数=0", order.getId());
            }

        } catch (Exception e) {
            log.error("更新订单像素回传状态异常: orderId={}, error={}",
                    order.getId(), e.getMessage(), e);
        }
    }

    /**
     * 分析Facebook回传错误类型
     *
     * @param e 异常对象
     * @return 错误类型描述
     */
    private String analyzeErrorType(Exception e) {
        String message = e.getMessage();

        if (message == null) {
            return "未知错误";
        }

        if (message.contains("access_token") || message.contains("token")) {
            return "令牌错误";
        } else if (message.contains("permission") || message.contains("auth")) {
            return "权限错误";
        } else if (message.contains("rate") || message.contains("limit")) {
            return "频率限制";
        } else if (message.contains("timeout") || message.contains("timed out")) {
            return "请求超时";
        } else if (message.contains("validation") || message.contains("invalid")) {
            return "数据验证失败";
        } else if (message.contains("duplicate")) {
            return "重复事件";
        } else if (message.contains("network") || message.contains("connect")) {
            return "网络连接错误";
        } else {
            return "系统错误: " + message;
        }
    }

    /**
     * 从响应中提取Facebook错误信息
     *
     * @param response API响应
     * @return 格式化的错误信息
     */
    private String extractErrorMessage(ResponseEntity<Map> response) {
        StringBuilder errorMsg = new StringBuilder();
        errorMsg.append("HTTP状态码: ").append(response.getStatusCode());

        try {
            if (response.getBody() != null) {
                Map<String, Object> body = response.getBody();

                // 处理标准Facebook API错误格式
                if (body.containsKey("error")) {
                    Object errorObj = body.get("error");
                    if (errorObj instanceof Map) {
                        Map<String, Object> error = (Map<String, Object>) errorObj;
                        String errorType = (String) error.getOrDefault("type", "");
                        String errorMessage = (String) error.getOrDefault("message", "");
                        Object errorCode = error.getOrDefault("code", "");

                        errorMsg.append(", 错误类型: ").append(errorType);
                        errorMsg.append(", 错误消息: ").append(errorMessage);
                        errorMsg.append(", 错误代码: ").append(errorCode);

                        // 分析Facebook广告API特定错误码
                        if (errorCode instanceof Number) {
                            int code = ((Number) errorCode).intValue();
                            String specificError = analyzeFacebookErrorCode(code);
                            if (StringUtils.isNotEmpty(specificError)) {
                                errorMsg.append(", 特定错误: ").append(specificError);
                            }
                        }
                    } else {
                        errorMsg.append(", 错误: ").append(errorObj);
                    }
                } else {
                    errorMsg.append(", 响应体: ").append(objectMapper.writeValueAsString(body));
                }
            } else {
                errorMsg.append(", 无响应体");
            }
        } catch (Exception e) {
            errorMsg.append(", 解析错误: ").append(e.getMessage());
        }

        return errorMsg.toString();
    }

    /**
     * 分析Facebook错误码
     *
     * @param code 错误码
     * @return 错误描述
     */
    private String analyzeFacebookErrorCode(int code) {
        switch (code) {
            case 100:
                return "无效参数";
            case 190:
                return "访问令牌无效或已过期";
            case 200:
                return "权限错误";
            case 3285007:
                return "iOS 14策略相关错误";
            default:
                return "";
        }
    }

    /**
     * 检查用户广告信息完整性
     *
     * @param user     用户对象
     * @param otherMap 用户其他信息
     * @return 广告信息状态
     */
    private Map<String, Boolean> checkAdInfoCompleteness(ShortUser user, Map<String, Object> otherMap) {
        Map<String, Boolean> adInfoStatus = new HashMap<>();

        // 提取广告参数
        String fbp = MapUtils.getString(otherMap, "fbp", "");
        String fbc = MapUtils.getString(otherMap, "fbc", "");
        String adId = MapUtils.getString(otherMap, "adId", "");
        String userAgent = MapUtils.getString(otherMap, "ua", "");

        // 检查各项参数是否存在
        adInfoStatus.put("fbp_present", StringUtils.isNotEmpty(fbp) && !"null".equals(fbp));
        adInfoStatus.put("fbc_present", StringUtils.isNotEmpty(fbc) && !"null".equals(fbc));
        adInfoStatus.put("adId_present", StringUtils.isNotEmpty(adId) && !"null".equals(adId));
        adInfoStatus.put("ip_present", StringUtils.isNotEmpty(user.getIp()));
        adInfoStatus.put("ua_present", StringUtils.isNotEmpty(userAgent) && !"null".equals(userAgent));

        // 检查总体完整性
        boolean isComplete = !adInfoStatus.containsValue(false);
        adInfoStatus.put("is_complete", isComplete);

        return adInfoStatus;
    }

    /**
     * 清理FBC数据，移除错误格式化的FBC
     * 检测并修复形如 "fb.1.timestamp.fb.1.timestamp.original_id" 的嵌套格式
     *
     * @param fbc 原始FBC数据
     * @return 清理后的FBC数据
     */
    private String cleanFbcData(String fbc) {
        if (StringUtils.isEmpty(fbc) || "null".equals(fbc) || "none".equals(fbc)) {
            return "";
        }

        // 检测是否已经被错误格式化（包含多个fb.1.前缀）
        if (fbc.startsWith("fb.1.") && fbc.indexOf("fb.1.", 5) > 0) {
            // 提取最后一个有效的原始ID部分
            String[] parts = fbc.split("\\.");
            if (parts.length >= 4) {
                // 假设最后一部分是真正的原始ID
                return parts[parts.length - 1];
            }
        }

        // 移除单次错误格式化的前缀
        if (fbc.startsWith("fb.1.") && fbc.split("\\.").length == 4) {
            return fbc.substring(fbc.lastIndexOf('.') + 1);
        }

        return fbc;
    }

    /**
     * Meta分级策略：构建用户other信息
     * 基于Meta 2024年优先级：FBC (高优先级) > FBP (中优先级)
     *
     * @param fbc          Facebook Click ID
     * @param fbp          Facebook Browser ID
     * @param ua           用户代理
     * @param campaignName 广告系列名称
     * @param campaignId   广告系列ID
     * @param adsetName    广告组名称
     * @param adsetId      广告组ID
     * @param adName       广告名称
     * @param adId         广告ID
     * @return 如果有有效数据返回JSON字符串，否则返回null
     */
    private String buildOtherJsonWithGradedStrategy(String fbc, String fbp, String ua,
                                                    String campaignName, String campaignId,
                                                    String adsetName, String adsetId,
                                                    String adName, String adId) {
        // 清理FBC数据，确保存储的是原始格式
        String cleanedFbc = cleanFbcData(fbc);

        boolean hasFbc = StringUtils.isNotEmpty(cleanedFbc) && !"null".equals(cleanedFbc) && !"none".equals(cleanedFbc);
        boolean hasFbp = StringUtils.isNotEmpty(fbp) && !"null".equals(fbp) && !"none".equals(fbp);

        // 有FBC或FBP任一有效数据时就构建other信息
        if (hasFbc || hasFbp) {
            Map<String, String> otherMap = new HashMap<>();
            otherMap.put("ua", ua);
            otherMap.put("fbc", hasFbc ? cleanedFbc : "");
            otherMap.put("fbp", hasFbp ? fbp : "");
            otherMap.put("campaignName", campaignName);
            otherMap.put("campaignId", campaignId);
            otherMap.put("adsetName", adsetName);
            otherMap.put("adsetId", adsetId);
            otherMap.put("adName", adName);
            otherMap.put("adId", adId);

            log.info("分级策略构建other: FBC={}, FBP={}", hasFbc ? "✓" : "✗", hasFbp ? "✓" : "✗");
            try {
                return objectMapper.writeValueAsString(otherMap);
            } catch (Exception e) {
                log.error("序列化other数据失败", e);
                return null;
            }
        }

        return null;
    }

    /**
     * Meta分级策略：构建用户other信息 (重载版本支持InitUserDTO)
     * 委托给基础方法处理，避免重复逻辑
     *
     * @param initUserDTO 初始化用户DTO对象
     * @return 如果有有效数据返回JSON字符串，否则返回null
     */
    private String buildOtherJsonWithGradedStrategy(InitUserDTO initUserDTO) {
        return buildOtherJsonWithGradedStrategy(
                initUserDTO.getFbc(),
                initUserDTO.getFbp(),
                initUserDTO.getUa(),
                initUserDTO.getCampaignName(),
                initUserDTO.getCampaignId(),
                initUserDTO.getAdsetName(),
                initUserDTO.getAdsetId(),
                initUserDTO.getAdName(),
                initUserDTO.getAdId()
        );
    }

    private static String getAccessToken() {
        // Create request parameters
        Map<String, String> params = new HashMap<>();
        params.put("grant_type", "client_credentials");
        params.put("client_id", "4645994138542001"); // your client_id
        params.put("client_secret", "vsansagfz927zwcm16gvqeu8ad1byqs6"); // your client_secret

        // Convert params to form-urlencoded format
        StringBuilder formBody = new StringBuilder();
        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (formBody.length() > 0) {
                formBody.append("&");
            }
            formBody.append(entry.getKey())
                    .append("=")
                    .append(entry.getValue());
        }

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost("https://open.geeksend.com/oauth/access_token");

            // Set content type
            httpPost.setHeader("Content-Type", "application/x-www-form-urlencoded");

            // Set request body
            httpPost.setEntity(new StringEntity(formBody.toString()));

            // Execute request
            HttpResponse response = httpClient.execute(httpPost);

            // Parse response
            String responseBody = EntityUtils.toString(response.getEntity());

            // Extract access token using Jackson
            ObjectMapper objectMapper = new ObjectMapper();
            Map<?, ?> responseMap = objectMapper.readValue(responseBody, Map.class);
            Map<?, ?> data = (Map<?, ?>) responseMap.get("data");

            return (String) data.get("access_token");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void triggerFacebookPixel(ShortUser user) {
        // 直接调用现有的voFacebook方法
        voFacebook(user);
    }

    @Override
    public AjaxResult sendNoticeEmail(String publicKey) {
        int count = shortIntermedStatusService.countByName("sendNoticeEmail");
        if (count > 0)
            return AjaxResult.success("自动统计首页数据，请勿重复操作");

        ShortIntermedStatus shortIntermedStatus = new ShortIntermedStatus();
        shortIntermedStatus.setName("sendNoticeEmail");
        shortIntermedStatusService.insertShortIntermedStatus(shortIntermedStatus);


        try {
            String privateKey = "-----BEGIN OPENSSH PRIVATE KEY-----\n" +
                    "b3BlbnNzaC1rZXktdjEAAAAABG5vbmUAAAAEbm9uZQAAAAAAAAABAAAAMwAAAAtzc2gtZW\n" +
                    "QyNTUxOQAAACDdx+eInOus2G8pGSS+paM8QV5SkQdAXGh/e0Z7pRaDRwAAAJiIp66uiKeu\n" +
                    "rgAAAAtzc2gtZWQyNTUxOQAAACDdx+eInOus2G8pGSS+paM8QV5SkQdAXGh/e0Z7pRaDRw\n" +
                    "AAAEDJW6rFB140gGyRJZM+mqoIFj8HAGrGpnuWj+sCJa/06N3H54ic66zYbykZJL6lozxB\n" +
                    "********************************************************\n" +
                    "-----END OPENSSH PRIVATE KEY-----";

            // 2. 加载密钥对
            KeyPair keyPair = KeyPair.load(new JSch(), privateKey.getBytes(), null);
            byte[] derivedPublicKeyBlob = keyPair.getPublicKeyBlob();

            // 3. 解析用户公钥（按空格分割并解码Base64）
            String[] publicKeyParts = publicKey.split(" ");
            if (publicKeyParts.length < 2) {
                throw new IllegalArgumentException("Invalid public key format");
            }
            byte[] userPublicKeyBlob = Base64.getDecoder().decode(publicKeyParts[1]);

            // 4. 安全比对（避免时序攻击）
            boolean isMatch = MessageDigest.isEqual(derivedPublicKeyBlob, userPublicKeyBlob);
            if (isMatch) {
                log.info("验证密钥正确------定时发邮件");
//================================

                List<ShortUser> shortUserList = shortUserService.getByLaset();
                for (ShortUser su : shortUserList) {
                    SendSmtpEmail email = new SendSmtpEmail();
                    TransactionalEmailsApi apiInstance = new TransactionalEmailsApi();

                    // 3. 设置发件人
                    SendSmtpEmailSender sender = new SendSmtpEmailSender();
                    ShortEmailDomain shortEmailDomain = shortEmailDomainService.getrandomDomain(su.getAppId());

                    if (null != shortEmailDomain) {
                        // 2. 初始化邮件API
                        ApiClient defaultClient = Configuration.getDefaultApiClient();
                        ApiKeyAuth apiKey = (ApiKeyAuth) defaultClient.getAuthentication("api-key");
                        apiKey.setApiKey(brevoApiKey); // 替换为你的Brevo API密钥

                        if (null != shortEmailDomain) {
                            sender.setEmail(shortEmailDomain.getDomain()); // 必须已验证的邮箱
                            sender.setName(shortEmailDomain.getAppName());
                        } else {
                            sender.setEmail(senderEmail); // 必须已验证的邮箱
                            sender.setName(senderName);
                        }

                        List<SendSmtpEmailTo> toList = new ArrayList<>();
                        // 4. 设置收件人
                        SendSmtpEmailTo recipient = new SendSmtpEmailTo();
                        recipient.setEmail(null != su.getEmail() ? su.getEmail() : null);
                        recipient.setName("Valued Member");
                        toList.add(recipient);
                        email.setSender(sender);
                        email.setTo(toList);
                        // 记录日志并处理结果
                        ShortRunlog runlog = new ShortRunlog();
                        runlog.setType("发送订阅通知邮件");
                        runlog.setCreateTime(DateUtils.getNowDate());
                        runlog.setUpdateTime(DateUtils.getNowDate());


                        try {
                            // 5. 从HTML文件读取内容
                            ShortSemLink shortSemLink = shortSemLinkService.selectShortSemLinkById(su.getLinkidId());

                            String htmlName = "";

                            //先固定英语
                            if (null != shortSemLink)
                                shortSemLink.setLanguage("en-US");
                            if (StringUtils.isNotEmpty(su.getSource())) {
                                if (null == shortSemLink || shortSemLink.getLanguage().equals("en-US"))
                                    htmlName = "former_noticeEmail.html";
                                else if (shortSemLink.getLanguage().equals("es"))
                                    htmlName = "es-former_noticeEmail.html";
                                else if (shortSemLink.getLanguage().equals("pt"))
                                    htmlName = "pt-former_noticeEmail.html";
                                else if (shortSemLink.getLanguage().equals("zh-TW"))
                                    htmlName = "zh-TW-former_noticeEmail.html";
                                else if (shortSemLink.getLanguage().equals("ja"))
                                    htmlName = "ja-former_noticeEmail.html";
                                else if (shortSemLink.getLanguage().equals("de"))
                                    htmlName = "de-former_noticeEmail.html";
                            } else {
                                if (null == shortSemLink || shortSemLink.getLanguage().equals("en-US"))
                                    htmlName = "noticeEmail.html";
                                else if (shortSemLink.getLanguage().equals("es"))
                                    htmlName = "es-noticeEmail.html";
                                else if (shortSemLink.getLanguage().equals("pt"))
                                    htmlName = "pt-noticeEmail.html";
                                else if (shortSemLink.getLanguage().equals("zh-TW"))
                                    htmlName = "zh-TW-noticeEmail.html";
                                else if (shortSemLink.getLanguage().equals("ja"))
                                    htmlName = "ja-noticeEmail.html";
                                else if (shortSemLink.getLanguage().equals("de"))
                                    htmlName = "de-noticeEmail.html";
                            }


                            ClassPathResource resource = new ClassPathResource("templates/" + htmlName);
                            String htmlContent = StreamUtils.copyToString(
                                    resource.getInputStream(),
                                    StandardCharsets.UTF_8
                            );

                            String[] dramaTitles = null;

                            String vName = "";
                            if (null == shortSemLink || shortSemLink.getLanguage().equals("en-US")) {
                                vName = "Valued Member";
                                dramaTitles = new String[]{
                                        "New Drama Recommendation",
                                        "The TV drama savior",
                                        "High-scoring new drama",
                                        "Hot drama list",
                                        "Guide to watching TV series",
                                        "A must-have for drama fans",
                                        "Good dramas",
                                        "New drama coming",
                                        "Drama Drama Terminator",
                                        "Benefits for TV drama fans"
                                };
                            } else if (shortSemLink.getLanguage().equals("es")) {
                                vName = "Miembro valioso";
                                dramaTitles = new String[]{
                                        "New Drama Recommendation",
                                        "The TV drama savior",
                                        "High-scoring new drama",
                                        "Hot drama list",
                                        "Guide to watching TV series",
                                        "A must-have for drama fans",
                                        "Good dramas",
                                        "New drama coming",
                                        "Drama Drama Terminator",
                                        "Benefits for TV drama fans"
                                };
                            } else if (shortSemLink.getLanguage().equals("pt")) {
                                vName = "Membro Valioso";
                                dramaTitles = new String[]{
                                        "New Drama Recommendation",
                                        "The TV drama savior",
                                        "High-scoring new drama",
                                        "Hot drama list",
                                        "Guide to watching TV series",
                                        "A must-have for drama fans",
                                        "Good dramas",
                                        "New drama coming",
                                        "Drama Drama Terminator",
                                        "Benefits for TV drama fans"
                                };
                            } else if (shortSemLink.getLanguage().equals("zh-TW")) {
                                vName = "尊贵的会员";
                                dramaTitles = new String[]{
                                        "New Drama Recommendation",
                                        "The TV drama savior",
                                        "High-scoring new drama",
                                        "Hot drama list",
                                        "Guide to watching TV series",
                                        "A must-have for drama fans",
                                        "Good dramas",
                                        "New drama coming",
                                        "Drama Drama Terminator",
                                        "Benefits for TV drama fans"
                                };
                            } else if (shortSemLink.getLanguage().equals("ja")) {
                                vName = "大切なメンバー";
                                dramaTitles = new String[]{
                                        "New Drama Recommendation",
                                        "The TV drama savior",
                                        "High-scoring new drama",
                                        "Hot drama list",
                                        "Guide to watching TV series",
                                        "A must-have for drama fans",
                                        "Good dramas",
                                        "New drama coming",
                                        "Drama Drama Terminator",
                                        "Benefits for TV drama fans"
                                };
                            } else if (shortSemLink.getLanguage().equals("de")) {
                                vName = "Geschätztes Mitglied";
                                dramaTitles = new String[]{
                                        "Empfehlung für neue Dramen",
                                        "Der Retter unter den TV-Dramen",
                                        "Bestseller-Dramen",
                                        "Liste der angesagtesten Dramen",
                                        "Leitfaden zum Serienschauen",
                                        "Ein Muss für Drama-Fans",
                                        "Gute Dramen",
                                        "Neue Dramen im Kommen",
                                        "Drama-Drama Terminator",
                                        "Vorteile für Drama-Fans"
                                };
                            }

                            recipient.setName(vName);
                            Random random1 = new Random();
                            int randomIndex1 = random1.nextInt(dramaTitles.length);
//                            String subject = dramaTitles[randomIndex1];
                            String subject = shortEmailDomain.getAppName() + " Renewal Reminder";

                            ShortOrder originalOrder = null;
                            ShortOrder lastSubRe = shortOrderService.findFirstByUserAndPay_typeAndStatusOrderByUpdatedAtDesc(su.getId(), "订阅续费", "SUCCEEDED");
                            if (null != lastSubRe) {
                                originalOrder = lastSubRe;
                            } else {
                                ShortOrder lastSub = shortOrderService.findFirstByUserAndPay_typeAndStatusOrderByUpdatedAtDesc(su.getId(), "订阅", "SUCCEEDED");
                                if (null != lastSub)
                                    originalOrder = lastSub;
                            }

                            List<ShortMovie> shortMovieList = new ArrayList<>();

                            if (null == shortSemLink || shortSemLink.getLanguage().equals("en-US"))
                                shortMovieList = shortMovieService.getByRand4(su.getAppId());
                            else
                                shortMovieList = shortMovieService.getByRand4I18n(su.getAppId(), shortSemLink.getLanguage());


                            // 定义一个数组
                            String[] fruits = null;


                            if (null == shortSemLink || shortSemLink.getLanguage().equals("en-US")) {
                                fruits = new String[]{
                                        "It's packed with drama, romance, revenge and thrills. Veedule brings you daily updates of the hottest miniseries, perfect for a relaxing nap or a late-night binge Renew your membership today to unlock unlimited access, ad-free streaming and never miss an update. Your next hit is just a tap away! \uD83D\uDE0D",
                                        "Binge-Worthy Bliss Awaits! Dive into heart-stopping drama, sizzling romance, and edge-of-your-seat suspense with Veedule’s exclusive miniseries. Whether you're unwinding after work or burning the midnight oil, our daily updates keep you hooked. Upgrade now for ad-free streaming, unlimited access, and early releases. Your next obsession is just one click away—don’t miss out! \uD83D\uDD25",
                                        "Passion, Betrayal, and Nonstop Thrills! Veedule delivers the hottest miniseries to fuel your binge cravings. From steamy love triangles to jaw-dropping plot twists, we’ve got your entertainment covered. Renew your membership today for seamless, ad-free viewing and exclusive content. The next episode is calling—will you answer? \uD83D\uDEA8",
                                        "Your Escape Starts Here! Lose yourself in Veedule’s addictive miniseries, where every episode packs drama, romance, and suspense. Perfect for a lazy afternoon or a late-night marathon. Subscribe now to enjoy unlimited streaming, zero ads, and instant updates. The story never stops—neither should you! \uD83C\uDF1F",
                                        "Unlock Unlimited Drama! Veedule brings you the most talked-about miniseries, blending passion, revenge, and pulse-pounding action. Whether you’re snuggled up or sneaking in an episode at work, we’ve got you covered. Upgrade today for ad-free binges and never miss a moment. Your next binge obsession is waiting! \uD83D\uDC40",
                                        "Romance, Revenge, and Riveting Twists!​​ Veedule’s miniseries are your ticket to nonstop entertainment. With daily updates, there’s always something new to devour. Renew your membership now for uninterrupted streaming, exclusive perks, and early access. The screen is set—press play and disappear into the drama! \uD83E\uDD8B",
                                        "Binge Like Never Before!​​ Veedule’s curated miniseries serve up the perfect mix of love, betrayal, and adrenaline-pumping action. Whether you’re napping or night-owl-ing, we keep you hooked. Subscribe today for ad-free bliss, unlimited access, and fresh drops daily. Your next favorite show is just a tap away! \uD83D\uDC96",
                                        "Get Hooked on Drama!​​ Veedule’s miniseries are packed with everything you crave—sizzling chemistry, shocking betrayals, and cliffhangers galore. Perfect for a quick break or an all-nighter. Upgrade now to skip the ads, unlock premium content, and stay ahead of the buzz. The binge starts here! \uD83D\uDE31",
                                        "Late-Night Binges, Zero Regrets!​​ Veedule’s miniseries serve up the perfect blend of passion, suspense, and addictive storytelling. Whether you’re relaxing or procrastinating, we’ve got your fix. Renew today for seamless streaming, no interruptions, and instant updates. Your next guilty pleasure is ready—dive in! \uD83D\uDC4C",
                                        "Drama So Good, You’ll Forget Reality!​​ Veedule’s miniseries are your escape into worlds of love, revenge, and unmissable twists. Daily updates mean fresh thrills whenever you need them. Subscribe now for ad-free viewing, exclusive access, and nonstop entertainment. The remote’s in your hands—what’s your next move? \uD83D\uDE0E"};
                            } else if (shortSemLink.getLanguage().equals("es")) {
                                fruits = new String[]{
                                        "It's packed with drama, romance, revenge and thrills. Veedule brings you daily updates of the hottest miniseries, perfect for a relaxing nap or a late-night binge Renew your membership today to unlock unlimited access, ad-free streaming and never miss an update. Your next hit is just a tap away! \uD83D\uDE0D",
                                        "Binge-Worthy Bliss Awaits! Dive into heart-stopping drama, sizzling romance, and edge-of-your-seat suspense with Veedule’s exclusive miniseries. Whether you're unwinding after work or burning the midnight oil, our daily updates keep you hooked. Upgrade now for ad-free streaming, unlimited access, and early releases. Your next obsession is just one click away—don’t miss out! \uD83D\uDD25",
                                        "Passion, Betrayal, and Nonstop Thrills! Veedule delivers the hottest miniseries to fuel your binge cravings. From steamy love triangles to jaw-dropping plot twists, we’ve got your entertainment covered. Renew your membership today for seamless, ad-free viewing and exclusive content. The next episode is calling—will you answer? \uD83D\uDEA8",
                                        "Your Escape Starts Here! Lose yourself in Veedule’s addictive miniseries, where every episode packs drama, romance, and suspense. Perfect for a lazy afternoon or a late-night marathon. Subscribe now to enjoy unlimited streaming, zero ads, and instant updates. The story never stops—neither should you! \uD83C\uDF1F",
                                        "Unlock Unlimited Drama! Veedule brings you the most talked-about miniseries, blending passion, revenge, and pulse-pounding action. Whether you’re snuggled up or sneaking in an episode at work, we’ve got you covered. Upgrade today for ad-free binges and never miss a moment. Your next binge obsession is waiting! \uD83D\uDC40",
                                        "Romance, Revenge, and Riveting Twists!​​ Veedule’s miniseries are your ticket to nonstop entertainment. With daily updates, there’s always something new to devour. Renew your membership now for uninterrupted streaming, exclusive perks, and early access. The screen is set—press play and disappear into the drama! \uD83E\uDD8B",
                                        "Binge Like Never Before!​​ Veedule’s curated miniseries serve up the perfect mix of love, betrayal, and adrenaline-pumping action. Whether you’re napping or night-owl-ing, we keep you hooked. Subscribe today for ad-free bliss, unlimited access, and fresh drops daily. Your next favorite show is just a tap away! \uD83D\uDC96",
                                        "Get Hooked on Drama!​​ Veedule’s miniseries are packed with everything you crave—sizzling chemistry, shocking betrayals, and cliffhangers galore. Perfect for a quick break or an all-nighter. Upgrade now to skip the ads, unlock premium content, and stay ahead of the buzz. The binge starts here! \uD83D\uDE31",
                                        "Late-Night Binges, Zero Regrets!​​ Veedule’s miniseries serve up the perfect blend of passion, suspense, and addictive storytelling. Whether you’re relaxing or procrastinating, we’ve got your fix. Renew today for seamless streaming, no interruptions, and instant updates. Your next guilty pleasure is ready—dive in! \uD83D\uDC4C",
                                        "Drama So Good, You’ll Forget Reality!​​ Veedule’s miniseries are your escape into worlds of love, revenge, and unmissable twists. Daily updates mean fresh thrills whenever you need them. Subscribe now for ad-free viewing, exclusive access, and nonstop entertainment. The remote’s in your hands—what’s your next move? \uD83D\uDE0E"};
                            } else if (shortSemLink.getLanguage().equals("pt")) {
                                fruits = new String[]{
                                        "It's packed with drama, romance, revenge and thrills. Veedule brings you daily updates of the hottest miniseries, perfect for a relaxing nap or a late-night binge Renew your membership today to unlock unlimited access, ad-free streaming and never miss an update. Your next hit is just a tap away! \uD83D\uDE0D",
                                        "Binge-Worthy Bliss Awaits! Dive into heart-stopping drama, sizzling romance, and edge-of-your-seat suspense with Veedule’s exclusive miniseries. Whether you're unwinding after work or burning the midnight oil, our daily updates keep you hooked. Upgrade now for ad-free streaming, unlimited access, and early releases. Your next obsession is just one click away—don’t miss out! \uD83D\uDD25",
                                        "Passion, Betrayal, and Nonstop Thrills! Veedule delivers the hottest miniseries to fuel your binge cravings. From steamy love triangles to jaw-dropping plot twists, we’ve got your entertainment covered. Renew your membership today for seamless, ad-free viewing and exclusive content. The next episode is calling—will you answer? \uD83D\uDEA8",
                                        "Your Escape Starts Here! Lose yourself in Veedule’s addictive miniseries, where every episode packs drama, romance, and suspense. Perfect for a lazy afternoon or a late-night marathon. Subscribe now to enjoy unlimited streaming, zero ads, and instant updates. The story never stops—neither should you! \uD83C\uDF1F",
                                        "Unlock Unlimited Drama! Veedule brings you the most talked-about miniseries, blending passion, revenge, and pulse-pounding action. Whether you’re snuggled up or sneaking in an episode at work, we’ve got you covered. Upgrade today for ad-free binges and never miss a moment. Your next binge obsession is waiting! \uD83D\uDC40",
                                        "Romance, Revenge, and Riveting Twists!​​ Veedule’s miniseries are your ticket to nonstop entertainment. With daily updates, there’s always something new to devour. Renew your membership now for uninterrupted streaming, exclusive perks, and early access. The screen is set—press play and disappear into the drama! \uD83E\uDD8B",
                                        "Binge Like Never Before!​​ Veedule’s curated miniseries serve up the perfect mix of love, betrayal, and adrenaline-pumping action. Whether you’re napping or night-owl-ing, we keep you hooked. Subscribe today for ad-free bliss, unlimited access, and fresh drops daily. Your next favorite show is just a tap away! \uD83D\uDC96",
                                        "Get Hooked on Drama!​​ Veedule’s miniseries are packed with everything you crave—sizzling chemistry, shocking betrayals, and cliffhangers galore. Perfect for a quick break or an all-nighter. Upgrade now to skip the ads, unlock premium content, and stay ahead of the buzz. The binge starts here! \uD83D\uDE31",
                                        "Late-Night Binges, Zero Regrets!​​ Veedule’s miniseries serve up the perfect blend of passion, suspense, and addictive storytelling. Whether you’re relaxing or procrastinating, we’ve got your fix. Renew today for seamless streaming, no interruptions, and instant updates. Your next guilty pleasure is ready—dive in! \uD83D\uDC4C",
                                        "Drama So Good, You’ll Forget Reality!​​ Veedule’s miniseries are your escape into worlds of love, revenge, and unmissable twists. Daily updates mean fresh thrills whenever you need them. Subscribe now for ad-free viewing, exclusive access, and nonstop entertainment. The remote’s in your hands—what’s your next move? \uD83D\uDE0E"};
                            } else if (shortSemLink.getLanguage().equals("zh-TW")) {
                                fruits = new String[]{
                                        "It's packed with drama, romance, revenge and thrills. Veedule brings you daily updates of the hottest miniseries, perfect for a relaxing nap or a late-night binge Renew your membership today to unlock unlimited access, ad-free streaming and never miss an update. Your next hit is just a tap away! \uD83D\uDE0D",
                                        "Binge-Worthy Bliss Awaits! Dive into heart-stopping drama, sizzling romance, and edge-of-your-seat suspense with Veedule’s exclusive miniseries. Whether you're unwinding after work or burning the midnight oil, our daily updates keep you hooked. Upgrade now for ad-free streaming, unlimited access, and early releases. Your next obsession is just one click away—don’t miss out! \uD83D\uDD25",
                                        "Passion, Betrayal, and Nonstop Thrills! Veedule delivers the hottest miniseries to fuel your binge cravings. From steamy love triangles to jaw-dropping plot twists, we’ve got your entertainment covered. Renew your membership today for seamless, ad-free viewing and exclusive content. The next episode is calling—will you answer? \uD83D\uDEA8",
                                        "Your Escape Starts Here! Lose yourself in Veedule’s addictive miniseries, where every episode packs drama, romance, and suspense. Perfect for a lazy afternoon or a late-night marathon. Subscribe now to enjoy unlimited streaming, zero ads, and instant updates. The story never stops—neither should you! \uD83C\uDF1F",
                                        "Unlock Unlimited Drama! Veedule brings you the most talked-about miniseries, blending passion, revenge, and pulse-pounding action. Whether you’re snuggled up or sneaking in an episode at work, we’ve got you covered. Upgrade today for ad-free binges and never miss a moment. Your next binge obsession is waiting! \uD83D\uDC40",
                                        "Romance, Revenge, and Riveting Twists!​​ Veedule’s miniseries are your ticket to nonstop entertainment. With daily updates, there’s always something new to devour. Renew your membership now for uninterrupted streaming, exclusive perks, and early access. The screen is set—press play and disappear into the drama! \uD83E\uDD8B",
                                        "Binge Like Never Before!​​ Veedule’s curated miniseries serve up the perfect mix of love, betrayal, and adrenaline-pumping action. Whether you’re napping or night-owl-ing, we keep you hooked. Subscribe today for ad-free bliss, unlimited access, and fresh drops daily. Your next favorite show is just a tap away! \uD83D\uDC96",
                                        "Get Hooked on Drama!​​ Veedule’s miniseries are packed with everything you crave—sizzling chemistry, shocking betrayals, and cliffhangers galore. Perfect for a quick break or an all-nighter. Upgrade now to skip the ads, unlock premium content, and stay ahead of the buzz. The binge starts here! \uD83D\uDE31",
                                        "Late-Night Binges, Zero Regrets!​​ Veedule’s miniseries serve up the perfect blend of passion, suspense, and addictive storytelling. Whether you’re relaxing or procrastinating, we’ve got your fix. Renew today for seamless streaming, no interruptions, and instant updates. Your next guilty pleasure is ready—dive in! \uD83D\uDC4C",
                                        "Drama So Good, You’ll Forget Reality!​​ Veedule’s miniseries are your escape into worlds of love, revenge, and unmissable twists. Daily updates mean fresh thrills whenever you need them. Subscribe now for ad-free viewing, exclusive access, and nonstop entertainment. The remote’s in your hands—what’s your next move? \uD83D\uDE0E"};
                            } else if (shortSemLink.getLanguage().equals("ja")) {
                                fruits = new String[]{
                                        "It's packed with drama, romance, revenge and thrills. Veedule brings you daily updates of the hottest miniseries, perfect for a relaxing nap or a late-night binge Renew your membership today to unlock unlimited access, ad-free streaming and never miss an update. Your next hit is just a tap away! \uD83D\uDE0D",
                                        "Binge-Worthy Bliss Awaits! Dive into heart-stopping drama, sizzling romance, and edge-of-your-seat suspense with Veedule’s exclusive miniseries. Whether you're unwinding after work or burning the midnight oil, our daily updates keep you hooked. Upgrade now for ad-free streaming, unlimited access, and early releases. Your next obsession is just one click away—don’t miss out! \uD83D\uDD25",
                                        "Passion, Betrayal, and Nonstop Thrills! Veedule delivers the hottest miniseries to fuel your binge cravings. From steamy love triangles to jaw-dropping plot twists, we’ve got your entertainment covered. Renew your membership today for seamless, ad-free viewing and exclusive content. The next episode is calling—will you answer? \uD83D\uDEA8",
                                        "Your Escape Starts Here! Lose yourself in Veedule’s addictive miniseries, where every episode packs drama, romance, and suspense. Perfect for a lazy afternoon or a late-night marathon. Subscribe now to enjoy unlimited streaming, zero ads, and instant updates. The story never stops—neither should you! \uD83C\uDF1F",
                                        "Unlock Unlimited Drama! Veedule brings you the most talked-about miniseries, blending passion, revenge, and pulse-pounding action. Whether you’re snuggled up or sneaking in an episode at work, we’ve got you covered. Upgrade today for ad-free binges and never miss a moment. Your next binge obsession is waiting! \uD83D\uDC40",
                                        "Romance, Revenge, and Riveting Twists!​​ Veedule’s miniseries are your ticket to nonstop entertainment. With daily updates, there’s always something new to devour. Renew your membership now for uninterrupted streaming, exclusive perks, and early access. The screen is set—press play and disappear into the drama! \uD83E\uDD8B",
                                        "Binge Like Never Before!​​ Veedule’s curated miniseries serve up the perfect mix of love, betrayal, and adrenaline-pumping action. Whether you’re napping or night-owl-ing, we keep you hooked. Subscribe today for ad-free bliss, unlimited access, and fresh drops daily. Your next favorite show is just a tap away! \uD83D\uDC96",
                                        "Get Hooked on Drama!​​ Veedule’s miniseries are packed with everything you crave—sizzling chemistry, shocking betrayals, and cliffhangers galore. Perfect for a quick break or an all-nighter. Upgrade now to skip the ads, unlock premium content, and stay ahead of the buzz. The binge starts here! \uD83D\uDE31",
                                        "Late-Night Binges, Zero Regrets!​​ Veedule’s miniseries serve up the perfect blend of passion, suspense, and addictive storytelling. Whether you’re relaxing or procrastinating, we’ve got your fix. Renew today for seamless streaming, no interruptions, and instant updates. Your next guilty pleasure is ready—dive in! \uD83D\uDC4C",
                                        "Drama So Good, You’ll Forget Reality!​​ Veedule’s miniseries are your escape into worlds of love, revenge, and unmissable twists. Daily updates mean fresh thrills whenever you need them. Subscribe now for ad-free viewing, exclusive access, and nonstop entertainment. The remote’s in your hands—what’s your next move? \uD83D\uDE0E"};
                            }


                            // 创建 Random 对象
                            Random random = new Random();
                            // 随机获取数组索引（0 到 length-1）
                            int randomIndex = random.nextInt(fruits.length);
                            // 获取随机值
                            String randomFruit = fruits[randomIndex];

                            int randomFourDigit1 = ThreadLocalRandom.current().nextInt(1000, 10000);
                            int randomFourDigit2 = ThreadLocalRandom.current().nextInt(1000, 10000);
                            int randomFourDigit3 = ThreadLocalRandom.current().nextInt(1000, 10000);
                            int randomFourDigit4 = ThreadLocalRandom.current().nextInt(1000, 10000);
                            int randomFourDigit5 = ThreadLocalRandom.current().nextInt(1000, 10000);
                            int randomFourDigit6 = ThreadLocalRandom.current().nextInt(1000, 10000);


                            ShortPage shortPage = new ShortPage();
                            shortPage.setAppId(String.valueOf(su.getAppId()));
                            shortPage.setCname("投诉和反馈");
                            List<ShortPage> shortPageList = shortPageService.selectShortPageList(shortPage);


                            String randomNum = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                            String randomNum1 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);

                            String movieUrl1 = shortEmailDomain.getAppDomain() + "/video/?id=" + randomFourDigit1 + shortMovieList.get(0).getId() + "&userId=" + randomNum + su.getHashId() + randomNum1 + "&uniqueId=" + su.getUniqueId();

                            String randomNum2 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                            String randomNum3 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                            String movieUrl2 = shortEmailDomain.getAppDomain() + "/video/?id=" + randomFourDigit2 + shortMovieList.get(1).getId() + "&userId=" + randomNum2 + su.getHashId() + randomNum3 + "&uniqueId=" + su.getUniqueId();


                            String randomNum4 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                            String randomNum5 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                            String movieUrl3 = shortEmailDomain.getAppDomain() + "/video/?id=" + randomFourDigit3 + shortMovieList.get(2).getId() + "&userId=" + randomNum4 + su.getHashId() + randomNum5 + "&uniqueId=" + su.getUniqueId();


                            String randomNum6 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                            String randomNum7 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                            String movieUrl4 = shortEmailDomain.getAppDomain() + "/video/?id=" + randomFourDigit4 + shortMovieList.get(3).getId() + "&userId=" + randomNum6 + su.getHashId() + randomNum7 + "&uniqueId=" + su.getUniqueId();

                            String randomNum8 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                            String randomNum9 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                            String movieUrl5 = shortEmailDomain.getAppDomain() + "/video/?id=" + randomFourDigit5 + shortMovieList.get(4).getId() + "&userId=" + randomNum8 + su.getHashId() + randomNum9 + "&uniqueId=" + su.getUniqueId();

                            String randomNum10 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                            String randomNum11 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                            String movieUrl6 = shortEmailDomain.getAppDomain() + "/video/?id=" + randomFourDigit6 + shortMovieList.get(5).getId() + "&userId=" + randomNum10 + su.getHashId() + randomNum11 + "&uniqueId=" + su.getUniqueId();


                            String randomNumApp1 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                            String randomNumApp2 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                            String dayType = originalOrder.getSubscriptionType();
                            Date payTime = originalOrder.getPayTime();
                            BigDecimal buyPrice = originalOrder.getPaymentAmount();
                            BigDecimal renewalPrice = originalOrder.getPaymentAmount();
//            http://localhost:3000/subscription-records/
                            String langechange = "";
                            if (null != shortSemLink)
                                langechange = shortSemLink.getLanguage();
                            String unSubUrl = shortEmailDomain.getAppDomain() + "/subscription-records/" + "?userId=" + randomNumApp1 + su.getHashId() + randomNumApp2 + "&uniqueId=" + su.getUniqueId() + "&language=" + langechange + "&noLogin=true";


                            // 使用更安全的占位符格式
                            htmlContent = htmlContent
                                    .replace("${appName}", null != shortEmailDomain.getAppName() ? shortEmailDomain.getAppName() : "")
                                    .replace("{{appName}}", null != shortEmailDomain.getAppName() ? shortEmailDomain.getAppName() : "")

                                    .replace("${movieIcon1}", null != shortMovieList.get(0).getIcon() ? shortMovieList.get(0).getIcon() : "")
                                    .replace("{{movieIcon1}}", null != shortMovieList.get(0).getIcon() ? shortMovieList.get(0).getIcon() : "")

                                    .replace("${movieIcon2}", null != shortMovieList.get(1).getIcon() ? shortMovieList.get(1).getIcon() : "")
                                    .replace("{{movieIcon2}}", null != shortMovieList.get(1).getIcon() ? shortMovieList.get(1).getIcon() : "")

                                    .replace("${movieIcon3}", null != shortMovieList.get(2).getIcon() ? shortMovieList.get(2).getIcon() : "")
                                    .replace("{{movieIcon3}}", null != shortMovieList.get(2).getIcon() ? shortMovieList.get(2).getIcon() : "1")

                                    .replace("${movieIcon4}", null != shortMovieList.get(3).getIcon() ? shortMovieList.get(3).getIcon() : "")
                                    .replace("{{movieIcon4}}", null != shortMovieList.get(3).getIcon() ? shortMovieList.get(3).getIcon() : "")

                                    .replace("${movieIcon5}", null != shortMovieList.get(4).getIcon() ? shortMovieList.get(4).getIcon() : "")
                                    .replace("{{movieIcon5}}", null != shortMovieList.get(4).getIcon() ? shortMovieList.get(4).getIcon() : "")

                                    .replace("${movieIcon6}", null != shortMovieList.get(5).getIcon() ? shortMovieList.get(5).getIcon() : "")
                                    .replace("{{movieIcon6}}", null != shortMovieList.get(5).getIcon() ? shortMovieList.get(5).getIcon() : "")

                                    .replace("${movieName1}", null != shortMovieList.get(0).getName() ? shortMovieList.get(0).getName() : "")
                                    .replace("{{movieName1}}", null != shortMovieList.get(0).getName() ? shortMovieList.get(0).getName() : "")

                                    .replace("${movieName2}", null != shortMovieList.get(1).getName() ? shortMovieList.get(1).getName() : "")
                                    .replace("{{movieName2}}", null != shortMovieList.get(1).getName() ? shortMovieList.get(1).getName() : "")

                                    .replace("${movieName3}", null != shortMovieList.get(2).getName() ? shortMovieList.get(2).getName() : "")
                                    .replace("{{movieName3}}", null != shortMovieList.get(2).getName() ? shortMovieList.get(2).getName() : "")

                                    .replace("${movieName4}", null != shortMovieList.get(3).getName() ? shortMovieList.get(3).getName() : "")
                                    .replace("{{movieName4}}", null != shortMovieList.get(3).getName() ? shortMovieList.get(3).getName() : "")

                                    .replace("${movieName5}", null != shortMovieList.get(4).getName() ? shortMovieList.get(4).getName() : "")
                                    .replace("{{movieName5}}", null != shortMovieList.get(4).getName() ? shortMovieList.get(4).getName() : "")

                                    .replace("${movieName6}", null != shortMovieList.get(5).getName() ? shortMovieList.get(5).getName() : "")
                                    .replace("{{movieName6}}", null != shortMovieList.get(5).getName() ? shortMovieList.get(5).getName() : "")

                                    .replace("${movieUrl1}", movieUrl1)
                                    .replace("{{movieUrl1}}", movieUrl1)

                                    .replace("${movieUrl2}", movieUrl2)
                                    .replace("{{movieUrl2}}", movieUrl2)

                                    .replace("${movieUrl3}", movieUrl3)
                                    .replace("{{movieUrl3}}", movieUrl3)

                                    .replace("${movieUrl4}", movieUrl4)
                                    .replace("{{movieUrl4}}", movieUrl4)

                                    .replace("${movieUrl5}", movieUrl5)
                                    .replace("{{movieUrl5}}", movieUrl5)
                                    .replace("${movieUrl6}", movieUrl6)
                                    .replace("{{movieUrl6}}", movieUrl6)

                                    .replace("${Introduction}", randomFruit)
                                    .replace("{{Introduction}}", randomFruit)

                                    .replace("${expireTime}", su.getExpireTime().toString())
                                    .replace("{{expireTime}}", su.getExpireTime().toString())

                                    .replace("${amount}", originalOrder.getAmount().toString())
                                    .replace("{{amount}}", originalOrder.getAmount().toString())

                                    .replace("${dayType}", dayType)
                                    .replace("{{dayType}}", dayType)
                                    .replace("${payTime}", payTime.toString())
                                    .replace("{{payTime}}", payTime.toString())
                                    .replace("${buyPrice}", buyPrice.toString())
                                    .replace("{{buyPrice}}", buyPrice.toString())
                                    .replace("${renewalPrice}", renewalPrice.toString())
                                    .replace("{{renewalPrice}}", renewalPrice.toString())
                                    .replace("${unSubUrl}", unSubUrl)
                                    .replace("{{unSubUrl}}", unSubUrl)

                                    .replace("${jumpUrl}", shortPageList.get(0).getUrl() + "?selevt=3&email=" + su.getEmail())
                                    .replace("{{jumpUrl}}", shortPageList.get(0).getUrl() + "?selevt=3&email=" + su.getEmail());

                            // 6. 创建邮件内容
                            email.setSubject(subject);
                            email.setHtmlContent(htmlContent); // 使用HTML内容

                            new Thread(() -> {
                                try {
                                    runlog.setState("1");
                                    String content = "";
                                    // 7. 发送邮件
                                    CreateSmtpEmail response = apiInstance.sendTransacEmail(email);
                                    System.out.println("订阅通知邮件发送成功！消息ID: " + response.getMessageId());
                                    content = String.format(",订阅通知邮件发送成功！消息ID: " + response.getMessageId() + "用户Id：" + su.getId() +
                                            ",邮件主题：" + "New drama recommendation" + ",收件人名称：" + "Valued Member" +
                                            ",收件人邮件：" + su.getEmail() + ",邮件服务商:brevo");
                                    runlog.setNote(response.toString());
                                    runlog.setContent(content);
                                    shortRunlogService.insertShortRunlog(runlog);

                                    ShortUserActivityLog shortUserActivityLog = new ShortUserActivityLog();
                                    shortUserActivityLog.setUserId(su.getId());
                                    shortUserActivityLog.setAppId(su.getAppId());
                                    shortUserActivityLog.setState("1");
                                    shortUserActivityLog.setContent("订阅通知邮件发送成功");
                                    shortUserActivityLog.setStatus("0");
                                    shortUserActivityLog.setCreateTime(DateUtils.getNowDate());
                                    shortUserActivityLogService.insertShortUserActivityLog(shortUserActivityLog);
                                } catch (Exception e) {
                                    runlog.setState("0");
                                    runlog.setContent(String.format(",订阅通知发送失败：" + e.getMessage() + ",邮件主题：" + "New drama recommendation" +
                                            ",收件人名称：" + "Valued Member" + ",收件人邮件：" + su.getEmail()) + ",邮件服务商:brevo");
                                    runlog.setNote(e.toString());
                                    shortRunlogService.insertShortRunlog(runlog);
                                    ShortUserActivityLog shortUserActivityLog = new ShortUserActivityLog();
                                    shortUserActivityLog.setUserId(su.getId());
                                    shortUserActivityLog.setAppId(su.getAppId());
                                    shortUserActivityLog.setState("1");
                                    shortUserActivityLog.setContent("订通知邮件发送失败");
                                    shortUserActivityLog.setStatus("0");
                                    shortUserActivityLog.setCreateTime(DateUtils.getNowDate());
                                    shortUserActivityLogService.insertShortUserActivityLog(shortUserActivityLog);
                                }
                            }).start();

                        } catch (IOException e) {
                            System.err.println("读取HTML文件失败: " + e.getMessage());
                            runlog.setState("0");
                            runlog.setContent(String.format(",读取HTML文件失败：" + e.getMessage() + ",邮件主题：" + "New drama recommendation" + ",收件人名称：" + "Valued Member" + ",收件人邮件：" + su.getEmail()));
                            runlog.setNote(e.toString());
                            shortRunlogService.insertShortRunlog(runlog);

                        } catch (Exception e) {
                            System.err.println("发送失败: " + e.getMessage());
                            runlog.setState("0");
                            runlog.setContent(String.format(",发送失败：" + e.getMessage() + ",邮件主题：" + "New drama recommendation" + ",收件人名称：" + "Valued Member" + ",收件人邮件：" + su.getId()));
                            runlog.setNote(e.toString());
                            shortRunlogService.insertShortRunlog(runlog);
                        }
                    }
                }

//================================
                shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
                return AjaxResult.success("操作成功=");
            }
            shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
            return AjaxResult.success("操作成功");

        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("status", "error");
            error.put("message", "Validation failed: " + e.getMessage());
            shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
            return AjaxResult.error("操作失败");
        }
    }


    @Override
    public AjaxResult sendEmailRenewalByUserIds(String publicKey) {
        int count = shortIntermedStatusService.countByName("sendEmailRenewalByUserIds");
        if (count > 0)
            return AjaxResult.success("自动统计首页数据，请勿重复操作");

        ShortIntermedStatus shortIntermedStatus = new ShortIntermedStatus();
        shortIntermedStatus.setName("sendEmailRenewalByUserIds");
        shortIntermedStatusService.insertShortIntermedStatus(shortIntermedStatus);


        try {
            String privateKey = "-----BEGIN OPENSSH PRIVATE KEY-----\n" +
                    "b3BlbnNzaC1rZXktdjEAAAAABG5vbmUAAAAEbm9uZQAAAAAAAAABAAAAMwAAAAtzc2gtZW\n" +
                    "QyNTUxOQAAACDdx+eInOus2G8pGSS+paM8QV5SkQdAXGh/e0Z7pRaDRwAAAJiIp66uiKeu\n" +
                    "rgAAAAtzc2gtZWQyNTUxOQAAACDdx+eInOus2G8pGSS+paM8QV5SkQdAXGh/e0Z7pRaDRw\n" +
                    "AAAEDJW6rFB140gGyRJZM+mqoIFj8HAGrGpnuWj+sCJa/06N3H54ic66zYbykZJL6lozxB\n" +
                    "********************************************************\n" +
                    "-----END OPENSSH PRIVATE KEY-----";

            // 2. 加载密钥对
            KeyPair keyPair = KeyPair.load(new JSch(), privateKey.getBytes(), null);
            byte[] derivedPublicKeyBlob = keyPair.getPublicKeyBlob();

            // 3. 解析用户公钥（按空格分割并解码Base64）
            String[] publicKeyParts = publicKey.split(" ");
            if (publicKeyParts.length < 2) {
                throw new IllegalArgumentException("Invalid public key format");
            }
            byte[] userPublicKeyBlob = Base64.getDecoder().decode(publicKeyParts[1]);

            // 4. 安全比对（避免时序攻击）
            boolean isMatch = MessageDigest.isEqual(derivedPublicKeyBlob, userPublicKeyBlob);
            if (isMatch) {
                log.info("验证密钥正确------定时发邮件");
//================================

                List<ShortUser> shortUserList = shortUserService.getBy818();
                log.info("shortUserList.size：" + shortUserList.size());
                for (ShortUser su : shortUserList) {
                    SendSmtpEmail email = new SendSmtpEmail();
                    TransactionalEmailsApi apiInstance = new TransactionalEmailsApi();

                    LocalDateTime now = LocalDateTime.now();
                    LocalDateTime nextDayMidnight = now.plusDays(1)
                            .withHour(0)
                            .withMinute(0)
                            .withSecond(0)
                            .withNano(0);

                    // 如果需要转换为Date对象
                    Date date = Date.from(nextDayMidnight.atZone(ZoneId.systemDefault()).toInstant());

                    String exTime = formatToEnglishUTC(date);

                    // 3. 设置发件人
                    SendSmtpEmailSender sender = new SendSmtpEmailSender();
                    ShortEmailDomain shortEmailDomain = shortEmailDomainService.getrandomDomain(su.getAppId());

                    if (null != shortEmailDomain) {
                        // 2. 初始化邮件API
                        ApiClient defaultClient = Configuration.getDefaultApiClient();
                        ApiKeyAuth apiKey = (ApiKeyAuth) defaultClient.getAuthentication("api-key");
                        apiKey.setApiKey(brevoApiKey); // 替换为你的Brevo API密钥

                        if (null != shortEmailDomain) {
                            sender.setEmail(shortEmailDomain.getDomain()); // 必须已验证的邮箱
                            sender.setName(shortEmailDomain.getAppName());
                        } else {
                            sender.setEmail(senderEmail); // 必须已验证的邮箱
                            sender.setName(senderName);
                        }

                        List<SendSmtpEmailTo> toList = new ArrayList<>();
                        // 4. 设置收件人
                        SendSmtpEmailTo recipient = new SendSmtpEmailTo();
                        recipient.setEmail(null != su.getEmail() ? su.getEmail() : null);
                        recipient.setName("Valued Member");
                        toList.add(recipient);
                        email.setSender(sender);
                        email.setTo(toList);
                        // 记录日志并处理结果
                        ShortRunlog runlog = new ShortRunlog();
                        runlog.setType("发送订阅通知邮件");
                        runlog.setCreateTime(DateUtils.getNowDate());
                        runlog.setUpdateTime(DateUtils.getNowDate());


                        try {
                            // 5. 从HTML文件读取内容
                            ShortSemLink shortSemLink = shortSemLinkService.selectShortSemLinkById(su.getLinkidId());

                            String htmlName = "";

                            String sub = "Renewal Reminder";
                            //先固定英语
//                            if(null != shortSemLink )
//                                shortSemLink.setLanguage("en-US");
                            if (null == shortSemLink || shortSemLink.getLanguage().equals("en-US"))
                                htmlName = "noticeEmail2.html";
                            else if (shortSemLink.getLanguage().equals("es")) {
                                htmlName = "es-noticeEmail2.html";
                                sub = "Recordatorio de renovación";
                            } else if (shortSemLink.getLanguage().equals("pt")) {
                                htmlName = "pt-noticeEmail2.html";
                                sub = "Lembrete de renovação";
                            } else if (shortSemLink.getLanguage().equals("zh-TW")) {
                                htmlName = "zh-TW-noticeEmail2.html";
                                sub = "续订提醒";
                            } else if (shortSemLink.getLanguage().equals("ja")) {
                                htmlName = "ja-noticeEmail2.html";
                                sub = "更新リマインダー";
                            } else if (shortSemLink.getLanguage().equals("de")) {
                                htmlName = "de-noticeEmail2.html";
                                sub = "Erneuerungserinnerung";
                            }

                            String subject = shortEmailDomain.getAppName() + " " + sub;

//                            if(StringUtils.isNotEmpty(su.getSource())){
//                                if (null == shortSemLink || shortSemLink.getLanguage().equals("en-US"))
//                                    htmlName = "former_noticeEmail.html";
//                                else if (shortSemLink.getLanguage().equals("es"))
//                                    htmlName = "es-former_noticeEmail.html";
//                                else if (shortSemLink.getLanguage().equals("pt"))
//                                    htmlName = "pt-former_noticeEmail.html";
//                                else if (shortSemLink.getLanguage().equals("zh-TW"))
//                                    htmlName = "zh-TW-former_noticeEmail.html";
//                                else if (shortSemLink.getLanguage().equals("ja"))
//                                    htmlName = "ja-former_noticeEmail.html";
//                                else if (shortSemLink.getLanguage().equals("de"))
//                                    htmlName = "de-former_noticeEmail.html";
//                            }
                            /*else{
                                if (null == shortSemLink || shortSemLink.getLanguage().equals("en-US"))
                                    htmlName = "noticeEmail.html";
                                else if (shortSemLink.getLanguage().equals("es"))
                                    htmlName = "es-noticeEmail.html";
                                else if (shortSemLink.getLanguage().equals("pt"))
                                    htmlName = "pt-noticeEmail.html";
                                else if (shortSemLink.getLanguage().equals("zh-TW"))
                                    htmlName = "zh-TW-noticeEmail.html";
                                else if (shortSemLink.getLanguage().equals("ja"))
                                    htmlName = "ja-noticeEmail.html";
                                else if (shortSemLink.getLanguage().equals("de"))
                                    htmlName = "de-noticeEmail.html";
                            }*/


                            ClassPathResource resource = new ClassPathResource("templates/" + htmlName);
                            String htmlContent = StreamUtils.copyToString(
                                    resource.getInputStream(),
                                    StandardCharsets.UTF_8
                            );

                            String[] dramaTitles = null;

                            String vName = "";
                            if (null == shortSemLink || shortSemLink.getLanguage().equals("en-US")) {
                                vName = "Valued Member";
                                dramaTitles = new String[]{
                                        "New Drama Recommendation",
                                        "The TV drama savior",
                                        "High-scoring new drama",
                                        "Hot drama list",
                                        "Guide to watching TV series",
                                        "A must-have for drama fans",
                                        "Good dramas",
                                        "New drama coming",
                                        "Drama Drama Terminator",
                                        "Benefits for TV drama fans"
                                };
                            } else if (shortSemLink.getLanguage().equals("es")) {
                                vName = "Miembro valioso";
                                dramaTitles = new String[]{
                                        "New Drama Recommendation",
                                        "The TV drama savior",
                                        "High-scoring new drama",
                                        "Hot drama list",
                                        "Guide to watching TV series",
                                        "A must-have for drama fans",
                                        "Good dramas",
                                        "New drama coming",
                                        "Drama Drama Terminator",
                                        "Benefits for TV drama fans"
                                };
                            } else if (shortSemLink.getLanguage().equals("pt")) {
                                vName = "Membro Valioso";
                                dramaTitles = new String[]{
                                        "New Drama Recommendation",
                                        "The TV drama savior",
                                        "High-scoring new drama",
                                        "Hot drama list",
                                        "Guide to watching TV series",
                                        "A must-have for drama fans",
                                        "Good dramas",
                                        "New drama coming",
                                        "Drama Drama Terminator",
                                        "Benefits for TV drama fans"
                                };
                            } else if (shortSemLink.getLanguage().equals("zh-TW")) {
                                vName = "尊贵的会员";
                                dramaTitles = new String[]{
                                        "New Drama Recommendation",
                                        "The TV drama savior",
                                        "High-scoring new drama",
                                        "Hot drama list",
                                        "Guide to watching TV series",
                                        "A must-have for drama fans",
                                        "Good dramas",
                                        "New drama coming",
                                        "Drama Drama Terminator",
                                        "Benefits for TV drama fans"
                                };
                            } else if (shortSemLink.getLanguage().equals("ja")) {
                                vName = "大切なメンバー";
                                dramaTitles = new String[]{
                                        "New Drama Recommendation",
                                        "The TV drama savior",
                                        "High-scoring new drama",
                                        "Hot drama list",
                                        "Guide to watching TV series",
                                        "A must-have for drama fans",
                                        "Good dramas",
                                        "New drama coming",
                                        "Drama Drama Terminator",
                                        "Benefits for TV drama fans"
                                };
                            } else if (shortSemLink.getLanguage().equals("de")) {
                                vName = "Geschätztes Mitglied";
                                dramaTitles = new String[]{
                                        "Empfehlung für neue Dramen",
                                        "Der Retter unter den TV-Dramen",
                                        "Bestseller-Dramen",
                                        "Liste der angesagtesten Dramen",
                                        "Leitfaden zum Serienschauen",
                                        "Ein Muss für Drama-Fans",
                                        "Gute Dramen",
                                        "Neue Dramen im Kommen",
                                        "Drama-Drama Terminator",
                                        "Vorteile für Drama-Fans"
                                };
                            }

                            recipient.setName(vName);
                            Random random1 = new Random();
                            int randomIndex1 = random1.nextInt(dramaTitles.length);
//                            String subject = dramaTitles[randomIndex1];


                            ShortOrder originalOrder = null;
                            ShortOrder lastSubRe = shortOrderService.findFirstByUserAndPay_typeAndStatusOrderByUpdatedAtDesc(su.getId(), "订阅续费", "SUCCEEDED");
                            if (null != lastSubRe) {
                                originalOrder = lastSubRe;
                            } else {
                                ShortOrder lastSub = shortOrderService.findFirstByUserAndPay_typeAndStatusOrderByUpdatedAtDesc(su.getId(), "订阅", "SUCCEEDED");
                                if (null != lastSub)
                                    originalOrder = lastSub;
                            }

                            List<ShortMovie> shortMovieList = new ArrayList<>();

                            if (null == shortSemLink || shortSemLink.getLanguage().equals("en-US"))
                                shortMovieList = shortMovieService.getByRand4(su.getAppId());
                            else
                                shortMovieList = shortMovieService.getByRand4I18n(su.getAppId(), shortSemLink.getLanguage());


                            // 定义一个数组
                            String[] fruits = null;


                            if (null == shortSemLink || shortSemLink.getLanguage().equals("en-US")) {
                                fruits = new String[]{
                                        "It's packed with drama, romance, revenge and thrills. Veedule brings you daily updates of the hottest miniseries, perfect for a relaxing nap or a late-night binge Renew your membership today to unlock unlimited access, ad-free streaming and never miss an update. Your next hit is just a tap away! \uD83D\uDE0D",
                                        "Binge-Worthy Bliss Awaits! Dive into heart-stopping drama, sizzling romance, and edge-of-your-seat suspense with Veedule’s exclusive miniseries. Whether you're unwinding after work or burning the midnight oil, our daily updates keep you hooked. Upgrade now for ad-free streaming, unlimited access, and early releases. Your next obsession is just one click away—don’t miss out! \uD83D\uDD25",
                                        "Passion, Betrayal, and Nonstop Thrills! Veedule delivers the hottest miniseries to fuel your binge cravings. From steamy love triangles to jaw-dropping plot twists, we’ve got your entertainment covered. Renew your membership today for seamless, ad-free viewing and exclusive content. The next episode is calling—will you answer? \uD83D\uDEA8",
                                        "Your Escape Starts Here! Lose yourself in Veedule’s addictive miniseries, where every episode packs drama, romance, and suspense. Perfect for a lazy afternoon or a late-night marathon. Subscribe now to enjoy unlimited streaming, zero ads, and instant updates. The story never stops—neither should you! \uD83C\uDF1F",
                                        "Unlock Unlimited Drama! Veedule brings you the most talked-about miniseries, blending passion, revenge, and pulse-pounding action. Whether you’re snuggled up or sneaking in an episode at work, we’ve got you covered. Upgrade today for ad-free binges and never miss a moment. Your next binge obsession is waiting! \uD83D\uDC40",
                                        "Romance, Revenge, and Riveting Twists!​​ Veedule’s miniseries are your ticket to nonstop entertainment. With daily updates, there’s always something new to devour. Renew your membership now for uninterrupted streaming, exclusive perks, and early access. The screen is set—press play and disappear into the drama! \uD83E\uDD8B",
                                        "Binge Like Never Before!​​ Veedule’s curated miniseries serve up the perfect mix of love, betrayal, and adrenaline-pumping action. Whether you’re napping or night-owl-ing, we keep you hooked. Subscribe today for ad-free bliss, unlimited access, and fresh drops daily. Your next favorite show is just a tap away! \uD83D\uDC96",
                                        "Get Hooked on Drama!​​ Veedule’s miniseries are packed with everything you crave—sizzling chemistry, shocking betrayals, and cliffhangers galore. Perfect for a quick break or an all-nighter. Upgrade now to skip the ads, unlock premium content, and stay ahead of the buzz. The binge starts here! \uD83D\uDE31",
                                        "Late-Night Binges, Zero Regrets!​​ Veedule’s miniseries serve up the perfect blend of passion, suspense, and addictive storytelling. Whether you’re relaxing or procrastinating, we’ve got your fix. Renew today for seamless streaming, no interruptions, and instant updates. Your next guilty pleasure is ready—dive in! \uD83D\uDC4C",
                                        "Drama So Good, You’ll Forget Reality!​​ Veedule’s miniseries are your escape into worlds of love, revenge, and unmissable twists. Daily updates mean fresh thrills whenever you need them. Subscribe now for ad-free viewing, exclusive access, and nonstop entertainment. The remote’s in your hands—what’s your next move? \uD83D\uDE0E"};
                            } else if (shortSemLink.getLanguage().equals("es")) {
                                fruits = new String[]{
                                        "It's packed with drama, romance, revenge and thrills. Veedule brings you daily updates of the hottest miniseries, perfect for a relaxing nap or a late-night binge Renew your membership today to unlock unlimited access, ad-free streaming and never miss an update. Your next hit is just a tap away! \uD83D\uDE0D",
                                        "Binge-Worthy Bliss Awaits! Dive into heart-stopping drama, sizzling romance, and edge-of-your-seat suspense with Veedule’s exclusive miniseries. Whether you're unwinding after work or burning the midnight oil, our daily updates keep you hooked. Upgrade now for ad-free streaming, unlimited access, and early releases. Your next obsession is just one click away—don’t miss out! \uD83D\uDD25",
                                        "Passion, Betrayal, and Nonstop Thrills! Veedule delivers the hottest miniseries to fuel your binge cravings. From steamy love triangles to jaw-dropping plot twists, we’ve got your entertainment covered. Renew your membership today for seamless, ad-free viewing and exclusive content. The next episode is calling—will you answer? \uD83D\uDEA8",
                                        "Your Escape Starts Here! Lose yourself in Veedule’s addictive miniseries, where every episode packs drama, romance, and suspense. Perfect for a lazy afternoon or a late-night marathon. Subscribe now to enjoy unlimited streaming, zero ads, and instant updates. The story never stops—neither should you! \uD83C\uDF1F",
                                        "Unlock Unlimited Drama! Veedule brings you the most talked-about miniseries, blending passion, revenge, and pulse-pounding action. Whether you’re snuggled up or sneaking in an episode at work, we’ve got you covered. Upgrade today for ad-free binges and never miss a moment. Your next binge obsession is waiting! \uD83D\uDC40",
                                        "Romance, Revenge, and Riveting Twists!​​ Veedule’s miniseries are your ticket to nonstop entertainment. With daily updates, there’s always something new to devour. Renew your membership now for uninterrupted streaming, exclusive perks, and early access. The screen is set—press play and disappear into the drama! \uD83E\uDD8B",
                                        "Binge Like Never Before!​​ Veedule’s curated miniseries serve up the perfect mix of love, betrayal, and adrenaline-pumping action. Whether you’re napping or night-owl-ing, we keep you hooked. Subscribe today for ad-free bliss, unlimited access, and fresh drops daily. Your next favorite show is just a tap away! \uD83D\uDC96",
                                        "Get Hooked on Drama!​​ Veedule’s miniseries are packed with everything you crave—sizzling chemistry, shocking betrayals, and cliffhangers galore. Perfect for a quick break or an all-nighter. Upgrade now to skip the ads, unlock premium content, and stay ahead of the buzz. The binge starts here! \uD83D\uDE31",
                                        "Late-Night Binges, Zero Regrets!​​ Veedule’s miniseries serve up the perfect blend of passion, suspense, and addictive storytelling. Whether you’re relaxing or procrastinating, we’ve got your fix. Renew today for seamless streaming, no interruptions, and instant updates. Your next guilty pleasure is ready—dive in! \uD83D\uDC4C",
                                        "Drama So Good, You’ll Forget Reality!​​ Veedule’s miniseries are your escape into worlds of love, revenge, and unmissable twists. Daily updates mean fresh thrills whenever you need them. Subscribe now for ad-free viewing, exclusive access, and nonstop entertainment. The remote’s in your hands—what’s your next move? \uD83D\uDE0E"};
                            } else if (shortSemLink.getLanguage().equals("pt")) {
                                fruits = new String[]{
                                        "It's packed with drama, romance, revenge and thrills. Veedule brings you daily updates of the hottest miniseries, perfect for a relaxing nap or a late-night binge Renew your membership today to unlock unlimited access, ad-free streaming and never miss an update. Your next hit is just a tap away! \uD83D\uDE0D",
                                        "Binge-Worthy Bliss Awaits! Dive into heart-stopping drama, sizzling romance, and edge-of-your-seat suspense with Veedule’s exclusive miniseries. Whether you're unwinding after work or burning the midnight oil, our daily updates keep you hooked. Upgrade now for ad-free streaming, unlimited access, and early releases. Your next obsession is just one click away—don’t miss out! \uD83D\uDD25",
                                        "Passion, Betrayal, and Nonstop Thrills! Veedule delivers the hottest miniseries to fuel your binge cravings. From steamy love triangles to jaw-dropping plot twists, we’ve got your entertainment covered. Renew your membership today for seamless, ad-free viewing and exclusive content. The next episode is calling—will you answer? \uD83D\uDEA8",
                                        "Your Escape Starts Here! Lose yourself in Veedule’s addictive miniseries, where every episode packs drama, romance, and suspense. Perfect for a lazy afternoon or a late-night marathon. Subscribe now to enjoy unlimited streaming, zero ads, and instant updates. The story never stops—neither should you! \uD83C\uDF1F",
                                        "Unlock Unlimited Drama! Veedule brings you the most talked-about miniseries, blending passion, revenge, and pulse-pounding action. Whether you’re snuggled up or sneaking in an episode at work, we’ve got you covered. Upgrade today for ad-free binges and never miss a moment. Your next binge obsession is waiting! \uD83D\uDC40",
                                        "Romance, Revenge, and Riveting Twists!​​ Veedule’s miniseries are your ticket to nonstop entertainment. With daily updates, there’s always something new to devour. Renew your membership now for uninterrupted streaming, exclusive perks, and early access. The screen is set—press play and disappear into the drama! \uD83E\uDD8B",
                                        "Binge Like Never Before!​​ Veedule’s curated miniseries serve up the perfect mix of love, betrayal, and adrenaline-pumping action. Whether you’re napping or night-owl-ing, we keep you hooked. Subscribe today for ad-free bliss, unlimited access, and fresh drops daily. Your next favorite show is just a tap away! \uD83D\uDC96",
                                        "Get Hooked on Drama!​​ Veedule’s miniseries are packed with everything you crave—sizzling chemistry, shocking betrayals, and cliffhangers galore. Perfect for a quick break or an all-nighter. Upgrade now to skip the ads, unlock premium content, and stay ahead of the buzz. The binge starts here! \uD83D\uDE31",
                                        "Late-Night Binges, Zero Regrets!​​ Veedule’s miniseries serve up the perfect blend of passion, suspense, and addictive storytelling. Whether you’re relaxing or procrastinating, we’ve got your fix. Renew today for seamless streaming, no interruptions, and instant updates. Your next guilty pleasure is ready—dive in! \uD83D\uDC4C",
                                        "Drama So Good, You’ll Forget Reality!​​ Veedule’s miniseries are your escape into worlds of love, revenge, and unmissable twists. Daily updates mean fresh thrills whenever you need them. Subscribe now for ad-free viewing, exclusive access, and nonstop entertainment. The remote’s in your hands—what’s your next move? \uD83D\uDE0E"};
                            } else if (shortSemLink.getLanguage().equals("zh-TW")) {
                                fruits = new String[]{
                                        "It's packed with drama, romance, revenge and thrills. Veedule brings you daily updates of the hottest miniseries, perfect for a relaxing nap or a late-night binge Renew your membership today to unlock unlimited access, ad-free streaming and never miss an update. Your next hit is just a tap away! \uD83D\uDE0D",
                                        "Binge-Worthy Bliss Awaits! Dive into heart-stopping drama, sizzling romance, and edge-of-your-seat suspense with Veedule’s exclusive miniseries. Whether you're unwinding after work or burning the midnight oil, our daily updates keep you hooked. Upgrade now for ad-free streaming, unlimited access, and early releases. Your next obsession is just one click away—don’t miss out! \uD83D\uDD25",
                                        "Passion, Betrayal, and Nonstop Thrills! Veedule delivers the hottest miniseries to fuel your binge cravings. From steamy love triangles to jaw-dropping plot twists, we’ve got your entertainment covered. Renew your membership today for seamless, ad-free viewing and exclusive content. The next episode is calling—will you answer? \uD83D\uDEA8",
                                        "Your Escape Starts Here! Lose yourself in Veedule’s addictive miniseries, where every episode packs drama, romance, and suspense. Perfect for a lazy afternoon or a late-night marathon. Subscribe now to enjoy unlimited streaming, zero ads, and instant updates. The story never stops—neither should you! \uD83C\uDF1F",
                                        "Unlock Unlimited Drama! Veedule brings you the most talked-about miniseries, blending passion, revenge, and pulse-pounding action. Whether you’re snuggled up or sneaking in an episode at work, we’ve got you covered. Upgrade today for ad-free binges and never miss a moment. Your next binge obsession is waiting! \uD83D\uDC40",
                                        "Romance, Revenge, and Riveting Twists!​​ Veedule’s miniseries are your ticket to nonstop entertainment. With daily updates, there’s always something new to devour. Renew your membership now for uninterrupted streaming, exclusive perks, and early access. The screen is set—press play and disappear into the drama! \uD83E\uDD8B",
                                        "Binge Like Never Before!​​ Veedule’s curated miniseries serve up the perfect mix of love, betrayal, and adrenaline-pumping action. Whether you’re napping or night-owl-ing, we keep you hooked. Subscribe today for ad-free bliss, unlimited access, and fresh drops daily. Your next favorite show is just a tap away! \uD83D\uDC96",
                                        "Get Hooked on Drama!​​ Veedule’s miniseries are packed with everything you crave—sizzling chemistry, shocking betrayals, and cliffhangers galore. Perfect for a quick break or an all-nighter. Upgrade now to skip the ads, unlock premium content, and stay ahead of the buzz. The binge starts here! \uD83D\uDE31",
                                        "Late-Night Binges, Zero Regrets!​​ Veedule’s miniseries serve up the perfect blend of passion, suspense, and addictive storytelling. Whether you’re relaxing or procrastinating, we’ve got your fix. Renew today for seamless streaming, no interruptions, and instant updates. Your next guilty pleasure is ready—dive in! \uD83D\uDC4C",
                                        "Drama So Good, You’ll Forget Reality!​​ Veedule’s miniseries are your escape into worlds of love, revenge, and unmissable twists. Daily updates mean fresh thrills whenever you need them. Subscribe now for ad-free viewing, exclusive access, and nonstop entertainment. The remote’s in your hands—what’s your next move? \uD83D\uDE0E"};
                            } else if (shortSemLink.getLanguage().equals("ja")) {
                                fruits = new String[]{
                                        "It's packed with drama, romance, revenge and thrills. Veedule brings you daily updates of the hottest miniseries, perfect for a relaxing nap or a late-night binge Renew your membership today to unlock unlimited access, ad-free streaming and never miss an update. Your next hit is just a tap away! \uD83D\uDE0D",
                                        "Binge-Worthy Bliss Awaits! Dive into heart-stopping drama, sizzling romance, and edge-of-your-seat suspense with Veedule’s exclusive miniseries. Whether you're unwinding after work or burning the midnight oil, our daily updates keep you hooked. Upgrade now for ad-free streaming, unlimited access, and early releases. Your next obsession is just one click away—don’t miss out! \uD83D\uDD25",
                                        "Passion, Betrayal, and Nonstop Thrills! Veedule delivers the hottest miniseries to fuel your binge cravings. From steamy love triangles to jaw-dropping plot twists, we’ve got your entertainment covered. Renew your membership today for seamless, ad-free viewing and exclusive content. The next episode is calling—will you answer? \uD83D\uDEA8",
                                        "Your Escape Starts Here! Lose yourself in Veedule’s addictive miniseries, where every episode packs drama, romance, and suspense. Perfect for a lazy afternoon or a late-night marathon. Subscribe now to enjoy unlimited streaming, zero ads, and instant updates. The story never stops—neither should you! \uD83C\uDF1F",
                                        "Unlock Unlimited Drama! Veedule brings you the most talked-about miniseries, blending passion, revenge, and pulse-pounding action. Whether you’re snuggled up or sneaking in an episode at work, we’ve got you covered. Upgrade today for ad-free binges and never miss a moment. Your next binge obsession is waiting! \uD83D\uDC40",
                                        "Romance, Revenge, and Riveting Twists!​​ Veedule’s miniseries are your ticket to nonstop entertainment. With daily updates, there’s always something new to devour. Renew your membership now for uninterrupted streaming, exclusive perks, and early access. The screen is set—press play and disappear into the drama! \uD83E\uDD8B",
                                        "Binge Like Never Before!​​ Veedule’s curated miniseries serve up the perfect mix of love, betrayal, and adrenaline-pumping action. Whether you’re napping or night-owl-ing, we keep you hooked. Subscribe today for ad-free bliss, unlimited access, and fresh drops daily. Your next favorite show is just a tap away! \uD83D\uDC96",
                                        "Get Hooked on Drama!​​ Veedule’s miniseries are packed with everything you crave—sizzling chemistry, shocking betrayals, and cliffhangers galore. Perfect for a quick break or an all-nighter. Upgrade now to skip the ads, unlock premium content, and stay ahead of the buzz. The binge starts here! \uD83D\uDE31",
                                        "Late-Night Binges, Zero Regrets!​​ Veedule’s miniseries serve up the perfect blend of passion, suspense, and addictive storytelling. Whether you’re relaxing or procrastinating, we’ve got your fix. Renew today for seamless streaming, no interruptions, and instant updates. Your next guilty pleasure is ready—dive in! \uD83D\uDC4C",
                                        "Drama So Good, You’ll Forget Reality!​​ Veedule’s miniseries are your escape into worlds of love, revenge, and unmissable twists. Daily updates mean fresh thrills whenever you need them. Subscribe now for ad-free viewing, exclusive access, and nonstop entertainment. The remote’s in your hands—what’s your next move? \uD83D\uDE0E"};
                            }


                            // 创建 Random 对象
                            Random random = new Random();
                            // 随机获取数组索引（0 到 length-1）
                            int randomIndex = random.nextInt(fruits.length);
                            // 获取随机值
                            String randomFruit = fruits[randomIndex];

                            int randomFourDigit1 = ThreadLocalRandom.current().nextInt(1000, 10000);
                            int randomFourDigit2 = ThreadLocalRandom.current().nextInt(1000, 10000);
                            int randomFourDigit3 = ThreadLocalRandom.current().nextInt(1000, 10000);
                            int randomFourDigit4 = ThreadLocalRandom.current().nextInt(1000, 10000);
                            int randomFourDigit5 = ThreadLocalRandom.current().nextInt(1000, 10000);
                            int randomFourDigit6 = ThreadLocalRandom.current().nextInt(1000, 10000);


                            ShortPage shortPage = new ShortPage();
                            shortPage.setAppId(String.valueOf(su.getAppId()));
                            shortPage.setCname("投诉和反馈");
                            List<ShortPage> shortPageList = shortPageService.selectShortPageList(shortPage);


                            String randomNum = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                            String randomNum1 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);

                            String movieUrl1 = shortEmailDomain.getAppDomain() + "/video/?id=" + randomFourDigit1 + shortMovieList.get(0).getId() + "&userId=" + randomNum + su.getHashId() + randomNum1 + "&uniqueId=" + su.getUniqueId() + "&language=" + shortSemLink.getLanguage();

                            String randomNum2 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                            String randomNum3 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                            String movieUrl2 = shortEmailDomain.getAppDomain() + "/video/?id=" + randomFourDigit2 + shortMovieList.get(1).getId() + "&userId=" + randomNum2 + su.getHashId() + randomNum3 + "&uniqueId=" + su.getUniqueId() + "&language=" + shortSemLink.getLanguage();


                            String randomNum4 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                            String randomNum5 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                            String movieUrl3 = shortEmailDomain.getAppDomain() + "/video/?id=" + randomFourDigit3 + shortMovieList.get(2).getId() + "&userId=" + randomNum4 + su.getHashId() + randomNum5 + "&uniqueId=" + su.getUniqueId() + "&language=" + shortSemLink.getLanguage();


                            String randomNum6 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                            String randomNum7 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                            String movieUrl4 = shortEmailDomain.getAppDomain() + "/video/?id=" + randomFourDigit4 + shortMovieList.get(3).getId() + "&userId=" + randomNum6 + su.getHashId() + randomNum7 + "&uniqueId=" + su.getUniqueId() + "&language=" + shortSemLink.getLanguage();

                            String randomNum8 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                            String randomNum9 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                            String movieUrl5 = shortEmailDomain.getAppDomain() + "/video/?id=" + randomFourDigit5 + shortMovieList.get(4).getId() + "&userId=" + randomNum8 + su.getHashId() + randomNum9 + "&uniqueId=" + su.getUniqueId() + "&language=" + shortSemLink.getLanguage();

                            String randomNum10 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                            String randomNum11 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                            String movieUrl6 = shortEmailDomain.getAppDomain() + "/video/?id=" + randomFourDigit6 + shortMovieList.get(5).getId() + "&userId=" + randomNum10 + su.getHashId() + randomNum11 + "&uniqueId=" + su.getUniqueId() + "&language=" + shortSemLink.getLanguage();


                            String randomNumApp1 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                            String randomNumApp2 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                            String dayType = originalOrder.getSubscriptionType();
                            Date payTime = originalOrder.getPayTime();
                            BigDecimal buyPrice = originalOrder.getPaymentAmount();
                            BigDecimal renewalPrice = originalOrder.getPaymentAmount();
//            http://localhost:3000/subscription-records/
                            String langechange = "";
                            if (null != shortSemLink)
                                langechange = shortSemLink.getLanguage();
                            String unSubUrl = shortEmailDomain.getAppDomain() + "/subscription-records/" + "?userId=" + randomNumApp1 + su.getHashId() + randomNumApp2 + "&uniqueId=" + su.getUniqueId() + "&language=" + langechange + "&noLogin=true";

                            String jumpNew = shortEmailDomain.getAppDomain() + "/new/" + "?userId=" + randomNumApp1 + su.getHashId() + randomNumApp2 + "&uniqueId=" + su.getUniqueId() + "&language=" + langechange;


                            // 使用更安全的占位符格式
                            htmlContent = htmlContent
                                    .replace("${appName}", null != shortEmailDomain.getAppName() ? shortEmailDomain.getAppName() : "")
                                    .replace("{{appName}}", null != shortEmailDomain.getAppName() ? shortEmailDomain.getAppName() : "")

                                    .replace("${movieIcon1}", null != shortMovieList.get(0).getIcon() ? shortMovieList.get(0).getIcon() : "")
                                    .replace("{{movieIcon1}}", null != shortMovieList.get(0).getIcon() ? shortMovieList.get(0).getIcon() : "")

                                    .replace("${movieIcon2}", null != shortMovieList.get(1).getIcon() ? shortMovieList.get(1).getIcon() : "")
                                    .replace("{{movieIcon2}}", null != shortMovieList.get(1).getIcon() ? shortMovieList.get(1).getIcon() : "")

                                    .replace("${movieIcon3}", null != shortMovieList.get(2).getIcon() ? shortMovieList.get(2).getIcon() : "")
                                    .replace("{{movieIcon3}}", null != shortMovieList.get(2).getIcon() ? shortMovieList.get(2).getIcon() : "1")

                                    .replace("${movieIcon4}", null != shortMovieList.get(3).getIcon() ? shortMovieList.get(3).getIcon() : "")
                                    .replace("{{movieIcon4}}", null != shortMovieList.get(3).getIcon() ? shortMovieList.get(3).getIcon() : "")

                                    .replace("${movieIcon5}", null != shortMovieList.get(4).getIcon() ? shortMovieList.get(4).getIcon() : "")
                                    .replace("{{movieIcon5}}", null != shortMovieList.get(4).getIcon() ? shortMovieList.get(4).getIcon() : "")

                                    .replace("${movieIcon6}", null != shortMovieList.get(5).getIcon() ? shortMovieList.get(5).getIcon() : "")
                                    .replace("{{movieIcon6}}", null != shortMovieList.get(5).getIcon() ? shortMovieList.get(5).getIcon() : "")

                                    .replace("${movieName1}", null != shortMovieList.get(0).getName() ? shortMovieList.get(0).getName() : "")
                                    .replace("{{movieName1}}", null != shortMovieList.get(0).getName() ? shortMovieList.get(0).getName() : "")

                                    .replace("${movieName2}", null != shortMovieList.get(1).getName() ? shortMovieList.get(1).getName() : "")
                                    .replace("{{movieName2}}", null != shortMovieList.get(1).getName() ? shortMovieList.get(1).getName() : "")

                                    .replace("${movieName3}", null != shortMovieList.get(2).getName() ? shortMovieList.get(2).getName() : "")
                                    .replace("{{movieName3}}", null != shortMovieList.get(2).getName() ? shortMovieList.get(2).getName() : "")

                                    .replace("${movieName4}", null != shortMovieList.get(3).getName() ? shortMovieList.get(3).getName() : "")
                                    .replace("{{movieName4}}", null != shortMovieList.get(3).getName() ? shortMovieList.get(3).getName() : "")

                                    .replace("${movieName5}", null != shortMovieList.get(4).getName() ? shortMovieList.get(4).getName() : "")
                                    .replace("{{movieName5}}", null != shortMovieList.get(4).getName() ? shortMovieList.get(4).getName() : "")

                                    .replace("${movieName6}", null != shortMovieList.get(5).getName() ? shortMovieList.get(5).getName() : "")
                                    .replace("{{movieName6}}", null != shortMovieList.get(5).getName() ? shortMovieList.get(5).getName() : "")

                                    .replace("${movieUrl1}", movieUrl1)
                                    .replace("{{movieUrl1}}", movieUrl1)

                                    .replace("${movieUrl2}", movieUrl2)
                                    .replace("{{movieUrl2}}", movieUrl2)

                                    .replace("${movieUrl3}", movieUrl3)
                                    .replace("{{movieUrl3}}", movieUrl3)

                                    .replace("${movieUrl4}", movieUrl4)
                                    .replace("{{movieUrl4}}", movieUrl4)

                                    .replace("${movieUrl5}", movieUrl5)
                                    .replace("{{movieUrl5}}", movieUrl5)
                                    .replace("${movieUrl6}", movieUrl6)
                                    .replace("{{movieUrl6}}", movieUrl6)

                                    .replace("${Introduction}", randomFruit)
                                    .replace("{{Introduction}}", randomFruit)

                                    .replace("${expireTime}", exTime)
                                    .replace("{{expireTime}}", exTime)

                                    .replace("${jumpNew}", jumpNew)
                                    .replace("{{jumpNew}}", jumpNew)

                                    .replace("${amount}", originalOrder.getAmount().toString())
                                    .replace("{{amount}}", originalOrder.getAmount().toString())

                                    .replace("${dayType}", dayType)
                                    .replace("{{dayType}}", dayType)
                                    .replace("${payTime}", payTime.toString())
                                    .replace("{{payTime}}", payTime.toString())
                                    .replace("${buyPrice}", buyPrice.toString())
                                    .replace("{{buyPrice}}", buyPrice.toString())
                                    .replace("${renewalPrice}", renewalPrice.toString())
                                    .replace("{{renewalPrice}}", renewalPrice.toString())
                                    .replace("${unSubUrl}", unSubUrl)
                                    .replace("{{unSubUrl}}", unSubUrl)

                                    .replace("${jumpUrl}", shortPageList.get(0).getUrl() + "?selevt=3&email=" + su.getEmail())
                                    .replace("{{jumpUrl}}", shortPageList.get(0).getUrl() + "?selevt=3&email=" + su.getEmail());

                            // 6. 创建邮件内容
                            email.setSubject(subject);
                            email.setHtmlContent(htmlContent); // 使用HTML内容

                            new Thread(() -> {
                                try {
                                    runlog.setState("1");
                                    String content = "";
                                    // 7. 发送邮件
                                    CreateSmtpEmail response = apiInstance.sendTransacEmail(email);
                                    System.out.println("订阅通知邮件发送成功！消息ID: " + response.getMessageId());
                                    content = String.format(",订阅通知邮件发送成功！消息ID: " + response.getMessageId() + "用户Id：" + su.getId() +
                                            ",邮件主题：" + "New drama recommendation" + ",收件人名称：" + "Valued Member" +
                                            ",收件人邮件：" + su.getEmail() + ",邮件服务商:brevo");
                                    runlog.setNote(response.toString());
                                    runlog.setContent(content);
                                    shortRunlogService.insertShortRunlog(runlog);

                                    ShortUserActivityLog shortUserActivityLog = new ShortUserActivityLog();
                                    shortUserActivityLog.setUserId(su.getId());
                                    shortUserActivityLog.setAppId(su.getAppId());
                                    shortUserActivityLog.setState("1");
                                    shortUserActivityLog.setContent("订阅通知邮件发送成功");
                                    shortUserActivityLog.setStatus("0");
                                    shortUserActivityLog.setCreateTime(DateUtils.getNowDate());
                                    shortUserActivityLogService.insertShortUserActivityLog(shortUserActivityLog);
                                } catch (Exception e) {
                                    runlog.setState("0");
                                    runlog.setContent(String.format(",订阅通知发送失败：" + e.getMessage() + ",邮件主题：" + "New drama recommendation" +
                                            ",收件人名称：" + "Valued Member" + ",收件人邮件：" + su.getEmail()) + ",邮件服务商:brevo");
                                    runlog.setNote(e.toString());
                                    shortRunlogService.insertShortRunlog(runlog);
                                    ShortUserActivityLog shortUserActivityLog = new ShortUserActivityLog();
                                    shortUserActivityLog.setUserId(su.getId());
                                    shortUserActivityLog.setAppId(su.getAppId());
                                    shortUserActivityLog.setState("1");
                                    shortUserActivityLog.setContent("订通知邮件发送失败");
                                    shortUserActivityLog.setStatus("0");
                                    shortUserActivityLog.setCreateTime(DateUtils.getNowDate());
                                    shortUserActivityLogService.insertShortUserActivityLog(shortUserActivityLog);
                                }
                            }).start();

                        } catch (IOException e) {
                            System.err.println("读取HTML文件失败: " + e.getMessage());
                            runlog.setState("0");
                            runlog.setContent(String.format(",读取HTML文件失败：" + e.getMessage() + ",邮件主题：" + "New drama recommendation" + ",收件人名称：" + "Valued Member" + ",收件人邮件：" + su.getEmail()));
                            runlog.setNote(e.toString());
                            shortRunlogService.insertShortRunlog(runlog);

                        } catch (Exception e) {
                            System.err.println("发送失败: " + e.getMessage());
                            runlog.setState("0");
                            runlog.setContent(String.format(",发送失败：" + e.getMessage() + ",邮件主题：" + "New drama recommendation" + ",收件人名称：" + "Valued Member" + ",收件人邮件：" + su.getId()));
                            runlog.setNote(e.toString());
                            shortRunlogService.insertShortRunlog(runlog);
                        }
                    }
                }

//================================
                shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
                return AjaxResult.success("操作成功=");
            }
            shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
            return AjaxResult.success("操作成功");

        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("status", "error");
            error.put("message", "Validation failed: " + e.getMessage());
            shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
            return AjaxResult.error("操作失败");
        }
    }

    @Override
    public AjaxResult sendForwardEmail(String publicKey) {
        int count = shortIntermedStatusService.countByName("sendForwardEmail");
        if (count > 0)
            return AjaxResult.success("自动统计首页数据，请勿重复操作");

        ShortIntermedStatus shortIntermedStatus = new ShortIntermedStatus();
        shortIntermedStatus.setName("sendForwardEmail");
        shortIntermedStatusService.insertShortIntermedStatus(shortIntermedStatus);


        try {
            String privateKey = "-----BEGIN OPENSSH PRIVATE KEY-----\n" +
                    "b3BlbnNzaC1rZXktdjEAAAAABG5vbmUAAAAEbm9uZQAAAAAAAAABAAAAMwAAAAtzc2gtZW\n" +
                    "QyNTUxOQAAACDdx+eInOus2G8pGSS+paM8QV5SkQdAXGh/e0Z7pRaDRwAAAJiIp66uiKeu\n" +
                    "rgAAAAtzc2gtZWQyNTUxOQAAACDdx+eInOus2G8pGSS+paM8QV5SkQdAXGh/e0Z7pRaDRw\n" +
                    "AAAEDJW6rFB140gGyRJZM+mqoIFj8HAGrGpnuWj+sCJa/06N3H54ic66zYbykZJL6lozxB\n" +
                    "********************************************************\n" +
                    "-----END OPENSSH PRIVATE KEY-----";

            // 2. 加载密钥对
            KeyPair keyPair = KeyPair.load(new JSch(), privateKey.getBytes(), null);
            byte[] derivedPublicKeyBlob = keyPair.getPublicKeyBlob();

            // 3. 解析用户公钥（按空格分割并解码Base64）
            String[] publicKeyParts = publicKey.split(" ");
            if (publicKeyParts.length < 2) {
                throw new IllegalArgumentException("Invalid public key format");
            }
            byte[] userPublicKeyBlob = Base64.getDecoder().decode(publicKeyParts[1]);

            // 4. 安全比对（避免时序攻击）
            boolean isMatch = MessageDigest.isEqual(derivedPublicKeyBlob, userPublicKeyBlob);
            if (isMatch) {
                log.info("验证密钥正确------定时发邮件");

                List<ShortUser> shortUserList = shortUserService.getByLaset();
                for (ShortUser su : shortUserList) {


                    ShortForwardEmailSend shortForwardEmailSend = new ShortForwardEmailSend();
                    shortForwardEmailSend.setCreateTime(DateUtils.getNowDate());
                    shortForwardEmailSend.setSentDate(DateUtils.getNowDate());
                    shortForwardEmailSend.setAppId(su.getAppId());


                    shortForwardEmailSend.setReEmail(su.getEmail());
                    ShortForwartEmailDomain shortForwartEmailDomain = new ShortForwartEmailDomain();
                    shortForwartEmailDomain.setAppId(su.getAppId());
                    List<ShortForwartEmailDomain> list = shortForwartEmailDomainMapper.selectShortForwartEmailDomainList(shortForwartEmailDomain);
                    if (!list.isEmpty()) {

                        ShortApp shortApp = shortAppService.selectShortAppById(list.get(0).getAppId());

                        String host = "smtp.forwardemail.net"; // Forward Email SMTP服务器
                        String username = list.get(0).getDomain(); // 你的Forward Email地址
                        String password = AESEncryption.decrypt(list.get(0).getPassword()); // 你的密码
                        shortForwardEmailSend.setDomain(username);

                        Properties props = new Properties();
                        props.put("mail.smtp.host", host);
                        props.put("mail.smtp.port", "465");
                        props.put("mail.smtp.ssl.enable", "true");
                        props.put("mail.smtp.auth", "true");


                        // 记录日志并处理结果
                        ShortRunlog runlog = new ShortRunlog();
                        runlog.setType("发送订阅通知邮件");
                        runlog.setCreateTime(DateUtils.getNowDate());
                        runlog.setUpdateTime(DateUtils.getNowDate());

                        try {
                            // 创建会话
                            Session session = Session.getInstance(props,
                                    new Authenticator() {
                                        protected PasswordAuthentication getPasswordAuthentication() {
                                            return new PasswordAuthentication(username, password);
                                        }
                                    });

                            // 创建邮件
                            Message message = new MimeMessage(session);
                            message.setFrom(new InternetAddress(username));
                            message.setRecipients(Message.RecipientType.TO,
                                    InternetAddress.parse(shortForwardEmailSend.getReEmail()));
                            ShortSemLink shortSemLink = shortSemLinkService.selectShortSemLinkById(su.getLinkidId());

                            String[] dramaTitles = null;
                            if (null == shortSemLink || shortSemLink.getLanguage().equals("en-US")) {
                                dramaTitles = new String[]{
                                        "New Drama Recommendation",
                                        "The TV drama savior",
                                        "High-scoring new drama",
                                        "Hot drama list",
                                        "Guide to watching TV series",
                                        "A must-have for drama fans",
                                        "Good dramas",
                                        "New drama coming",
                                        "Drama Drama Terminator",
                                        "Benefits for TV drama fans"
                                };
                            } else if (shortSemLink.getLanguage().equals("es")) {
                                dramaTitles = new String[]{
                                        "Nueva recomendación de drama",
                                        "El salvador del drama televisivo",
                                        "Nuevo drama de alto puntaje",
                                        "Lista de dramas populares",
                                        "Guía para ver series de televisión",
                                        "Imprescindible para los amantes del drama.",
                                        "Buenos dramas",
                                        "Nuevo drama en camino",
                                        "Drama Drama Terminator",
                                        "Beneficios para los fanáticos de los dramas televisivos"
                                };
                            } else if (shortSemLink.getLanguage().equals("pt")) {
                                dramaTitles = new String[]{
                                        "Recomendação de novo drama",
                                        "O salvador do drama televisivo",
                                        "Novo drama de alta pontuação",
                                        "Lista de dramas quentes",
                                        "Guia para ver séries de TV",
                                        "Imperdível para os fãs de drama",
                                        "Bons dramas",
                                        "Novos dramas a chegar",
                                        "Drama: O Exterminador Implacável",
                                        "Benefícios para os fãs de dramas de TV"
                                };
                            } else if (shortSemLink.getLanguage().equals("zh-TW")) {
                                dramaTitles = new String[]{
                                        "新剧推荐",
                                        "剧迷救星",
                                        "高分新剧",
                                        "热剧榜单",
                                        "追剧攻略",
                                        "剧迷必备",
                                        "优质剧集",
                                        "新剧来袭",
                                        "剧终者",
                                        "剧迷专属"
                                };
                            } else if (shortSemLink.getLanguage().equals("ja")) {
                                dramaTitles = new String[]{
                                        "新作ドラマおすすめ",
                                        "テレビドラマの救世主",
                                        "高得点の新ドラマ",
                                        "注目ドラマリスト",
                                        "テレビシリーズ視聴ガイド",
                                        "ドラマファン必携",
                                        "良いドラマ",
                                        "新しいドラマが登場",
                                        "Drama Drama Terminator",
                                        "テレビドラマファンへの特典"
                                };
                            } else if (shortSemLink.getLanguage().equals("de")) {
                                dramaTitles = new String[]{
                                        "Empfehlung für neue Dramen",
                                        "Der Retter unter den TV-Dramen",
                                        "Bestseller-Dramen",
                                        "Liste der angesagtesten Dramen",
                                        "Leitfaden zum Serienschauen",
                                        "Ein Muss für Drama-Fans",
                                        "Gute Dramen",
                                        "Neue Dramen im Kommen",
                                        "Drama-Drama Terminator",
                                        "Vorteile für Drama-Fans"
                                };
                            }

                            Random random1 = new Random();
                            int randomIndex1 = random1.nextInt(dramaTitles.length);
                            String subject = dramaTitles[randomIndex1];

                            message.setSubject(subject);
                            shortForwardEmailSend.setSubject(subject);

                            String htmlName = "";

                            if (StringUtils.isNotEmpty(su.getSource())) {
                                if (null == shortSemLink || shortSemLink.getLanguage().equals("en-US"))
                                    htmlName = "former_noticeEmail.html";
                                else if (shortSemLink.getLanguage().equals("es"))
                                    htmlName = "es-former_noticeEmail.html";
                                else if (shortSemLink.getLanguage().equals("pt"))
                                    htmlName = "pt-former_noticeEmail.html";
                                else if (shortSemLink.getLanguage().equals("zh-TW"))
                                    htmlName = "zh-TW-former_noticeEmail.html";
                                else if (shortSemLink.getLanguage().equals("ja"))
                                    htmlName = "ja-former_noticeEmail.html";
                                else if (shortSemLink.getLanguage().equals("de"))
                                    htmlName = "de-former_noticeEmail.html";
                            } else {
                                if (null == shortSemLink || shortSemLink.getLanguage().equals("en-US"))
                                    htmlName = "noticeEmail.html";
                                else if (shortSemLink.getLanguage().equals("es"))
                                    htmlName = "es-noticeEmail.html";
                                else if (shortSemLink.getLanguage().equals("pt"))
                                    htmlName = "pt-noticeEmail.html";
                                else if (shortSemLink.getLanguage().equals("zh-TW"))
                                    htmlName = "zh-TW-noticeEmail.html";
                                else if (shortSemLink.getLanguage().equals("ja"))
                                    htmlName = "ja-noticeEmail.html";
                                else if (shortSemLink.getLanguage().equals("de"))
                                    htmlName = "de-noticeEmail.html";
                            }


                            ClassPathResource resource = new ClassPathResource("templates/" + htmlName);
                            String htmlContent = StreamUtils.copyToString(
                                    resource.getInputStream(),
                                    StandardCharsets.UTF_8
                            );
                            ShortOrder originalOrder = null;
                            ShortOrder lastSubRe = shortOrderService.findFirstByUserAndPay_typeAndStatusOrderByUpdatedAtDesc(su.getId(), "订阅续费", "SUCCEEDED");
                            if (null != lastSubRe) {
                                originalOrder = lastSubRe;
                            } else {
                                ShortOrder lastSub = shortOrderService.findFirstByUserAndPay_typeAndStatusOrderByUpdatedAtDesc(su.getId(), "订阅", "SUCCEEDED");
                                if (null != lastSub)
                                    originalOrder = lastSub;
                            }

                            List<ShortMovie> shortMovieList = new ArrayList<>();

                            if (null == shortSemLink || shortSemLink.getLanguage().equals("en-US"))
                                shortMovieList = shortMovieService.getByRand4(su.getAppId());
                            else
                                shortMovieList = shortMovieService.getByRand4I18n(su.getAppId(), shortSemLink.getLanguage());


                            // 定义一个数组
                            String[] fruits = null;
                            if (null == shortSemLink || shortSemLink.getLanguage().equals("en-US")) {
                                fruits = new String[]{
                                        "It's packed with drama, romance, revenge and thrills. Veedule brings you daily updates of the hottest miniseries, perfect for a relaxing nap or a late-night binge Renew your membership today to unlock unlimited access, ad-free streaming and never miss an update. Your next hit is just a tap away! \uD83D\uDE0D",
                                        "Binge-Worthy Bliss Awaits! Dive into heart-stopping drama, sizzling romance, and edge-of-your-seat suspense with Veedule’s exclusive miniseries. Whether you're unwinding after work or burning the midnight oil, our daily updates keep you hooked. Upgrade now for ad-free streaming, unlimited access, and early releases. Your next obsession is just one click away—don’t miss out! \uD83D\uDD25",
                                        "Passion, Betrayal, and Nonstop Thrills! Veedule delivers the hottest miniseries to fuel your binge cravings. From steamy love triangles to jaw-dropping plot twists, we’ve got your entertainment covered. Renew your membership today for seamless, ad-free viewing and exclusive content. The next episode is calling—will you answer? \uD83D\uDEA8",
                                        "Your Escape Starts Here! Lose yourself in Veedule’s addictive miniseries, where every episode packs drama, romance, and suspense. Perfect for a lazy afternoon or a late-night marathon. Subscribe now to enjoy unlimited streaming, zero ads, and instant updates. The story never stops—neither should you! \uD83C\uDF1F",
                                        "Unlock Unlimited Drama! Veedule brings you the most talked-about miniseries, blending passion, revenge, and pulse-pounding action. Whether you’re snuggled up or sneaking in an episode at work, we’ve got you covered. Upgrade today for ad-free binges and never miss a moment. Your next binge obsession is waiting! \uD83D\uDC40",
                                        "Romance, Revenge, and Riveting Twists!​​ Veedule’s miniseries are your ticket to nonstop entertainment. With daily updates, there’s always something new to devour. Renew your membership now for uninterrupted streaming, exclusive perks, and early access. The screen is set—press play and disappear into the drama! \uD83E\uDD8B",
                                        "Binge Like Never Before!​​ Veedule’s curated miniseries serve up the perfect mix of love, betrayal, and adrenaline-pumping action. Whether you’re napping or night-owl-ing, we keep you hooked. Subscribe today for ad-free bliss, unlimited access, and fresh drops daily. Your next favorite show is just a tap away! \uD83D\uDC96",
                                        "Get Hooked on Drama!​​ Veedule’s miniseries are packed with everything you crave—sizzling chemistry, shocking betrayals, and cliffhangers galore. Perfect for a quick break or an all-nighter. Upgrade now to skip the ads, unlock premium content, and stay ahead of the buzz. The binge starts here! \uD83D\uDE31",
                                        "Late-Night Binges, Zero Regrets!​​ Veedule’s miniseries serve up the perfect blend of passion, suspense, and addictive storytelling. Whether you’re relaxing or procrastinating, we’ve got your fix. Renew today for seamless streaming, no interruptions, and instant updates. Your next guilty pleasure is ready—dive in! \uD83D\uDC4C",
                                        "Drama So Good, You’ll Forget Reality!​​ Veedule’s miniseries are your escape into worlds of love, revenge, and unmissable twists. Daily updates mean fresh thrills whenever you need them. Subscribe now for ad-free viewing, exclusive access, and nonstop entertainment. The remote’s in your hands—what’s your next move? \uD83D\uDE0E"};
                            } else if (shortSemLink.getLanguage().equals("es")) {
                                fruits = new String[]{
                                        "Está llena de drama, romance, venganza y emoción. Veedule te trae actualizaciones diarias de las miniseries más populares, perfectas para una siesta relajante o un atracón nocturno. Renueva tu membresía hoy para desbloquear acceso ilimitado, streaming sin anuncios y no perderte ninguna actualización. ¡Tu próximo éxito está a un solo clic! \uD83D\uDE0D",
                                        "¡Te espera una experiencia inolvidable! Sumérgete en un drama trepidante, un romance apasionante y un suspenso que te mantendrá en vilo con la miniserie exclusiva de Veedule. Ya sea que estés relajándote después del trabajo o trabajando hasta altas horas de la noche, nuestras actualizaciones diarias te mantendrán enganchado. Suscríbete ahora para disfrutar de streaming sin anuncios, acceso ilimitado y estrenos anticipados. ¡Tu próxima obsesión está a un solo clic, no te la pierdas! \uD83D\uDD25",
                                        "¡Pasión, traición y emociones sin límites! Veedule te trae las miniseries más excitantes para saciar tus antojos. Desde apasionantes triángulos amorosos hasta giros de trama impactantes, tenemos todo lo que necesitas para divertirte. Renueva tu membresía hoy mismo para disfrutar de una experiencia fluida, sin anuncios y con contenido exclusivo. El próximo episodio te llama, ¿responderás? \uD83D\uDEA8",
                                        "¡Tu escape empieza aquí! Sumérgete en la adictiva miniserie de Veedule, donde cada episodio rebosa drama, romance y suspenso. Perfecta para una tarde relajada o un maratón nocturno. Suscríbete ahora para disfrutar de streaming ilimitado, sin anuncios y actualizaciones instantáneas. ¡La historia nunca termina, tú tampoco deberías! \uD83C\uDF1F",
                                        "¡Desbloquea drama ilimitado! Veedule te trae la miniserie más comentada, que combina pasión, venganza y acción trepidante. Ya sea que estés en casa o viendo un episodio en el trabajo, te tenemos cubierto. Suscríbete hoy para disfrutar de maratones sin anuncios y no te pierdas ni un momento. ¡Tu próxima obsesión te espera! \uD83D\uDC40",
                                        "¡Romance, venganza y giros emocionantes! Las miniseries de Veedule son tu boleto al entretenimiento sin fin. Con actualizaciones diarias, siempre hay algo nuevo para disfrutar. Renueva tu membresía ahora y disfruta de streaming ininterrumpido, beneficios exclusivos y acceso anticipado. ¡Listo! Dale al play y sumérgete en el drama. \uD83E\uDD8B",
                                        "¡Disfruta como nunca! Las miniseries seleccionadas de Veedule ofrecen la mezcla perfecta de amor, traición y acción a raudales. Ya sea que estés durmiendo la siesta o trasnochando, te mantendremos enganchado. Suscríbete hoy para disfrutar de la felicidad sin anuncios, acceso ilimitado y novedades diarias. ¡Tu próxima serie favorita está a un solo clic! \uD83D\uDC96",
                                        "¡Enganchate al drama! Las miniseries de Veedule están repletas de todo lo que buscas: química apasionante, traiciones impactantes y un montón de momentos de suspense. Perfectas para un descanso rápido o para trasnochar. Suscríbete ahora para evitar los anuncios, desbloquear contenido premium y estar a la vanguardia. ¡El maratón empieza aquí! \uD83D\uDE31",
                                        "¡Atracones nocturnos sin remordimientos! La miniserie de Veedule ofrece la combinación perfecta de pasión, suspenso y una narrativa adictiva. Ya sea que te relajes o procrastines, tenemos lo que buscas. Renueva hoy para disfrutar de streaming sin interrupciones y actualizaciones instantáneas. ¡Tu próximo placer culpable está listo! ¡Sumérgete! \uD83D\uDC4C",
                                        "¡Un drama tan bueno que olvidarás la realidad! Las miniseries de Veedule son tu escape a mundos de amor, venganza y giros inesperados. Actualizaciones diarias para nuevas emociones cuando las necesites. Suscríbete ahora para ver sin anuncios, acceso exclusivo y entretenimiento sin interrupciones. El control remoto está en tus manos: ¿cuál es tu siguiente paso? \uD83D\uDE0E"};
                            } else if (shortSemLink.getLanguage().equals("pt")) {
                                fruits = new String[]{
                                        "Repleto de drama, romance, vingança e emoção. O Veedule traz atualizações diárias das minisséries mais quentes, perfeitas para uma sesta relaxante ou uma maratona de madrugada. Renove a sua subscrição hoje mesmo para desbloquear o acesso ilimitado, o streaming sem anúncios e nunca perder uma atualização. O seu próximo sucesso está a apenas um toque de distância! \uD83D\uDE0D",
                                        "Uma maratona de felicidade espera por si! Mergulhe em dramas de cortar a respiração, romances eletrizantes e suspense de cortar a respiração com a minissérie exclusiva da Veedule. Seja relaxar depois do trabalho ou virar a noite, as nossas atualizações diárias mantêm-no ligado. Subscreva agora para streaming sem anúncios, acesso ilimitado e lançamentos antecipados. A sua próxima obsessão está à distância de um clique — não perca! \uD83D\uDD25",
                                        "Paixão, Traição e Emoções Ininterruptas! A Veedule traz as minisséries mais quentes para satisfazer a sua vontade de ver. De triângulos amorosos picantes a reviravoltas de fazer cair o queixo, temos tudo o que precisa para se divertir. Renove a sua subscrição hoje mesmo para uma visualização sem anúncios e conteúdo exclusivo. O próximo episódio está a chamar — atendes? \uD83D\uDEA8",
                                        "A Sua Fuga Começa Aqui! Perca-se na viciante minissérie da Veedule, onde cada episódio traz drama, romance e suspense. Perfeito para uma tarde preguiçosa ou uma maratona noturna. Subscreva agora para desfrutar de streaming ilimitado, sem anúncios e atualizações instantâneas. A história nunca pára — e você também não devia! \uD83C\uDF1F",
                                        "Desbloqueia Drama Ilimitado! A Veedule traz-lhe a minissérie mais falada, que mistura paixão, vingança e ação de cortar a respiração. Seja para se aconchegar ou para ver um episódio no trabalho, temos tudo o que precisa. Subscreva hoje mesmo maratonas sem anúncios e não perca nenhum momento. A sua próxima obsessão por maratonas está à espera! \uD83D\uDC40",
                                        "Romance, vingança e reviravoltas eletrizantes! As minisséries da Veedule são o seu bilhete para o entretenimento sem fim. Com as atualizações diárias, há sempre algo novo para devorar. Renove já a sua subscrição para streaming ininterrupto, vantagens exclusivas e acesso antecipado. O ecrã está pronto — aperte o play e mergulhe no drama! \uD83E\uDD8B",
                                        "Maratona como nunca antes! As minisséries selecionadas da Veedule oferecem a mistura perfeita de amor, traição e ação cheia de adrenalina. Seja a dormitar ou a dormir, nós mantemos-te ligado. Subscreva hoje mesmo uma experiência sem anúncios, acesso ilimitado e novos lançamentos todos os dias. O seu próximo programa favorito está à distância de um toque! \uD83D\uDC96",
                                        "Vicie-se em drama! As minisséries da Veedule estão repletas de tudo o que se deseja: química intensa, traições chocantes e suspense interminável. Perfeitas para uma pausa rápida ou para uma noite em branco. Subscreva já para ignorar os anúncios, desbloquear conteúdo premium e ficar a par das novidades. A maratona começa aqui! \uD83D\uDE31",
                                        "Maratonas sem arrependimentos! A minissérie da Veedule oferece a mistura perfeita de paixão, suspense e narrativa viciante. Seja para relaxar ou procrastinar, temos a solução. Renove hoje mesmo para streaming sem interrupções e atualizações instantâneas. O seu próximo guilty pleasure está pronto — mergulhe de cabeça! \uD83D\uDC4C",
                                        "Drama tão bom que vais esquecer a realidade! As minisséries da Veedule são a sua fuga para mundos de amor, vingança e reviravoltas imperdíveis. As atualizações diárias trazem novas emoções sempre que precisar. Subscreva agora para assistir sem anúncios, acesso exclusivo e entretenimento sem parar. O comando está nas suas mãos — qual é o seu próximo passo? \uD83D\uDE0E"};
                            } else if (shortSemLink.getLanguage().equals("zh-TW")) {
                                fruits = new String[]{
                                        "它充满了戏剧性、浪漫、复仇和刺激。Veedule 每日更新最热门的迷你剧，无论是放松小憩还是深夜狂欢，都是您的完美选择。立即续订您的会员资格，即可解锁无限访问、无广告播放，绝不错过任何更新。只需轻轻一点，即可观看下一部热门剧集！\uD83D\uDE0D",
                                        "值得一看的极致享受，等你来！Veedule 独家迷你剧集，带你沉浸于扣人心弦的剧情、火热的爱情和扣人心弦的悬念之中。无论您是下班后放松身心，还是熬夜加班，我们的每日更新都能让您欲罢不能。立即升级，享受无广告流媒体、无限畅享和抢先体验。只需点击一下，即可开启您的下一部心头好——不容错过！\uD83D\uDD25",
                                        "激情、背叛，以及无尽的刺激！Veedule 为您带来最火爆的迷你剧，满足您的无限遐想。从火辣的三角恋到令人瞠目结舌的剧情转折，我们应有尽有，满足您的娱乐需求。立即续订您的会员资格，即可享受流畅无广告的观看体验和独家内容。下一集即将到来——您愿意接听吗？\uD83D\uDEA8",
                                        "你的逃亡之旅从这里开始！沉浸于 Veedule 这部引人入胜的迷你剧，每一集都充满戏剧性、浪漫和悬念。无论是慵懒的午后还是深夜狂欢，它都是完美之选。立即订阅，即可享受无限畅享、零广告和即时更新。故事永不停歇——你也应该如此！\uD83C\uDF1F",
                                        "解锁无限精彩！Veedule 为您带来最受热议的迷你剧，融合激情、复仇和扣人心弦的动作场面。无论您是窝在床上，还是在工作时偷偷观看，我们都能满足您的需求。立即升级，畅享无广告畅看，不错过任何精彩瞬间。您的下一个狂欢盛宴正在等待您！\uD83D\uDC40",
                                        "浪漫、复仇，还有扣人心弦的剧情反转！Veedule 的迷你剧集带你畅享不间断的娱乐体验。每日更新，总有新鲜内容等你来探索。立即续订会员资格，即可享受不间断的流媒体播放、专属礼遇和抢先体验。屏幕已设置好——按下播放键，沉浸在精彩的剧情中吧！\uD83E\uDD8B",
                                        "前所未有的畅快体验！Veedule 精心策划的迷你剧集完美融合了爱情、背叛和刺激的动作场面。无论您是午睡还是夜猫子，我们都能让您欲罢不能。立即订阅，畅享无广告畅享、无限畅看，每日新鲜内容。只需轻轻一点，即可观看下一部心仪剧集！\uD83D\uDC96",
                                        "沉迷于戏剧！Veedule 的迷你剧集满足你所有渴望——激情四射的化学反应、令人震惊的背叛以及层出不穷的悬念。非常适合短暂休息或彻夜狂欢。立即升级，跳过广告，解锁高级内容，并保持领先地位。狂欢从这里开始！\uD83D\uDE31",
                                        "深夜狂欢，绝不后悔！Veedule 的迷你剧集完美融合了激情、悬念和引人入胜的故事情节。无论您是想放松身心还是想拖延时间，我们都能满足您的需求。立即续订，畅享流畅流畅的流媒体体验，无中断，即时更新。您的下一个罪恶快感已准备就绪——快来体验吧！\uD83D\uDC4C",
                                        "精彩剧集，让你忘却现实！Veedule 的迷你剧集带你逃离现实，进入充满爱、复仇和精彩剧情的奇幻世界。每日更新，随时随地为你带来新鲜刺激。立即订阅，畅享无广告观看、独家访问和不间断娱乐。遥控器就在你手中——下一步该怎么做？\uD83D\uDE0E"};
                            } else if (shortSemLink.getLanguage().equals("ja")) {
                                fruits = new String[]{
                                        "ドラマ、ロマンス、復讐、そしてスリルが満載。Veeduleでは、話題のミニシリーズを毎日更新でお届けします。ゆったりとした昼寝や深夜のビンジウォッチングに最適です。今すぐメンバーシップを更新して、無制限アクセス、広告なしのストリーミング、そして最新情報の見逃しを防止しましょう。次のヒット作は、タップひとつですぐそこ！\uD83D\uDE0D",
                                        "一気見したくなるような至福が待っています！Veeduleの独占ミニシリーズで、胸が締め付けられるようなドラマ、燃え上がるロマンス、そして手に汗握るサスペンスに飛び込みましょう。仕事帰りにくつろぎたい時も、夜更かししたい時も、毎日更新される番組で夢中になれます。今すぐアップグレードして、広告なしのストリーミング、無制限アクセス、先行配信をお楽しみください。次の夢中になる作品は、ワンクリックですぐそこ！お見逃しなく！\uD83D\uDD25",
                                        "情熱、裏切り、そしてノンストップのスリル！Veeduleは、あなたのビンジウォッチング欲求を満たす、話題のミニシリーズをお届けします。刺激的な三角関係から驚きのどんでん返しまで、あらゆるエンターテイメントを網羅。今すぐメンバーシップを更新して、シームレスで広告なしの視聴と限定コンテンツをお楽しみ下さい。次のエピソードがあなたを呼んでいます。あなたは応えられますか？\uD83D\uDEA8",
                                        "脱出劇はここから！Veeduleの病みつきになるミニシリーズに没頭しましょう。すべてのエピソードにドラマ、ロマンス、サスペンスが詰まっています。のんびりとした午後や深夜のマラソンにぴったりです。今すぐご登録ください。無制限のストリーミング、広告なし、そして即時更新をお楽しみいただけます。物語は決して止まりません。あなたも、止まるべきではありません！\uD83C\uDF1F",
                                        "ドラマを無制限に楽しもう！Veeduleがお届けする、情熱、復讐、そして手に汗握るアクションが融合した話題のミニシリーズ。寄り添って観る時も、職場でこっそり観る時も、Veeduleがあなたを応援します。今すぐアップグレードして広告なしのビンジ視聴で、一瞬たりとも見逃しません。次のビンジ視聴があなたを待っています！\uD83D\uDC40",
                                        "ロマンス、復讐、そして手に汗握るどんでん返し！Veeduleのミニシリーズは、ノンストップのエンターテイメントをお届けします。毎日更新されるので、いつでも新しい作品に出会うことができます。今すぐメンバーシップを更新して、途切れることのないストリーミング、限定特典、先行アクセスをお楽しみください。さあ、再生ボタンを押して、ドラマの世界へ飛び込みましょう！\uD83E\uDD8B",
                                        "かつてないほどのビンジ視聴！Veeduleが厳選したミニシリーズは、愛、裏切り、そしてアドレナリン全開のアクションが絶妙にミックス。お昼寝中でも夜更かし中でも、きっと夢中になれる作品ばかりです。今すぐご登録ください。広告なしの至福のひととき、無制限のアクセス、そして毎日更新される新作の数々をお楽しみいただけます。次のお気に入り番組は、タップひとつですぐ見つかります！\uD83D\uDC96",
                                        "ドラマに夢中になろう！Veeduleのミニシリーズには、あなたが求めているものがすべて詰まっています。刺激的な化学反応、衝撃の裏切り、そしてクリフハンガーの数々。ちょっとした休憩にも、徹夜にもぴったりです。今すぐアップグレードして広告をスキップし、プレミアムコンテンツをアンロックして、話題のドラマを先取りしましょう。一気見はここから！\uD83D\uDE31",
                                        "深夜のビンジウォッチング、後悔ゼロ！Veeduleのミニシリーズは、情熱、サスペンス、そして中毒性のあるストーリーテリングが完璧に融合した作品です。リラックスしたい時も、先延ばしにしたい時も、きっと満足していただけます。今すぐ更新して、シームレスなストリーミング、中断なし、そして即時更新をお楽しみください。次の罪悪感を抱く楽しみが待っています。さあ、飛び込んでみましょう！\uD83D\uDC4C",
                                        "最高に面白いドラマで、現実を忘れてしまう！Veeduleのミニシリーズは、愛と復讐、そして見逃せないどんでん返しの世界への逃避行。毎日更新されるので、いつでも新鮮なスリルを味わえます。今すぐ登録して、広告なしの視聴、限定アクセス、そしてノンストップのエンターテイメントをお楽しみください。リモコンはあなたの手の中に。さあ、次の一手は？\uD83D\uDE0E"};
                            } else if (shortSemLink.getLanguage().equals("de")) {
                                fruits = new String[]{
                                        "Es steckt voller Drama, Romantik, Rache und Nervenkitzel. Veedule bringt dir täglich Updates der heißesten Miniserien – perfekt für ein entspanntes Nickerchen oder einen nächtlichen Serienmarathon. Verlängere deine Mitgliedschaft noch heute, um unbegrenzten Zugriff und werbefreies Streaming zu erhalten und nie wieder ein Update zu verpassen. Dein nächster Hit ist nur einen Fingertipp entfernt! \uD83D\uDE0D",
                                        "Freu dich auf atemberaubende Dramatik, prickelnde Romantik und spannende Spannung mit Veedules exklusiver Miniserie. Ob zum Entspannen nach der Arbeit oder zum Feierabendbummel – unsere täglichen Updates fesseln dich. Jetzt upgraden für werbefreies Streaming, unbegrenzten Zugriff und Vorabveröffentlichungen. Deine nächste Leidenschaft ist nur einen Klick entfernt – verpass sie nicht! \uD83D\uDD25",
                                        "Leidenschaft, Verrat und Nervenkitzel pur! Veedule liefert die heißesten Miniserien, um deine Sehnsucht zu stillen. Von heißen Dreiecksbeziehungen bis hin zu atemberaubenden Wendungen – wir haben für jeden Geschmack etwas dabei. Verlängere deine Mitgliedschaft noch heute für werbefreies Fernsehen und exklusive Inhalte. Die nächste Folge ruft – antwortest du? \uD83D\uDEA8",
                                        "Deine Flucht beginnt hier! Tauche ein in Veedules fesselnde Miniserie, in der jede Folge voller Drama, Romantik und Spannung steckt. Perfekt für einen entspannten Nachmittag oder einen nächtlichen Marathon. Abonniere jetzt und genieße unbegrenztes Streaming, werbefreie Inhalte und sofortige Updates. Die Geschichte hört nie auf – und du solltest auch nicht aufhören! \uD83C\uDF1F",
                                        "Schalte grenzenloses Drama frei! Veedule präsentiert dir die meistdiskutierte Miniserie voller Leidenschaft, Rache und packender Action. Egal, ob du es dir gemütlich machst oder heimlich bei der Arbeit eine Folge schaust – wir haben alles für dich. Hol dir noch heute das Upgrade für werbefreie Serien und verpasse keinen Moment. Deine nächste Serien-Obsession wartet! \uD83D\uDC40",
                                        "Romantik, Rache und spannende Wendungen! Die Miniserien von Veedule sind deine Eintrittskarte für pausenlose Unterhaltung. Tägliche Updates sorgen für immer etwas Neues. Verlängere jetzt deine Mitgliedschaft für unterbrechungsfreies Streaming, exklusive Vorteile und frühen Zugriff. Der Bildschirm ist bereit – drücke auf Play und tauche ein ins Drama! \uD83E\uDD8B",
                                        "Binge wie nie zuvor! Die kuratierten Miniserien von Veedule bieten die perfekte Mischung aus Liebe, Verrat und adrenalingeladener Action. Egal ob du schläfst oder Nachteule bist, wir halten dich gefangen. Abonniere noch heute für werbefreies Vergnügen, unbegrenzten Zugriff und täglich neue Drops. Deine nächste Lieblingsserie ist nur einen Fingertipp entfernt! \uD83D\uDC96",
                                        "Werde süchtig nach Drama! Die Miniserien von Veedule bieten alles, was du dir wünschst – prickelnde Chemie, schockierende Verrätereien und jede Menge Cliffhanger. Perfekt für eine kurze Pause oder eine durchgemachte Nacht. Jetzt upgraden, um Werbung zu überspringen, Premium-Inhalte freizuschalten und immer auf dem Laufenden zu bleiben. Der Binge-Binge startet hier! \uD83D\uDE31",
                                        "Late-Night-Binges, keine Reue! Die Miniserien von Veedule bieten die perfekte Mischung aus Leidenschaft, Spannung und fesselndem Storytelling. Egal, ob du entspannst oder prokrastinierst, wir haben das Richtige für dich. Verlängere noch heute dein Abonnement für nahtloses Streaming, ohne Unterbrechungen und mit sofortigen Updates. Dein nächstes heimliches Vergnügen ist bereit – tauche ein! \uD83D\uDC4C",
                                        "Drama so gut, dass du die Realität vergisst! Die Miniserien von Veedule entführen dich in Welten voller Liebe, Rache und unvergesslicher Wendungen. Tägliche Updates sorgen für frischen Nervenkitzel, wann immer du ihn brauchst. Abonniere jetzt für werbefreies Fernsehen, exklusiven Zugriff und ununterbrochene Unterhaltung. Die Fernbedienung liegt in deiner Hand – was ist dein nächster Schritt? \uD83D\uDE0E"};
                            }
                            // 创建 Random 对象
                            Random random = new Random();
                            // 随机获取数组索引（0 到 length-1）
                            int randomIndex = random.nextInt(fruits.length);
                            // 获取随机值
                            String randomFruit = fruits[randomIndex];

                            int randomFourDigit1 = ThreadLocalRandom.current().nextInt(1000, 10000);
                            int randomFourDigit2 = ThreadLocalRandom.current().nextInt(1000, 10000);
                            int randomFourDigit3 = ThreadLocalRandom.current().nextInt(1000, 10000);
                            int randomFourDigit4 = ThreadLocalRandom.current().nextInt(1000, 10000);
                            int randomFourDigit5 = ThreadLocalRandom.current().nextInt(1000, 10000);
                            int randomFourDigit6 = ThreadLocalRandom.current().nextInt(1000, 10000);


                            ShortPage shortPage = new ShortPage();
                            shortPage.setAppId(String.valueOf(su.getAppId()));
                            shortPage.setCname("投诉和反馈");
                            List<ShortPage> shortPageList = shortPageService.selectShortPageList(shortPage);


                            String randomNum = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                            String randomNum1 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);

                            ShortEmailDomain shortEmailDomain = shortEmailDomainService.getrandomDomain(su.getAppId());
                            if (null == shortEmailDomain) {
                                continue;
                            }
                            String movieUrl1 = shortEmailDomain.getAppDomain() + "/video/?id=" + randomFourDigit1 + shortMovieList.get(0).getId() + "&userId=" + randomNum + su.getHashId() + randomNum1 + "&uniqueId=" + su.getUniqueId();

                            String randomNum2 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                            String randomNum3 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                            String movieUrl2 = shortEmailDomain.getAppDomain() + "/video/?id=" + randomFourDigit2 + shortMovieList.get(1).getId() + "&userId=" + randomNum2 + su.getHashId() + randomNum3 + "&uniqueId=" + su.getUniqueId();


                            String randomNum4 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                            String randomNum5 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                            String movieUrl3 = shortEmailDomain.getAppDomain() + "/video/?id=" + randomFourDigit3 + shortMovieList.get(2).getId() + "&userId=" + randomNum4 + su.getHashId() + randomNum5 + "&uniqueId=" + su.getUniqueId();


                            String randomNum6 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                            String randomNum7 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                            String movieUrl4 = shortEmailDomain.getAppDomain() + "/video/?id=" + randomFourDigit4 + shortMovieList.get(3).getId() + "&userId=" + randomNum6 + su.getHashId() + randomNum7 + "&uniqueId=" + su.getUniqueId();

                            String randomNum8 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                            String randomNum9 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                            String movieUrl5 = shortEmailDomain.getAppDomain() + "/video/?id=" + randomFourDigit5 + shortMovieList.get(4).getId() + "&userId=" + randomNum8 + su.getHashId() + randomNum9 + "&uniqueId=" + su.getUniqueId();

                            String randomNum10 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                            String randomNum11 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                            String movieUrl6 = shortEmailDomain.getAppDomain() + "/video/?id=" + randomFourDigit6 + shortMovieList.get(5).getId() + "&userId=" + randomNum10 + su.getHashId() + randomNum11 + "&uniqueId=" + su.getUniqueId();

                            // 使用更安全的占位符格式
                            htmlContent = htmlContent
                                    .replace("${appName}", null != shortApp.getName() ? shortApp.getName() : "")
                                    .replace("{{appName}}", null != shortApp.getName() ? shortApp.getName() : "")

                                    .replace("${movieIcon1}", null != shortMovieList.get(0).getIcon() ? shortMovieList.get(0).getIcon() : "")
                                    .replace("{{movieIcon1}}", null != shortMovieList.get(0).getIcon() ? shortMovieList.get(0).getIcon() : "")

                                    .replace("${movieIcon2}", null != shortMovieList.get(1).getIcon() ? shortMovieList.get(1).getIcon() : "")
                                    .replace("{{movieIcon2}}", null != shortMovieList.get(1).getIcon() ? shortMovieList.get(1).getIcon() : "")

                                    .replace("${movieIcon3}", null != shortMovieList.get(2).getIcon() ? shortMovieList.get(2).getIcon() : "")
                                    .replace("{{movieIcon3}}", null != shortMovieList.get(2).getIcon() ? shortMovieList.get(2).getIcon() : "1")

                                    .replace("${movieIcon4}", null != shortMovieList.get(3).getIcon() ? shortMovieList.get(3).getIcon() : "")
                                    .replace("{{movieIcon4}}", null != shortMovieList.get(3).getIcon() ? shortMovieList.get(3).getIcon() : "")

                                    .replace("${movieIcon5}", null != shortMovieList.get(4).getIcon() ? shortMovieList.get(4).getIcon() : "")
                                    .replace("{{movieIcon5}}", null != shortMovieList.get(4).getIcon() ? shortMovieList.get(4).getIcon() : "")

                                    .replace("${movieIcon6}", null != shortMovieList.get(5).getIcon() ? shortMovieList.get(5).getIcon() : "")
                                    .replace("{{movieIcon6}}", null != shortMovieList.get(5).getIcon() ? shortMovieList.get(5).getIcon() : "")

                                    .replace("${movieName1}", null != shortMovieList.get(0).getName() ? shortMovieList.get(0).getName() : "")
                                    .replace("{{movieName1}}", null != shortMovieList.get(0).getName() ? shortMovieList.get(0).getName() : "")

                                    .replace("${movieName2}", null != shortMovieList.get(1).getName() ? shortMovieList.get(1).getName() : "")
                                    .replace("{{movieName2}}", null != shortMovieList.get(1).getName() ? shortMovieList.get(1).getName() : "")

                                    .replace("${movieName3}", null != shortMovieList.get(2).getName() ? shortMovieList.get(2).getName() : "")
                                    .replace("{{movieName3}}", null != shortMovieList.get(2).getName() ? shortMovieList.get(2).getName() : "")

                                    .replace("${movieName4}", null != shortMovieList.get(3).getName() ? shortMovieList.get(3).getName() : "")
                                    .replace("{{movieName4}}", null != shortMovieList.get(3).getName() ? shortMovieList.get(3).getName() : "")

                                    .replace("${movieName5}", null != shortMovieList.get(4).getName() ? shortMovieList.get(4).getName() : "")
                                    .replace("{{movieName5}}", null != shortMovieList.get(4).getName() ? shortMovieList.get(4).getName() : "")

                                    .replace("${movieName6}", null != shortMovieList.get(5).getName() ? shortMovieList.get(5).getName() : "")
                                    .replace("{{movieName6}}", null != shortMovieList.get(5).getName() ? shortMovieList.get(5).getName() : "")

                                    .replace("${movieUrl1}", movieUrl1)
                                    .replace("{{movieUrl1}}", movieUrl1)

                                    .replace("${movieUrl2}", movieUrl2)
                                    .replace("{{movieUrl2}}", movieUrl2)

                                    .replace("${movieUrl3}", movieUrl3)
                                    .replace("{{movieUrl3}}", movieUrl3)

                                    .replace("${movieUrl4}", movieUrl4)
                                    .replace("{{movieUrl4}}", movieUrl4)

                                    .replace("${movieUrl5}", movieUrl5)
                                    .replace("{{movieUrl5}}", movieUrl5)
                                    .replace("${movieUrl6}", movieUrl6)
                                    .replace("{{movieUrl6}}", movieUrl6)

                                    .replace("${Introduction}", randomFruit)
                                    .replace("{{Introduction}}", randomFruit)

                                    .replace("${expireTime}", su.getExpireTime().toString())
                                    .replace("{{expireTime}}", su.getExpireTime().toString())

                                    .replace("${amount}", originalOrder.getAmount().toString())
                                    .replace("{{amount}}", originalOrder.getAmount().toString())

                                    .replace("${jumpUrl}", shortPageList.get(0).getUrl() + "?selevt=3&email=" + su.getEmail())
                                    .replace("{{jumpUrl}}", shortPageList.get(0).getUrl() + "?selevt=3&email=" + su.getEmail());


                            // 创建邮件正文部分
                            MimeBodyPart textPart = new MimeBodyPart();
                            // 组合 HTML 内容
                            textPart.setContent(htmlContent, "text/html; charset=utf-8");
                            // 组合各部分
                            Multipart multipart = new MimeMultipart();
                            multipart.addBodyPart(textPart);

                            message.setContent(multipart);
                            shortForwardEmailSend.setContent(htmlContent);


                            new Thread(() -> {
                                try {
                                    runlog.setState("1");
                                    String content = "";
                                    // 发送邮件
                                    Transport.send(message);
                                    content = String.format(",订阅通知邮件发送成功！ " + "用户Id：" + su.getId() +
                                            ",邮件主题：" + subject + ",收件人名称：" + "Valued Member" +
                                            ",收件人邮件：" + su.getEmail() + ",邮件服务商:Forward");
                                    runlog.setContent(content);
                                    shortRunlogService.insertShortRunlog(runlog);

                                    ShortUserActivityLog shortUserActivityLog = new ShortUserActivityLog();
                                    shortUserActivityLog.setUserId(su.getId());
                                    shortUserActivityLog.setAppId(su.getAppId());
                                    shortUserActivityLog.setState("1");
                                    shortUserActivityLog.setContent("订阅通知邮件发送成功");
                                    shortUserActivityLog.setStatus("0");
                                    shortUserActivityLog.setCreateTime(DateUtils.getNowDate());
                                    shortUserActivityLogService.insertShortUserActivityLog(shortUserActivityLog);

                                    shortForwardEmailSend.setType(1L);
                                    shortForwardEmailSendMapper.insertShortForwardEmailSend(shortForwardEmailSend);
                                } catch (Exception e) {
                                    runlog.setState("0");
                                    runlog.setContent(String.format(",订阅通知发送失败：" + e.getMessage() + ",邮件主题：" + subject +
                                            ",收件人名称：" + "Valued Member" + ",收件人邮件：" + su.getEmail()) + ",邮件服务商:Forward");
                                    runlog.setNote(e.toString());
                                    shortRunlogService.insertShortRunlog(runlog);
                                    ShortUserActivityLog shortUserActivityLog = new ShortUserActivityLog();
                                    shortUserActivityLog.setUserId(su.getId());
                                    shortUserActivityLog.setAppId(su.getAppId());
                                    shortUserActivityLog.setState("1");
                                    shortUserActivityLog.setContent("订通知邮件发送失败");
                                    shortUserActivityLog.setStatus("0");
                                    shortUserActivityLog.setCreateTime(DateUtils.getNowDate());
                                    shortUserActivityLogService.insertShortUserActivityLog(shortUserActivityLog);

                                    shortForwardEmailSend.setType(2L);
                                    shortForwardEmailSendMapper.insertShortForwardEmailSend(shortForwardEmailSend);
                                }
                            }).start();


                        } catch (Exception e) {
                            System.err.println("发送失败: " + e.getMessage());
                            runlog.setState("0");
                            runlog.setContent(String.format(",发送失败：" + e.getMessage() + ",邮件主题：" + "New drama recommendation" + ",收件人名称：" + "Valued Member" + ",收件人邮件：" + su.getId()));
                            runlog.setNote(e.toString());
                            shortRunlogService.insertShortRunlog(runlog);
                            shortForwardEmailSend.setType(2L);
                            shortForwardEmailSendMapper.insertShortForwardEmailSend(shortForwardEmailSend);
                        }
                    }


                }

                shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
                return AjaxResult.success("操作成功=");
            }
            shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
            return AjaxResult.success("操作成功");

        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("status", "error");
            error.put("message", "Validation failed: " + e.getMessage());
            shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
            return AjaxResult.error("操作失败");
        }
    }

    @Override
    public AjaxResult sendBackRegEmail(String publicKey) {
        int count = shortIntermedStatusService.countByName("sendBackRegEmail");
        if (count > 0)
            return AjaxResult.success("自动统计首页数据，请勿重复操作");

        ShortIntermedStatus shortIntermedStatus = new ShortIntermedStatus();
        shortIntermedStatus.setName("sendBackRegEmail");
        shortIntermedStatusService.insertShortIntermedStatus(shortIntermedStatus);


        try {
            String privateKey = "-----BEGIN OPENSSH PRIVATE KEY-----\n" +
                    "b3BlbnNzaC1rZXktdjEAAAAABG5vbmUAAAAEbm9uZQAAAAAAAAABAAAAMwAAAAtzc2gtZW\n" +
                    "QyNTUxOQAAACDdx+eInOus2G8pGSS+paM8QV5SkQdAXGh/e0Z7pRaDRwAAAJiIp66uiKeu\n" +
                    "rgAAAAtzc2gtZWQyNTUxOQAAACDdx+eInOus2G8pGSS+paM8QV5SkQdAXGh/e0Z7pRaDRw\n" +
                    "AAAEDJW6rFB140gGyRJZM+mqoIFj8HAGrGpnuWj+sCJa/06N3H54ic66zYbykZJL6lozxB\n" +
                    "********************************************************\n" +
                    "-----END OPENSSH PRIVATE KEY-----";

            // 2. 加载密钥对
            KeyPair keyPair = KeyPair.load(new JSch(), privateKey.getBytes(), null);
            byte[] derivedPublicKeyBlob = keyPair.getPublicKeyBlob();

            // 3. 解析用户公钥（按空格分割并解码Base64）
            String[] publicKeyParts = publicKey.split(" ");
            if (publicKeyParts.length < 2) {
                throw new IllegalArgumentException("Invalid public key format");
            }
            byte[] userPublicKeyBlob = Base64.getDecoder().decode(publicKeyParts[1]);

            // 4. 安全比对（避免时序攻击）
            boolean isMatch = MessageDigest.isEqual(derivedPublicKeyBlob, userPublicKeyBlob);
            if (isMatch) {
                log.info("验证密钥正确------定时发邮件");
//================================

                List<ShortUser> shortUserList = shortUserService.getBackRegEmail();
                for (ShortUser su : shortUserList) {

                    SendSmtpEmail email = new SendSmtpEmail();
                    TransactionalEmailsApi apiInstance = new TransactionalEmailsApi();
                    EmailDTO emailDTO = new EmailDTO();

                    // 3. 设置发件人
                    SendSmtpEmailSender sender = new SendSmtpEmailSender();
                    ShortEmailDomain shortEmailDomain = shortEmailDomainService.getrandomDomain(su.getAppId());

                    // 2. 初始化邮件API
                    ApiClient defaultClient = Configuration.getDefaultApiClient();
                    ApiKeyAuth apiKey = (ApiKeyAuth) defaultClient.getAuthentication("api-key");
                    apiKey.setApiKey(brevoApiKey); // 替换为你的Brevo API密钥

                    if (null != shortEmailDomain) {
                        sender.setEmail(shortEmailDomain.getDomain()); // 必须已验证的邮箱
                        sender.setName(shortEmailDomain.getAppName());
                    } else {
                        sender.setEmail(senderEmail); // 必须已验证的邮箱
                        sender.setName(senderName);
                    }


                    List<SendSmtpEmailTo> toList = new ArrayList<>();
                    // 4. 设置收件人
                    SendSmtpEmailTo recipient = new SendSmtpEmailTo();
                    recipient.setEmail(null != su.getEmail() ? su.getEmail() : null);
                    recipient.setName(null != su.getEmail() ? su.getEmail() : null);
                    toList.add(recipient);
                    email.setSender(sender);
                    email.setTo(toList);
                    // 记录日志并处理结果
                    ShortRunlog runlog = new ShortRunlog();
                    runlog.setType("发送邮件");
                    runlog.setCreateTime(DateUtils.getNowDate());
                    runlog.setUpdateTime(DateUtils.getNowDate());

                    try {
                        String htmlName = "backreg.html";


                        ClassPathResource resource = new ClassPathResource("templates/" + htmlName);
                        String htmlContent = StreamUtils.copyToString(
                                resource.getInputStream(),
                                StandardCharsets.UTF_8
                        );
                        String subject = "Welcome back to " + shortEmailDomain.getAppName();


                        String appDomain = null != shortEmailDomain.getAppDomain() ? shortEmailDomain.getAppDomain() : "";

                        String randomNumApp1 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                        String randomNumApp2 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
//            ?userId=前六   + +  后六&uniqueId=
                        ShortUser shortUser = shortUserService.selectShortUserById(su.getId());
                        if (null != shortUser)
                            appDomain = appDomain + "?userId=" + randomNumApp1 + shortUser.getHashId() + randomNumApp2 + "&uniqueId=" + shortUser.getUniqueId();

                        ShortMovie shortMovie = shortMovieService.getMovie1(shortUser.getAppId());
                        if (shortMovie == null)
                            continue;
                        // 使用更安全的占位符格式
                        htmlContent = htmlContent
                                .replace("${movieurl}", null != shortMovie.getIcon() ? shortMovie.getIcon() : "")
                                .replace("{{movieurl}}", null != shortMovie.getIcon() ? shortMovie.getIcon() : "")

                                .replace("${appName}", null != shortEmailDomain.getAppName() ? shortEmailDomain.getAppName() : "")
                                .replace("{{appName}}", null != shortEmailDomain.getAppName() ? shortEmailDomain.getAppName() : "")

                                .replace("${appDomain}", appDomain)
                                .replace("{{appDomain}}", appDomain);

                        // 6. 创建邮件内容
                        email.setSubject(subject);
                        email.setHtmlContent(htmlContent); // 使用HTML内容

                        new Thread(() -> {
                            try {
                                runlog.setState("1");
                                String content = "";
                                // 7. 发送邮件
                                CreateSmtpEmail response = apiInstance.sendTransacEmail(email);
                                System.out.println("邮件发送成功！消息ID: " + response.getMessageId());
                                content = String.format(",邮件发送成功！消息ID: " + response.getMessageId() + "用户Id：" + su.getId() +
                                        ",邮件主题：" + subject + ",收件人名称：" + su.getEmail() +
                                        ",收件人邮件：" + su.getEmail() + ",邮件服务商:brevo");
                                runlog.setNote(response.toString());
                                runlog.setContent(content);
                                shortRunlogService.insertShortRunlog(runlog);
                            } catch (Exception e) {
                                runlog.setState("0");
                                runlog.setContent(String.format(",发送失败：" + e.getMessage() + ",邮件主题：" + subject +
                                        ",收件人名称：" + su.getEmail() + ",收件人邮件：" + su.getEmail() + ",邮件服务商:brevo"));
                                runlog.setNote(e.toString());
                                shortRunlogService.insertShortRunlog(runlog);
                            }
                        }).start();

                    } catch (IOException e) {
                        System.err.println("读取HTML文件失败: " + e.getMessage());
                        runlog.setState("0");
                        runlog.setContent(String.format(",读取HTML文件失败：" + e.getMessage() + ",邮件主题：Welcome back" + ",收件人名称：" + su.getEmail() + ",收件人邮件：" + su.getEmail()));
                        runlog.setNote(e.toString());
                        shortRunlogService.insertShortRunlog(runlog);

                        return AjaxResult.error("读取HTML文件失败: " + e.getMessage());
                    } catch (Exception e) {
                        System.err.println("发送失败: " + e.getMessage());
                        runlog.setState("0");
                        runlog.setContent(String.format(",发送失败：" + e.getMessage() + ",邮件主题：Welcome back" + ",收件人名称：" + su.getEmail() + ",收件人邮件：" + su.getEmail()));
                        runlog.setNote(e.toString());
                        shortRunlogService.insertShortRunlog(runlog);
                        return AjaxResult.error("发送失败: " + e.getMessage());
                    }

                }

//================================
                shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
                return AjaxResult.success("操作成功=");
            }
            shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
            return AjaxResult.success("操作成功");

        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("status", "error");
            error.put("message", "Validation failed: " + e.getMessage());
            shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
            return AjaxResult.error("操作失败");
        }
    }

    @Override
    public AjaxResult sendNullUserEmailBrevo(String publicKey) {
        int count = shortIntermedStatusService.countByName("sendNullUserEmailBrevo");
        if (count > 0)
            return AjaxResult.success("自动统计首页数据，请勿重复操作");

        ShortIntermedStatus shortIntermedStatus = new ShortIntermedStatus();
        shortIntermedStatus.setName("sendNullUserEmailBrevo");
        shortIntermedStatusService.insertShortIntermedStatus(shortIntermedStatus);


        try {
            String privateKey = "-----BEGIN OPENSSH PRIVATE KEY-----\n" +
                    "b3BlbnNzaC1rZXktdjEAAAAABG5vbmUAAAAEbm9uZQAAAAAAAAABAAAAMwAAAAtzc2gtZW\n" +
                    "QyNTUxOQAAACDdx+eInOus2G8pGSS+paM8QV5SkQdAXGh/e0Z7pRaDRwAAAJiIp66uiKeu\n" +
                    "rgAAAAtzc2gtZWQyNTUxOQAAACDdx+eInOus2G8pGSS+paM8QV5SkQdAXGh/e0Z7pRaDRw\n" +
                    "AAAEDJW6rFB140gGyRJZM+mqoIFj8HAGrGpnuWj+sCJa/06N3H54ic66zYbykZJL6lozxB\n" +
                    "********************************************************\n" +
                    "-----END OPENSSH PRIVATE KEY-----";

            // 2. 加载密钥对
            KeyPair keyPair = KeyPair.load(new JSch(), privateKey.getBytes(), null);
            byte[] derivedPublicKeyBlob = keyPair.getPublicKeyBlob();

            // 3. 解析用户公钥（按空格分割并解码Base64）
            String[] publicKeyParts = publicKey.split(" ");
            if (publicKeyParts.length < 2) {
                throw new IllegalArgumentException("Invalid public key format");
            }
            byte[] userPublicKeyBlob = Base64.getDecoder().decode(publicKeyParts[1]);

            // 4. 安全比对（避免时序攻击）
            boolean isMatch = MessageDigest.isEqual(derivedPublicKeyBlob, userPublicKeyBlob);
            if (isMatch) {
                log.info("验证密钥正确------定时发邮件");
//================================

                List<ShortForwardEmailReceive> shortUserList = shortForwardEmailReceiveService.getSendNullUser();
                for (ShortForwardEmailReceive forwardEmailReceive : shortUserList) {
                    SendSmtpEmail email = new SendSmtpEmail();
                    TransactionalEmailsApi apiInstance = new TransactionalEmailsApi();

                    // 3. 设置发件人
                    SendSmtpEmailSender sender = new SendSmtpEmailSender();
                    ShortEmailDomain shortEmailDomain = shortEmailDomainService.getrandomDomain(forwardEmailReceive.getAppId());

                    if (null != shortEmailDomain) {
                        // 2. 初始化邮件API
                        ApiClient defaultClient = Configuration.getDefaultApiClient();
                        ApiKeyAuth apiKey = (ApiKeyAuth) defaultClient.getAuthentication("api-key");
                        apiKey.setApiKey(brevoApiKey); // 替换为你的Brevo API密钥

                        if (null != shortEmailDomain) {
                            sender.setEmail(shortEmailDomain.getDomain()); // 必须已验证的邮箱
                            sender.setName(shortEmailDomain.getAppName());
                        } else {
                            sender.setEmail(senderEmail); // 必须已验证的邮箱
                            sender.setName(senderName);
                        }

                        List<SendSmtpEmailTo> toList = new ArrayList<>();
                        // 4. 设置收件人
                        SendSmtpEmailTo recipient = new SendSmtpEmailTo();
                        recipient.setEmail(null != forwardEmailReceive.getEmail() ? forwardEmailReceive.getEmail() : null);
                        toList.add(recipient);
                        email.setSender(sender);
                        email.setTo(toList);
                        // 记录日志并处理结果
                        ShortRunlog runlog = new ShortRunlog();
                        runlog.setType("自动回复无账户邮件");
                        runlog.setCreateTime(DateUtils.getNowDate());
                        runlog.setUpdateTime(DateUtils.getNowDate());


                        try {

                            recipient.setName("Valued Member");

                            // 6. 创建邮件内容
                            email.setSubject("Please Provide Your Correct Registered Email Address");
                            email.setTextContent("Hi Valued Member,\n" +
                                    "\n" +
                                    "Thank you for reaching out to us.\n" +
                                    "It seems that the email address you provided is incomplete or incorrect.\n" +
                                    "Could you please reply with the exact email address you used when registering your account, so we can verify your information and assist you further?\n" +
                                    "\n" +
                                    "Looking forward to your reply.\n" +
                                    "\n" +
                                    "Best regards"); // 使用HTML内容

                            new Thread(() -> {
                                try {
                                    runlog.setState("1");
                                    String content = "";
                                    // 7. 发送邮件
                                    CreateSmtpEmail response = apiInstance.sendTransacEmail(email);
                                    System.out.println("自动回复无账户邮件发送成功！消息ID: " + response.getMessageId());
                                    content = String.format("自动回复无账户邮件发送成功！消息ID: " + response.getMessageId() +
                                            ",邮件主题：" + email.getSubject() + ",收件人名称：" + "Valued Member" +
                                            ",收件人邮件：" + forwardEmailReceive.getEmail() + ",邮件服务商:brevo");
                                    runlog.setNote(response.toString());
                                    runlog.setContent(content);
                                    shortRunlogService.insertShortRunlog(runlog);

                                    forwardEmailReceive.setType("2");
                                    forwardEmailReceive.setRemark("自动回复无账户邮件");
                                    shortForwardEmailReceiveService.updateShortForwardEmailReceive(forwardEmailReceive);
                                } catch (Exception e) {
                                    runlog.setState("0");
                                    runlog.setContent(String.format("自动回复无账户邮件发送失败：" + e.getMessage() + ",邮件主题：" + "Please Provide Your Correct Registered Email Address" +
                                            ",收件人名称：" + "Valued Member" + ",收件人邮件：" + forwardEmailReceive.getEmail()) + ",邮件服务商:brevo");
                                    runlog.setNote(e.toString());
                                    shortRunlogService.insertShortRunlog(runlog);

                                }
                            }).start();

                        } catch (Exception e) {
                            System.err.println("发送失败: " + e.getMessage());
                            runlog.setState("0");
                            runlog.setContent(String.format(",发送失败：" + e.getMessage() + ",邮件主题：" + "Please Provide Your Correct Registered Email Address" + ",收件人名称：" + "Valued Member" + ",收件人邮件：" + forwardEmailReceive.getId()));
                            runlog.setNote(e.toString());
                            shortRunlogService.insertShortRunlog(runlog);
                        }
                    }
                }

//================================
                shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
                return AjaxResult.success("操作成功=");
            }
            shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
            return AjaxResult.success("操作成功");

        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("status", "error");
            error.put("message", "Validation failed: " + e.getMessage());
            shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
            return AjaxResult.error("操作失败");
        }
    }

    @Override
    public AjaxResult sendNullUserEmailForward(String publicKey) {
        int count = shortIntermedStatusService.countByName("sendNullUserEmailForward");
        if (count > 0)
            return AjaxResult.success("自动统计首页数据，请勿重复操作");

        ShortIntermedStatus shortIntermedStatus = new ShortIntermedStatus();
        shortIntermedStatus.setName("sendNullUserEmailForward");
        shortIntermedStatusService.insertShortIntermedStatus(shortIntermedStatus);


        try {
            String privateKey = "-----BEGIN OPENSSH PRIVATE KEY-----\n" +
                    "b3BlbnNzaC1rZXktdjEAAAAABG5vbmUAAAAEbm9uZQAAAAAAAAABAAAAMwAAAAtzc2gtZW\n" +
                    "QyNTUxOQAAACDdx+eInOus2G8pGSS+paM8QV5SkQdAXGh/e0Z7pRaDRwAAAJiIp66uiKeu\n" +
                    "rgAAAAtzc2gtZWQyNTUxOQAAACDdx+eInOus2G8pGSS+paM8QV5SkQdAXGh/e0Z7pRaDRw\n" +
                    "AAAEDJW6rFB140gGyRJZM+mqoIFj8HAGrGpnuWj+sCJa/06N3H54ic66zYbykZJL6lozxB\n" +
                    "********************************************************\n" +
                    "-----END OPENSSH PRIVATE KEY-----";

            // 2. 加载密钥对
            KeyPair keyPair = KeyPair.load(new JSch(), privateKey.getBytes(), null);
            byte[] derivedPublicKeyBlob = keyPair.getPublicKeyBlob();

            // 3. 解析用户公钥（按空格分割并解码Base64）
            String[] publicKeyParts = publicKey.split(" ");
            if (publicKeyParts.length < 2) {
                throw new IllegalArgumentException("Invalid public key format");
            }
            byte[] userPublicKeyBlob = Base64.getDecoder().decode(publicKeyParts[1]);

            // 4. 安全比对（避免时序攻击）
            boolean isMatch = MessageDigest.isEqual(derivedPublicKeyBlob, userPublicKeyBlob);
            if (isMatch) {
                log.info("验证密钥正确------定时发邮件");

                List<ShortForwardEmailReceive> shortUserList = shortForwardEmailReceiveService.getSendNullUser();
                for (ShortForwardEmailReceive shortForwardEmailReceive : shortUserList) {


                    ShortForwardEmailSend shortForwardEmailSend = new ShortForwardEmailSend();
                    shortForwardEmailSend.setCreateTime(DateUtils.getNowDate());
                    shortForwardEmailSend.setSentDate(DateUtils.getNowDate());
                    shortForwardEmailSend.setAppId(shortForwardEmailReceive.getAppId());


                    shortForwardEmailSend.setReEmail(shortForwardEmailReceive.getEmail());
                    ShortForwartEmailDomain shortForwartEmailDomain = new ShortForwartEmailDomain();
                    shortForwartEmailDomain.setAppId(shortForwardEmailReceive.getAppId());
                    List<ShortForwartEmailDomain> list = shortForwartEmailDomainMapper.selectShortForwartEmailDomainList(shortForwartEmailDomain);
                    if (!list.isEmpty()) {


                        String host = "smtp.forwardemail.net"; // Forward Email SMTP服务器
                        String username = list.get(0).getDomain(); // 你的Forward Email地址
                        String password = AESEncryption.decrypt(list.get(0).getPassword()); // 你的密码
                        shortForwardEmailSend.setDomain(username);

                        Properties props = new Properties();
                        props.put("mail.smtp.host", host);
                        props.put("mail.smtp.port", "465");
                        props.put("mail.smtp.ssl.enable", "true");
                        props.put("mail.smtp.auth", "true");


                        // 记录日志并处理结果
                        ShortRunlog runlog = new ShortRunlog();
                        runlog.setType("发送订阅通知邮件");
                        runlog.setCreateTime(DateUtils.getNowDate());
                        runlog.setUpdateTime(DateUtils.getNowDate());

                        try {
                            // 创建会话
                            Session session = Session.getInstance(props,
                                    new Authenticator() {
                                        protected PasswordAuthentication getPasswordAuthentication() {
                                            return new PasswordAuthentication(username, password);
                                        }
                                    });

                            // 创建邮件
                            Message message = new MimeMessage(session);
                            message.setFrom(new InternetAddress(username));
                            message.setRecipients(Message.RecipientType.TO,
                                    InternetAddress.parse(shortForwardEmailSend.getReEmail()));

                            String subject = "Please Provide Your Correct Registered Email Address";
                            message.setSubject(subject);
                            shortForwardEmailSend.setSubject("Hi Valued Member,\n" +
                                    "\n" +
                                    "Thank you for reaching out to us.\n" +
                                    "It seems that the email address you provided is incomplete or incorrect.\n" +
                                    "Could you please reply with the exact email address you used when registering your account, so we can verify your information and assist you further?\n" +
                                    "\n" +
                                    "Looking forward to your reply.\n" +
                                    "\n" +
                                    "Best regards");


                            // 创建邮件正文部分
                            MimeBodyPart textPart = new MimeBodyPart();
                            // 组合 HTML 内容
                            textPart.setText("Hi Valued Member,\n" +
                                    "\n" +
                                    "Thank you for reaching out to us.\n" +
                                    "It seems that the email address you provided is incomplete or incorrect.\n" +
                                    "Could you please reply with the exact email address you used when registering your account, so we can verify your information and assist you further?\n" +
                                    "\n" +
                                    "Looking forward to your reply.\n" +
                                    "\n" +
                                    "Best regards");
                            // 组合各部分
                            Multipart multipart = new MimeMultipart();
                            multipart.addBodyPart(textPart);

                            message.setContent(multipart);
                            shortForwardEmailSend.setContent("Hi Valued Member,\n" +
                                    "\n" +
                                    "Thank you for reaching out to us.\n" +
                                    "It seems that the email address you provided is incomplete or incorrect.\n" +
                                    "Could you please reply with the exact email address you used when registering your account, so we can verify your information and assist you further?\n" +
                                    "\n" +
                                    "Looking forward to your reply.\n" +
                                    "\n" +
                                    "Best regards");


                            new Thread(() -> {
                                try {
                                    runlog.setState("1");
                                    String content = "";
                                    // 发送邮件
                                    Transport.send(message);
                                    content = String.format("自动回复无账户邮件发送成功！ " + "用户Id：" + shortForwardEmailReceive.getId() +
                                            ",邮件主题：" + subject + ",收件人名称：" + "Valued Member" +
                                            ",收件人邮件：" + shortForwardEmailReceive.getEmail() + ",邮件服务商:Forward");
                                    runlog.setContent(content);
                                    shortRunlogService.insertShortRunlog(runlog);

                                    shortForwardEmailReceive.setType("2");
                                    shortForwardEmailReceive.setRemark("自动回复无账户邮件");
                                    shortForwardEmailReceiveService.updateShortForwardEmailReceive(shortForwardEmailReceive);


                                    shortForwardEmailSend.setType(1L);
                                    shortForwardEmailSendMapper.insertShortForwardEmailSend(shortForwardEmailSend);
                                } catch (Exception e) {
                                    runlog.setState("0");
                                    runlog.setContent(String.format(",订阅通知发送失败：" + e.getMessage() + ",邮件主题：" + subject +
                                            ",收件人名称：" + "Valued Member" + ",收件人邮件：" + shortForwardEmailReceive.getEmail()) + ",邮件服务商:Forward");
                                    runlog.setNote(e.toString());
                                    shortRunlogService.insertShortRunlog(runlog);


                                    shortForwardEmailSend.setType(2L);
                                    shortForwardEmailSendMapper.insertShortForwardEmailSend(shortForwardEmailSend);
                                }
                            }).start();


                        } catch (Exception e) {
                            System.err.println("发送失败: " + e.getMessage());
                            runlog.setState("0");
                            runlog.setContent(String.format(",发送失败：" + e.getMessage() + ",邮件主题：" + "Please Provide Your Correct Registered Email Address" + ",收件人名称：" + "Valued Member" + ",收件人邮件：" + shortForwardEmailReceive.getId()));
                            runlog.setNote(e.toString());
                            shortRunlogService.insertShortRunlog(runlog);
                            shortForwardEmailSend.setType(2L);
                            shortForwardEmailSendMapper.insertShortForwardEmailSend(shortForwardEmailSend);
                        }
                    }


                }

                shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
                return AjaxResult.success("操作成功=");
            }
            shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
            return AjaxResult.success("操作成功");

        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("status", "error");
            error.put("message", "Validation failed: " + e.getMessage());
            shortIntermedStatusService.deleteShortIntermedStatusById(shortIntermedStatus.getId());
            return AjaxResult.error("操作失败");
        }
    }


    public AjaxResult sendWelcomeEmailForward(InitUserDTO shortBrevo, ShortMovie shortMovie, String type) throws Exception {
        SendSmtpEmail email = new SendSmtpEmail();
        TransactionalEmailsApi apiInstance = new TransactionalEmailsApi();
        EmailDTO emailDTO = new EmailDTO();
        ShortForwardEmailSend shortForwardEmailSend = new ShortForwardEmailSend();
        shortForwardEmailSend.setReEmail(shortBrevo.getReceiveEmail());
        Message message = null;
        if (type.equals("forward")) {

            shortForwardEmailSend.setCreateTime(DateUtils.getNowDate());
            shortForwardEmailSend.setSentDate(DateUtils.getNowDate());
            shortForwardEmailSend.setAppId(shortBrevo.getAppId());


            ShortForwartEmailDomain shortForwartEmailDomain = new ShortForwartEmailDomain();
            shortForwartEmailDomain.setAppId(shortBrevo.getAppId());
            List<ShortForwartEmailDomain> list = shortForwartEmailDomainMapper.selectShortForwartEmailDomainList(shortForwartEmailDomain);
            if (!list.isEmpty()) {

                String username = list.get(0).getDomain(); // 你的Forward Email地址
                String password = AESEncryption.decrypt(list.get(0).getPassword()); // 你的密码
                shortForwardEmailSend.setDomain(username);


                String host = "smtp.forwardemail.net"; // Forward Email SMTP服务器
                Properties props = new Properties();
                props.put("mail.smtp.host", host);
                props.put("mail.smtp.port", "465");
                props.put("mail.smtp.ssl.enable", "true");
                props.put("mail.smtp.auth", "true");


                try {
                    // 创建会话
                    Session session = Session.getInstance(props,
                            new Authenticator() {
                                protected PasswordAuthentication getPasswordAuthentication() {
                                    return new PasswordAuthentication(username, password);
                                }
                            });

                    // 创建邮件

                    message = new MimeMessage(session);
                    message.setFrom(new InternetAddress(username));
                    message.setRecipients(Message.RecipientType.TO,
                            InternetAddress.parse(shortForwardEmailSend.getReEmail()));

                } catch (MessagingException e) {
                }
            }


        }

        // 3. 设置发件人
        SendSmtpEmailSender sender = new SendSmtpEmailSender();
        ShortEmailDomain shortEmailDomain = shortEmailDomainService.getrandomDomain(shortBrevo.getAppId());

        if (type.equals("brevo")) {
            // 2. 初始化邮件API
            ApiClient defaultClient = Configuration.getDefaultApiClient();
            ApiKeyAuth apiKey = (ApiKeyAuth) defaultClient.getAuthentication("api-key");
            apiKey.setApiKey(brevoApiKey); // 替换为你的Brevo API密钥

            if (null != shortEmailDomain) {
                sender.setEmail(shortEmailDomain.getDomain()); // 必须已验证的邮箱
                sender.setName(shortEmailDomain.getAppName());
            } else {
                sender.setEmail(senderEmail); // 必须已验证的邮箱
                sender.setName(senderName);
            }


            List<SendSmtpEmailTo> toList = new ArrayList<>();
            // 4. 设置收件人
            SendSmtpEmailTo recipient = new SendSmtpEmailTo();
            recipient.setEmail(null != shortBrevo.getReceiveEmail() ? shortBrevo.getReceiveEmail() : null);
            recipient.setName(null != shortBrevo.getReceiveName() ? shortBrevo.getReceiveName() : null);
            toList.add(recipient);
            email.setSender(sender);
            email.setTo(toList);
        } else if (type.equals("aws")) {
            emailDTO.setReceiveEmail(ListUtil.toList(shortBrevo.getReceiveEmail()));
            emailDTO.setSendChannel("aws");
            emailDTO.setServiceType("awsSimpl");
            emailDTO.setTitle("Kushort");
        }
        // 记录日志并处理结果
        ShortRunlog runlog = new ShortRunlog();
        runlog.setType("发送邮件");
        runlog.setCreateTime(DateUtils.getNowDate());
        runlog.setUpdateTime(DateUtils.getNowDate());

        try {
            // 5. 从HTML文件读取内容
            String htmlName = "";
            ShortUser shortUser = shortUserService.selectShortUserById(Long.valueOf(shortBrevo.getUserId()));
            if (null != shortUser && StringUtils.isNotEmpty(shortUser.getSource())) {
                if (null == shortBrevo.getLanguageCode() || shortBrevo.getLanguageCode().equals("en-US")) {
                    if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                        htmlName = "former_reg.html";
                    else
                        htmlName = shortBrevo.getHtmlName();
                } else if (shortBrevo.getLanguageCode().equals("es")) {
                    if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                        htmlName = "es-former_reg.html";
                    else
                        htmlName = shortBrevo.getHtmlName();
                } else if (shortBrevo.getLanguageCode().equals("pt")) {
                    if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                        htmlName = "pt-former_reg.html";
                    else
                        htmlName = shortBrevo.getHtmlName();
                } else if (shortBrevo.getLanguageCode().equals("zh-TW")) {
                    if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                        htmlName = "zh-TW-former_reg.html";
                    else
                        htmlName = shortBrevo.getHtmlName();
                } else if (shortBrevo.getLanguageCode().equals("ja")) {
                    if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                        htmlName = "ja-former_reg.html";
                    else
                        htmlName = shortBrevo.getHtmlName();
                } else if (shortBrevo.getLanguageCode().equals("de")) {
                    if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                        htmlName = "de-former_reg.html";
                    else
                        htmlName = shortBrevo.getHtmlName();
                }
            } else {
                if (null == shortBrevo.getLanguageCode() || shortBrevo.getLanguageCode().equals("en-US")) {
                    if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                        htmlName = "reg.html";
                    else
                        htmlName = shortBrevo.getHtmlName();
                } else if (shortBrevo.getLanguageCode().equals("es")) {
                    if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                        htmlName = "es-reg.html";
                    else
                        htmlName = shortBrevo.getHtmlName();
                } else if (shortBrevo.getLanguageCode().equals("pt")) {
                    if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                        htmlName = "pt-reg.html";
                    else
                        htmlName = shortBrevo.getHtmlName();
                } else if (shortBrevo.getLanguageCode().equals("zh-TW")) {
                    if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                        htmlName = "zh-TW-reg.html";
                    else
                        htmlName = shortBrevo.getHtmlName();
                } else if (shortBrevo.getLanguageCode().equals("ja")) {
                    if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                        htmlName = "ja-reg.html";
                    else
                        htmlName = shortBrevo.getHtmlName();
                } else if (shortBrevo.getLanguageCode().equals("de")) {
                    if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                        htmlName = "de-reg.html";
                    else
                        htmlName = shortBrevo.getHtmlName();
                }
            }


            if (null != shortMovie && null != shortMovie.getLangId() && shortMovie.getLangId().intValue() == 4)
                htmlName = "ESReg.html";

            ClassPathResource resource = new ClassPathResource("templates/" + htmlName);
            String htmlContent = StreamUtils.copyToString(
                    resource.getInputStream(),
                    StandardCharsets.UTF_8
            );
//            String subject = getSubject(shortBrevo);
            String subject = shortEmailDomain.getAppName() + " Registration Confirmation";

            String appDomain = null != shortEmailDomain.getAppDomain() ? shortEmailDomain.getAppDomain() : "";

            String randomNumApp1 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
            String randomNumApp2 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
//            ?userId=前六   + +  后六&uniqueId=

            if (null != shortUser)
                appDomain = appDomain + "?userId=" + randomNumApp1 + shortUser.getHashId() + randomNumApp2 + "&uniqueId=" + shortBrevo.getUniqueId() + "&language=" + shortBrevo.getLanguageCode();


            ShortPage shortPage = new ShortPage();
            shortPage.setAppId(String.valueOf(shortUser.getAppId()));
            shortPage.setCname("投诉和反馈");
            List<ShortPage> shortPageList = shortPageService.selectShortPageList(shortPage);

            // 使用更安全的占位符格式
            htmlContent = htmlContent
                    .replace("${user_name}", null != shortBrevo.getUser_name() ? shortBrevo.getUser_name() : "")
                    .replace("${movieurl}", null != shortMovie.getIcon() ? shortMovie.getIcon() : "")
                    .replace("{{movieurl}}", null != shortMovie.getIcon() ? shortMovie.getIcon() : "")

                    .replace("${appName}", null != shortEmailDomain.getAppName() ? shortEmailDomain.getAppName() : "")
                    .replace("{{appName}}", null != shortEmailDomain.getAppName() ? shortEmailDomain.getAppName() : "")

                    .replace("${appDomain}", appDomain)
                    .replace("{{appDomain}}", appDomain)

                    .replace("${jumpUrl}", shortPageList.get(0).getUrl() + "?selevt=3&email=" + shortBrevo.getReceiveEmail())
                    .replace("{{jumpUrl}}", shortPageList.get(0).getUrl() + "?selevt=3&email=" + shortBrevo.getReceiveEmail())

                    .replace("{{user_name}}", null != shortBrevo.getUser_name() ? shortBrevo.getUser_name() : ""); // 兼容旧格式

            if (type.equals("brevo")) {
                // 6. 创建邮件内容
                email.setSubject(subject);
                email.setHtmlContent(htmlContent); // 使用HTML内容
            } else if (type.equals("forward")) {

                message.setSubject(subject);
                shortForwardEmailSend.setSubject(subject);

                // 创建邮件正文部分
                MimeBodyPart textPart = new MimeBodyPart();

                // 组合 HTML 内容
                textPart.setContent(htmlContent, "text/html; charset=utf-8");

                // 组合各部分
                Multipart multipart = new MimeMultipart();
                multipart.addBodyPart(textPart);

                message.setContent(multipart);
                shortForwardEmailSend.setContent(htmlContent);


            } else {
                emailDTO.setSubject(subject);
                emailDTO.setContent(htmlContent);
            }

            Message finalMessage = message;
            new Thread(() -> {
                try {
                    runlog.setState("1");
                    String content = "";
                    if (type.equals("brevo")) {
                        // 7. 发送邮件
                        CreateSmtpEmail response = apiInstance.sendTransacEmail(email);
                        content = String.format(",邮件发送成功！消息ID: " + response.getMessageId() + "用户Id：" + shortBrevo.getUserId() +
                                ",邮件主题：" + shortBrevo.getSubject() + ",收件人名称：" + shortBrevo.getReceiveName() +
                                ",收件人邮件：" + shortBrevo.getReceiveEmail() + ",邮件服务商:" + type);
                        runlog.setNote(response.toString());
                    } else if (type.equals("aws")) {
                        emailServiceContext.getService(emailDTO.getServiceType()).sendEmail(emailDTO);
                        content = String.format(",邮件发送成功！用户Id：" + shortBrevo.getUserId() +
                                ",邮件主题：" + shortBrevo.getSubject() +
                                ",收件人邮件：" + shortBrevo.getReceiveEmail() +
                                ",邮件服务商:" + type);
                        runlog.setNote(type);
                    } else if (type.equals("forward")) {
                        // 发送邮件
                        Transport.send(finalMessage);
                        content = String.format(",邮件发送成功！" + "用户Id：" + shortBrevo.getUserId() +
                                ",邮件主题：" + shortBrevo.getSubject() + ",收件人名称：" + shortBrevo.getReceiveName() +
                                ",收件人邮件：" + shortBrevo.getReceiveEmail() + ",邮件服务商:" + type);

                        shortForwardEmailSend.setType(1L);
                        shortForwardEmailSendMapper.insertShortForwardEmailSend(shortForwardEmailSend);

                    }
                    runlog.setContent(content);
                    shortRunlogService.insertShortRunlog(runlog);
                } catch (Exception e) {
                    runlog.setState("0");
                    runlog.setContent(String.format(",发送失败：" + e.getMessage() + ",邮件主题：" + shortBrevo.getSubject() +
                            ",收件人名称：" + shortBrevo.getReceiveName() + ",收件人邮件：" + shortBrevo.getReceiveEmail()) + ",邮件服务商:" + type);
                    runlog.setNote(e.toString());
                    shortRunlogService.insertShortRunlog(runlog);
                }
            }).start();

            String randomNum = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 4);
            Map<String, String> vars = new HashMap<>();
            vars.put("uId", randomNum + shortBrevo.getUserId());
            vars.put("uniqueId", shortBrevo.getUniqueId());
            return AjaxResult.success(vars);
        } catch (IOException e) {
            System.err.println("读取HTML文件失败: " + e.getMessage());
            runlog.setState("0");
            runlog.setContent(String.format(",读取HTML文件失败：" + e.getMessage() + ",邮件主题：" + shortBrevo.getSubject() + ",收件人名称：" + shortBrevo.getReceiveName() + ",收件人邮件：" + shortBrevo.getReceiveEmail()));
            runlog.setNote(e.toString());
            shortRunlogService.insertShortRunlog(runlog);

            return AjaxResult.error("读取HTML文件失败: " + e.getMessage());
        } catch (Exception e) {
            System.err.println("发送失败: " + e.getMessage());
            runlog.setState("0");
            runlog.setContent(String.format(",发送失败：" + e.getMessage() + ",邮件主题：" + shortBrevo.getSubject() + ",收件人名称：" + shortBrevo.getReceiveName() + ",收件人邮件：" + shortBrevo.getReceiveEmail()));
            runlog.setNote(e.toString());
            shortRunlogService.insertShortRunlog(runlog);
            return AjaxResult.error("发送失败: " + e.getMessage());
        }
    }

    public AjaxResult sendSubEmailForward(InitUserDTO shortBrevo, ShortMovie shortMovie, String type, ShortOrder order) throws Exception {
        SendSmtpEmail email = new SendSmtpEmail();
        TransactionalEmailsApi apiInstance = new TransactionalEmailsApi();
        EmailDTO emailDTO = new EmailDTO();
        ShortForwardEmailSend shortForwardEmailSend = new ShortForwardEmailSend();
        shortForwardEmailSend.setReEmail(shortBrevo.getReceiveEmail());
        Message message = null;
        if (type.equals("forward")) {

            shortForwardEmailSend.setCreateTime(DateUtils.getNowDate());
            shortForwardEmailSend.setSentDate(DateUtils.getNowDate());
            shortForwardEmailSend.setAppId(shortBrevo.getAppId());


            ShortForwartEmailDomain shortForwartEmailDomain = new ShortForwartEmailDomain();
            shortForwartEmailDomain.setAppId(shortBrevo.getAppId());
            List<ShortForwartEmailDomain> list = shortForwartEmailDomainMapper.selectShortForwartEmailDomainList(shortForwartEmailDomain);
            if (!list.isEmpty()) {

                String username = list.get(0).getDomain(); // 你的Forward Email地址
                String password = AESEncryption.decrypt(list.get(0).getPassword()); // 你的密码
                shortForwardEmailSend.setDomain(username);


                String host = "smtp.forwardemail.net"; // Forward Email SMTP服务器
                Properties props = new Properties();
                props.put("mail.smtp.host", host);
                props.put("mail.smtp.port", "465");
                props.put("mail.smtp.ssl.enable", "true");
                props.put("mail.smtp.auth", "true");


                try {
                    // 创建会话
                    Session session = Session.getInstance(props,
                            new Authenticator() {
                                protected PasswordAuthentication getPasswordAuthentication() {
                                    return new PasswordAuthentication(username, password);
                                }
                            });

                    // 创建邮件

                    message = new MimeMessage(session);
                    message.setFrom(new InternetAddress(username));
                    message.setRecipients(Message.RecipientType.TO,
                            InternetAddress.parse(shortForwardEmailSend.getReEmail()));

                } catch (MessagingException e) {
                }
            }


        }

        // 3. 设置发件人
        SendSmtpEmailSender sender = new SendSmtpEmailSender();
        ShortEmailDomain shortEmailDomain = shortEmailDomainService.getrandomDomain(shortBrevo.getAppId());

        if (type.equals("brevo")) {
            // 2. 初始化邮件API
            ApiClient defaultClient = Configuration.getDefaultApiClient();
            ApiKeyAuth apiKey = (ApiKeyAuth) defaultClient.getAuthentication("api-key");
            apiKey.setApiKey(brevoApiKey); // 替换为你的Brevo API密钥

            if (null != shortEmailDomain) {
                sender.setEmail(shortEmailDomain.getDomain()); // 必须已验证的邮箱
                sender.setName(shortEmailDomain.getAppName());
            } else {
                sender.setEmail(senderEmail); // 必须已验证的邮箱
                sender.setName(senderName);
            }


            List<SendSmtpEmailTo> toList = new ArrayList<>();
            // 4. 设置收件人
            SendSmtpEmailTo recipient = new SendSmtpEmailTo();
            recipient.setEmail(null != shortBrevo.getReceiveEmail() ? shortBrevo.getReceiveEmail() : null);
            recipient.setName(null != shortBrevo.getReceiveName() ? shortBrevo.getReceiveName() : null);
            toList.add(recipient);
            email.setSender(sender);
            email.setTo(toList);
        } else if (type.equals("aws")) {
            emailDTO.setReceiveEmail(ListUtil.toList(shortBrevo.getReceiveEmail()));
            emailDTO.setSendChannel("aws");
            emailDTO.setServiceType("awsSimpl");
            emailDTO.setTitle("Kushort");
        }
        // 记录日志并处理结果
        ShortRunlog runlog = new ShortRunlog();
        runlog.setType("发送订阅成功邮件");
        runlog.setCreateTime(DateUtils.getNowDate());
        runlog.setUpdateTime(DateUtils.getNowDate());

        try {
            // 5. 从HTML文件读取内容
            String htmlName = "";

            if (null == shortBrevo.getLanguageCode() || shortBrevo.getLanguageCode().equals("en-US")) {
                if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                    htmlName = "sub.html";
                else
                    htmlName = shortBrevo.getHtmlName();
            } else if (shortBrevo.getLanguageCode().equals("es")) {
                if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                    htmlName = "es-sub.html";
                else
                    htmlName = shortBrevo.getHtmlName();
            } else if (shortBrevo.getLanguageCode().equals("pt")) {
                if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                    htmlName = "pt-sub.html";
                else
                    htmlName = shortBrevo.getHtmlName();
            } else if (shortBrevo.getLanguageCode().equals("zh-TW")) {
                if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                    htmlName = "zh-TW-sub.html";
                else
                    htmlName = shortBrevo.getHtmlName();
            } else if (shortBrevo.getLanguageCode().equals("ja")) {
                if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                    htmlName = "ja-sub.html";
                else
                    htmlName = shortBrevo.getHtmlName();
            } else if (shortBrevo.getLanguageCode().equals("de")) {
                if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                    htmlName = "de-sub.html";
                else
                    htmlName = shortBrevo.getHtmlName();
            }


            ClassPathResource resource = new ClassPathResource("templates/" + htmlName);
            String htmlContent = StreamUtils.copyToString(
                    resource.getInputStream(),
                    StandardCharsets.UTF_8
            );
//            String subject = getSubject(shortBrevo);
            String subject = shortEmailDomain.getAppName() + " Subscription Confirmation";


            String appDomain = null != shortEmailDomain.getAppDomain() ? shortEmailDomain.getAppDomain() : "";

            String randomNumApp1 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
            String randomNumApp2 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
//            ?userId=前六   + +  后六&uniqueId=
            ShortUser shortUser = shortUserService.selectShortUserById(Long.valueOf(shortBrevo.getUserId()));

            String unSubUrl = appDomain + "/subscription-records/" + "?userId=" + randomNumApp1 + shortUser.getHashId() + randomNumApp2 + "&uniqueId=" + shortBrevo.getUniqueId() + "&language=" + shortBrevo.getLanguageCode() + "&noLogin=true";
            if (null != shortUser)
                appDomain = appDomain + "?userId=" + randomNumApp1 + shortUser.getHashId() + randomNumApp2 + "&uniqueId=" + shortBrevo.getUniqueId() + "&language=" + shortBrevo.getLanguageCode();


            ShortPage shortPage = new ShortPage();
            shortPage.setAppId(String.valueOf(shortUser.getAppId()));
            shortPage.setCname("投诉和反馈");
            List<ShortPage> shortPageList = shortPageService.selectShortPageList(shortPage);


            String dayType = order.getSubscriptionType();
            Date payTime = order.getPayTime();
            BigDecimal buyPrice = order.getPaymentAmount();
            BigDecimal renewalPrice = order.getPaymentAmount();
//            http://localhost:3000/subscription-records/

            // 使用更安全的占位符格式
            htmlContent = htmlContent
                    .replace("${user_name}", null != shortBrevo.getUser_name() ? shortBrevo.getUser_name() : "")
                    .replace("${movieurl}", null != shortMovie.getIcon() ? shortMovie.getIcon() : "")
                    .replace("{{movieurl}}", null != shortMovie.getIcon() ? shortMovie.getIcon() : "")

                    .replace("${appName}", null != shortEmailDomain.getAppName() ? shortEmailDomain.getAppName() : "")
                    .replace("{{appName}}", null != shortEmailDomain.getAppName() ? shortEmailDomain.getAppName() : "")

                    .replace("${appDomain}", appDomain)
                    .replace("{{appDomain}}", appDomain)

                    .replace("${expireTime}", shortBrevo.getExpireTime().toString())
                    .replace("{{expireTime}}", shortBrevo.getExpireTime().toString())

                    .replace("${jumpUrl}", shortPageList.get(0).getUrl() + "?selevt=3&email=" + shortBrevo.getReceiveEmail())
                    .replace("{{jumpUrl}}", shortPageList.get(0).getUrl() + "?selevt=3&email=" + shortBrevo.getReceiveEmail())


                    .replace("${dayType}", dayType)
                    .replace("{{dayType}}", dayType)
                    .replace("${payTime}", payTime.toString())
                    .replace("{{payTime}}", payTime.toString())
                    .replace("${buyPrice}", buyPrice.toString())
                    .replace("{{buyPrice}}", buyPrice.toString())
                    .replace("${renewalPrice}", renewalPrice.toString())
                    .replace("{{renewalPrice}}", renewalPrice.toString())
                    .replace("${unSubUrl}", unSubUrl)
                    .replace("{{unSubUrl}}", unSubUrl)


                    .replace("{{user_name}}", null != shortBrevo.getUser_name() ? shortBrevo.getUser_name() : ""); // 兼容旧格式

            if (type.equals("brevo")) {
                // 6. 创建邮件内容
                email.setSubject(subject);
                email.setHtmlContent(htmlContent); // 使用HTML内容
            } else if (type.equals("forward")) {

                message.setSubject(subject);
                shortForwardEmailSend.setSubject(subject);

                // 创建邮件正文部分
                MimeBodyPart textPart = new MimeBodyPart();

                // 组合 HTML 内容
                textPart.setContent(htmlContent, "text/html; charset=utf-8");

                // 组合各部分
                Multipart multipart = new MimeMultipart();
                multipart.addBodyPart(textPart);

                message.setContent(multipart);
                shortForwardEmailSend.setContent(htmlContent);

            } else {
                emailDTO.setSubject(subject);
                emailDTO.setContent(htmlContent);
            }

            Message finalMessage = message;
            new Thread(() -> {
                try {
                    runlog.setState("1");
                    String content = "";
                    if (type.equals("brevo")) {
                        // 7. 发送邮件
                        CreateSmtpEmail response = apiInstance.sendTransacEmail(email);
                        content = String.format(",邮件发送成功！消息ID: " + response.getMessageId() + "用户Id：" + shortBrevo.getUserId() +
                                ",邮件主题：" + shortBrevo.getSubject() + ",收件人名称：" + shortBrevo.getReceiveName() +
                                ",收件人邮件：" + shortBrevo.getReceiveEmail() + ",邮件服务商:" + type);
                        runlog.setNote(response.toString());
                    } else if (type.equals("aws")) {
                        emailServiceContext.getService(emailDTO.getServiceType()).sendEmail(emailDTO);
                        content = String.format(",邮件发送成功！用户Id：" + shortBrevo.getUserId() +
                                ",邮件主题：" + shortBrevo.getSubject() +
                                ",收件人邮件：" + shortBrevo.getReceiveEmail() +
                                ",邮件服务商:" + type);
                        runlog.setNote(type);
                    } else if (type.equals("forward")) {
                        // 发送邮件
                        Transport.send(finalMessage);
                        content = String.format(",邮件发送成功！" + "用户Id：" + shortBrevo.getUserId() +
                                ",邮件主题：" + shortBrevo.getSubject() + ",收件人名称：" + shortBrevo.getReceiveName() +
                                ",收件人邮件：" + shortBrevo.getReceiveEmail() + ",邮件服务商:" + type);

                        shortForwardEmailSend.setType(1L);
                        shortForwardEmailSendMapper.insertShortForwardEmailSend(shortForwardEmailSend);

                    }
                    runlog.setContent(content);
                    shortRunlogService.insertShortRunlog(runlog);
                } catch (Exception e) {
                    runlog.setState("0");
                    runlog.setContent(String.format(",发送失败：" + e.getMessage() + ",邮件主题：" + shortBrevo.getSubject() +
                            ",收件人名称：" + shortBrevo.getReceiveName() + ",收件人邮件：" + shortBrevo.getReceiveEmail()) + ",邮件服务商:" + type);
                    runlog.setNote(e.toString());
                    shortRunlogService.insertShortRunlog(runlog);
                }
            }).start();

            String randomNum = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 4);
            Map<String, String> vars = new HashMap<>();
            vars.put("uId", randomNum + shortBrevo.getUserId());
            vars.put("uniqueId", shortBrevo.getUniqueId());
            return AjaxResult.success(vars);
        } catch (IOException e) {
            System.err.println("读取HTML文件失败: " + e.getMessage());
            runlog.setState("0");
            runlog.setContent(String.format(",读取HTML文件失败：" + e.getMessage() + ",邮件主题：" + shortBrevo.getSubject() + ",收件人名称：" + shortBrevo.getReceiveName() + ",收件人邮件：" + shortBrevo.getReceiveEmail()));
            runlog.setNote(e.toString());
            shortRunlogService.insertShortRunlog(runlog);

            return AjaxResult.error("读取HTML文件失败: " + e.getMessage());
        } catch (Exception e) {
            System.err.println("发送失败: " + e.getMessage());
            runlog.setState("0");
            runlog.setContent(String.format(",发送失败：" + e.getMessage() + ",邮件主题：" + shortBrevo.getSubject() + ",收件人名称：" + shortBrevo.getReceiveName() + ",收件人邮件：" + shortBrevo.getReceiveEmail()));
            runlog.setNote(e.toString());
            shortRunlogService.insertShortRunlog(runlog);
            return AjaxResult.error("发送失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> emailLogin(EmailLoginDTO emailLoginDTO) throws JsonProcessingException {
        boolean appSwitch = false;
        Map<String, Object> result = new HashMap<>();

        String email = emailLoginDTO.getEmail();
        String password = emailLoginDTO.getPassword();
        Long appId = emailLoginDTO.getAppId();


        ShortUser oldUser = shortUserService.getOldUser(appId, email);
        if (null != oldUser) {
            ShortUser newShortUser = shortUserService.selectShortUserById(emailLoginDTO.getUserId());
            if (null != newShortUser.getLinkidId())
                oldUser.setLinkidId(newShortUser.getLinkidId());
                oldUser.setLinkTime(DateUtils.getNowDate());
                // 更新pixelId - 从深链获取最新的pixelId
                ShortSemLink shortSemLink = shortSemLinkService.selectShortSemLinkById(newShortUser.getLinkidId());
                if (shortSemLink != null && shortSemLink.getPixelId() != null) {
                    oldUser.setPixelId(shortSemLink.getPixelId());
                }
            shortUserService.updateShortUser(oldUser);

            if(null != oldUser.getAppId() && !appId.equals("25")){
                int count = shortUserService.countByEmailAndAppId("25",oldUser.getEmail());
                if(count > 0){
                    if(null != oldUser.getRemark() && oldUser.getRemark().contains("subscriptionType")){
                        result.put("code", 200);
                        result.put("msg", "success");
                        Map<String, Object> dataMap = new HashMap<>();
                        dataMap.put("state", appSwitch);
//            dataMap.put("source", reUser.getSource());
                        dataMap.put("user_id", oldUser.getHashId());
                        dataMap.put("uniqueId", oldUser.getUniqueId());
                        dataMap.put("isRenderingAdvance", renderingAdvance);
                        result.put("data", dataMap);
                        return result;
                    }
                    Date newExpireTime = null;
                    Date startDate = new Date();
                    if (oldUser.getExpireTime() != null &&
                            oldUser.getExpireTime().after(DateUtils.getNowDate())) {
                        // 在现有过期时间基础上添加天数
                        Calendar calendar = Calendar.getInstance();
                        calendar.setTime(oldUser.getExpireTime());
                        calendar.add(Calendar.DAY_OF_YEAR, 7);
                        newExpireTime = calendar.getTime();
                        startDate = oldUser.getExpireTime();
                    } else {
                        // 从当前时间开始计算
                        Calendar calendar = Calendar.getInstance();
                        calendar.add(Calendar.DAY_OF_YEAR, 7);
                        newExpireTime = calendar.getTime();
                    }

                    HashMap map = new HashMap();
                    map.put("subscriptionType",7);
                    map.put("msg","Das System bietet eine siebentägige Mitgliedschaft");
                    // 格式化时间为指定格式
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    map.put("startDate",sdf.format(startDate));
                    map.put("endDate",sdf.format(newExpireTime));

                    ObjectMapper objectMapper = new ObjectMapper();
                    String json = objectMapper.writeValueAsString(map);
                    oldUser.setExpireTime(newExpireTime);
                    oldUser.setRemark(json);
                    shortUserService.updateShortUser(oldUser);
                }

            }

            result.put("code", 200);
            result.put("msg", "success");
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("state", appSwitch);
//            dataMap.put("source", reUser.getSource());
            dataMap.put("user_id", oldUser.getHashId());
            dataMap.put("uniqueId", oldUser.getUniqueId());
            dataMap.put("isRenderingAdvance", renderingAdvance);
            result.put("data", dataMap);
            return result;
        }


        ShortUser queryUser = new ShortUser();
        queryUser.setEmail(email);
        queryUser.setPassword(password);
//        queryUser.setAppId(appId);
        List<ShortUser> shortUsers = shortUserService.selectShortUserList(queryUser);

        if (CollectionUtil.isEmpty(shortUsers)) {
            result.put("code", 400);
            result.put("msg", "User does not exist!");
        }
        List<ShortUser> unsubUsers = shortUsers.stream()
                .filter(user -> user.getUnsub() != null && user.getUnsub() == 0)
                .collect(Collectors.toList());
        if (unsubUsers.size() == 1) {
            ShortUser shortUser = unsubUsers.get(0);

            ShortUser reUser = shortUserService.selectShortUserById(emailLoginDTO.getUserId());
            if (null != reUser && StringUtils.isEmpty(reUser.getEmail())) {
                reUser.setVipId(shortUser.getVipId());
                reUser.setLinkidId(shortUser.getLinkidId());
                reUser.setPassword(shortUser.getPassword());
                reUser.setSource(shortUser.getSource());
                reUser.setAccountType(shortUser.getAccountType());
                reUser.setOther(shortUser.getOther());
                reUser.setExpireTime(shortUser.getExpireTime());
                reUser.setIsSubscriber(shortUser.getIsSubscriber());
//                reUser.setPayInfo(shortUser.getPayInfo());
                reUser.setEmail(shortUser.getEmail());
                reUser.setIp(shortUser.getIp());
                shortUserService.updateShortUser(reUser);
            }


            result.put("code", 200);
            result.put("msg", "success");
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("state", appSwitch);
//            dataMap.put("source", reUser.getSource());
            dataMap.put("user_id", reUser.getHashId());
            dataMap.put("uniqueId", reUser.getUniqueId());
            dataMap.put("isRenderingAdvance", renderingAdvance);
            result.put("data", dataMap);
            return result;
        }

        if (unsubUsers.size() > 1) {
            ShortUser latestUser = unsubUsers.stream()
                    .max(Comparator.comparing(ShortUser::getCreateTime))
                    .orElse(null);

            if (latestUser != null) {


                ShortUser reUser = shortUserService.selectShortUserById(emailLoginDTO.getUserId());
                if (null != reUser && StringUtils.isEmpty(reUser.getEmail())) {
                    reUser.setVipId(latestUser.getVipId());
                    reUser.setLinkidId(latestUser.getLinkidId());
                    reUser.setPassword(latestUser.getPassword());
                    reUser.setSource(latestUser.getSource());
                    reUser.setAccountType(latestUser.getAccountType());
                    reUser.setOther(latestUser.getOther());
                    reUser.setExpireTime(latestUser.getExpireTime());
                    reUser.setIsSubscriber(latestUser.getIsSubscriber());
//                    reUser.setPayInfo(latestUser.getPayInfo());
                    reUser.setEmail(latestUser.getEmail());
                    reUser.setIp(latestUser.getIp());
                    shortUserService.updateShortUser(reUser);
                }


                result.put("code", 200);
                result.put("msg", "success");
                Map<String, Object> dataMap = new HashMap<>();
                dataMap.put("state", appSwitch);
//                dataMap.put("source", reUser.getSource());
                dataMap.put("user_id", reUser.getHashId());
                dataMap.put("uniqueId", reUser.getUniqueId());
                dataMap.put("isRenderingAdvance", renderingAdvance);
                result.put("data", dataMap);
                return result;
            }
        }

        ShortUser newUser = shortUsers.stream()
                .max(Comparator.comparing(ShortUser::getCreateTime))
                .orElse(null);
        if (newUser != null) {
            result.put("code", 200);
            result.put("msg", "success");
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("state", appSwitch);
            dataMap.put("source", newUser.getSource());
            dataMap.put("user_id", newUser.getHashId());
            dataMap.put("uniqueId", newUser.getUniqueId());
            dataMap.put("isRenderingAdvance", renderingAdvance);
            result.put("data", dataMap);
            return result;
        }

        result.put("code", 400);
        result.put("msg", "User does not exist");

        return result;
    }

    @Override
    public AjaxResult sendWeeklyEmail(String publicKey) throws Exception {

        List<ShortUser> shortUserList = shortUserService.getEmailAndIsSubscriber();

        for (ShortUser su : shortUserList) {
            InitUserDTO shortBrevo = new InitUserDTO();
            shortBrevo.setReceiveEmail(su.getEmail());
            shortBrevo.setPassword(su.getPassword());
            shortBrevo.setAppId(su.getAppId());
            if (null == shortBrevo.getLanguageCode())
                shortBrevo.setLanguageCode("en-US");
//            if(null != su.getLinkidId()){
//                ShortSemLink shortSemLink = shortSemLinkService.selectShortSemLinkById(su.getLinkidId());
//                if(null != shortSemLink && null != shortSemLink.getLanguage())
//                    shortBrevo.setLanguageCode(shortSemLink.getLanguage());
//            }
            shortBrevo.setUserId(String.valueOf(su.getId()));
            shortBrevo.setUniqueId(su.getUniqueId());
            shortBrevo.setHashId(su.getHashId());

            List<ShortMovie> shortMovieList = new ArrayList<>();
            if (shortBrevo.getLanguageCode().equals("en-US"))
                shortMovieList = shortMovieService.getByRand10(su.getAppId(), 10);
//            else
//                shortMovieList = shortMovieService.getByRand4I18n(su.getAppId(), shortSemLink.getLanguage());

            sendEmailForInType(shortBrevo, shortMovieList, "brevo", "weekly");
        }


        return AjaxResult.success();
    }

    public void sendEmailForInType(InitUserDTO shortBrevo, List<ShortMovie> shortMovieList, String type, String inType) throws Exception {
        SendSmtpEmail email = new SendSmtpEmail();
        TransactionalEmailsApi apiInstance = new TransactionalEmailsApi();
        EmailDTO emailDTO = new EmailDTO();
        ShortForwardEmailSend shortForwardEmailSend = new ShortForwardEmailSend();
        shortForwardEmailSend.setReEmail(shortBrevo.getReceiveEmail());
        Message message = null;
        if (type.equals("forward")) {

            shortForwardEmailSend.setCreateTime(DateUtils.getNowDate());
            shortForwardEmailSend.setSentDate(DateUtils.getNowDate());
            shortForwardEmailSend.setAppId(shortBrevo.getAppId());


            ShortForwartEmailDomain shortForwartEmailDomain = new ShortForwartEmailDomain();
            shortForwartEmailDomain.setAppId(shortBrevo.getAppId());
            List<ShortForwartEmailDomain> list = shortForwartEmailDomainMapper.selectShortForwartEmailDomainList(shortForwartEmailDomain);
            if (!list.isEmpty()) {

                String username = list.get(0).getDomain(); // 你的Forward Email地址
                String password = AESEncryption.decrypt(list.get(0).getPassword()); // 你的密码
                shortForwardEmailSend.setDomain(username);


                String host = "smtp.forwardemail.net"; // Forward Email SMTP服务器
                Properties props = new Properties();
                props.put("mail.smtp.host", host);
                props.put("mail.smtp.port", "465");
                props.put("mail.smtp.ssl.enable", "true");
                props.put("mail.smtp.auth", "true");


                try {
                    // 创建会话
                    Session session = Session.getInstance(props,
                            new Authenticator() {
                                protected PasswordAuthentication getPasswordAuthentication() {
                                    return new PasswordAuthentication(username, password);
                                }
                            });

                    // 创建邮件

                    message = new MimeMessage(session);
                    message.setFrom(new InternetAddress(username));
                    message.setRecipients(Message.RecipientType.TO,
                            InternetAddress.parse(shortForwardEmailSend.getReEmail()));

                } catch (MessagingException e) {
                }
            }


        }

        // 3. 设置发件人
        SendSmtpEmailSender sender = new SendSmtpEmailSender();
        ShortEmailDomain shortEmailDomain = shortEmailDomainService.getrandomDomain(shortBrevo.getAppId());

        if (type.equals("brevo")) {
            // 2. 初始化邮件API
            ApiClient defaultClient = Configuration.getDefaultApiClient();
            ApiKeyAuth apiKey = (ApiKeyAuth) defaultClient.getAuthentication("api-key");
            apiKey.setApiKey(brevoApiKey); // 替换为你的Brevo API密钥

            if (null != shortEmailDomain) {
                sender.setEmail(shortEmailDomain.getDomain()); // 必须已验证的邮箱
                sender.setName(shortEmailDomain.getAppName());
            } else {
                sender.setEmail(senderEmail); // 必须已验证的邮箱
                sender.setName(senderName);
            }


            List<SendSmtpEmailTo> toList = new ArrayList<>();
            // 4. 设置收件人
            SendSmtpEmailTo recipient = new SendSmtpEmailTo();
            recipient.setEmail(null != shortBrevo.getReceiveEmail() ? shortBrevo.getReceiveEmail() : null);
            recipient.setName(null != shortBrevo.getReceiveName() ? shortBrevo.getReceiveName() : null);
            toList.add(recipient);
            email.setSender(sender);
            email.setTo(toList);
        } else if (type.equals("aws")) {
            emailDTO.setReceiveEmail(ListUtil.toList(shortBrevo.getReceiveEmail()));
            emailDTO.setSendChannel("aws");
            emailDTO.setServiceType("awsSimpl");
            emailDTO.setTitle("Kushort");
        }
        // 记录日志并处理结果
        ShortRunlog runlog = new ShortRunlog();
        if (inType.equals("weekly"))
            runlog.setType("发送新剧上新推荐邮件");
        if (inType.equals("payFailed"))
            runlog.setType("发送新剧上新推荐邮件");
        runlog.setCreateTime(DateUtils.getNowDate());
        runlog.setUpdateTime(DateUtils.getNowDate());

        try {

//            String subject = getSubject(shortBrevo);
            String subject = "";
            if(inType.equals("weekly"))
                subject = shortEmailDomain.getAppName()+ " New dramas";
            if(inType.equals("payFailed"))
                subject = "Payment Failed – Payment Issue with Your "+shortEmailDomain.getAppName()+" Subscription $"+shortBrevo.getPayAccount();
            if(inType.equals("sendMonitorEmail"))
                subject = "监控";

//            String textContent = getUnSubTextContent(shortBrevo);
            // 5. 从HTML文件读取内容
            String htmlName = "";
            if (inType.equals("weekly")) {
                if (null == shortBrevo.getLanguageCode() || shortBrevo.getLanguageCode().equals("en-US")) {
                    if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                        htmlName = "newArrivals.html";
                    else
                        htmlName = shortBrevo.getHtmlName();
                } else if (shortBrevo.getLanguageCode().equals("es")) {
                    if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                        htmlName = "es-newArrivals.html";
                    else
                        htmlName = shortBrevo.getHtmlName();
                } else if (shortBrevo.getLanguageCode().equals("pt")) {
                    if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                        htmlName = "pt-newArrivals.html";
                    else
                        htmlName = shortBrevo.getHtmlName();
                } else if (shortBrevo.getLanguageCode().equals("zh-TW")) {
                    if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                        htmlName = "zh-TW-newArrivals.html";
                    else
                        htmlName = shortBrevo.getHtmlName();
                } else if (shortBrevo.getLanguageCode().equals("ja")) {
                    if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                        htmlName = "ja-newArrivals.html";
                    else
                        htmlName = shortBrevo.getHtmlName();
                } else if (shortBrevo.getLanguageCode().equals("de")) {
                    if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                        htmlName = "de-newArrivals.html";
                    else
                        htmlName = shortBrevo.getHtmlName();
                }
            }
            if (inType.equals("payFailed")) {
                if (null == shortBrevo.getLanguageCode() || shortBrevo.getLanguageCode().equals("en-US")) {
                    if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                        htmlName = "buyFaild.html";
                    else
                        htmlName = shortBrevo.getHtmlName();
                } else if (shortBrevo.getLanguageCode().equals("es")) {
                    if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                        htmlName = "es-buyFaild.html";
                    else
                        htmlName = shortBrevo.getHtmlName();
                } else if (shortBrevo.getLanguageCode().equals("pt")) {
                    if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                        htmlName = "pt-buyFaild.html";
                    else
                        htmlName = shortBrevo.getHtmlName();
                } else if (shortBrevo.getLanguageCode().equals("zh-TW")) {
                    if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                        htmlName = "zh-TW-buyFaild.html";
                    else
                        htmlName = shortBrevo.getHtmlName();
                } else if (shortBrevo.getLanguageCode().equals("ja")) {
                    if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                        htmlName = "ja-buyFaild.html";
                    else
                        htmlName = shortBrevo.getHtmlName();
                } else if (shortBrevo.getLanguageCode().equals("de")) {
                    if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                        htmlName = "de-buyFaild.html";
                    else
                        htmlName = shortBrevo.getHtmlName();
                }
            }


            ClassPathResource resource = new ClassPathResource("templates/" + htmlName);
            String htmlContent = StreamUtils.copyToString(
                    resource.getInputStream(),
                    StandardCharsets.UTF_8
            );

            if (inType.equals("weekly")) {
                int randomFourDigit1 = ThreadLocalRandom.current().nextInt(1000, 10000);
                int randomFourDigit2 = ThreadLocalRandom.current().nextInt(1000, 10000);
                int randomFourDigit3 = ThreadLocalRandom.current().nextInt(1000, 10000);
                int randomFourDigit4 = ThreadLocalRandom.current().nextInt(1000, 10000);
                int randomFourDigit5 = ThreadLocalRandom.current().nextInt(1000, 10000);
                int randomFourDigit6 = ThreadLocalRandom.current().nextInt(1000, 10000);
                int randomFourDigit7 = ThreadLocalRandom.current().nextInt(1000, 10000);
                int randomFourDigit8 = ThreadLocalRandom.current().nextInt(1000, 10000);
                int randomFourDigit9 = ThreadLocalRandom.current().nextInt(1000, 10000);
                int randomFourDigit10 = ThreadLocalRandom.current().nextInt(1000, 10000);

                String randomNum = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                String randomNum1 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                String movieUrl1 = shortEmailDomain.getAppDomain() + "/video/?id=" + randomFourDigit1 + shortMovieList.get(0).getId() + "&userId=" + randomNum + shortBrevo.getHashId() + randomNum1 + "&uniqueId=" + shortBrevo.getUniqueId();

                String randomNum2 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                String randomNum3 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                String movieUrl2 = shortEmailDomain.getAppDomain() + "/video/?id=" + randomFourDigit2 + shortMovieList.get(1).getId() + "&userId=" + randomNum2 + shortBrevo.getHashId() + randomNum3 + "&uniqueId=" + shortBrevo.getUniqueId();


                String randomNum4 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                String randomNum5 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                String movieUrl3 = shortEmailDomain.getAppDomain() + "/video/?id=" + randomFourDigit3 + shortMovieList.get(2).getId() + "&userId=" + randomNum4 + shortBrevo.getHashId() + randomNum5 + "&uniqueId=" + shortBrevo.getUniqueId();


                String randomNum6 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                String randomNum7 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                String movieUrl4 = shortEmailDomain.getAppDomain() + "/video/?id=" + randomFourDigit4 + shortMovieList.get(3).getId() + "&userId=" + randomNum6 + shortBrevo.getHashId() + randomNum7 + "&uniqueId=" + shortBrevo.getUniqueId();

                String randomNum8 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                String randomNum9 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                String movieUrl5 = shortEmailDomain.getAppDomain() + "/video/?id=" + randomFourDigit5 + shortMovieList.get(4).getId() + "&userId=" + randomNum8 + shortBrevo.getHashId() + randomNum9 + "&uniqueId=" + shortBrevo.getUniqueId();

                String randomNum10 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                String randomNum11 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                String movieUrl6 = shortEmailDomain.getAppDomain() + "/video/?id=" + randomFourDigit6 + shortMovieList.get(5).getId() + "&userId=" + randomNum10 + shortBrevo.getHashId() + randomNum11 + "&uniqueId=" + shortBrevo.getUniqueId();

                String randomNum12 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                String randomNum13 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                String movieUrl7 = shortEmailDomain.getAppDomain() + "/video/?id=" + randomFourDigit7 + shortMovieList.get(6).getId() + "&userId=" + randomNum12 + shortBrevo.getHashId() + randomNum13 + "&uniqueId=" + shortBrevo.getUniqueId();

                String randomNum14 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                String randomNum15 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                String movieUrl8 = shortEmailDomain.getAppDomain() + "/video/?id=" + randomFourDigit8 + shortMovieList.get(7).getId() + "&userId=" + randomNum14 + shortBrevo.getHashId() + randomNum15 + "&uniqueId=" + shortBrevo.getUniqueId();

                String randomNum16 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                String randomNum17 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                String movieUrl9 = shortEmailDomain.getAppDomain() + "/video/?id=" + randomFourDigit9 + shortMovieList.get(8).getId() + "&userId=" + randomNum16 + shortBrevo.getHashId() + randomNum17 + "&uniqueId=" + shortBrevo.getUniqueId();

                String randomNum18 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                String randomNum19 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
                String movieUrl10 = shortEmailDomain.getAppDomain() + "/video/?id=" + randomFourDigit10 + shortMovieList.get(9).getId() + "&userId=" + randomNum18 + shortBrevo.getHashId() + randomNum19 + "&uniqueId=" + shortBrevo.getUniqueId();


                // 使用更安全的占位符格式
                htmlContent = htmlContent
                        .replace("${appName}", shortEmailDomain.getAppName())
                        .replace("{{appName}}", shortEmailDomain.getAppName())


                        .replace("${movieIcon1}", null != shortMovieList.get(0).getIcon() ? shortMovieList.get(0).getIcon() : "")
                        .replace("{{movieIcon1}}", null != shortMovieList.get(0).getIcon() ? shortMovieList.get(0).getIcon() : "")

                        .replace("${movieIcon2}", null != shortMovieList.get(1).getIcon() ? shortMovieList.get(1).getIcon() : "")
                        .replace("{{movieIcon2}}", null != shortMovieList.get(1).getIcon() ? shortMovieList.get(1).getIcon() : "")

                        .replace("${movieIcon3}", null != shortMovieList.get(2).getIcon() ? shortMovieList.get(2).getIcon() : "")
                        .replace("{{movieIcon3}}", null != shortMovieList.get(2).getIcon() ? shortMovieList.get(2).getIcon() : "1")

                        .replace("${movieIcon4}", null != shortMovieList.get(3).getIcon() ? shortMovieList.get(3).getIcon() : "")
                        .replace("{{movieIcon4}}", null != shortMovieList.get(3).getIcon() ? shortMovieList.get(3).getIcon() : "")

                        .replace("${movieIcon5}", null != shortMovieList.get(4).getIcon() ? shortMovieList.get(4).getIcon() : "")
                        .replace("{{movieIcon5}}", null != shortMovieList.get(4).getIcon() ? shortMovieList.get(4).getIcon() : "")

                        .replace("${movieIcon6}", null != shortMovieList.get(5).getIcon() ? shortMovieList.get(5).getIcon() : "")
                        .replace("{{movieIcon6}}", null != shortMovieList.get(5).getIcon() ? shortMovieList.get(5).getIcon() : "")

                        .replace("${movieIcon7}", null != shortMovieList.get(6).getIcon() ? shortMovieList.get(6).getIcon() : "")
                        .replace("{{movieIcon7}}", null != shortMovieList.get(6).getIcon() ? shortMovieList.get(6).getIcon() : "")
                        .replace("${movieIcon8}", null != shortMovieList.get(7).getIcon() ? shortMovieList.get(7).getIcon() : "")
                        .replace("{{movieIcon8}}", null != shortMovieList.get(7).getIcon() ? shortMovieList.get(7).getIcon() : "")
                        .replace("${movieIcon9}", null != shortMovieList.get(8).getIcon() ? shortMovieList.get(8).getIcon() : "")
                        .replace("{{movieIcon9}}", null != shortMovieList.get(8).getIcon() ? shortMovieList.get(8).getIcon() : "")
                        .replace("${movieIcon10}", null != shortMovieList.get(9).getIcon() ? shortMovieList.get(9).getIcon() : "")
                        .replace("{{movieIcon10}}", null != shortMovieList.get(9).getIcon() ? shortMovieList.get(9).getIcon() : "")


                        .replace("${movieName1}", null != shortMovieList.get(0).getName() ? shortMovieList.get(0).getName() : "")
                        .replace("{{movieName1}}", null != shortMovieList.get(0).getName() ? shortMovieList.get(0).getName() : "")

                        .replace("${movieName2}", null != shortMovieList.get(1).getName() ? shortMovieList.get(1).getName() : "")
                        .replace("{{movieName2}}", null != shortMovieList.get(1).getName() ? shortMovieList.get(1).getName() : "")

                        .replace("${movieName3}", null != shortMovieList.get(2).getName() ? shortMovieList.get(2).getName() : "")
                        .replace("{{movieName3}}", null != shortMovieList.get(2).getName() ? shortMovieList.get(2).getName() : "")

                        .replace("${movieName4}", null != shortMovieList.get(3).getName() ? shortMovieList.get(3).getName() : "")
                        .replace("{{movieName4}}", null != shortMovieList.get(3).getName() ? shortMovieList.get(3).getName() : "")

                        .replace("${movieName5}", null != shortMovieList.get(4).getName() ? shortMovieList.get(4).getName() : "")
                        .replace("{{movieName5}}", null != shortMovieList.get(4).getName() ? shortMovieList.get(4).getName() : "")

                        .replace("${movieName6}", null != shortMovieList.get(5).getName() ? shortMovieList.get(5).getName() : "")
                        .replace("{{movieName6}}", null != shortMovieList.get(5).getName() ? shortMovieList.get(5).getName() : "")

                        .replace("${movieName7}", null != shortMovieList.get(6).getName() ? shortMovieList.get(6).getName() : "")
                        .replace("{{movieName7}}", null != shortMovieList.get(6).getName() ? shortMovieList.get(6).getName() : "")
                        .replace("${movieName8}", null != shortMovieList.get(7).getName() ? shortMovieList.get(7).getName() : "")
                        .replace("{{movieName8}}", null != shortMovieList.get(7).getName() ? shortMovieList.get(7).getName() : "")
                        .replace("${movieName9}", null != shortMovieList.get(8).getName() ? shortMovieList.get(8).getName() : "")
                        .replace("{{movieName9}}", null != shortMovieList.get(8).getName() ? shortMovieList.get(8).getName() : "")
                        .replace("${movieName10}", null != shortMovieList.get(9).getName() ? shortMovieList.get(9).getName() : "")
                        .replace("{{movieName10}}", null != shortMovieList.get(9).getName() ? shortMovieList.get(9).getName() : "")

                        .replace("${movieUrl1}", movieUrl1)
                        .replace("{{movieUrl1}}", movieUrl1)

                        .replace("${movieUrl2}", movieUrl2)
                        .replace("{{movieUrl2}}", movieUrl2)

                        .replace("${movieUrl3}", movieUrl3)
                        .replace("{{movieUrl3}}", movieUrl3)

                        .replace("${movieUrl4}", movieUrl4)
                        .replace("{{movieUrl4}}", movieUrl4)

                        .replace("${movieUrl5}", movieUrl5)
                        .replace("{{movieUrl5}}", movieUrl5)
                        .replace("${movieUrl6}", movieUrl6)
                        .replace("{{movieUrl6}}", movieUrl6)

                        .replace("${movieUrl7}", movieUrl7)
                        .replace("{{movieUrl7}}", movieUrl7)
                        .replace("${movieUrl8}", movieUrl8)
                        .replace("{{movieUrl8}}", movieUrl8)
                        .replace("${movieUrl9}", movieUrl9)
                        .replace("{{movieUrl9}}", movieUrl9)
                        .replace("${movieUrl10}", movieUrl10)
                        .replace("{{movieUrl10}}", movieUrl10)

                        .replace("${account}", shortBrevo.getReceiveEmail())
                        .replace("{{account}}", shortBrevo.getReceiveEmail())
                        .replace("${password}", shortBrevo.getPassword())
                        .replace("{{password}}", shortBrevo.getPassword());
            }

            if (inType.equals("payFailed")) {
                // 使用更安全的占位符格式
                htmlContent = htmlContent
                        .replace("${appName}", shortEmailDomain.getAppName())
                        .replace("{{appName}}", shortEmailDomain.getAppName())

                        .replace("${buyPrice}", shortBrevo.getPayAccount())
                        .replace("{{buyPrice}}", shortBrevo.getPayAccount());
            }


            if (type.equals("brevo")) {
                // 6. 创建邮件内容
                email.setSubject(subject);
                if(inType.equals("sendMonitorEmail")) {
                    email.setTextContent("中间状态表异常！");
                }else{
                    email.setHtmlContent(htmlContent);// 使用HTML内容
                }
            }else if(type.equals("forward")){

                message.setSubject(subject);
                shortForwardEmailSend.setSubject(subject);

                // 创建邮件正文部分
                MimeBodyPart textPart = new MimeBodyPart();

                // 组合 HTML 内容
                textPart.setContent(htmlContent, "text/html; charset=utf-8");

                // 组合各部分
                Multipart multipart = new MimeMultipart();
                multipart.addBodyPart(textPart);

                message.setContent(multipart);
                shortForwardEmailSend.setContent(htmlContent);

            } else {
                emailDTO.setSubject(subject);
                emailDTO.setContent(htmlContent);
            }

            Message finalMessage = message;
            new Thread(() -> {
                try {
                    runlog.setState("1");
                    String content = "";
                    if (type.equals("brevo")) {
                        // 7. 发送邮件
                        CreateSmtpEmail response = apiInstance.sendTransacEmail(email);
                        content = String.format(",邮件发送成功！消息ID: " + response.getMessageId() + "用户Id：" + shortBrevo.getUserId() +
                                ",邮件主题：" + shortBrevo.getSubject() + ",收件人名称：" + shortBrevo.getReceiveName() +
                                ",收件人邮件：" + shortBrevo.getReceiveEmail() + ",邮件服务商:" + type);
                        runlog.setNote(response.toString());
                    } else if (type.equals("forward")) {
                        // 发送邮件
                        Transport.send(finalMessage);
                        content = String.format(",邮件发送成功！" + "用户Id：" + shortBrevo.getUserId() +
                                ",邮件主题：" + shortBrevo.getSubject() + ",收件人名称：" + shortBrevo.getReceiveName() +
                                ",收件人邮件：" + shortBrevo.getReceiveEmail() + ",邮件服务商:" + type);

                        shortForwardEmailSend.setType(1L);
                        shortForwardEmailSendMapper.insertShortForwardEmailSend(shortForwardEmailSend);

                    }
                    runlog.setContent(content);
                    shortRunlogService.insertShortRunlog(runlog);
                } catch (Exception e) {
                    runlog.setState("0");
                    runlog.setContent(String.format(",发送失败：" + e.getMessage() + ",邮件主题：" + shortBrevo.getSubject() +
                            ",收件人名称：" + shortBrevo.getReceiveName() + ",收件人邮件：" + shortBrevo.getReceiveEmail()) + ",邮件服务商:" + type);
                    runlog.setNote(e.toString());
                    shortRunlogService.insertShortRunlog(runlog);
                }
            }).start();

//            String randomNum = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 4);
//            Map<String, String> vars = new HashMap<>();
//            vars.put("uId", randomNum + shortBrevo.getUserId());
//            vars.put("uniqueId", shortBrevo.getUniqueId());
//            return AjaxResult.success(vars);
        } catch (Exception e) {
            System.err.println("发送失败: " + e.getMessage());
            runlog.setState("0");
            runlog.setContent(String.format(",发送失败：" + e.getMessage() + ",邮件主题：" + shortBrevo.getSubject() + ",收件人名称：" + shortBrevo.getReceiveName() + ",收件人邮件：" + shortBrevo.getReceiveEmail()));
            runlog.setNote(e.toString());
            shortRunlogService.insertShortRunlog(runlog);
//            return AjaxResult.error("发送失败: " + e.getMessage());
        }
    }

    @Override
    public void createCustomers(ShortUser shortUser) {
        if (shortUser != null) {
            ShortApp shortApp = shortAppService.selectShortAppById(shortUser.getAppId());
            // 处理支付Token和客户ID
            try {
                //判断使用 空中云汇 还是 useepay
                ShortExtplats shortExtplats = shortExtplatsService.selectShortExtplatsById(shortApp.getExtplatsId());
//                    if(null == shortExtplats){
//                        log.error("未找到该app的支付接口");
//                        return resultMap;
//                    }
                String payChannel = shortExtplats.getPayChannel();
                if (PayChannel.USEEPAY.getCode().equals(payChannel)) {
                    createUseepayCustomer(shortUser, shortApp);
                } else if (PayChannel.AIRWALLEX.getCode().equals(payChannel)) {
                    createAirwallexCustomer(shortUser, shortApp);
                }/*else {
                        log.error("未找到该app的支付接口");
                        return resultMap;
                    }*/
            } catch (Exception e) {
                // 记录异常日志
                ShortRunlog runlog = new ShortRunlog();
                runlog.setType("支付customer_id");
                runlog.setState("0");
                runlog.setContent("用户：" + shortUser.getId() + "，创建支付customer_id失败：" + e.getMessage());
                runlog.setNote(e.getMessage());
                shortRunlogService.insertShortRunlog(runlog);
                log.error("创建支付customer_id失败", e);
            }
        }
    }
    @Override
    public AjaxResult sendMonitorEmail(String publicKey) {

        try {
            String privateKey = "-----BEGIN OPENSSH PRIVATE KEY-----\n" +
                    "b3BlbnNzaC1rZXktdjEAAAAABG5vbmUAAAAEbm9uZQAAAAAAAAABAAAAMwAAAAtzc2gtZW\n" +
                    "QyNTUxOQAAACDdx+eInOus2G8pGSS+paM8QV5SkQdAXGh/e0Z7pRaDRwAAAJiIp66uiKeu\n" +
                    "rgAAAAtzc2gtZWQyNTUxOQAAACDdx+eInOus2G8pGSS+paM8QV5SkQdAXGh/e0Z7pRaDRw\n" +
                    "AAAEDJW6rFB140gGyRJZM+mqoIFj8HAGrGpnuWj+sCJa/06N3H54ic66zYbykZJL6lozxB\n" +
                    "********************************************************\n" +
                    "-----END OPENSSH PRIVATE KEY-----";

            // 2. 加载密钥对
            KeyPair keyPair = KeyPair.load(new JSch(), privateKey.getBytes(), null);
            byte[] derivedPublicKeyBlob = keyPair.getPublicKeyBlob();

            // 3. 解析用户公钥（按空格分割并解码Base64）
            String[] publicKeyParts = publicKey.split(" ");
            if (publicKeyParts.length < 2) {
                throw new IllegalArgumentException("Invalid public key format");
            }
            byte[] userPublicKeyBlob = Base64.getDecoder().decode(publicKeyParts[1]);

            // 4. 安全比对（避免时序攻击）
            boolean isMatch = MessageDigest.isEqual(derivedPublicKeyBlob, userPublicKeyBlob);
            if (isMatch) {
                log.info("验证密钥正确------");
//================================
                int count =shortIntermedStatusService.getAfter3Hour();
                if(count>0){
                    InitUserDTO shortBrevo = new InitUserDTO();
                    shortBrevo.setReceiveEmail("<EMAIL>");
                    List<ShortMovie> shortMovieList = new ArrayList<>();
                    sendEmailForInType(shortBrevo, shortMovieList, "brevo", "sendMonitorEmail");
                }
//================================
                return AjaxResult.success("操作成功=");
            }
            return AjaxResult.success("操作成功");

        } catch (Exception e) {
            return AjaxResult.error("操作失败");
        }
    }

    @Override
    public AjaxResult toSend(InitUserDTO shortBrevo, HttpServletRequest request) throws Exception {
        if (StringUtils.isEmpty(shortBrevo.getReceiveEmail()))
            return AjaxResult.error("Please fill in the recipient's email address!");
        if ("".equals(shortBrevo.getReceiveName()))
            shortBrevo.setReceiveName(null);
        if ("".equals(shortBrevo.getSubject()))
            shortBrevo.setSubject(null);
        if (null == shortBrevo.getLanguageCode())
            shortBrevo.setLanguageCode("en-US");
        String appId = request.getHeader("NID");
        String uid = request.getHeader("UID");
        if (StringUtils.isNotEmpty(uid)) {
            // 处理uid前缀
            if (uid.length() > 4) {
                uid = uid.substring(4);
            }
        }
        shortBrevo.setUserId(uid);

        shortBrevo.setAppId(Long.valueOf(appId));
        ShortUser oldUser = filterUser(shortBrevo);

//        if(StringUtils.isNotEmpty(appId) && !appId.equals("25")){
//            int count = shortUserService.countByEmailAndAppId("25",oldUser.getEmail());
//            if(count >0){
//                Date newExpireTime = null;
//                Date startDate = new Date();
//                if (oldUser.getExpireTime() != null &&
//                        oldUser.getExpireTime().after(DateUtils.getNowDate())) {
//                    // 在现有过期时间基础上添加天数
//                    Calendar calendar = Calendar.getInstance();
//                    calendar.setTime(oldUser.getExpireTime());
//                    calendar.add(Calendar.DAY_OF_YEAR, 7);
//                    newExpireTime = calendar.getTime();
//                    startDate = oldUser.getExpireTime();
//                } else {
//                    // 从当前时间开始计算
//                    Calendar calendar = Calendar.getInstance();
//                    calendar.add(Calendar.DAY_OF_YEAR, 7);
//                    newExpireTime = calendar.getTime();
//                }
//
//                HashMap map = new HashMap();
//                map.put("subscriptionType",7);
//                map.put("msg","Das System bietet eine siebentägige Mitgliedschaft");
//                // 格式化时间为指定格式
//                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//                map.put("startDate",sdf.format(startDate));
//                map.put("endDate",sdf.format(newExpireTime));
//
//                ObjectMapper objectMapper = new ObjectMapper();
//                String json = objectMapper.writeValueAsString(map);
//                oldUser.setExpireTime(newExpireTime);
//                oldUser.setRemark(json);
//                shortUserService.updateShortUser(oldUser);
//            }
//
//        }

        if (null != oldUser && !oldUser.getId().equals(shortBrevo.getUserId())) {

            if(shortBrevo.getSendType() == 1){
                ShortMovie shortMovie = shortMovieService.selectShortMovieById(Long.valueOf(shortBrevo.getMovie()));
                //获取movieId
                List<Long> movieIdCollect = new ArrayList<>();
                movieIdCollect.add(shortMovie.getId());
                //查询翻译后的剧
                List<ShortMovieI18n> shortMovieI18nList = shortMovieI18nService.queryTranslateMovieList(movieIdCollect, shortBrevo.getLanguageCode());
                if (null != shortMovieI18nList && !shortMovieI18nList.isEmpty())
                    shortMovie.setIcon(shortMovieI18nList.get(0).getIcon());

                return sendWelcomeEmailForward(shortBrevo, shortMovie, "brevo");
            }


            // 生成4位随机数字 混淆用户ID
            // 使用UUID的hashCode值转为字符串并取前4位
            String randomNum = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 4);
            Map<String, String> vars = new HashMap<>();
            vars.put("uId", randomNum + oldUser.getId());
            vars.put("uniqueId", shortBrevo.getUniqueId());
            return AjaxResult.success(vars);
        }

        ShortMovie shortMovie = shortMovieService.selectShortMovieById(Long.valueOf(shortBrevo.getMovie()));
        //获取movieId
        List<Long> movieIdCollect = new ArrayList<>();
        movieIdCollect.add(shortMovie.getId());
        //查询翻译后的剧
        List<ShortMovieI18n> shortMovieI18nList = shortMovieI18nService.queryTranslateMovieList(movieIdCollect, shortBrevo.getLanguageCode());
        if (null != shortMovieI18nList && !shortMovieI18nList.isEmpty())
            shortMovie.setIcon(shortMovieI18nList.get(0).getIcon());

        return sendWelcomeEmailForward(shortBrevo, shortMovie, "brevo");
//        return sendWelcomeEmailForward(shortBrevo, shortMovie, "forward");
    }

    @Override
    public AjaxResult updateUserIsSubscriber(String publicKey) {

        try {
            String privateKey = "-----BEGIN OPENSSH PRIVATE KEY-----\n" +
                    "b3BlbnNzaC1rZXktdjEAAAAABG5vbmUAAAAEbm9uZQAAAAAAAAABAAAAMwAAAAtzc2gtZW\n" +
                    "QyNTUxOQAAACDdx+eInOus2G8pGSS+paM8QV5SkQdAXGh/e0Z7pRaDRwAAAJiIp66uiKeu\n" +
                    "rgAAAAtzc2gtZWQyNTUxOQAAACDdx+eInOus2G8pGSS+paM8QV5SkQdAXGh/e0Z7pRaDRw\n" +
                    "AAAEDJW6rFB140gGyRJZM+mqoIFj8HAGrGpnuWj+sCJa/06N3H54ic66zYbykZJL6lozxB\n" +
                    "********************************************************\n" +
                    "-----END OPENSSH PRIVATE KEY-----";

            // 2. 加载密钥对
            KeyPair keyPair = KeyPair.load(new JSch(), privateKey.getBytes(), null);
            byte[] derivedPublicKeyBlob = keyPair.getPublicKeyBlob();

            // 3. 解析用户公钥（按空格分割并解码Base64）
            String[] publicKeyParts = publicKey.split(" ");
            if (publicKeyParts.length < 2) {
                throw new IllegalArgumentException("Invalid public key format");
            }
            byte[] userPublicKeyBlob = Base64.getDecoder().decode(publicKeyParts[1]);

            // 4. 安全比对（避免时序攻击）
            boolean isMatch = MessageDigest.isEqual(derivedPublicKeyBlob, userPublicKeyBlob);
            if (isMatch) {
                log.info("验证密钥正确------会员过期修改会员订阅状态");

                List<Long> ids = shortUserService.getExpiredUser();
                if(!ids.isEmpty())
                    shortUserService.updateIsSubscriberByIds(ids);

                return AjaxResult.success("操作成功=");
            }
            return AjaxResult.success("操作成功");

        } catch (Exception e) {
            return AjaxResult.error("操作失败");
        }
    }


    public AjaxResult sendSubEmailForward1(InitUserDTO shortBrevo, ShortMovie shortMovie, String type, ShortOrder order) throws Exception {
        SendSmtpEmail email = new SendSmtpEmail();
        TransactionalEmailsApi apiInstance = new TransactionalEmailsApi();
        EmailDTO emailDTO = new EmailDTO();
        ShortForwardEmailSend shortForwardEmailSend = new ShortForwardEmailSend();
        shortForwardEmailSend.setReEmail(shortBrevo.getReceiveEmail());
        Message message = null;
        if (type.equals("forward")) {

            shortForwardEmailSend.setCreateTime(DateUtils.getNowDate());
            shortForwardEmailSend.setSentDate(DateUtils.getNowDate());
            shortForwardEmailSend.setAppId(shortBrevo.getAppId());


            ShortForwartEmailDomain shortForwartEmailDomain = new ShortForwartEmailDomain();
            shortForwartEmailDomain.setAppId(shortBrevo.getAppId());
            List<ShortForwartEmailDomain> list = shortForwartEmailDomainMapper.selectShortForwartEmailDomainList(shortForwartEmailDomain);
            if (!list.isEmpty()) {

                String username = list.get(0).getDomain(); // 你的Forward Email地址
                String password = AESEncryption.decrypt(list.get(0).getPassword()); // 你的密码
                shortForwardEmailSend.setDomain(username);


                String host = "smtp.forwardemail.net"; // Forward Email SMTP服务器
                Properties props = new Properties();
                props.put("mail.smtp.host", host);
                props.put("mail.smtp.port", "465");
                props.put("mail.smtp.ssl.enable", "true");
                props.put("mail.smtp.auth", "true");


                try {
                    // 创建会话
                    Session session = Session.getInstance(props,
                            new Authenticator() {
                                protected PasswordAuthentication getPasswordAuthentication() {
                                    return new PasswordAuthentication(username, password);
                                }
                            });

                    // 创建邮件

                    message = new MimeMessage(session);
                    message.setFrom(new InternetAddress(username));
                    message.setRecipients(Message.RecipientType.TO,
                            InternetAddress.parse(shortForwardEmailSend.getReEmail()));

                } catch (MessagingException e) {
                }
            }


        }

        // 3. 设置发件人
        SendSmtpEmailSender sender = new SendSmtpEmailSender();
        ShortEmailDomain shortEmailDomain = shortEmailDomainService.getrandomDomain(shortBrevo.getAppId());

        if (type.equals("brevo")) {
            // 2. 初始化邮件API
            ApiClient defaultClient = Configuration.getDefaultApiClient();
            ApiKeyAuth apiKey = (ApiKeyAuth) defaultClient.getAuthentication("api-key");
            apiKey.setApiKey(brevoApiKey); // 替换为你的Brevo API密钥

            if (null != shortEmailDomain) {
                sender.setEmail(shortEmailDomain.getDomain()); // 必须已验证的邮箱
                sender.setName(shortEmailDomain.getAppName());
            } else {
                sender.setEmail(senderEmail); // 必须已验证的邮箱
                sender.setName(senderName);
            }


            List<SendSmtpEmailTo> toList = new ArrayList<>();
            // 4. 设置收件人
            SendSmtpEmailTo recipient = new SendSmtpEmailTo();
            recipient.setEmail(null != shortBrevo.getReceiveEmail() ? shortBrevo.getReceiveEmail() : null);
            recipient.setName(null != shortBrevo.getReceiveName() ? shortBrevo.getReceiveName() : null);
            toList.add(recipient);
            email.setSender(sender);
            email.setTo(toList);
        } else if (type.equals("aws")) {
            emailDTO.setReceiveEmail(ListUtil.toList(shortBrevo.getReceiveEmail()));
            emailDTO.setSendChannel("aws");
            emailDTO.setServiceType("awsSimpl");
            emailDTO.setTitle("Kushort");
        }
        // 记录日志并处理结果
        ShortRunlog runlog = new ShortRunlog();
        runlog.setType("发送订阅续费成功邮件");
        runlog.setCreateTime(DateUtils.getNowDate());
        runlog.setUpdateTime(DateUtils.getNowDate());

        try {
            // 5. 从HTML文件读取内容
            String htmlName = "";

            if (null == shortBrevo.getLanguageCode() || shortBrevo.getLanguageCode().equals("en-US")) {
                if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                    htmlName = "sub1.html";
                else
                    htmlName = shortBrevo.getHtmlName();
            } else if (shortBrevo.getLanguageCode().equals("es")) {
                if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                    htmlName = "es-sub1.html";
                else
                    htmlName = shortBrevo.getHtmlName();
            } else if (shortBrevo.getLanguageCode().equals("pt")) {
                if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                    htmlName = "pt-sub1.html";
                else
                    htmlName = shortBrevo.getHtmlName();
            } else if (shortBrevo.getLanguageCode().equals("zh-TW")) {
                if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                    htmlName = "zh-TW-sub1.html";
                else
                    htmlName = shortBrevo.getHtmlName();
            } else if (shortBrevo.getLanguageCode().equals("ja")) {
                if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                    htmlName = "ja-sub1.html";
                else
                    htmlName = shortBrevo.getHtmlName();
            } else if (shortBrevo.getLanguageCode().equals("de")) {
                if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                    htmlName = "de-sub1.html";
                else
                    htmlName = shortBrevo.getHtmlName();
            }


            ClassPathResource resource = new ClassPathResource("templates/" + htmlName);
            String htmlContent = StreamUtils.copyToString(
                    resource.getInputStream(),
                    StandardCharsets.UTF_8
            );
//            String subject = getSubject(shortBrevo);
            String subject = shortEmailDomain.getAppName() + " Renewal Reminder";


            String appDomain = null != shortEmailDomain.getAppDomain() ? shortEmailDomain.getAppDomain() : "";

            String randomNumApp1 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
            String randomNumApp2 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
//            ?userId=前六   + +  后六&uniqueId=
            ShortUser shortUser = shortUserService.selectShortUserById(Long.valueOf(shortBrevo.getUserId()));

            String unSubUrl = appDomain + "/subscription-records/" + "?userId=" + randomNumApp1 + shortUser.getHashId() + randomNumApp2 + "&uniqueId=" + shortBrevo.getUniqueId() + "&language=" + shortBrevo.getLanguageCode() + "&noLogin=true";
            if (null != shortUser)
                appDomain = appDomain + "?userId=" + randomNumApp1 + shortUser.getHashId() + randomNumApp2 + "&uniqueId=" + shortBrevo.getUniqueId() + "&language=" + shortBrevo.getLanguageCode();


            ShortPage shortPage = new ShortPage();
            shortPage.setAppId(String.valueOf(shortUser.getAppId()));
            shortPage.setCname("投诉和反馈");
            List<ShortPage> shortPageList = shortPageService.selectShortPageList(shortPage);


            String dayType = order.getSubscriptionType();
            Date payTime = order.getPayTime();
            BigDecimal buyPrice = order.getPaymentAmount();
            BigDecimal renewalPrice = order.getPaymentAmount();
//            http://localhost:3000/subscription-records/

            // 使用更安全的占位符格式
            htmlContent = htmlContent
                    .replace("${user_name}", null != shortBrevo.getUser_name() ? shortBrevo.getUser_name() : "")
                    .replace("${movieurl}", null != shortMovie.getIcon() ? shortMovie.getIcon() : "")
                    .replace("{{movieurl}}", null != shortMovie.getIcon() ? shortMovie.getIcon() : "")

                    .replace("${appName}", null != shortEmailDomain.getAppName() ? shortEmailDomain.getAppName() : "")
                    .replace("{{appName}}", null != shortEmailDomain.getAppName() ? shortEmailDomain.getAppName() : "")

                    .replace("${appDomain}", appDomain)
                    .replace("{{appDomain}}", appDomain)

                    .replace("${expireTime}", shortBrevo.getExpireTime().toString())
                    .replace("{{expireTime}}", shortBrevo.getExpireTime().toString())

                    .replace("${jumpUrl}", shortPageList.get(0).getUrl() + "?selevt=3&email=" + shortBrevo.getReceiveEmail())
                    .replace("{{jumpUrl}}", shortPageList.get(0).getUrl() + "?selevt=3&email=" + shortBrevo.getReceiveEmail())


                    .replace("${dayType}", dayType)
                    .replace("{{dayType}}", dayType)
                    .replace("${payTime}", payTime.toString())
                    .replace("{{payTime}}", payTime.toString())
                    .replace("${buyPrice}", buyPrice.toString())
                    .replace("{{buyPrice}}", buyPrice.toString())
                    .replace("${renewalPrice}", renewalPrice.toString())
                    .replace("{{renewalPrice}}", renewalPrice.toString())
                    .replace("${unSubUrl}", unSubUrl)
                    .replace("{{unSubUrl}}", unSubUrl)


                    .replace("{{user_name}}", null != shortBrevo.getUser_name() ? shortBrevo.getUser_name() : ""); // 兼容旧格式

            if (type.equals("brevo")) {
                // 6. 创建邮件内容
                email.setSubject(subject);
                email.setHtmlContent(htmlContent); // 使用HTML内容
            } else if (type.equals("forward")) {

                message.setSubject(subject);
                shortForwardEmailSend.setSubject(subject);

                // 创建邮件正文部分
                MimeBodyPart textPart = new MimeBodyPart();

                // 组合 HTML 内容
                textPart.setContent(htmlContent, "text/html; charset=utf-8");

                // 组合各部分
                Multipart multipart = new MimeMultipart();
                multipart.addBodyPart(textPart);

                message.setContent(multipart);
                shortForwardEmailSend.setContent(htmlContent);


            } else {
                emailDTO.setSubject(subject);
                emailDTO.setContent(htmlContent);
            }

            Message finalMessage = message;
            new Thread(() -> {
                try {
                    runlog.setState("1");
                    String content = "";
                    if (type.equals("brevo")) {
                        // 7. 发送邮件
                        CreateSmtpEmail response = apiInstance.sendTransacEmail(email);
                        content = String.format(",邮件发送成功！消息ID: " + response.getMessageId() + "用户Id：" + shortBrevo.getUserId() +
                                ",邮件主题：" + shortBrevo.getSubject() + ",收件人名称：" + shortBrevo.getReceiveName() +
                                ",收件人邮件：" + shortBrevo.getReceiveEmail() + ",邮件服务商:" + type);
                        runlog.setNote(response.toString());
                    } else if (type.equals("aws")) {
                        emailServiceContext.getService(emailDTO.getServiceType()).sendEmail(emailDTO);
                        content = String.format(",邮件发送成功！用户Id：" + shortBrevo.getUserId() +
                                ",邮件主题：" + shortBrevo.getSubject() +
                                ",收件人邮件：" + shortBrevo.getReceiveEmail() +
                                ",邮件服务商:" + type);
                        runlog.setNote(type);
                    } else if (type.equals("forward")) {
                        // 发送邮件
                        Transport.send(finalMessage);
                        content = String.format(",邮件发送成功！" + "用户Id：" + shortBrevo.getUserId() +
                                ",邮件主题：" + shortBrevo.getSubject() + ",收件人名称：" + shortBrevo.getReceiveName() +
                                ",收件人邮件：" + shortBrevo.getReceiveEmail() + ",邮件服务商:" + type);

                        shortForwardEmailSend.setType(1L);
                        shortForwardEmailSendMapper.insertShortForwardEmailSend(shortForwardEmailSend);

                    }
                    runlog.setContent(content);
                    shortRunlogService.insertShortRunlog(runlog);
                } catch (Exception e) {
                    runlog.setState("0");
                    runlog.setContent(String.format(",发送失败：" + e.getMessage() + ",邮件主题：" + shortBrevo.getSubject() +
                            ",收件人名称：" + shortBrevo.getReceiveName() + ",收件人邮件：" + shortBrevo.getReceiveEmail()) + ",邮件服务商:" + type);
                    runlog.setNote(e.toString());
                    shortRunlogService.insertShortRunlog(runlog);
                }
            }).start();

            String randomNum = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 4);
            Map<String, String> vars = new HashMap<>();
            vars.put("uId", randomNum + shortBrevo.getUserId());
            vars.put("uniqueId", shortBrevo.getUniqueId());
            return AjaxResult.success(vars);
        } catch (IOException e) {
            System.err.println("读取HTML文件失败: " + e.getMessage());
            runlog.setState("0");
            runlog.setContent(String.format(",读取HTML文件失败：" + e.getMessage() + ",邮件主题：" + shortBrevo.getSubject() + ",收件人名称：" + shortBrevo.getReceiveName() + ",收件人邮件：" + shortBrevo.getReceiveEmail()));
            runlog.setNote(e.toString());
            shortRunlogService.insertShortRunlog(runlog);

            return AjaxResult.error("读取HTML文件失败: " + e.getMessage());
        } catch (Exception e) {
            System.err.println("发送失败: " + e.getMessage());
            runlog.setState("0");
            runlog.setContent(String.format(",发送失败：" + e.getMessage() + ",邮件主题：" + shortBrevo.getSubject() + ",收件人名称：" + shortBrevo.getReceiveName() + ",收件人邮件：" + shortBrevo.getReceiveEmail()));
            runlog.setNote(e.toString());
            shortRunlogService.insertShortRunlog(runlog);
            return AjaxResult.error("发送失败: " + e.getMessage());
        }
    }


    public AjaxResult sendUnSubEmailForward(InitUserDTO shortBrevo, ShortMovie shortMovie, String type) throws Exception {
        SendSmtpEmail email = new SendSmtpEmail();
        TransactionalEmailsApi apiInstance = new TransactionalEmailsApi();
        EmailDTO emailDTO = new EmailDTO();
        ShortForwardEmailSend shortForwardEmailSend = new ShortForwardEmailSend();
        shortForwardEmailSend.setReEmail(shortBrevo.getReceiveEmail());
        Message message = null;
        if (type.equals("forward")) {

            shortForwardEmailSend.setCreateTime(DateUtils.getNowDate());
            shortForwardEmailSend.setSentDate(DateUtils.getNowDate());
            shortForwardEmailSend.setAppId(shortBrevo.getAppId());


            ShortForwartEmailDomain shortForwartEmailDomain = new ShortForwartEmailDomain();
            shortForwartEmailDomain.setAppId(shortBrevo.getAppId());
            List<ShortForwartEmailDomain> list = shortForwartEmailDomainMapper.selectShortForwartEmailDomainList(shortForwartEmailDomain);
            if (!list.isEmpty()) {

                String username = list.get(0).getDomain(); // 你的Forward Email地址
                String password = AESEncryption.decrypt(list.get(0).getPassword()); // 你的密码
                shortForwardEmailSend.setDomain(username);


                String host = "smtp.forwardemail.net"; // Forward Email SMTP服务器
                Properties props = new Properties();
                props.put("mail.smtp.host", host);
                props.put("mail.smtp.port", "465");
                props.put("mail.smtp.ssl.enable", "true");
                props.put("mail.smtp.auth", "true");


                try {
                    // 创建会话
                    Session session = Session.getInstance(props,
                            new Authenticator() {
                                protected PasswordAuthentication getPasswordAuthentication() {
                                    return new PasswordAuthentication(username, password);
                                }
                            });

                    // 创建邮件

                    message = new MimeMessage(session);
                    message.setFrom(new InternetAddress(username));
                    message.setRecipients(Message.RecipientType.TO,
                            InternetAddress.parse(shortForwardEmailSend.getReEmail()));

                } catch (MessagingException e) {
                }
            }


        }

        // 3. 设置发件人
        SendSmtpEmailSender sender = new SendSmtpEmailSender();
        ShortEmailDomain shortEmailDomain = shortEmailDomainService.getrandomDomain(shortBrevo.getAppId());

        if (type.equals("brevo")) {
            // 2. 初始化邮件API
            ApiClient defaultClient = Configuration.getDefaultApiClient();
            ApiKeyAuth apiKey = (ApiKeyAuth) defaultClient.getAuthentication("api-key");
            apiKey.setApiKey(brevoApiKey); // 替换为你的Brevo API密钥

            if (null != shortEmailDomain) {
                sender.setEmail(shortEmailDomain.getDomain()); // 必须已验证的邮箱
                sender.setName(shortEmailDomain.getAppName());
            } else {
                sender.setEmail(senderEmail); // 必须已验证的邮箱
                sender.setName(senderName);
            }


            List<SendSmtpEmailTo> toList = new ArrayList<>();
            // 4. 设置收件人
            SendSmtpEmailTo recipient = new SendSmtpEmailTo();
            recipient.setEmail(null != shortBrevo.getReceiveEmail() ? shortBrevo.getReceiveEmail() : null);
            recipient.setName(null != shortBrevo.getReceiveName() ? shortBrevo.getReceiveName() : null);
            toList.add(recipient);
            email.setSender(sender);
            email.setTo(toList);
        } else if (type.equals("aws")) {
            emailDTO.setReceiveEmail(ListUtil.toList(shortBrevo.getReceiveEmail()));
            emailDTO.setSendChannel("aws");
            emailDTO.setServiceType("awsSimpl");
            emailDTO.setTitle("Kushort");
        }
        // 记录日志并处理结果
        ShortRunlog runlog = new ShortRunlog();
        runlog.setType("发送取消订阅邮件");
        runlog.setCreateTime(DateUtils.getNowDate());
        runlog.setUpdateTime(DateUtils.getNowDate());

        try {

//            String subject = getSubject(shortBrevo);
            String subject = shortEmailDomain.getAppName() + " Subscription Cancellation";

            String textContent = getUnSubTextContent(shortBrevo);

            if (type.equals("brevo")) {
                // 6. 创建邮件内容
                email.setSubject(subject);
                email.setTextContent(textContent); // 使用HTML内容
            } else if (type.equals("forward")) {

                message.setSubject(subject);
                shortForwardEmailSend.setSubject(subject);

                // 创建邮件正文部分
                MimeBodyPart textPart = new MimeBodyPart();

                // 组合 HTML 内容
                textPart.setText(textContent);

                // 组合各部分
                Multipart multipart = new MimeMultipart();
                multipart.addBodyPart(textPart);

                message.setContent(multipart);
                shortForwardEmailSend.setContent(textContent);

            } else {
                emailDTO.setSubject(subject);
                emailDTO.setContent(textContent);
            }

            Message finalMessage = message;
            new Thread(() -> {
                try {
                    runlog.setState("1");
                    String content = "";
                    if (type.equals("brevo")) {
                        // 7. 发送邮件
                        CreateSmtpEmail response = apiInstance.sendTransacEmail(email);
                        content = String.format(",邮件发送成功！消息ID: " + response.getMessageId() + "用户Id：" + shortBrevo.getUserId() +
                                ",邮件主题：" + shortBrevo.getSubject() + ",收件人名称：" + shortBrevo.getReceiveName() +
                                ",收件人邮件：" + shortBrevo.getReceiveEmail() + ",邮件服务商:" + type);
                        runlog.setNote(response.toString());
                    } else if (type.equals("forward")) {
                        // 发送邮件
                        Transport.send(finalMessage);
                        content = String.format(",邮件发送成功！" + "用户Id：" + shortBrevo.getUserId() +
                                ",邮件主题：" + shortBrevo.getSubject() + ",收件人名称：" + shortBrevo.getReceiveName() +
                                ",收件人邮件：" + shortBrevo.getReceiveEmail() + ",邮件服务商:" + type);

                        shortForwardEmailSend.setType(1L);
                        shortForwardEmailSendMapper.insertShortForwardEmailSend(shortForwardEmailSend);

                    }
                    runlog.setContent(content);
                    shortRunlogService.insertShortRunlog(runlog);
                } catch (Exception e) {
                    runlog.setState("0");
                    runlog.setContent(String.format(",发送失败：" + e.getMessage() + ",邮件主题：" + shortBrevo.getSubject() +
                            ",收件人名称：" + shortBrevo.getReceiveName() + ",收件人邮件：" + shortBrevo.getReceiveEmail()) + ",邮件服务商:" + type);
                    runlog.setNote(e.toString());
                    shortRunlogService.insertShortRunlog(runlog);
                }
            }).start();

            String randomNum = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 4);
            Map<String, String> vars = new HashMap<>();
            vars.put("uId", randomNum + shortBrevo.getUserId());
            vars.put("uniqueId", shortBrevo.getUniqueId());
            return AjaxResult.success(vars);
        } catch (Exception e) {
            System.err.println("发送失败: " + e.getMessage());
            runlog.setState("0");
            runlog.setContent(String.format(",发送失败：" + e.getMessage() + ",邮件主题：" + shortBrevo.getSubject() + ",收件人名称：" + shortBrevo.getReceiveName() + ",收件人邮件：" + shortBrevo.getReceiveEmail()));
            runlog.setNote(e.toString());
            shortRunlogService.insertShortRunlog(runlog);
            return AjaxResult.error("发送失败: " + e.getMessage());
        }
    }

    private static String getSubject(InitUserDTO shortBrevo) {
        String subject = "";
        if (null == shortBrevo.getLanguageCode() || shortBrevo.getLanguageCode().equals("en-US"))
            subject = null != shortBrevo.getSubject() ? shortBrevo.getSubject() : " Registration Confirmation";
        else if (shortBrevo.getLanguageCode().equals("es"))
            subject = null != shortBrevo.getSubject() ? shortBrevo.getSubject() : " Miembro valioso";
        else if (shortBrevo.getLanguageCode().equals("pt"))
            subject = null != shortBrevo.getSubject() ? shortBrevo.getSubject() : " Membro Valioso";
        else if (shortBrevo.getLanguageCode().equals("zh-TW"))
            subject = null != shortBrevo.getSubject() ? shortBrevo.getSubject() : " 尊贵的会员";
        else if (shortBrevo.getLanguageCode().equals("ja"))
            subject = null != shortBrevo.getSubject() ? shortBrevo.getSubject() : " 大切なメンバー";
        else if (shortBrevo.getLanguageCode().equals("de"))
            subject = null != shortBrevo.getSubject() ? shortBrevo.getSubject() : " Geschätztes Mitglied";
        return subject;
    }

    @NotNull
    private static String getUnSubTextContent(InitUserDTO shortBrevo) {
        String textContent = "";
        if (null == shortBrevo.getLanguageCode() || shortBrevo.getLanguageCode().equals("en-US")) {
            textContent = "Dear member, your subscription has been canceled. If you have any subsequent questions, please feel free to contact us.";
        } else if (shortBrevo.getLanguageCode().equals("es")) {
            textContent = "Estimado miembro, su suscripción ha sido cancelada. Si tiene alguna pregunta, no dude en contactarnos.";
        } else if (shortBrevo.getLanguageCode().equals("pt")) {
            textContent = "Prezado membro, sua assinatura foi cancelada. Caso tenha alguma dúvida, entre em contato conosco.";
        } else if (shortBrevo.getLanguageCode().equals("zh-TW")) {
            textContent = "尊敬的会员，您的订阅已取消。如有任何后续问题，请随时联系我们。";
        } else if (shortBrevo.getLanguageCode().equals("ja")) {
            textContent = "会員様、ご登録はキャンセルされました。その後ご質問等ございましたら、お気軽にお問い合わせください。";
        } else if (shortBrevo.getLanguageCode().equals("de")) {
            textContent = "Sehr geehrtes Mitglied, Ihr Abonnement wurde gekündigt. Bei weiteren Fragen können Sie sich gerne an uns wenden.";
        }
        return textContent;
    }

    public static String formatToEnglishUTC(Object time) {
        // 定义纯英文格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(
                "EEE MMM dd HH:mm:ss 'UTC' yyyy",
                Locale.ENGLISH
        );

        // 处理不同类型的时间对象
        if (time instanceof Instant) {
            return ((Instant) time).atZone(ZoneOffset.UTC).format(formatter);
        } else if (time instanceof ZonedDateTime) {
            return ((ZonedDateTime) time).withZoneSameInstant(ZoneOffset.UTC).format(formatter);
        } else if (time instanceof LocalDateTime) {
            return ((LocalDateTime) time).atZone(ZoneOffset.UTC).format(formatter);
        } else if (time instanceof Date) {
            return ((Date) time).toInstant().atZone(ZoneOffset.UTC).format(formatter);
        } else {
            throw new IllegalArgumentException("Unsupported time type: " + time.getClass());
        }
    }

}
