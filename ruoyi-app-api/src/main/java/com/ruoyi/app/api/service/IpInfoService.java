package com.ruoyi.app.api.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.ruoyi.domain.ShortCoverImage;
import com.ruoyi.domain.ShortFeedback;
import com.ruoyi.domain.ShortUnsubscribe;
import com.ruoyi.domain.ShortUserActivityLog;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

/**
 * IP信息服务接口
 */
public interface IpInfoService {

    /**
     * 获取客户端IP地址
     *
     * @param request HTTP请求
     * @return IP地址
     */
    String getClientIp(HttpServletRequest request);

    /**
     * 根据IP获取国家信息
     *
     * @param ip IP地址
     * @return 国家信息，包含countryName和countryCode
     */
    Map<String, String> getCountryByIp(String ip);

    /**
     * 获取Banner数据
     *
     * @param appId 应用ID
     * @return 获取Banner数据
     */
    List<Map<String, Object>> getBannerListByAppId(Integer appId);

    /**
     * 获取Banner数据 多语言
     * @param integer
     * @param languageCode
     * @return
     */
    List<Map<String, Object>> getBannerListByAppId(Integer integer, String languageCode);

    /**
     * 获取page数据
     *
     * @param nid 应用ID
     * @return 获取page数据
     */
    List<Map<String, Object>> getPageListByAppId(Integer nid);

    List<Map<String, Object>> getMovies(Integer nid, String type,int ifApp,String unid);

    List<Map<String, Object>> getFilterMovieList(Map<String, Object> requestData) throws IOException;

    List<Map<String, Object>> getChannelMovies(String authorization, String uid, Integer nid, String recId);

    List<Map<String, Object>> getMovieList(Integer nid, String vid);

    Map<String, Object> getMovie(Integer movie, Integer nid, String qd, String kid, String tempId, String userId);

    Map<String, Object> getVideos(Integer movie, Integer nid, String qd, String kid, String tempId, String userId);

    void saveUserActivityLog(ShortUserActivityLog shortUserActivityLog);

    List<ShortCoverImage> listLimit();

    void saveFeedback(ShortFeedback shortFeedback);

    Map<String, Object> getVideos(Integer movie, Integer nid, String qd, String kid, String tempId, String userId, String languageCode);

    Map<String, Object> getMovie(Integer movie, Integer nid, String qd, String kid, String tempId, String userId, String languageCode);

    List<Map<String, Object>> getFilterMovieI18nList(Map<String, Object> requestData);

    List<Map<String, Object>> getMovies(Integer nid, String type, String languageCode,int ifApp,String unid);

    List<Map<String, Object>> getSeriesEpisodes(Integer nid, String type, String languageCode,int ifApp,String unid);

    List<Map<String, Object>> getPageListByAppId(Integer nid, String languageCode);

    List<Map<String, Object>> getMovieList(Integer nid, String vid, String languageCode);

    void saveUnsubscribe(ShortUnsubscribe shortUnsubscribe);

    List<Map<String, Object>> getBannerListByAppIdAndLanguageCode(Integer integer, String languageCode, String ifApp);
}
