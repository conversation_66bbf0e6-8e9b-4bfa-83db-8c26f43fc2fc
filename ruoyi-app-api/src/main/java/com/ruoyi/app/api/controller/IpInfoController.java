package com.ruoyi.app.api.controller;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.domain.ShortFeedback;
import com.ruoyi.domain.ShortUnsubscribe;
import com.ruoyi.domain.ShortUserActivityLog;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import com.ruoyi.app.api.service.IpInfoService;
import com.ruoyi.common.core.domain.AjaxResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponses;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * IP信息API控制器
 */
@Api(tags = "IP信息API")
@RestController
@RequestMapping("/api")
public class IpInfoController {
    
    @Autowired
    private IpInfoService ipInfoService;


    /**
     * 获取IP信息
     */
    @ApiOperation(value = "获取IP信息", notes = "根据请求的 IP 地址返回信息")
    @ApiResponses({
        @ApiResponse(code = 200, message = "成功返回 IP 地址信息", response = IpInfoResponseModel.class)
    })
    @GetMapping("/get_ip_info/")
    public AjaxResult getIpInfo(HttpServletRequest request, HttpServletResponse response) {
        // 添加CORS支持
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET, OPTIONS");
        response.setHeader("Access-Control-Allow-Headers", "Content-Type");
        
        // 获取客户端IP地址
        String ipAddress = ipInfoService.getClientIp(request);
        
        // 获取IP所属国家信息
        String countryName = null;
        String countryCode = null;
        
        try {
            Map<String, String> countryInfo = ipInfoService.getCountryByIp(ipAddress);
            countryName = countryInfo.get("countryName");
            countryCode = countryInfo.get("countryCode");
        } catch (Exception e) {
            // 忽略异常，使用默认值
        }
        
        // 构建返回数据
        Map<String, Object> data = new HashMap<>();
        data.put("state", true);
        data.put("ip", ipAddress);
        data.put("country", countryName);
        data.put("country_code", countryCode);
        
        return AjaxResult.success("获取IP信息成功", data);
    }



    @ApiOperation(value = "获取Banner数据", notes = "获取Banner数据")
    @ApiImplicitParam(name = "nid", value = "app_id", paramType = "header", required = true)
    @GetMapping("/banners/")
    public AjaxResult register(HttpServletRequest request) {
        // 获取AppId
        String appId = request.getHeader("nid");
        String languageCode = request.getHeader("languageCode");
        String ifApp = request.getHeader("ifApp");
        if(null == ifApp || ifApp.isEmpty()){
            ifApp = "0";
        }
        // 调用服务
        List<Map<String, Object>> result = ipInfoService.getBannerListByAppIdAndLanguageCode(Integer.valueOf(appId),languageCode,ifApp);
        return AjaxResult.success("成功", result);
    }


    @ApiOperation(value = "获取Page数据", notes = "获取Page数据")
    @ApiImplicitParam(name = "nid", value = "app_id", paramType = "header", required = true)
    @GetMapping("/pages/")
    public AjaxResult getPages(HttpServletRequest request) {
        // 获取AppId
        String appId = request.getHeader("nid");
        // 调用服务
        List<Map<String, Object>> result = ipInfoService.getPageListByAppId(Integer.valueOf(appId));
        return AjaxResult.success("成功", result);
    }


    @ApiOperation(value = "获取电影列表", notes = "获取电影列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "nid", value = "app_id", paramType = "header", required = true),
            @ApiImplicitParam(name = "type", value = "type", paramType = "header", required = true)

    })
    @GetMapping("/movies/")
    public AjaxResult getMovies(HttpServletRequest request,@RequestParam("type") String type) {
        // 获取AppId
        String appId = request.getHeader("nid");
        String ifApp = request.getHeader("ifApp");   //没有缓存的情况下，第一次从首页进入，标识
        if(null == ifApp || ifApp.isEmpty())
            ifApp = "0";
        String unid = request.getHeader("unid");
        // 调用服务
        List<Map<String, Object>> result = ipInfoService.getMovies(Integer.valueOf(appId),type,Integer.parseInt(ifApp),unid);
        return AjaxResult.success("成功", result);
    }


    @ApiOperation(value = " 获取指定电影", notes = " 获取指定电影")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "nid", value = "nid", paramType = "header", required = true),
            @ApiImplicitParam(name = "vid", value = "vid", paramType = "header", required = true)

    })
    @GetMapping("/get_movie_list/")
    public AjaxResult getMovieList(HttpServletRequest request,
                                   @RequestParam("vid") String vid) {
        // 获取AppId
        String appId = request.getHeader("nid");
        // 调用服务
        List<Map<String, Object>> result = ipInfoService.getMovieList(Integer.valueOf(appId),vid);
        return AjaxResult.success("成功", result);
    }


    @ApiOperation(value = "获取渠道短剧", notes = "获取渠道短剧")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "Authorization", paramType = "header", required = true),
            @ApiImplicitParam(name = "uid", value = "uid", paramType = "header", required = true),
            @ApiImplicitParam(name = "nid", value = "nid", paramType = "header", required = true),
            @ApiImplicitParam(name = "rec_id", value = "rec_id", paramType = "header", required = true)

    })
    @GetMapping("/get_channel_movie/")
    public AjaxResult getChannelMovies(HttpServletRequest request,
                                       @RequestParam("rec_id") String rec_id) {

        String Authorization = request.getHeader("Authorization");
        String appId = request.getHeader("nid");
        String uid = request.getHeader("uid");
        // 调用服务
        List<Map<String, Object>> result = ipInfoService.getChannelMovies(Authorization,StringUtils.isEmpty(uid)? null :uid,StringUtils.isEmpty(appId)? null :Integer.parseInt(appId),rec_id);
        return AjaxResult.success("成功", result);
    }


    @ApiOperation(value = "获取指定渠道电影", notes = "获取指定渠道电影")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "movie", value = "movie", paramType = "header", required = true),
            @ApiImplicitParam(name = "qd", value = "qd", paramType = "header", required = true),
            @ApiImplicitParam(name = "kid", value = "kid", paramType = "header", required = true),
            @ApiImplicitParam(name = "pid", value = "pid", paramType = "header", required = true)

    })
    @PostMapping("/filter_movie_list/")
    public AjaxResult getFilterMovieList(@RequestBody Map<String, Object> requestData) throws IOException {
        // 调用服务
        List<Map<String, Object>> result = ipInfoService.getFilterMovieList(requestData);
        return AjaxResult.success("成功", result);
    }





    @ApiOperation(value = "获取单个电影", notes = "获取单个电影")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "movie", value = "movie", paramType = "header", required = true),
            @ApiImplicitParam(name = "nid", value = "nid", paramType = "header", required = true),
            @ApiImplicitParam(name = "tempId", value = "tempId", paramType = "header", required = true),
            @ApiImplicitParam(name = "qd", value = "qd", paramType = "header", required = true),
            @ApiImplicitParam(name = "kid", value = "kid", paramType = "header", required = true),
            @ApiImplicitParam(name = "userId", value = "userId", paramType = "header", required = true),

    })
    @GetMapping("/get_movie/")
    public AjaxResult getMovie(@RequestParam("movie") String movie,
                               @RequestParam(value = "tempId",required = false) String tempId,
                               @RequestParam(value = "qd",required = false) String qd,
                               @RequestParam(value = "kid",required = false) String kid,
                               @RequestParam(value = "userId",required = false) String userId, HttpServletRequest request) {
        String nid = request.getHeader("nid");
        // 调用服务
        Map<String, Object> result = ipInfoService.getMovie(Integer.valueOf(movie),Integer.valueOf(nid),qd,kid,tempId,userId);
        return AjaxResult.success("成功", result);
    }

    @ApiOperation(value = "获取所有视频", notes = "获取所有视频")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "movie", value = "movie", paramType = "header", required = true),
            @ApiImplicitParam(name = "nid", value = "nid", paramType = "header", required = true),
            @ApiImplicitParam(name = "tempId", value = "tempId", paramType = "header", required = true),
            @ApiImplicitParam(name = "qd", value = "qd", paramType = "header", required = true),
            @ApiImplicitParam(name = "kid", value = "kid", paramType = "header", required = true),
            @ApiImplicitParam(name = "userId", value = "userId", paramType = "header", required = true),

    })
    @GetMapping("/videos/")
    public AjaxResult getVideos(@RequestParam("movie") String movie,
                               @RequestParam("tempId") String tempId,
                               @RequestParam("qd") String qd,
                               @RequestParam("kid") String kid,
                               @RequestParam("userId") String userId,HttpServletRequest request) {
        String nid = request.getHeader("nid");
        // 调用服务
        Map<String, Object> result = ipInfoService.getVideos(Integer.valueOf(movie),Integer.valueOf(nid),qd,kid,tempId,userId);
        return AjaxResult.success("成功", result);
    }


    @ApiOperation("新增用户行为记录-api")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "user_id", value = "用户", dataType = "Integer", dataTypeClass = Integer.class),
            @ApiImplicitParam(name = "app_id", value = "用户", dataType = "Integer", dataTypeClass = String.class),
            @ApiImplicitParam(name = "state", value = "状态 0为异常 1为成功", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "content", value = "内容", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "note", value = "备注", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "status", value = "状态（0正常 1停用）", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "create_by", value = "创建者", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "create_time", value = "创建时间", dataType = "Date", dataTypeClass = String.class),
            @ApiImplicitParam(name = "update_by", value = "更新者", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "update_time", value = "更新时间", dataType = "Date", dataTypeClass = String.class),
            @ApiImplicitParam(name = "remark", value = "备注", dataType = "String", dataTypeClass = String.class)

    })
    @PostMapping("/save_user_activity_log")
    public AjaxResult saveUserActivityLog(@RequestBody ShortUserActivityLog shortUserActivityLog)
    {
        ipInfoService.saveUserActivityLog(shortUserActivityLog);
        return AjaxResult.success("成功");
    }

    @ApiOperation("获取随机封面图3条")
    @GetMapping("/listLimit")
    public AjaxResult listLimit()
    {
        return AjaxResult.success(ipInfoService.listLimit());
    }


    @ApiOperation("用户反馈")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "user_id", value = "用户", dataType = "Integer", dataTypeClass = Integer.class),
            @ApiImplicitParam(name = "app_id", value = "用户", dataType = "Integer", dataTypeClass = String.class),
            @ApiImplicitParam(name = "content", value = "内容", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "email", value = "email", dataType = "String", dataTypeClass = String.class),

    })
    @PostMapping("/saveFeedback")
    public AjaxResult saveFeedback(@RequestBody ShortFeedback shortFeedback)
    {
        ipInfoService.saveFeedback(shortFeedback);
        return AjaxResult.success("成功");
    }

    @ApiOperation("退订")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "user_id", value = "用户", dataType = "Integer", dataTypeClass = Integer.class),
            @ApiImplicitParam(name = "app_id", value = "用户", dataType = "Integer", dataTypeClass = String.class),
            @ApiImplicitParam(name = "content", value = "内容", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "email", value = "email", dataType = "String", dataTypeClass = String.class),

    })
    @PostMapping("/saveUnsubscribe")
    public AjaxResult saveUnsubscribe(@RequestBody ShortUnsubscribe shortUnsubscribe)
    {
        ipInfoService.saveUnsubscribe(shortUnsubscribe);
        return AjaxResult.success("成功");
    }


}



/**
 * IP信息响应模型（用于Swagger文档）
 */
@ApiModel(description = "IP信息响应")
class IpInfoResponseModel {
    @ApiModelProperty(value = "响应状态码", example = "200")
    private Integer code;

    @ApiModelProperty(value = "响应消息", example = "success")
    private String msg;

    @ApiModelProperty(value = "响应数据")
    private IpInfoDataModel data;

    public Integer getCode() { return code; }
    public void setCode(Integer code) { this.code = code; }

    public String getMsg() { return msg; }
    public void setMsg(String msg) { this.msg = msg; }

    public IpInfoDataModel getData() { return data; }
    public void setData(IpInfoDataModel data) { this.data = data; }
}

/**
 * IP信息数据模型（用于Swagger文档）
 */
@ApiModel(description = "IP信息数据")
class IpInfoDataModel {
    @ApiModelProperty(value = "状态", example = "true")
    private Boolean state;

    @ApiModelProperty(value = "IP地址", example = "***********")
    private String ip;

    @ApiModelProperty(value = "国家名称", example = "China")
    private String country;

    @ApiModelProperty(value = "国家代码", example = "CN")
    private String country_code;

    public Boolean getState() { return state; }
    public void setState(Boolean state) { this.state = state; }

    public String getIp() { return ip; }
    public void setIp(String ip) { this.ip = ip; }

    public String getCountry() { return country; }
    public void setCountry(String country) { this.country = country; }

    public String getCountry_code() { return country_code; }
    public void setCountry_code(String country_code) { this.country_code = country_code; }
}