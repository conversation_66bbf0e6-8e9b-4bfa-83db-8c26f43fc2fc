package com.ruoyi.web.controller.short_center.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.utils.CrossModuleEnhancementUtil;
import com.ruoyi.domain.ShortFeedback;
import com.ruoyi.domain.ShortForwardEmailReceive;
import com.ruoyi.service.IShortForwardEmailReceiveService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 收件箱Controller
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
@Api("收件箱Controller")
@RestController
@RequestMapping("/short_center/receive")
public class ShortForwardEmailReceiveController extends BaseController
{
    @Autowired
    private IShortForwardEmailReceiveService shortForwardEmailReceiveService;

    @Autowired
    private CrossModuleEnhancementUtil crossModuleEnhancementUtil;

    /**
     * 查询收件箱列表
     */
    @ApiOperation("收件箱列表")
    @PreAuthorize("@ss.hasPermi('system:receive:list')")
    @GetMapping("/list")
    public TableDataInfo list(ShortForwardEmailReceive shortForwardEmailReceive)
    {
        startPage();
        List<ShortForwardEmailReceive> list = shortForwardEmailReceiveService.selectShortForwardEmailReceiveList(shortForwardEmailReceive);
        TableDataInfo dataTable = getDataTable(list);
        return crossModuleEnhancementUtil.enhanceTableData(dataTable);
    }

    /**
     * 导出收件箱列表
     */
    @ApiOperation("收件箱导出")
    @PreAuthorize("@ss.hasPermi('system:receive:export')")
    @Log(title = "收件箱", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ShortForwardEmailReceive shortForwardEmailReceive)
    {
        List<ShortForwardEmailReceive> list = shortForwardEmailReceiveService.selectShortForwardEmailReceiveList(shortForwardEmailReceive);
        ExcelUtil<ShortForwardEmailReceive> util = new ExcelUtil<ShortForwardEmailReceive>(ShortForwardEmailReceive.class);
        util.exportExcel(response, list, "收件箱数据");
    }

    /**
     * 获取收件箱详细信息
     */
    @ApiOperation("根据id获取收件箱详细信息")
    @PreAuthorize("@ss.hasPermi('system:receive:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(shortForwardEmailReceiveService.selectShortForwardEmailReceiveById(id));
    }

    /**
     * 新增收件箱
     */
    @PreAuthorize("@ss.hasPermi('system:receive:add')")
    @Log(title = "收件箱", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ShortForwardEmailReceive shortForwardEmailReceive)
    {
        return toAjax(shortForwardEmailReceiveService.insertShortForwardEmailReceive(shortForwardEmailReceive));
    }

    /**
     * 修改收件箱
     */
    @PreAuthorize("@ss.hasPermi('system:receive:edit')")
    @Log(title = "收件箱", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ShortForwardEmailReceive shortForwardEmailReceive)
    {
        return toAjax(shortForwardEmailReceiveService.updateShortForwardEmailReceive(shortForwardEmailReceive));
    }

    /**
     * 删除收件箱
     */
    @PreAuthorize("@ss.hasPermi('system:receive:remove')")
    @Log(title = "收件箱", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(shortForwardEmailReceiveService.deleteShortForwardEmailReceiveByIds(ids));
    }

    /**
     * 根据邮箱和appid查询历史邮件内容
     */
    @ApiOperation("根据邮箱和appid查询历史邮件内容")
    @PostMapping("/getEmailContentByEmailAndAppId")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "app_id", value = "appId", dataType = "Integer", dataTypeClass = Integer.class),
            @ApiImplicitParam(name = "user_id", value = "userId--针对于用户反馈可两个都穿", dataType = "Integer", dataTypeClass = String.class),
            @ApiImplicitParam(name = "email", value = "邮箱", dataType = "String", dataTypeClass = String.class)

    })
    public AjaxResult getEmailContentByEmailAndAppId(@RequestBody ShortForwardEmailReceive shortForwardEmailReceive)
    {
        List<ShortForwardEmailReceive> list = shortForwardEmailReceiveService.getEmailContentByEmailAndAppId(shortForwardEmailReceive);
        return success(list);
    }
}
