package com.ruoyi.service.impl;

import com.ruoyi.domain.dto.CrossModuleStatusDto;
import com.ruoyi.service.ICrossModuleStatusService;
import com.ruoyi.service.IShortFeedbackService;
import com.ruoyi.service.IShortForwardEmailReceiveService;
import com.ruoyi.service.IShortUnsubscribeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 跨模块状态查询服务实现
 *
 * <AUTHOR>
 * @date 2025-09-27
 */
@Service
public class CrossModuleStatusServiceImpl implements ICrossModuleStatusService {

    @Autowired
    private IShortUnsubscribeService unsubscribeService;

    @Autowired
    private IShortForwardEmailReceiveService receiveService;

    @Autowired
    private IShortFeedbackService feedbackService;

    @Override
    @Cacheable(value = "crossModuleStatus", key = "#email + '_' + #appId", unless = "#result == null")
    public CrossModuleStatusDto getCrossModuleStatus(String email, Long appId) {
        CrossModuleStatusDto status = new CrossModuleStatusDto();

        if (!StringUtils.hasText(email) || appId == null) {
            return status;
        }

        try {
            // 并行查询提高性能
            CompletableFuture<Boolean> unsubscribeFuture = CompletableFuture.supplyAsync(() ->
                    unsubscribeService.existsByEmailAndAppId(email, appId));

            CompletableFuture<Boolean> receiveFuture = CompletableFuture.supplyAsync(() ->
                    receiveService.existsByEmailAndAppId(email, appId));

            CompletableFuture<Boolean> feedbackFuture = CompletableFuture.supplyAsync(() ->
                    feedbackService.existsByEmailAndAppId(email, appId));

            // 等待所有查询完成
            CompletableFuture.allOf(unsubscribeFuture, receiveFuture, feedbackFuture).get();

            status.setHasUnsubscribeRecord(unsubscribeFuture.get());
            status.setHasReceiveRecord(receiveFuture.get());
            status.setHasFeedbackRecord(feedbackFuture.get());

        } catch (Exception e) {
            // 降级处理：出错时返回默认值
            status = new CrossModuleStatusDto();
        }

        return status;
    }

    @Override
    public Map<String, CrossModuleStatusDto> getBatchCrossModuleStatus(List<String> emails, Long appId) {
        Map<String, CrossModuleStatusDto> resultMap = new HashMap<>();

        if (CollectionUtils.isEmpty(emails) || appId == null) {
            return resultMap;
        }

        // 过滤掉空的邮箱
        List<String> validEmails = emails.stream()
                .filter(StringUtils::hasText)
                .distinct()
                .collect(Collectors.toList());

        if (validEmails.isEmpty()) {
            return resultMap;
        }

        try {
            // 并行批量查询
            CompletableFuture<List<String>> unsubscribeFuture = CompletableFuture.supplyAsync(() ->
                    unsubscribeService.batchExistsByEmailAndAppId(validEmails, appId));

            CompletableFuture<List<String>> receiveFuture = CompletableFuture.supplyAsync(() ->
                    receiveService.batchExistsByEmailAndAppId(validEmails, appId));

            CompletableFuture<List<String>> feedbackFuture = CompletableFuture.supplyAsync(() ->
                    feedbackService.batchExistsByEmailAndAppId(validEmails, appId));

            // 等待所有查询完成
            CompletableFuture.allOf(unsubscribeFuture, receiveFuture, feedbackFuture).get();

            List<String> unsubscribeEmails = unsubscribeFuture.get();
            List<String> receiveEmails = receiveFuture.get();
            List<String> feedbackEmails = feedbackFuture.get();

            // 组装结果
            for (String email : validEmails) {
                CrossModuleStatusDto status = new CrossModuleStatusDto();
                status.setHasUnsubscribeRecord(unsubscribeEmails.contains(email));
                status.setHasReceiveRecord(receiveEmails.contains(email));
                status.setHasFeedbackRecord(feedbackEmails.contains(email));
                resultMap.put(email, status);
            }

        } catch (Exception e) {
            // 降级处理：出错时返回空的状态
            for (String email : validEmails) {
                resultMap.put(email, new CrossModuleStatusDto());
            }
        }

        return resultMap;
    }
}