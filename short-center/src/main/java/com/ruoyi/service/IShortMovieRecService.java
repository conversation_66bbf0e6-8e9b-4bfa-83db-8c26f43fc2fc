package com.ruoyi.service;

import com.ruoyi.domain.ShortAppLanguage;
import com.ruoyi.domain.ShortMovie;
import com.ruoyi.domain.ShortMovieI18n;
import com.ruoyi.domain.ShortMovieRec;
import com.ruoyi.dto.ShortMovieRecDTO;
import com.ruoyi.dto.ShortMovieRecDataDTO;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 影片推荐Service接口
 *
 * <AUTHOR>
 * @date 2025-09-08
 */
public interface IShortMovieRecService
{
    /**
     * 查询影片推荐
     *
     * @param id 影片推荐主键
     * @return 影片推荐
     */
    public ShortMovieRec selectShortMovieRecById(Long id);

    /**
     * 查询影片推荐列表
     *
     * @param shortMovieRec 影片推荐
     * @return 影片推荐集合
     */
    public List<ShortMovieRec> selectShortMovieRecList(ShortMovieRec shortMovieRec);

    /**
     * 新增影片推荐
     *
     * @param shortMovieRec 影片推荐
     * @return 结果
     */
    public int insertShortMovieRec(ShortMovieRec shortMovieRec);

    /**
     * 修改影片推荐
     *
     * @param shortMovieRec 影片推荐
     * @return 结果
     */
    public int updateShortMovieRec(ShortMovieRec shortMovieRec);

    /**
     * 批量删除影片推荐
     *
     * @param ids 需要删除的影片推荐主键集合
     * @return 结果
     */
    public int deleteShortMovieRecByIds(Long[] ids);

    /**
     * 删除影片推荐信息
     *
     * @param id 影片推荐主键
     * @return 结果
     */
    public int deleteShortMovieRecById(Long id);

    int insertShortMovieRecList(List<ShortMovieRec> movieRecList);

    List<ShortMovie> getMoviesByRec(Integer nid, String type, int ifApp, Date exTime, String languageCode);

    List<String> batchAddRec(ShortMovieRecDTO shortMovieRecDTO);

    List<ShortMovieRecDataDTO> pageQuery(ShortMovieRec shortMovieRec);

    int deleteMovieRec(ShortMovieRec shortMovieRec);

    int updateStatus(ShortMovieRec shortMovieRec);
}
