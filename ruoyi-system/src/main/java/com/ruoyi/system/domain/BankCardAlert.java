package com.ruoyi.system.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 银行卡告警对象 bank_card_alerts
 */
public class BankCardAlert extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 银行卡号（脱敏） */
    @Excel(name = "银行卡号")
    private String cardNumber;

    /** 触发告警的用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 告警类型 */
    @Excel(name = "告警类型")
    private String alertType;

    /** 告警详情 */
    @Excel(name = "告警详情")
    private String alertDetail;

    /** 是否已处理 */
    @Excel(name = "处理状态", readConverterExp = "0=未处理,1=已处理")
    private Integer processedFlag;

    /** 处理时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private java.util.Date processedAt;

    /** 处理人ID */
    @Excel(name = "处理人ID")
    private Long processedBy;

    /** 处理备注 */
    @Excel(name = "处理备注")
    private String processNote;

    public BankCardAlert()
    {
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }

    public void setCardNumber(String cardNumber)
    {
        this.cardNumber = cardNumber;
    }

    public String getCardNumber()
    {
        return cardNumber;
    }

    public void setUserId(Long userId)
    {
        this.userId = userId;
    }

    public Long getUserId()
    {
        return userId;
    }

    public void setAlertType(String alertType)
    {
        this.alertType = alertType;
    }

    public String getAlertType()
    {
        return alertType;
    }

    public void setAlertDetail(String alertDetail)
    {
        this.alertDetail = alertDetail;
    }

    public String getAlertDetail()
    {
        return alertDetail;
    }

    public void setProcessedFlag(Integer processedFlag)
    {
        this.processedFlag = processedFlag;
    }

    public Integer getProcessedFlag()
    {
        return processedFlag;
    }

    public void setProcessedAt(java.util.Date processedAt)
    {
        this.processedAt = processedAt;
    }

    public java.util.Date getProcessedAt()
    {
        return processedAt;
    }

    public void setProcessedBy(Long processedBy)
    {
        this.processedBy = processedBy;
    }

    public Long getProcessedBy()
    {
        return processedBy;
    }

    public void setProcessNote(String processNote)
    {
        this.processNote = processNote;
    }

    public String getProcessNote()
    {
        return processNote;
    }

    /**
     * 标记为已处理
     */
    public void markAsProcessed(Long adminId, String note) {
        this.processedFlag = 1;
        this.processedAt = new java.util.Date();
        this.processedBy = adminId;
        this.processNote = note;
    }

    /**
     * 判断是否已处理
     */
    public boolean isProcessed() {
        return this.processedFlag != null && this.processedFlag == 1;
    }

    // 告警类型常量
    public static class AlertType {
        public static final String WEEK_OVERUSE = "week_overuse";
        public static final String MONTH_OVERUSE = "month_overuse";
        public static final String MULTI_USER = "multi_user";
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("cardNumber", getCardNumber())
            .append("userId", getUserId())
            .append("alertType", getAlertType())
            .append("alertDetail", getAlertDetail())
            .append("processedFlag", getProcessedFlag())
            .append("processedAt", getProcessedAt())
            .append("processedBy", getProcessedBy())
            .append("processNote", getProcessNote())
            .append("createTime", getCreateTime())
            .toString();
    }
}