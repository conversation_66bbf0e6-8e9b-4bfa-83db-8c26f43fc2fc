package com.ruoyi.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.mapper.ShortUserLinkMovieBindMapper;
import com.ruoyi.domain.ShortUserLinkMovieBind;
import com.ruoyi.service.IShortUserLinkMovieBindService;

/**
 * 【请填写功能名称】Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-22
 */
@Service
public class ShortUserLinkMovieBindServiceImpl implements IShortUserLinkMovieBindService 
{
    @Autowired
    private ShortUserLinkMovieBindMapper shortUserLinkMovieBindMapper;

    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public ShortUserLinkMovieBind selectShortUserLinkMovieBindById(Long id)
    {
        return shortUserLinkMovieBindMapper.selectShortUserLinkMovieBindById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param shortUserLinkMovieBind 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<ShortUserLinkMovieBind> selectShortUserLinkMovieBindList(ShortUserLinkMovieBind shortUserLinkMovieBind)
    {
        return shortUserLinkMovieBindMapper.selectShortUserLinkMovieBindList(shortUserLinkMovieBind);
    }

    /**
     * 新增【请填写功能名称】
     * 
     * @param shortUserLinkMovieBind 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertShortUserLinkMovieBind(ShortUserLinkMovieBind shortUserLinkMovieBind)
    {
        shortUserLinkMovieBind.setCreateTime(DateUtils.getNowDate());
        return shortUserLinkMovieBindMapper.insertShortUserLinkMovieBind(shortUserLinkMovieBind);
    }

    /**
     * 修改【请填写功能名称】
     * 
     * @param shortUserLinkMovieBind 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateShortUserLinkMovieBind(ShortUserLinkMovieBind shortUserLinkMovieBind)
    {
        shortUserLinkMovieBind.setUpdateTime(DateUtils.getNowDate());
        return shortUserLinkMovieBindMapper.updateShortUserLinkMovieBind(shortUserLinkMovieBind);
    }

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteShortUserLinkMovieBindByIds(Long[] ids)
    {
        return shortUserLinkMovieBindMapper.deleteShortUserLinkMovieBindByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteShortUserLinkMovieBindById(Long id)
    {
        return shortUserLinkMovieBindMapper.deleteShortUserLinkMovieBindById(id);
    }
}
