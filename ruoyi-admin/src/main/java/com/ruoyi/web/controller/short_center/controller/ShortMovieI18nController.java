package com.ruoyi.web.controller.short_center.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.domain.ShortAppLanguage;
import com.ruoyi.domain.ShortMovieI18n;
import com.ruoyi.dto.ShortMovieI18nDTO;
import com.ruoyi.dto.ShortMovieI18nIconDTO;
import com.ruoyi.dto.ShortMovieI18nInDTO;
import com.ruoyi.service.IShortMovieI18nService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 短剧多语言Controller
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Api(tags = "短剧多语言API")
@RestController
@RequestMapping("/short_center/movieI18n")
public class ShortMovieI18nController extends BaseController {
    @Autowired
    private IShortMovieI18nService shortMovieI18nService;

    /**
     * 查询短剧多语言列表
     */
    @ApiOperation(value = "查询短剧多语言列表", notes = "查询短剧多语言列表")
    @PreAuthorize("@ss.hasPermi('system:movieI18n:list')")
    @GetMapping("/list")
    public TableDataInfo list(ShortMovieI18n shortMovieI18n) {
        startPage();
        List<ShortMovieI18n> list = shortMovieI18nService.selectShortMovieI18nList(shortMovieI18n);
        return getDataTable(list);
    }

    /**
     * 导出短剧多语言列表
     */
    @PreAuthorize("@ss.hasPermi('system:movieI18n:export')")
    @Log(title = "短剧多语言", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ShortMovieI18n shortMovieI18n) {
        List<ShortMovieI18n> list = shortMovieI18nService.selectShortMovieI18nList(shortMovieI18n);
        ExcelUtil<ShortMovieI18n> util = new ExcelUtil<ShortMovieI18n>(ShortMovieI18n.class);
        util.exportExcel(response, list, "短剧多语言数据");
    }

    /**
     * 获取短剧多语言详细信息
     */
    @ApiOperation(value = "获取短剧多语言详细信息", notes = "获取短剧多语言详细信息")
    @PreAuthorize("@ss.hasPermi('system:movieI18n:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(shortMovieI18nService.selectShortMovieI18nById(id));
    }

    /**
     * 新增短剧多语言
     */
    @ApiOperation(value = "新增短剧多语言", notes = "新增短剧多语言")
    @PreAuthorize("@ss.hasPermi('system:movieI18n:add')")
    @Log(title = "短剧多语言", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ShortMovieI18n shortMovieI18n) {
        return toAjax(shortMovieI18nService.insertShortMovieI18n(shortMovieI18n));
    }

    /**
     * 修改短剧多语言
     */
    @ApiOperation(value = "修改短剧多语言", notes = "修改短剧多语言")
    @PreAuthorize("@ss.hasPermi('system:movieI18n:edit')")
    @Log(title = "短剧多语言", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ShortMovieI18n shortMovieI18n) {
        return toAjax(shortMovieI18nService.updateShortMovieI18n(shortMovieI18n));
    }

    @ApiOperation(value = "批量修改短剧多语言封面", notes = "批量修改短剧多语言封面")
    @PreAuthorize("@ss.hasPermi('system:movieI18n:iconEdit')")
    @Log(title = "短剧多语言", businessType = BusinessType.UPDATE)
    @PutMapping("/{movieId}/batchUpdateIcon")
    public AjaxResult batchUpdateIcon(@RequestBody List<ShortMovieI18nIconDTO> iconDTOS, @PathVariable Long movieId) {
        shortMovieI18nService.batchUpdateIcon(iconDTOS, movieId);
        return AjaxResult.success();
    }

    /**
     * 删除短剧多语言
     */
    @ApiOperation(value = "删除短剧多语言", notes = "删除短剧多语言")
    @PreAuthorize("@ss.hasPermi('system:movieI18n:remove')")
    @Log(title = "影片多语言", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(shortMovieI18nService.deleteShortMovieI18nByIds(ids));
    }

    @ApiOperation(value = "短剧翻译多语言", notes = "翻译多语言")
    @PostMapping("/translateMovie")
    public AjaxResult translateMovie(@RequestBody ShortMovieI18nDTO shortMovieI18nDTO) {
        shortMovieI18nDTO.setUserId(getUserId());
        shortMovieI18nService.translateMovie(shortMovieI18nDTO);
        return AjaxResult.success();
    }


    @ApiOperation(value = "批量分发短剧多语言翻译", notes = "翻译多语言")
    @PostMapping("/batchTranslateMovie")
    public AjaxResult batchTranslateMovie(@RequestBody ShortMovieI18nInDTO shortMovieI18nInDTO) {
        shortMovieI18nInDTO.setUserId(getUserId());
        shortMovieI18nService.batchTranslateMovie(shortMovieI18nInDTO);
        return AjaxResult.success();
    }

    @GetMapping("/queryByMovieId")
    public AjaxResult queryTranslateMovieList(Long movieId, String languageCode) {
        return AjaxResult.success(shortMovieI18nService.queryTranslateMovieList(movieId, languageCode));
    }

    @GetMapping("/translateAllMovie")
    public AjaxResult translateAllMovie() {
        shortMovieI18nService.translateAllMovie();
        return AjaxResult.success();
    }

    @ApiOperation(value = "修改短剧翻译审核状态", notes = "修改短剧翻译审核状态")
    @PutMapping("/updateState")
    public AjaxResult updateState(@RequestBody ShortMovieI18n shortMovieI18n) {
        return shortMovieI18nService.updateState(shortMovieI18n);
    }

    @ApiOperation(value = "修改短剧翻译审核状态", notes = "修改短剧翻译审核状态")
    @GetMapping("/batchUpdateState")
    public AjaxResult batchUpdateState(Long movieId) {
        return shortMovieI18nService.batchUpdateState(movieId);
    }

    //删除短剧翻译记录
    @ApiOperation(value = "删除短剧翻译记录", notes = "删除短剧翻译记录")
    @GetMapping(value = "/delete")
    public AjaxResult deleteMovieI18n(Long movieId, String languageCode) {
        return toAjax(shortMovieI18nService.deleteShortMovieI18n(movieId, languageCode));
    }

    @ApiOperation(value = "查询可翻译的语言种类")
    @GetMapping(value = "/getAccessLanguage/{id}")
    public AjaxResult queryAccessTranslateLanguage(@PathVariable(value = "id") Long movieId) {
        return AjaxResult.success(shortMovieI18nService.queryAccessTranslateLanguageList(movieId));
    }
}
