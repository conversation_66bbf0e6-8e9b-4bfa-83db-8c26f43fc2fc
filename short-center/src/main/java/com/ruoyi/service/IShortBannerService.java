package com.ruoyi.service;

import com.ruoyi.domain.ShortBanner;
import com.ruoyi.dto.ShortBannerInDTO;

import java.util.List;

/**
 * 轮播图数据Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
public interface IShortBannerService 
{
    /**
     * 查询轮播图数据
     * 
     * @param id 轮播图数据主键
     * @return 轮播图数据
     */
    public ShortBanner selectShortBannerById(Long id);

    /**
     * 查询轮播图数据列表
     * 
     * @param shortBanner 轮播图数据
     * @return 轮播图数据集合
     */
    public List<ShortBanner> selectShortBannerList(ShortBanner shortBanner);

    /**
     * 新增轮播图数据
     * 
     * @param shortBanner 轮播图数据
     * @return 结果
     */
    public int insertShortBanner(ShortBanner shortBanner);

    /**
     * 修改轮播图数据
     * 
     * @param shortBanner 轮播图数据
     * @return 结果
     */
    public int updateShortBanner(ShortBanner shortBanner);

    /**
     * 批量删除轮播图数据
     * 
     * @param ids 需要删除的轮播图数据主键集合
     * @return 结果
     */
    public int deleteShortBannerByIds(Long[] ids);

    /**
     * 删除轮播图数据信息
     * 
     * @param id 轮播图数据主键
     * @return 结果
     */
    public int deleteShortBannerById(Long id);

    /**
     * 根据appid获取banner数据
     * @param appId
     * @return
     */
    List<ShortBanner> getBannerListByAppId(Integer appId);

    List<ShortBanner> getBannerListByAppIdAndLanguage(Integer appId, String languageCode, String ifApp);

    int saveBanner(ShortBannerInDTO shortBannerInDTO);

    int deleteByMovieId(Long movieId,Long appId);
}
