package com.ruoyi.mapper;

import com.ruoyi.domain.ShortUserRenewLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 订阅续费日志记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
public interface ShortUserRenewLogMapper
{
    /**
     * 查询订阅续费日志记录
     *
     * @param id 订阅续费日志记录主键
     * @return 订阅续费日志记录
     */
    public ShortUserRenewLog selectShortUserRenewLogById(Long id);

    /**
     * 查询订阅续费日志记录列表
     *
     * @param shortUserRenewLog 订阅续费日志记录
     * @return 订阅续费日志记录集合
     */
    public List<ShortUserRenewLog> selectShortUserRenewLogList(ShortUserRenewLog shortUserRenewLog);

    /**
     * 新增订阅续费日志记录
     *
     * @param shortUserRenewLog 订阅续费日志记录
     * @return 结果
     */
    public int insertShortUserRenewLog(ShortUserRenewLog shortUserRenewLog);

    /**
     * 修改订阅续费日志记录
     *
     * @param shortUserRenewLog 订阅续费日志记录
     * @return 结果
     */
    public int updateShortUserRenewLog(ShortUserRenewLog shortUserRenewLog);

    /**
     * 删除订阅续费日志记录
     *
     * @param id 订阅续费日志记录主键
     * @return 结果
     */
    public int deleteShortUserRenewLogById(Long id);

    /**
     * 批量删除订阅续费日志记录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteShortUserRenewLogByIds(Long[] ids);

    List<Long> selectShortUserCodeIs51(@Param("userIds") List<Long> userIds);

    String selectShortUserCodeByUserId(Long userId);

    int checkLast3ByUserId(Long id);

    String getBankCardByInitId(String paymentIntentId);

    List<Long> getByIds(@Param("list") List<Long> mergedList);

    String getLastCodeByUserId(Long id);
}
