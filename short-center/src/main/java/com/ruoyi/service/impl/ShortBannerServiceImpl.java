package com.ruoyi.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.domain.ShortBanner;
import com.ruoyi.dto.ShortBannerInDTO;
import com.ruoyi.dto.ShortBannerMovieDTO;
import com.ruoyi.dto.ShortBannerTmpDTO;
import com.ruoyi.dto.ShortImageDTO;
import com.ruoyi.mapper.ShortBannerMapper;
import com.ruoyi.service.IShortBannerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 轮播图数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
@Service
public class ShortBannerServiceImpl implements IShortBannerService
{
    @Autowired
    private ShortBannerMapper shortBannerMapper;


    /**
     * 查询轮播图数据
     * 
     * @param id 轮播图数据主键
     * @return 轮播图数据
     */
    @Override
    public ShortBanner selectShortBannerById(Long id)
    {
        return shortBannerMapper.selectShortBannerById(id);
    }

    /**
     * 查询轮播图数据列表
     * 
     * @param shortBanner 轮播图数据
     * @return 轮播图数据
     */
    @Override
    public List<ShortBanner> selectShortBannerList(ShortBanner shortBanner)
    {
        return shortBannerMapper.selectShortBannerList(shortBanner);
    }

    /**
     * 新增轮播图数据
     * 
     * @param shortBanner 轮播图数据
     * @return 结果
     */
    @Override
    public int insertShortBanner(ShortBanner shortBanner)
    {
        shortBanner.setCreateTime(DateUtils.getNowDate());
        shortBanner.setUpdateTime(DateUtils.getNowDate());
        return shortBannerMapper.insertShortBanner(shortBanner);
    }

    /**
     * 修改轮播图数据
     * 
     * @param shortBanner 轮播图数据
     * @return 结果
     */
    @Override
    public int updateShortBanner(ShortBanner shortBanner)
    {
        shortBanner.setUpdateTime(DateUtils.getNowDate());
        return shortBannerMapper.updateShortBanner(shortBanner);
    }

    /**
     * 批量删除轮播图数据
     * 
     * @param ids 需要删除的轮播图数据主键
     * @return 结果
     */
    @Override
    public int deleteShortBannerByIds(Long[] ids)
    {
        return shortBannerMapper.deleteShortBannerByIds(ids);
    }

    /**
     * 删除轮播图数据信息
     * 
     * @param id 轮播图数据主键
     * @return 结果
     */
    @Override
    public int deleteShortBannerById(Long id)
    {
        return shortBannerMapper.deleteShortBannerById(id);
    }

    @Override
    public List<ShortBanner> getBannerListByAppId(Integer appId) {
        return shortBannerMapper.getBannerListByAppId(appId);
    }

    @Override
    public List<ShortBanner> getBannerListByAppIdAndLanguage(Integer appId, String languageCode, String ifApp) {
        if("1".equals(ifApp)){
            appId = 0;
        }
        List<ShortBanner> list = shortBannerMapper.getBannerListByAppIdAndLanguage(appId, languageCode);
        //如果APP 未配置专属banner图 则使用底档数据
        if(CollectionUtil.isEmpty(list)){
            list = shortBannerMapper.getBannerListByAppIdAndLanguage(0, languageCode);
        }
        return list;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int saveBanner(ShortBannerInDTO shortBannerInDTO) {
       //构造数据
       try {
           List<ShortBannerMovieDTO> list = shortBannerInDTO.getList();
           list.forEach(item -> {
               ShortBannerTmpDTO tmpDTO = new ShortBannerTmpDTO();
               tmpDTO.setAppId(shortBannerInDTO.getAppId());
               tmpDTO.setImageList(item.getImageList());
               tmpDTO.setMovieId(item.getMovieId());
               tmpDTO.setOriginalImage(item.getOriginalImage());
               dealBannerData(tmpDTO);
           });
       }catch (Exception e){
           throw new RuntimeException("保存失败");
       }
       return 1;
    }

    @Override
    public int deleteByMovieId(Long movieId,Long appId) {
        return shortBannerMapper.deleteByMovieId(movieId,appId);
    }

    private void dealBannerData(ShortBannerTmpDTO shortBannerInDTO) {
        //先根据 appId和languageCode 删除数据
        ShortBanner shortBannerQuery = new ShortBanner();
        shortBannerQuery.setAppId(shortBannerInDTO.getAppId());
        shortBannerQuery.setMovieId(shortBannerInDTO.getMovieId());
        List<ShortBanner> shortBanners = shortBannerMapper.selectShortBannerList(shortBannerQuery);
        if(CollectionUtil.isNotEmpty(shortBanners)){
            //筛选出需要更新的数据
            Map<String, String> languageImageMap = shortBannerInDTO.getImageList().stream().collect(Collectors.toMap(ShortImageDTO::getLanguageCode, ShortImageDTO::getImage));
            List<String> languageCodeList = shortBannerInDTO.getImageList().stream().map(ShortImageDTO::getLanguageCode).collect(Collectors.toList());
            List<ShortBanner> updateList = shortBanners.stream().filter(item -> languageCodeList.contains(item.getLanguageCode())).collect(Collectors.toList());
            for (ShortBanner shortBanner : updateList) {
                shortBanner.setImage(languageImageMap.get(shortBanner.getLanguageCode()));
                shortBanner.setUpdateTime(DateUtils.getNowDate());
                shortBanner.setOriginalImage(shortBannerInDTO.getOriginalImage());
            }
            shortBannerMapper.batchUpdateShortBanner(updateList);
            //筛选出 languageCodeList 中不存在的记录
            List<String> existLanguageCode = shortBanners.stream().map(ShortBanner::getLanguageCode).collect(Collectors.toList());
            List<ShortImageDTO> insertData = shortBannerInDTO.getImageList().stream().filter(item -> !existLanguageCode.contains(item.getLanguageCode())).collect(Collectors.toList());
            ShortBannerTmpDTO insertDTO = new ShortBannerTmpDTO();
            insertDTO.setOriginalImage(shortBannerInDTO.getOriginalImage());
            insertDTO.setAppId(shortBannerInDTO.getAppId());
            insertDTO.setMovieId(shortBannerInDTO.getMovieId());
            insertDTO.setImageList(insertData);
            saveBannerData(insertDTO);
        }else {
            saveBannerData(shortBannerInDTO);
        }
    }

    private int saveBannerData(ShortBannerTmpDTO shortBannerInDTO) {
        List<ShortImageDTO> imageList = shortBannerInDTO.getImageList();
        if(CollectionUtil.isNotEmpty(imageList)){
            List<ShortBanner> list = new ArrayList<>();
            for (ShortImageDTO shortImageDTO : imageList) {
                ShortBanner shortBanner = new ShortBanner();
                shortBanner.setAppId(shortBannerInDTO.getAppId());
                shortBanner.setLanguageCode(shortImageDTO.getLanguageCode());
                shortBanner.setImage(shortImageDTO.getImage());
                shortBanner.setOriginalImage(shortBannerInDTO.getOriginalImage());
                shortBanner.setMovieId(shortBannerInDTO.getMovieId());
                shortBanner.setCreateTime(DateUtils.getNowDate());
                list.add(shortBanner);
            }
            return shortBannerMapper.insertShortBannerList(list);
        }
        return 0;
    }
}
