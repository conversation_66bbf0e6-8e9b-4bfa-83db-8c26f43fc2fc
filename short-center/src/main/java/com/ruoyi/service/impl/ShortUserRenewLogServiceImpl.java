package com.ruoyi.service.impl;

import java.util.Collections;
import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.domain.ShortUserRenewLog;
import com.ruoyi.mapper.ShortUserRenewLogMapper;
import com.ruoyi.service.IShortUserRenewLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 订阅续费日志记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
@Service
public class ShortUserRenewLogServiceImpl implements IShortUserRenewLogService
{
    @Autowired
    private ShortUserRenewLogMapper shortUserRenewLogMapper;

    /**
     * 查询订阅续费日志记录
     *
     * @param id 订阅续费日志记录主键
     * @return 订阅续费日志记录
     */
    @Override
    public ShortUserRenewLog selectShortUserRenewLogById(Long id)
    {
        return shortUserRenewLogMapper.selectShortUserRenewLogById(id);
    }

    /**
     * 查询订阅续费日志记录列表
     *
     * @param shortUserRenewLog 订阅续费日志记录
     * @return 订阅续费日志记录
     */
    @Override
    public List<ShortUserRenewLog> selectShortUserRenewLogList(ShortUserRenewLog shortUserRenewLog)
    {
        return shortUserRenewLogMapper.selectShortUserRenewLogList(shortUserRenewLog);
    }

    /**
     * 新增订阅续费日志记录
     *
     * @param shortUserRenewLog 订阅续费日志记录
     * @return 结果
     */
    @Override
    public int insertShortUserRenewLog(ShortUserRenewLog shortUserRenewLog)
    {
        shortUserRenewLog.setCreateTime(DateUtils.getNowDate());
        return shortUserRenewLogMapper.insertShortUserRenewLog(shortUserRenewLog);
    }

    /**
     * 修改订阅续费日志记录
     *
     * @param shortUserRenewLog 订阅续费日志记录
     * @return 结果
     */
    @Override
    public int updateShortUserRenewLog(ShortUserRenewLog shortUserRenewLog)
    {
        shortUserRenewLog.setUpdateTime(DateUtils.getNowDate());
        return shortUserRenewLogMapper.updateShortUserRenewLog(shortUserRenewLog);
    }

    /**
     * 批量删除订阅续费日志记录
     *
     * @param ids 需要删除的订阅续费日志记录主键
     * @return 结果
     */
    @Override
    public int deleteShortUserRenewLogByIds(Long[] ids)
    {
        return shortUserRenewLogMapper.deleteShortUserRenewLogByIds(ids);
    }

    /**
     * 删除订阅续费日志记录信息
     *
     * @param id 订阅续费日志记录主键
     * @return 结果
     */
    @Override
    public int deleteShortUserRenewLogById(Long id)
    {
        return shortUserRenewLogMapper.deleteShortUserRenewLogById(id);
    }

    @Override
    public List<Long> selectShortUserCodeIs51(List<Long> userIds) {
        return shortUserRenewLogMapper.selectShortUserCodeIs51(userIds);
    }

    @Override
    public String selectShortUserCodeByUserId(Long userId) {
        return shortUserRenewLogMapper.selectShortUserCodeByUserId(userId);
    }

    @Override
    public List<Long> getByIds(List<Long> mergedList) {
        return shortUserRenewLogMapper.getByIds(mergedList);
    }

    @Override
    public String getLastCodeByUserId(Long id) {
        return shortUserRenewLogMapper.getLastCodeByUserId(id);
    }
}
