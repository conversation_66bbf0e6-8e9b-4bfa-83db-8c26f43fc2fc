package com.ruoyi.web.controller.short_center.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.utils.CrossModuleEnhancementUtil;
import com.ruoyi.domain.ShortApp;
import com.ruoyi.domain.ShortAppLanding;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.domain.ShortFeedback;
import com.ruoyi.service.IShortFeedbackService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 用户反馈Controller
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
@Api("用户反馈管理")
@RestController
@RequestMapping("/short_center/feedback")
public class ShortFeedbackController extends BaseController
{
    @Autowired
    private IShortFeedbackService shortFeedbackService;

    @Autowired
    private CrossModuleEnhancementUtil crossModuleEnhancementUtil;

    /**
     * 查询用户反馈列表
     */
    @ApiOperation("查询用户反馈列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "app_id", value = "选择app", dataType = "Integer", dataTypeClass = Integer.class),
            @ApiImplicitParam(name = "user_id", value = "选择用户", dataType = "Integer", dataTypeClass = String.class),
            @ApiImplicitParam(name = "content", value = "反馈内容", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "status", value = "状态（0正常 1停用）", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "create_by", value = "创建者", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "create_time", value = "创建时间", dataType = "Date", dataTypeClass = String.class),
            @ApiImplicitParam(name = "update_by", value = "更新者", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "update_time", value = "更新时间", dataType = "Date", dataTypeClass = String.class),
            @ApiImplicitParam(name = "remark", value = "备注", dataType = "String", dataTypeClass = String.class)

    })
    @PreAuthorize("@ss.hasPermi('system:feedback:list')")
    @GetMapping("/list")
    public TableDataInfo list(ShortFeedback shortFeedback)
    {
        startPage();
        List<ShortFeedback> list = shortFeedbackService.selectShortFeedbackList(shortFeedback);
        TableDataInfo dataTable = getDataTable(list);
        return crossModuleEnhancementUtil.enhanceTableData(dataTable);
    }

    /**
     * 导出用户反馈列表
     */
    @PreAuthorize("@ss.hasPermi('system:feedback:export')")
    @Log(title = "用户反馈", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ShortFeedback shortFeedback)
    {
        List<ShortFeedback> list = shortFeedbackService.selectShortFeedbackList(shortFeedback);
        ExcelUtil<ShortFeedback> util = new ExcelUtil<ShortFeedback>(ShortFeedback.class);
        util.exportExcel(response, list, "用户反馈数据");
    }

    /**
     * 获取用户反馈详细信息
     */
    @ApiOperation("获取用户反馈详细信息")
    @PreAuthorize("@ss.hasPermi('system:feedback:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(shortFeedbackService.selectShortFeedbackById(id));
    }

    /**
     * 新增用户反馈
     */
    @ApiOperation("新增用户反馈")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "app_id", value = "选择app", dataType = "Integer", dataTypeClass = Integer.class),
            @ApiImplicitParam(name = "user_id", value = "选择用户", dataType = "Integer", dataTypeClass = String.class),
            @ApiImplicitParam(name = "content", value = "反馈内容", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "status", value = "状态（0正常 1停用）", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "create_by", value = "创建者", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "create_time", value = "创建时间", dataType = "Date", dataTypeClass = String.class),
            @ApiImplicitParam(name = "update_by", value = "更新者", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "update_time", value = "更新时间", dataType = "Date", dataTypeClass = String.class),
            @ApiImplicitParam(name = "remark", value = "备注", dataType = "String", dataTypeClass = String.class)

    })
    @PreAuthorize("@ss.hasPermi('system:feedback:add')")
    @Log(title = "用户反馈", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ShortFeedback shortFeedback)
    {
        return toAjax(shortFeedbackService.insertShortFeedback(shortFeedback));
    }

    /**
     * 修改用户反馈
     */
    @ApiOperation("修改用户反馈")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "id", dataType = "Integer", dataTypeClass = Integer.class),
            @ApiImplicitParam(name = "app_id", value = "选择app", dataType = "Integer", dataTypeClass = Integer.class),
            @ApiImplicitParam(name = "user_id", value = "选择用户", dataType = "Integer", dataTypeClass = String.class),
            @ApiImplicitParam(name = "content", value = "反馈内容", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "status", value = "状态（0正常 1停用）", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "create_by", value = "创建者", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "create_time", value = "创建时间", dataType = "Date", dataTypeClass = String.class),
            @ApiImplicitParam(name = "update_by", value = "更新者", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "update_time", value = "更新时间", dataType = "Date", dataTypeClass = String.class),
            @ApiImplicitParam(name = "remark", value = "备注", dataType = "String", dataTypeClass = String.class)

    })
    @PreAuthorize("@ss.hasPermi('system:feedback:edit')")
    @Log(title = "用户反馈", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ShortFeedback shortFeedback)
    {
        return toAjax(shortFeedbackService.updateShortFeedback(shortFeedback));
    }

    /**
     * 删除用户反馈
     */
    @ApiOperation("修改用户反馈")
    @PreAuthorize("@ss.hasPermi('system:feedback:remove')")
    @Log(title = "用户反馈", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(shortFeedbackService.deleteShortFeedbackByIds(ids));
    }

    /**
     * 根据用户id或邮箱查询用户信息及订单信息
     */
    @ApiOperation("根据用户id或邮箱查询用户信息及订单信息")
    @GetMapping("/getUserAndOrder")
    public AjaxResult getUserAndOrder(@RequestParam(required = false) Long userId,@RequestParam Long appId,@RequestParam(required = false) String email)
    {
        ShortFeedback list = shortFeedbackService.getUserAndOrder(userId,appId,email);
        return success(list);
    }
}
