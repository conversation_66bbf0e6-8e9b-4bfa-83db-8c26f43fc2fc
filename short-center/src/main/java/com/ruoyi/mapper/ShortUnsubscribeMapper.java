package com.ruoyi.mapper;

import com.ruoyi.domain.ShortUnsubscribe;

import java.util.List;

/**
 * 退订Mapper接口
 *
 * <AUTHOR>
 * @date 2025-09-22
 */
public interface ShortUnsubscribeMapper
{
    /**
     * 查询退订
     *
     * @param id 退订主键
     * @return 退订
     */
    public ShortUnsubscribe selectShortUnsubscribeById(Long id);

    /**
     * 查询退订列表
     *
     * @param shortUnsubscribe 退订
     * @return 退订集合
     */
    public List<ShortUnsubscribe> selectShortUnsubscribeList(ShortUnsubscribe shortUnsubscribe);

    /**
     * 新增退订
     *
     * @param shortUnsubscribe 退订
     * @return 结果
     */
    public int insertShortUnsubscribe(ShortUnsubscribe shortUnsubscribe);

    /**
     * 修改退订
     *
     * @param shortUnsubscribe 退订
     * @return 结果
     */
    public int updateShortUnsubscribe(ShortUnsubscribe shortUnsubscribe);

    /**
     * 删除退订
     *
     * @param id 退订主键
     * @return 结果
     */
    public int deleteShortUnsubscribeById(Long id);

    /**
     * 批量删除退订
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteShortUnsubscribeByIds(Long[] ids);
}
