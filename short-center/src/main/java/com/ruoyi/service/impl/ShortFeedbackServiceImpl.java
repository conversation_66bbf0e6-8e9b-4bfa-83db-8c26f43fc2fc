package com.ruoyi.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.domain.*;
import com.ruoyi.mapper.*;
import com.ruoyi.service.IShortFeedbackService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户反馈Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
@Service
public class ShortFeedbackServiceImpl implements IShortFeedbackService 
{
    @Autowired
    private ShortFeedbackMapper shortFeedbackMapper;

    @Autowired
    private ShortUserMapper shortUserMapper;

    @Autowired
    private ShortOrderMapper shortOrderMapper;

    @Autowired
    private ShortAppMapper shortAppMapper;

    @Autowired
    private ShortMovieMapper shortMovieMapper;


    /**
     * 查询用户反馈
     * 
     * @param id 用户反馈主键
     * @return 用户反馈
     */
    @Override
    public ShortFeedback selectShortFeedbackById(Long id)
    {
        return shortFeedbackMapper.selectShortFeedbackById(id);
    }

    /**
     * 查询用户反馈列表
     * 
     * @param shortFeedback 用户反馈
     * @return 用户反馈
     */
    @Override
    public List<ShortFeedback> selectShortFeedbackList(ShortFeedback shortFeedback)
    {
        List<ShortFeedback> shortFeedbackList = shortFeedbackMapper.selectShortFeedbackList(shortFeedback);
        if(CollectionUtil.isNotEmpty(shortFeedbackList)){
            List<Long> userIdList = shortFeedbackList.stream().map(ShortFeedback::getUserId).distinct().collect(Collectors.toList());
            //查询用户信息
            List<ShortUser> shortUserList =shortUserMapper.selectByUserIdList(userIdList);
            Map<Long, String> subcributeMap = shortUserList.stream()
                    .filter(user -> user.getId() != null) // 过滤掉id为null的用户
                    .collect(Collectors.toMap(
                            ShortUser::getId,
                            user -> user.getIsSubscriber() != null ? user.getIsSubscriber() : "0", // 处理null值
                            (existing, replacement) -> existing // 处理重复键的情况
                    ));
            for (ShortFeedback feedback : shortFeedbackList) {
                feedback.setIsSubscriber(subcributeMap.getOrDefault(feedback.getUserId(), "0"));
            }
        }
        return shortFeedbackList;
    }

    /**
     * 新增用户反馈
     * 
     * @param shortFeedback 用户反馈
     * @return 结果
     */
    @Override
    public int insertShortFeedback(ShortFeedback shortFeedback)
    {
        shortFeedback.setCreateTime(DateUtils.getNowDate());
        shortFeedback.setReplyType(null == shortFeedback.getReplyType() ? 0 : shortFeedback.getReplyType());
        return shortFeedbackMapper.insertShortFeedback(shortFeedback);
    }

    /**
     * 修改用户反馈
     * 
     * @param shortFeedback 用户反馈
     * @return 结果
     */
    @Override
    public int updateShortFeedback(ShortFeedback shortFeedback)
    {
        shortFeedback.setUpdateTime(DateUtils.getNowDate());
        return shortFeedbackMapper.updateShortFeedback(shortFeedback);
    }

    /**
     * 批量删除用户反馈
     * 
     * @param ids 需要删除的用户反馈主键
     * @return 结果
     */
    @Override
    public int deleteShortFeedbackByIds(Long[] ids)
    {
        return shortFeedbackMapper.deleteShortFeedbackByIds(ids);
    }

    /**
     * 删除用户反馈信息
     * 
     * @param id 用户反馈主键
     * @return 结果
     */
    @Override
    public int deleteShortFeedbackById(Long id)
    {
        return shortFeedbackMapper.deleteShortFeedbackById(id);
    }

    @Override
    public ShortFeedback getUserAndOrder(Long userId,Long appId,String email) {
        ShortFeedback feedback = new ShortFeedback();
        List<ShortUser> shortUserList = new ArrayList<>();
        List<ShortOrder> shortOrderList = new ArrayList<>();
        if(userId != null){
            ShortUser shortUser = shortUserMapper.selectShortUserById(userId);
            if(null ==shortUser) {
                feedback.setShortUserList(shortUserList);
                feedback.setShortOrderList(shortOrderList);
                return feedback;
            }
            ShortApp shortApp = shortAppMapper.selectShortAppById(shortUser.getAppId());
            if(shortApp != null){
                shortUser.setAppName(shortApp.getName());
                if(shortApp.getType() == "0")
                    shortUser.setPayType(shortUserMapper.getPayTypeById(shortUser.getId(),shortUser.getAppId()));
                else
                    shortUser.setPayType(shortOrderMapper.getPayTypeByUserId(shortUser.getId(),shortUser.getAppId()));
            }

            ShortMovie shortMovie = shortMovieMapper.getMovieNameByUserId(shortUser.getId());
            if(shortMovie != null){
                shortUser.setMovieName(shortMovie.getName());
                shortUser.setMovieOldName(shortMovie.getOldname());
            }
            shortUser.setSumPrice(shortOrderMapper.getSumPriceByUserId(shortUser.getId()));
            shortUserList.add(shortUser);

            feedback.setShortUserList(shortUserList);

            shortOrderList = shortOrderMapper.getByUserId(shortUser.getId());
            feedback.setShortOrderList(shortOrderList);
        }else{
            if(appId == null || email == null)
                return feedback;
            ShortUser shortUserReq = new ShortUser();
//            shortUserReq.setAppId(appId);
            shortUserReq.setEmail(email);
            List<ShortUser> shortUsers = shortUserMapper.selectShortUserList(shortUserReq);
            for(ShortUser shortUser : shortUsers){
                ShortApp shortApp = shortAppMapper.selectShortAppById(shortUser.getAppId());
                if(shortApp != null){
                    shortUser.setAppName(shortApp.getName());
                    if(shortApp.getType() == "0")
                        shortUser.setPayType(shortUserMapper.getPayTypeById(shortUser.getId(),shortUser.getAppId()));
                    else
                        shortUser.setPayType(shortOrderMapper.getPayTypeByUserId(shortUser.getId(),shortUser.getAppId()));
                }

                ShortMovie shortMovie = shortMovieMapper.getMovieNameByUserId(shortUser.getId());
                if(shortMovie != null){
                    shortUser.setMovieName(shortMovie.getName());
                    shortUser.setMovieOldName(shortMovie.getOldname());
                }
                shortUser.setSumPrice(shortOrderMapper.getSumPriceByUserId(shortUser.getId()));
                shortUserList.add(shortUser);

                List<ShortOrder> orderList = shortOrderMapper.getByUserId(shortUser.getId());
                shortOrderList.addAll(orderList);
            }
            feedback.setShortUserList(shortUserList);
            feedback.setShortOrderList(shortOrderList);
        }
        return feedback;
    }

    /**
     * 检查邮箱在指定应用中是否存在反馈记录
     *
     * @param email 邮箱
     * @param appId 应用ID
     * @return 是否存在
     */
    @Override
    public boolean existsByEmailAndAppId(String email, Long appId) {
        if (email == null || appId == null) {
            return false;
        }

        ShortFeedback query = new ShortFeedback();
        query.setEmail(email);
        query.setAppId(appId);
        List<ShortFeedback> list = shortFeedbackMapper.selectShortFeedbackList(query);
        return !list.isEmpty();
    }

    /**
     * 批量检查邮箱在指定应用中是否存在反馈记录
     *
     * @param emails 邮箱列表
     * @param appId 应用ID
     * @return 存在的邮箱列表
     */
    @Override
    public List<String> batchExistsByEmailAndAppId(List<String> emails, Long appId) {
        if (emails == null || emails.isEmpty() || appId == null) {
            return new java.util.ArrayList<>();
        }

        List<String> existingEmails = new java.util.ArrayList<>();
        for (String email : emails) {
            if (existsByEmailAndAppId(email, appId)) {
                existingEmails.add(email);
            }
        }
        return existingEmails;
    }
}
