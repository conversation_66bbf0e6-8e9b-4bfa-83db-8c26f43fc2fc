package com.ruoyi.quartz.task;

import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.system.domain.BankCardAlert;
import com.ruoyi.system.domain.BankCardRule;
import com.ruoyi.system.domain.BankCardStatistics;
import com.ruoyi.system.service.IBankCardAlertService;
import com.ruoyi.system.service.IBankCardRuleService;
import com.ruoyi.system.service.IBankCardStatisticsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 银行卡每日增量统计定时任务
 *
 * 在RuoYi定时任务管理中配置：
 * 任务名称：银行卡统计任务
 * 调用方法：bankCardStatisticsTask.executeDailyStatistics
 * cron表达式：0 30 0 * * ?
 * 是否并发：否
 */
@Component("bankCardStatisticsTask")
public class BankCardStatisticsTask
{
    private static final Logger log = LoggerFactory.getLogger(BankCardStatisticsTask.class);

    /**
     * 每日增量统计主任务
     * 处理前一日所有有银行卡号的订单，更新统计数据并检查预警
     */
    public void executeDailyStatistics()
    {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        LocalDateTime startTime = LocalDateTime.now();
        String lockKey = "bankcard:statistics:lock:" + yesterday.toString();
        String requestId = UUID.randomUUID().toString();

        log.info("=== 开始执行银行卡增量统计任务，目标日期: {}, 锁标识: {} ===", yesterday, lockKey);

        RedisCache redisCache = null;
        boolean lockAcquired = false;
        try {
            IBankCardStatisticsService statisticsService = SpringUtils.getBean(IBankCardStatisticsService.class);
            IBankCardAlertService alertService = SpringUtils.getBean(IBankCardAlertService.class);
            redisCache = SpringUtils.getBean(RedisCache.class);

            // 使用Redis分布式锁防止重复执行
            int lockExpireSeconds = 7200; // 2小时锁超时
            lockAcquired = redisCache.tryLock(lockKey, requestId, lockExpireSeconds);
            if (!lockAcquired) {
                log.warn("银行卡统计任务已在执行中，跳过本次执行: {}", lockKey);
                return;
            }

            log.info("成功获取Redis分布式锁: {}", lockKey);

            // 1. 获取昨日有订单的银行卡列表
            List<String> activeCardNumbers = statisticsService.getActiveCardNumbers(yesterday);

            if (activeCardNumbers == null || activeCardNumbers.isEmpty()) {
                log.info("昨日无银行卡订单，跳过统计");
                return;
            }

            log.info("昨日活跃银行卡数量: {}", activeCardNumbers.size());

            // 2. 分批处理银行卡统计
            int batchSize = 50; // 每批处理50张卡
            int totalBatches = (int) Math.ceil((double) activeCardNumbers.size() / batchSize);
            int successCount = 0;
            int failureCount = 0;

            for (int i = 0; i < totalBatches; i++) {
                int fromIndex = i * batchSize;
                int toIndex = Math.min((i + 1) * batchSize, activeCardNumbers.size());
                List<String> batch = activeCardNumbers.subList(fromIndex, toIndex);

                log.info("处理第 {}/{} 批次，卡数量: {}", i + 1, totalBatches, batch.size());

                // 处理当前批次
                for (String cardNumber : batch) {
                    try {
                        processCardStatistics(cardNumber, yesterday, statisticsService, alertService);
                        successCount++;
                    } catch (Exception e) {
                        log.error("处理银行卡统计失败: cardNumber={}, date={}", cardNumber, yesterday, e);
                        failureCount++;
                    }
                }
            }

            // 3. 任务完成统计
            LocalDateTime endTime = LocalDateTime.now();
            long duration = java.time.Duration.between(startTime, endTime).toMillis();

            log.info("=== 银行卡增量统计任务完成 ===");
            log.info("处理时间: {}ms", duration);
            log.info("成功处理: {} 张卡", successCount);
            log.info("失败处理: {} 张卡", failureCount);
            log.info("处理成功率: {}%", successCount * 100.0 / activeCardNumbers.size());

        } catch (Exception e) {
            log.error("银行卡增量统计任务执行失败", e);
        } finally {
            // 确保释放Redis分布式锁
            if (redisCache != null && lockAcquired) {
                try {
                    boolean released = redisCache.releaseLock(lockKey, requestId);
                    log.info("释放Redis分布式锁: {} - {}", lockKey, released ? "成功" : "失败");
                } catch (Exception ex) {
                    log.error("释放Redis锁异常: {}", lockKey, ex);
                }
            }
        }
    }

    /**
     * 处理单张银行卡的统计更新
     *
     * @param cardNumber 银行卡号
     * @param targetDate 目标日期
     */
    private void processCardStatistics(String cardNumber, LocalDate targetDate,
                                     IBankCardStatisticsService statisticsService,
                                     IBankCardAlertService alertService) {
        log.debug("开始处理银行卡统计: {}", cardNumber);

        try {
            // 1. 获取或创建银行卡统计记录
            BankCardStatistics cardStats = statisticsService.getOrCreateStatistics(cardNumber);

            // 2. 获取该卡昨日的订单数据（这里需要实际实现查询逻辑）
            List<OrderData> yesterdayOrders = getYesterdayOrders(cardNumber, targetDate);

            if (yesterdayOrders.isEmpty()) {
                log.debug("银行卡 {} 昨日无订单数据", cardNumber);
                return;
            }

            // 3. 按用户分组处理（一张卡可能被多个用户使用）
            Map<Long, List<OrderData>> userOrderMap = yesterdayOrders.stream()
                .collect(Collectors.groupingBy(OrderData::getUserId));

            // 4. 更新用户关联信息
            updateUserAssociation(cardStats, userOrderMap.keySet(), statisticsService);

            // 5. 按订单类型累计统计
            updateOrderStatistics(cardStats, yesterdayOrders, statisticsService);

            // 6. 保存统计数据
            int updateResult = statisticsService.updateBankCardStatistics(cardStats);

            if (updateResult <= 0) {
                throw new RuntimeException("更新银行卡统计失败: " + cardNumber);
            }

            // 7. 检查预警阈值（基于会员周期）
            checkAndCreateAlerts(cardNumber, cardStats, targetDate, alertService, statisticsService);

            log.debug("完成处理银行卡统计: {}", cardNumber);

        } catch (Exception e) {
            log.error("处理银行卡统计异常: cardNumber={}", cardNumber, e);
            throw e;
        }
    }

    /**
     * 获取指定银行卡昨日的订单数据
     * 通过BankCardStatisticsMapper查询
     */
    private List<OrderData> getYesterdayOrders(String cardNumber, LocalDate targetDate) {
        try {
            IBankCardStatisticsService statisticsService = SpringUtils.getBean(IBankCardStatisticsService.class);

            // 通过Mapper查询昨日的订单数据
            List<Map<String, Object>> orderResults = statisticsService.selectCardOrdersByDate(cardNumber, targetDate);

            List<OrderData> orders = new ArrayList<>();

            if (orderResults != null && !orderResults.isEmpty()) {
                for (Map<String, Object> row : orderResults) {
                    Long userId = (Long) row.get("user_id");
                    String payType = (String) row.get("pay_type");
                    BigDecimal amount = (BigDecimal) row.get("amount");

                    orders.add(new OrderData(userId, payType, amount));
                }
            }

            log.debug("查询银行卡 {} 在日期 {} 的订单数据: {} 条", cardNumber, targetDate, orders.size());
            return orders;

        } catch (Exception e) {
            log.error("查询银行卡订单数据失败: cardNumber={}, date={}", cardNumber, targetDate, e);
            return new ArrayList<>();
        }
    }

    /**
     * 更新银行卡关联用户信息
     */
    private void updateUserAssociation(BankCardStatistics cardStats, Set<Long> newUserIds,
                                     IBankCardStatisticsService statisticsService) {
        // 解析历史用户ID列表
        Set<Long> historyUserIds = parseUserIds(cardStats.getHistoryUserIds());

        // 添加新用户ID
        historyUserIds.addAll(newUserIds);

        // 如果是首次使用，设置主用户（选择ID最小的用户）
        Long primaryUserId = cardStats.getUserId();
        if (primaryUserId == null && !newUserIds.isEmpty()) {
            primaryUserId = Collections.min(newUserIds); // 选择ID最小的用户
        }

        // 更新关联用户信息
        List<Long> allUserIds = new ArrayList<>(historyUserIds);
        boolean success = statisticsService.updateUserAssociation(
            cardStats.getCardNumber(), primaryUserId, allUserIds);

        if (success) {
            // 更新内存中的对象
            cardStats.setUserId(primaryUserId);
            // 简单JSON格式：[1,2,3]
            cardStats.setHistoryUserIds("[" + String.join(",", allUserIds.stream().map(String::valueOf).toArray(String[]::new)) + "]");
            cardStats.setRelatedUsers(historyUserIds.size());
        }
    }

    /**
     * 更新订单统计数据
     */
    private void updateOrderStatistics(BankCardStatistics cardStats, List<OrderData> orders,
                                     IBankCardStatisticsService statisticsService) {
        int subCount = 0;
        BigDecimal subAmount = BigDecimal.ZERO;
        int recCount = 0;
        BigDecimal recAmount = BigDecimal.ZERO;

        for (OrderData order : orders) {
            String payType = order.getPayType();
            if ("订阅".equals(payType) || "订阅续费".equals(payType)) {
                subCount++;
                subAmount = subAmount.add(order.getAmount());
            } else if ("充值".equals(payType)) {
                recCount++;
                recAmount = recAmount.add(order.getAmount());
            }
        }

        // 更新订阅统计
        if (subCount > 0) {
            statisticsService.addSubscriptionStats(cardStats.getCardNumber(), subCount, subAmount);
            cardStats.setSubTotalCount(cardStats.getSubTotalCount() + subCount);
            cardStats.setSubTotalAmount(cardStats.getSubTotalAmount().add(subAmount));
        }

        // 更新充值统计
        if (recCount > 0) {
            statisticsService.addRechargeStats(cardStats.getCardNumber(), recCount, recAmount);
            cardStats.setRecTotalCount(cardStats.getRecTotalCount() + recCount);
            cardStats.setRecTotalAmount(cardStats.getRecTotalAmount().add(recAmount));
        }

        log.debug("更新订单统计 - 卡号: {}, 订阅: {}次/{}元, 充值: {}次/{}元",
            cardStats.getCardNumber(), subCount, subAmount, recCount, recAmount);
    }

    /**
     * 检查并创建预警
     */
    private void checkAndCreateAlerts(String cardNumber, BankCardStatistics cardStats, LocalDate targetDate,
                                    IBankCardAlertService alertService, IBankCardStatisticsService statisticsService) {
        try {
            // 1. 检查多用户绑定预警
            checkMultiUserAlert(cardNumber, cardStats, alertService);

            // 2. 检查会员周期阈值（基于主用户的订阅周期）
            if (cardStats.getUserId() != null) {
                checkMemberCycleAlerts(cardNumber, cardStats, targetDate, alertService, statisticsService);
            }

        } catch (Exception e) {
            log.error("检查预警失败: cardNumber={}", cardNumber, e);
        }
    }

    /**
     * 检查多用户绑定预警（只检查订阅续费用户）
     */
    private void checkMultiUserAlert(String cardNumber, BankCardStatistics cardStats, IBankCardAlertService alertService) {
        try {
            IBankCardStatisticsService statisticsService = SpringUtils.getBean(IBankCardStatisticsService.class);

            // 获取该银行卡的所有订阅续费用户
            List<Long> subscriptionRenewalUsers = statisticsService.getCardSubscriptionRenewalUsers(cardNumber);

            if (subscriptionRenewalUsers.size() > 1) {
                String alertDetail = String.format("银行卡 %s 被 %d 个用户用于订阅续费：%s",
                    cardNumber, subscriptionRenewalUsers.size(), subscriptionRenewalUsers.toString());

                createAlert(cardNumber, cardStats.getUserId(), BankCardAlert.AlertType.MULTI_USER,
                          alertDetail, alertService);

                log.info("检测到多用户订阅续费告警: {} - 用户数: {}", cardNumber, subscriptionRenewalUsers.size());
            } else {
                log.debug("银行卡 {} 的订阅续费用户数: {} (无需告警)", cardNumber, subscriptionRenewalUsers.size());
            }
        } catch (Exception e) {
            log.error("检查多用户订阅续费告警失败: cardNumber={}", cardNumber, e);
        }
    }

    /**
     * 检查会员周期阈值
     */
    private void checkMemberCycleAlerts(String cardNumber, BankCardStatistics cardStats, LocalDate targetDate,
                                      IBankCardAlertService alertService, IBankCardStatisticsService statisticsService) {
        Long primaryUserId = cardStats.getUserId();

        try {
            IBankCardRuleService ruleService = SpringUtils.getBean(IBankCardRuleService.class);

            // 计算当前会员周期
            int currentWeek = calculateCurrentWeek(cardNumber, targetDate, statisticsService);
            int currentMonth = calculateCurrentMonth(cardNumber, targetDate, statisticsService);

            // 获取规则配置
            BankCardRule weekRule = ruleService.selectBankCardRuleByRuleType("week");
            BankCardRule monthRule = ruleService.selectBankCardRuleByRuleType("month");

            // 计算当前周期的时间范围并获取该周期内的统计数据
            LocalDate cardFirstUseDate = statisticsService.getCardFirstUseDate(cardNumber);
            if (cardFirstUseDate == null) {
                log.debug("银行卡 {} 没有首次使用记录，跳过周期检查", cardNumber);
                return;
            }

            // 检查周阈值
            if (weekRule != null && weekRule.getIsActive() == 1) {
                // 计算当前周的开始和结束日期
                LocalDate weekStartDate = cardFirstUseDate.plusWeeks(currentWeek - 1);
                LocalDate weekEndDate = weekStartDate.plusDays(6);
                if (weekEndDate.isAfter(targetDate)) {
                    weekEndDate = targetDate; // 不超过当前统计日期
                }

                // 获取周期内的订阅统计数据（排除充值）
                Map<String, Object> weekStats = statisticsService.getCardStatsByDateRange(cardNumber, weekStartDate, weekEndDate);
                int weekSubCount = ((Number) weekStats.getOrDefault("subscription_count", 0)).intValue();
                BigDecimal weekSubAmount = (BigDecimal) weekStats.getOrDefault("subscription_amount", BigDecimal.ZERO);

                // 告警检测：只基于订阅/订阅续费数据，完全排除充值
                boolean weekOverLimit = (weekRule.getThresholdCount() != null && weekSubCount > weekRule.getThresholdCount()) ||
                                      (weekRule.getThresholdAmount() != null && weekSubAmount.compareTo(weekRule.getThresholdAmount()) > 0);

                if (weekOverLimit) {
                    // 获取该周期内实际有订阅订单的用户ID列表
                    List<Long> weekSubscriptionUsers = statisticsService.getCardSubscriptionUsersByDateRange(cardNumber, weekStartDate, weekEndDate);
                    String userIdsList = weekSubscriptionUsers.isEmpty() ? "无" : weekSubscriptionUsers.toString();

                    String alertDetail = String.format(
                        "会员第%d周超限：订阅次数=%d(阈值=%d)，订阅金额=%.2f(阈值=%.2f)，涉及用户ID=%s",
                        currentWeek, weekSubCount,
                        weekRule.getThresholdCount() != null ? weekRule.getThresholdCount() : 0,
                        weekSubAmount,
                        weekRule.getThresholdAmount() != null ? weekRule.getThresholdAmount() : BigDecimal.ZERO,
                        userIdsList);

                    createAlert(cardNumber, primaryUserId, BankCardAlert.AlertType.WEEK_OVERUSE, alertDetail, alertService);
                }
            }

            // 检查月阈值
            if (monthRule != null && monthRule.getIsActive() == 1) {
                // 计算当前月的开始和结束日期
                LocalDate monthStartDate = cardFirstUseDate.plusMonths(currentMonth - 1);
                LocalDate monthEndDate = monthStartDate.plusMonths(1).minusDays(1);
                if (monthEndDate.isAfter(targetDate)) {
                    monthEndDate = targetDate; // 不超过当前统计日期
                }

                // 获取月期内的订阅统计数据（排除充值）
                Map<String, Object> monthStats = statisticsService.getCardStatsByDateRange(cardNumber, monthStartDate, monthEndDate);
                int monthSubCount = ((Number) monthStats.getOrDefault("subscription_count", 0)).intValue();
                BigDecimal monthSubAmount = (BigDecimal) monthStats.getOrDefault("subscription_amount", BigDecimal.ZERO);

                // 告警检测：只基于订阅/订阅续费数据，完全排除充值
                boolean monthOverLimit = (monthRule.getThresholdCount() != null && monthSubCount > monthRule.getThresholdCount()) ||
                                       (monthRule.getThresholdAmount() != null && monthSubAmount.compareTo(monthRule.getThresholdAmount()) > 0);

                if (monthOverLimit) {
                    // 获取该月期内实际有订阅订单的用户ID列表
                    List<Long> monthSubscriptionUsers = statisticsService.getCardSubscriptionUsersByDateRange(cardNumber, monthStartDate, monthEndDate);
                    String userIdsList = monthSubscriptionUsers.isEmpty() ? "无" : monthSubscriptionUsers.toString();

                    String alertDetail = String.format(
                        "会员第%d月超限：订阅次数=%d(阈值=%d)，订阅金额=%.2f(阈值=%.2f)，涉及用户ID=%s",
                        currentMonth, monthSubCount,
                        monthRule.getThresholdCount() != null ? monthRule.getThresholdCount() : 0,
                        monthSubAmount,
                        monthRule.getThresholdAmount() != null ? monthRule.getThresholdAmount() : BigDecimal.ZERO,
                        userIdsList);

                    createAlert(cardNumber, primaryUserId, BankCardAlert.AlertType.MONTH_OVERUSE, alertDetail, alertService);
                }
            }

        } catch (Exception e) {
            log.error("检查会员周期阈值失败: cardNumber={}", cardNumber, e);
        }
    }

    /**
     * 计算银行卡当前是使用的第几周
     */
    private int calculateCurrentWeek(String cardNumber, LocalDate targetDate, IBankCardStatisticsService statisticsService) {
        LocalDate cardFirstUseDate = statisticsService.getCardFirstUseDate(cardNumber);
        if (cardFirstUseDate == null) {
            return 1; // 新卡默认第1周
        }

        long daysBetween = ChronoUnit.DAYS.between(cardFirstUseDate, targetDate);
        return Math.max(1, (int) (daysBetween / 7) + 1);
    }

    /**
     * 计算银行卡当前是使用的第几月
     */
    private int calculateCurrentMonth(String cardNumber, LocalDate targetDate, IBankCardStatisticsService statisticsService) {
        LocalDate cardFirstUseDate = statisticsService.getCardFirstUseDate(cardNumber);
        if (cardFirstUseDate == null) {
            return 1; // 新卡默认第1月
        }

        long monthsBetween = ChronoUnit.MONTHS.between(cardFirstUseDate, targetDate);
        return Math.max(1, (int) monthsBetween + 1);
    }

    /**
     * 创建告警记录
     */
    private void createAlert(String cardNumber, Long userId, String alertType, String alertDetail,
                           IBankCardAlertService alertService) {
        // 避免重复告警（24小时内同类型告警只生成一次）
        LocalDateTime since = LocalDateTime.now().minusHours(24);
        if (alertService.alertExists(cardNumber, alertType, since)) {
            log.debug("告警已存在，跳过创建: {} - {}", cardNumber, alertType);
            return;
        }

        // 创建新告警
        BankCardAlert alert = new BankCardAlert();
        alert.setCardNumber(cardNumber);
        alert.setUserId(userId);
        alert.setAlertType(alertType);
        alert.setAlertDetail(alertDetail);
        alert.setProcessedFlag(0); // 新告警默认未处理

        boolean success = alertService.createAlert(alert);
        if (success) {
            log.info("创建新告警: {} - {} - {}", cardNumber, alertType, alertDetail);

            // 发送通知
            alertService.sendAlertNotification(alert);
        } else {
            log.error("创建告警失败: {} - {}", cardNumber, alertType);
        }
    }

    /**
     * 解析用户ID列表
     */
    private Set<Long> parseUserIds(String historyUserIdsJson) {
        if (historyUserIdsJson == null || historyUserIdsJson.trim().isEmpty()) {
            return new HashSet<>();
        }

        try {
            // 简单解析JSON数组格式：[1,2,3]
            String cleaned = historyUserIdsJson.trim().replace("[", "").replace("]", "");
            if (cleaned.isEmpty()) {
                return new HashSet<>();
            }

            Set<Long> userIds = new HashSet<>();
            String[] parts = cleaned.split(",");
            for (String part : parts) {
                try {
                    userIds.add(Long.valueOf(part.trim()));
                } catch (NumberFormatException e) {
                    log.warn("解析用户ID失败: {}", part, e);
                }
            }
            return userIds;
        } catch (Exception e) {
            log.warn("解析用户ID列表失败: {}", historyUserIdsJson, e);
            return new HashSet<>();
        }
    }

    /**
     * 订单数据内部类
     */
    private static class OrderData {
        private Long userId;
        private String payType;
        private BigDecimal amount;

        // 构造方法和getter/setter
        public OrderData(Long userId, String payType, BigDecimal amount) {
            this.userId = userId;
            this.payType = payType;
            this.amount = amount;
        }

        public Long getUserId() { return userId; }
        public String getPayType() { return payType; }
        public BigDecimal getAmount() { return amount; }
    }
}