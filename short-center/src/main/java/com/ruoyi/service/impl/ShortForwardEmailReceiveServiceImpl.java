package com.ruoyi.service.impl;

import java.util.Collections;
import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.domain.ShortForwardEmailReceive;
import com.ruoyi.mapper.ShortForwardEmailReceiveMapper;
import com.ruoyi.service.IShortForwardEmailReceiveService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 收件箱Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
@Service
public class ShortForwardEmailReceiveServiceImpl implements IShortForwardEmailReceiveService
{
    @Autowired
    private ShortForwardEmailReceiveMapper shortForwardEmailReceiveMapper;

    /**
     * 查询收件箱
     *
     * @param id 收件箱主键
     * @return 收件箱
     */
    @Override
    public ShortForwardEmailReceive selectShortForwardEmailReceiveById(Long id)
    {
        return shortForwardEmailReceiveMapper.selectShortForwardEmailReceiveById(id);
    }

    /**
     * 查询收件箱列表
     *
     * @param shortForwardEmailReceive 收件箱
     * @return 收件箱
     */
    @Override
    public List<ShortForwardEmailReceive> selectShortForwardEmailReceiveList(ShortForwardEmailReceive shortForwardEmailReceive)
    {
        return shortForwardEmailReceiveMapper.selectShortForwardEmailReceiveList(shortForwardEmailReceive);
    }

    /**
     * 新增收件箱
     *
     * @param shortForwardEmailReceive 收件箱
     * @return 结果
     */
    @Override
    public int insertShortForwardEmailReceive(ShortForwardEmailReceive shortForwardEmailReceive)
    {
        shortForwardEmailReceive.setCreateTime(DateUtils.getNowDate());
        return shortForwardEmailReceiveMapper.insertShortForwardEmailReceive(shortForwardEmailReceive);
    }

    /**
     * 修改收件箱
     *
     * @param shortForwardEmailReceive 收件箱
     * @return 结果
     */
    @Override
    public int updateShortForwardEmailReceive(ShortForwardEmailReceive shortForwardEmailReceive)
    {
        shortForwardEmailReceive.setUpdateTime(DateUtils.getNowDate());
        return shortForwardEmailReceiveMapper.updateShortForwardEmailReceive(shortForwardEmailReceive);
    }

    /**
     * 批量删除收件箱
     *
     * @param ids 需要删除的收件箱主键
     * @return 结果
     */
    @Override
    public int deleteShortForwardEmailReceiveByIds(Long[] ids)
    {
        return shortForwardEmailReceiveMapper.deleteShortForwardEmailReceiveByIds(ids);
    }

    /**
     * 删除收件箱信息
     *
     * @param id 收件箱主键
     * @return 结果
     */
    @Override
    public int deleteShortForwardEmailReceiveById(Long id)
    {
        return shortForwardEmailReceiveMapper.deleteShortForwardEmailReceiveById(id);
    }

    @Override
    public List<ShortForwardEmailReceive>  getEmailContentByEmailAndAppId(ShortForwardEmailReceive shortForwardEmailReceive) {
        return shortForwardEmailReceiveMapper.getEmailContentByEmailAndAppId(shortForwardEmailReceive);
    }

    @Override
    public List<ShortForwardEmailReceive> getSendNullUser() {
        return shortForwardEmailReceiveMapper.getSendNullUser();
    }

    /**
     * 检查邮箱在指定应用中是否存在收件记录
     *
     * @param email 邮箱
     * @param appId 应用ID
     * @return 是否存在
     */
    @Override
    public boolean existsByEmailAndAppId(String email, Long appId) {
        if (email == null || appId == null) {
            return false;
        }

        ShortForwardEmailReceive query = new ShortForwardEmailReceive();
        query.setEmail(email);
        query.setAppId(appId);
        List<ShortForwardEmailReceive> list = shortForwardEmailReceiveMapper.selectShortForwardEmailReceiveList(query);
        return !list.isEmpty();
    }

    /**
     * 批量检查邮箱在指定应用中是否存在收件记录
     *
     * @param emails 邮箱列表
     * @param appId 应用ID
     * @return 存在的邮箱列表
     */
    @Override
    public List<String> batchExistsByEmailAndAppId(List<String> emails, Long appId) {
        if (emails == null || emails.isEmpty() || appId == null) {
            return new java.util.ArrayList<>();
        }

        List<String> existingEmails = new java.util.ArrayList<>();
        for (String email : emails) {
            if (existsByEmailAndAppId(email, appId)) {
                existingEmails.add(email);
            }
        }
        return existingEmails;
    }
}
