package com.ruoyi.web.controller.bankcard;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.BankCardAlert;
import com.ruoyi.system.service.IBankCardAlertService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 银行卡告警Controller
 */
@RestController
@RequestMapping("/bankcard/alert")
public class BankCardAlertController extends BaseController
{
    @Autowired
    private IBankCardAlertService bankCardAlertService;

    /**
     * 查询银行卡告警列表
     */
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody BankCardAlert bankCardAlert)
    {
        startPage();
        List<BankCardAlert> list = bankCardAlertService.selectBankCardAlertList(bankCardAlert);
        return getDataTable(list);
    }

    /**
     * 导出银行卡告警列表
     */
    @Log(title = "银行卡告警", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BankCardAlert bankCardAlert)
    {
        List<BankCardAlert> list = bankCardAlertService.selectBankCardAlertList(bankCardAlert);
        ExcelUtil<BankCardAlert> util = new ExcelUtil<BankCardAlert>(BankCardAlert.class);
        util.exportExcel(response, list, "银行卡告警数据");
    }

    /**
     * 新增保存银行卡告警
     */
    @Log(title = "银行卡告警", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody BankCardAlert bankCardAlert)
    {
        return toAjax(bankCardAlertService.insertBankCardAlert(bankCardAlert));
    }

    /**
     * 修改保存银行卡告警
     */
    @Log(title = "银行卡告警", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody BankCardAlert bankCardAlert)
    {
        return toAjax(bankCardAlertService.updateBankCardAlert(bankCardAlert));
    }

    /**
     * 删除银行卡告警
     */
    @Log(title = "银行卡告警", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    public AjaxResult remove(@RequestBody String ids)
    {
        return toAjax(bankCardAlertService.deleteBankCardAlertByIds(Convert.toLongArray(ids)));
    }

    /**
     * 获取未处理的告警列表
     */
    @PostMapping("/unprocessed")
    public TableDataInfo unprocessedAlerts(@RequestParam(defaultValue = "100") Integer limit)
    {
        List<BankCardAlert> list = bankCardAlertService.getUnprocessedAlerts(limit);
        return getDataTable(list);
    }

    /**
     * 处理告警
     */
    @Log(title = "处理银行卡告警", businessType = BusinessType.UPDATE)
    @PostMapping("/process/{id}")
    public AjaxResult processAlert(@PathVariable Long id,
                                  @RequestParam(required = false) String note)
    {
        try
        {
            Long adminId = getUserId(); // 从当前登录用户获取管理员ID
            boolean success = bankCardAlertService.markAsProcessed(id, adminId, note);

            if (success)
            {
                return AjaxResult.success("告警处理成功");
            }
            else
            {
                return AjaxResult.error("告警处理失败");
            }
        }
        catch (Exception e)
        {
            logger.error("处理告警失败", e);
            return AjaxResult.error("处理失败: " + e.getMessage());
        }
    }

    /**
     * 批量处理告警
     */
    @Log(title = "批量处理银行卡告警", businessType = BusinessType.UPDATE)
    @PostMapping("/batchProcess")
    public AjaxResult batchProcessAlerts(@RequestParam String ids,
                                        @RequestParam(required = false) String note)
    {
        try
        {
            Long[] alertIds = Convert.toLongArray(ids);
            List<Long> alertIdList = java.util.Arrays.asList(alertIds);
            Long adminId = getUserId(); // 从当前登录用户获取管理员ID

            boolean success = bankCardAlertService.batchMarkAsProcessed(alertIdList, adminId, note);

            if (success)
            {
                return AjaxResult.success("批量处理告警成功");
            }
            else
            {
                return AjaxResult.error("批量处理告警失败");
            }
        }
        catch (Exception e)
        {
            logger.error("批量处理告警失败", e);
            return AjaxResult.error("处理失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定银行卡的告警历史
     */
    @GetMapping("/cardHistory/{cardNumber}")
    public TableDataInfo getCardAlerts(@PathVariable String cardNumber,
                                      @RequestParam(defaultValue = "50") Integer limit)
    {
        List<BankCardAlert> list = bankCardAlertService.getAlertsByCard(cardNumber, limit);
        return getDataTable(list);
    }

    /**
     * 获取指定用户触发的告警
     */
    @GetMapping("/userHistory/{userId}")
    public TableDataInfo getUserAlerts(@PathVariable Long userId,
                                      @RequestParam(defaultValue = "50") Integer limit)
    {
        List<BankCardAlert> list = bankCardAlertService.getAlertsByUser(userId, limit);
        return getDataTable(list);
    }

    /**
     * 获取告警统计信息
     */
    @GetMapping("/statistics")
    public AjaxResult getAlertStatistics()
    {
        try
        {
            // 按告警类型统计最近7天的告警
            LocalDateTime startDate = LocalDateTime.now().minusDays(7);
            List<Object[]> alertTypeStats = bankCardAlertService.countByAlertType(startDate);

            // 最近24小时告警数量
            int recentAlertCount = bankCardAlertService.getRecentAlertCount();

            // 未处理告警数量
            List<BankCardAlert> unprocessedAlerts = bankCardAlertService.getUnprocessedAlerts(1000);
            int unprocessedCount = unprocessedAlerts.size();

            AjaxResult result = AjaxResult.success();
            result.put("alertTypeStats", alertTypeStats);
            result.put("recentAlertCount", recentAlertCount);
            result.put("unprocessedCount", unprocessedCount);

            return result;
        }
        catch (Exception e)
        {
            logger.error("获取告警统计失败", e);
            return AjaxResult.error("获取统计失败");
        }
    }

    /**
     * 清理过期告警
     */
    @Log(title = "清理过期银行卡告警", businessType = BusinessType.CLEAN)
    @PostMapping("/cleanExpired")
    public AjaxResult cleanExpiredAlerts()
    {
        try
        {
            int deletedCount = bankCardAlertService.cleanExpiredAlerts();
            return AjaxResult.success("清理完成，删除 " + deletedCount + " 条过期告警");
        }
        catch (Exception e)
        {
            logger.error("清理过期告警失败", e);
            return AjaxResult.error("清理失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询告警详细信息
     */
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(bankCardAlertService.selectBankCardAlertById(id));
    }
}