package com.ruoyi.common.utils;

import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.domain.dto.CrossModuleStatusDto;
import com.ruoyi.service.ICrossModuleStatusService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 跨模块状态数据增强工具类
 * 用于在列表响应中添加跨模块关联状态信息
 *
 * <AUTHOR>
 * @date 2025-09-27
 */
@Component
public class CrossModuleEnhancementUtil {

    @Autowired
    private ICrossModuleStatusService crossModuleStatusService;

    /**
     * 增强表格数据，添加跨模块状态信息
     *
     * @param tableDataInfo 表格数据
     * @return 增强后的表格数据
     */
    public TableDataInfo enhanceTableData(TableDataInfo tableDataInfo) {
        if (tableDataInfo == null || CollectionUtils.isEmpty(tableDataInfo.getRows())) {
            return tableDataInfo;
        }

        List<Object> rows = Collections.singletonList(tableDataInfo.getRows());
        List<Object> enhancedRows = new ArrayList<>();

        // 提取所有邮箱和应用ID进行批量查询
        List<String> emails = new ArrayList<>();
        Long appId = null;

        for (Object row : rows) {
            try {
                String email = getFieldValue(row, "email", String.class);
                Long currentAppId = getFieldValue(row, "appId", Long.class);

                if (email != null && currentAppId != null) {
                    emails.add(email);
                    if (appId == null) {
                        appId = currentAppId;
                    }
                }
            } catch (Exception e) {
                // 如果获取字段失败，跳过该记录
            }
        }

        // 批量查询跨模块状态
        Map<String, CrossModuleStatusDto> statusMap = null;
        if (!emails.isEmpty() && appId != null) {
            statusMap = crossModuleStatusService.getBatchCrossModuleStatus(emails, appId);
        }

        // 增强每一行数据
        for (Object row : rows) {
            Object enhancedRow = enhanceRowData(row, statusMap);
            enhancedRows.add(enhancedRow);
        }

        tableDataInfo.setRows(enhancedRows);
        return tableDataInfo;
    }

    /**
     * 增强单行数据
     */
    private Object enhanceRowData(Object row, Map<String, CrossModuleStatusDto> statusMap) {
        if (row == null || statusMap == null) {
            return createEnhancedResponse(row, new CrossModuleStatusDto());
        }

        try {
            String email = getFieldValue(row, "email", String.class);
            CrossModuleStatusDto status = statusMap.getOrDefault(email, new CrossModuleStatusDto());
            return createEnhancedResponse(row, status);
        } catch (Exception e) {
            return createEnhancedResponse(row, new CrossModuleStatusDto());
        }
    }

    /**
     * 创建增强响应对象
     */
    private Object createEnhancedResponse(Object originalData, CrossModuleStatusDto crossModuleStatus) {
        return new EnhancedResponse(originalData, crossModuleStatus);
    }

    /**
     * 通过反射获取对象字段值
     */
    @SuppressWarnings("unchecked")
    private <T> T getFieldValue(Object obj, String fieldName, Class<T> targetType) {
        try {
            // 尝试通过getter方法获取
            String getterName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
            Method getter = obj.getClass().getMethod(getterName);
            Object value = getter.invoke(obj);
            return targetType.cast(value);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 增强响应包装类
     */
    public static class EnhancedResponse {
        private Object data;
        private CrossModuleStatusDto crossModuleStatus;

        public EnhancedResponse(Object data, CrossModuleStatusDto crossModuleStatus) {
            this.data = data;
            this.crossModuleStatus = crossModuleStatus;
        }

        public Object getData() {
            return data;
        }

        public void setData(Object data) {
            this.data = data;
        }

        public CrossModuleStatusDto getCrossModuleStatus() {
            return crossModuleStatus;
        }

        public void setCrossModuleStatus(CrossModuleStatusDto crossModuleStatus) {
            this.crossModuleStatus = crossModuleStatus;
        }
    }
}