package com.ruoyi.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 【请填写功能名称】对象 short_user_collect_and_history
 * 
 * <AUTHOR>
 * @date 2025-09-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ShortUserCollectAndHistory extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** user id */
    @Excel(name = "user id")
    private Long userId;

    /** movie id */
    @Excel(name = "movie id")
    private Long movieId;

    /** 视频地址 */
    @Excel(name = "视频地址")
    private String url;

    /** 标题 */
    @Excel(name = "标题")
    private String title;

    /** 当前集 */
    @Excel(name = "当前集")
    private Long currentEp;

    /** 总集 */
    @Excel(name = "总集")
    private Long allEp;

    /** kid */
    @Excel(name = "kid")
    private String kid;

    /** 收藏：collect、观看历史：history */
    @Excel(name = "收藏：collect、观看历史：history")
    private String type;
}
