-- =====================================================
-- 银行卡订单统计 & 异常预警系统数据库脚本
-- 创建时间: 2025-09-17
-- 说明: 新增3个表+修改订单表
-- =====================================================

-- 1. 修改现有订单表，添加银行卡号字段
ALTER TABLE short_order ADD COLUMN card_number VARCHAR(64) COMMENT '脱敏银行卡号，如 1234****5678';

-- 添加索引支持统计查询
ALTER TABLE short_order ADD INDEX idx_card_number (card_number);

-- 2. 新建银行卡统计表
CREATE TABLE bank_card_statistics (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    card_number VARCHAR(64) NOT NULL COMMENT '脱敏卡号，例如 1234****5678',

    -- 主绑定用户
    user_id BIGINT COMMENT '用户ID',
    history_user_ids JSON COMMENT '使用过该卡的所有用户ID数组',
    related_users INT DEFAULT 1 COMMENT '历史累计的不同 user_id 数',
    status VARCHAR(20) DEFAULT 'active' COMMENT 'active / suspicious / blocked',

    -- 订阅（subscription）累计
    sub_total_amount DECIMAL(14,2) DEFAULT 0 COMMENT '订阅累计额',
    sub_total_count INT DEFAULT 0 COMMENT '订阅累计次数',

    -- 充值（recharge）累计
    rec_total_amount DECIMAL(14,2) DEFAULT 0 COMMENT '充值累计额',
    rec_total_count INT DEFAULT 0 COMMENT '充值累计次数',

    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    last_update DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    UNIQUE KEY uk_card_number(card_number),
    INDEX idx_card_number(card_number),
    INDEX idx_user_id(user_id),
    INDEX idx_status(status),
    INDEX idx_last_update(last_update)
);

-- 3. 新建银行卡告警表
CREATE TABLE bank_card_alerts (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    card_number VARCHAR(64) COMMENT '脱敏银行卡号，例如 1234****5678',
    user_id BIGINT COMMENT '触发告警的用户ID',
    alert_type VARCHAR(50) COMMENT '告警类型：week_overuse=周累计超限，month_overuse=月累计超限，multi_user=多用户共用',
    alert_detail TEXT COMMENT '告警详情，描述具体的超限情况，如累计次数、累计金额等',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '告警生成时间',
    processed_flag TINYINT DEFAULT 0 COMMENT '是否已处理：0=未处理，1=已处理',
    processed_at DATETIME COMMENT '处理时间',
    processed_by BIGINT COMMENT '处理人ID（后台管理员）',
    process_note TEXT COMMENT '处理备注',

    INDEX idx_card_number(card_number),
    INDEX idx_user_id(user_id),
    INDEX idx_alert_type(alert_type),
    INDEX idx_processed_flag(processed_flag),
    INDEX idx_created_at(created_at)
);

-- 4. 新建银行卡规则配置表
CREATE TABLE bank_card_rules (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    rule_type VARCHAR(50) NOT NULL COMMENT '规则类型: week / month / multi_user',
    threshold_count INT DEFAULT NULL COMMENT '次数阈值，例如周>1次、月>5次',
    threshold_amount DECIMAL(14,2) DEFAULT NULL COMMENT '金额阈值，例如周>40美金、月>100美金',
    description VARCHAR(255) COMMENT '规则描述',
    is_active TINYINT DEFAULT 1 COMMENT '是否启用：1=启用，0=停用',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_rule_type(rule_type),
    INDEX idx_is_active(is_active)
);

-- 初始化规则配置
INSERT INTO bank_card_rules (rule_type, threshold_count, threshold_amount, description) VALUES
('week', 1, 40.00, '单周超过1次或40美金'),
('month', 5, 100.00, '单月超过5次或100美金'),
('multi_user', 0, NULL, '同一银行卡绑定多个用户');


-- 为查询昨日数据优化
ALTER TABLE short_order
    ADD INDEX idx_card_create_status (card_number, create_time, status);
-- 为日期范围查询优化
ALTER TABLE short_order
    ADD INDEX idx_create_card_status (create_time, card_number, status);



-- 添加定时任务配置
INSERT INTO `short_center`.`sys_job` (`job_id`, `job_name`, `job_group`, `invoke_target`, `cron_expression`, `misfire_policy`, `concurrent`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (100, '银行卡统计任务', 'DEFAULT', 'bankCardStatisticsTask.executeDailyStatistics', '0 */2 * * * ?', '3', '1', '1', 'admin', '2025-09-18 20:17:18', 'admin', '2025-09-22 21:04:22', '');


SET SESSION group_concat_max_len = 50000;

-- 初始化bank_card_statistics表的历史基础数据
INSERT INTO bank_card_statistics (
    card_number,
    user_id,
    history_user_ids,
    related_users,
    sub_total_amount,
    sub_total_count,
    rec_total_amount,
    rec_total_count,
    status,
    created_at,
    last_update
)
SELECT
    card_number,
    MIN(user_id) as user_id,  -- 最早使用该卡的用户作为主用户
    CONCAT('[', GROUP_CONCAT(DISTINCT user_id), ']') as history_user_ids,  -- 简单JSON格式
    COUNT(DISTINCT user_id) as related_users,

    -- 订阅统计（根据实际pay_type值调整）
    SUM(CASE WHEN pay_type LIKE '%订阅%' THEN amount ELSE 0 END) as sub_total_amount,
    SUM(CASE WHEN pay_type LIKE '%订阅%' THEN 1 ELSE 0 END) as sub_total_count,

    -- 充值统计
    SUM(CASE WHEN pay_type LIKE '%充值%' THEN amount ELSE 0 END) as rec_total_amount,
    SUM(CASE WHEN pay_type LIKE '%充值%' THEN 1 ELSE 0 END) as rec_total_count,

    -- 状态设置
    'active' as status,

    MIN(create_time) as created_at,  -- 最早订单时间
    NOW() as last_update

FROM short_order
WHERE card_number IS NOT NULL
  AND card_number != ''
  AND status = 'SUCCEEDED'
  AND create_time < CURDATE()
GROUP BY card_number;


-- 检查初始化结果
SELECT
    COUNT(*) as total_cards,
    COUNT(CASE WHEN related_users > 1 THEN 1 END) as suspicious_cards,
    SUM(sub_total_count) as total_subscriptions,
    SUM(rec_total_count) as total_recharges,
    SUM(sub_total_amount + rec_total_amount) as total_amount
FROM bank_card_statistics;

-- 查看多用户使用的银行卡
SELECT card_number, related_users, history_user_ids
FROM bank_card_statistics
WHERE related_users > 1
ORDER BY related_users DESC
    LIMIT 10;



-- 清洗short_order表银行卡字段
UPDATE short_order
SET card_number = CASE
  WHEN paymentMethod = 'card' THEN
      CASE
          WHEN JSON_EXTRACT(raw_data, '$.data.object.latest_payment_attempt.payment_method.card.bin') IS NOT NULL
              THEN CONCAT(JSON_UNQUOTE(JSON_EXTRACT(raw_data, '$.data.object.latest_payment_attempt.payment_method.card.bin')), '****', JSON_UNQUOTE(JSON_EXTRACT(raw_data,
                                                                                                                                                                  '$.data.object.latest_payment_attempt.payment_method.card.last4')))
          ELSE CONCAT('********', '****', JSON_UNQUOTE(JSON_EXTRACT(raw_data, '$.data.object.latest_payment_attempt.payment_method.card.last4')))
          END
  WHEN paymentMethod = 'googlepay' THEN
      CASE
          WHEN JSON_EXTRACT(raw_data, '$.data.object.latest_payment_attempt.payment_method.googlepay.tokenized_card.bin') IS NOT NULL
              THEN CONCAT(JSON_UNQUOTE(JSON_EXTRACT(raw_data, '$.data.object.latest_payment_attempt.payment_method.googlepay.tokenized_card.bin')), '****', JSON_UNQUOTE(JSON_EXTRACT(raw_data,
                                                                                                                                                                                      '$.data.object.latest_payment_attempt.payment_method.googlepay.tokenized_card.last4')))
          ELSE CONCAT('********', '****', JSON_UNQUOTE(JSON_EXTRACT(raw_data, '$.data.object.latest_payment_attempt.payment_method.googlepay.tokenized_card.last4')))
          END
  WHEN paymentMethod = 'applepay' THEN
      CASE
          WHEN JSON_EXTRACT(raw_data, '$.data.object.latest_payment_attempt.payment_method.applepay.tokenized_card.bin') IS NOT NULL
              THEN CONCAT(JSON_UNQUOTE(JSON_EXTRACT(raw_data, '$.data.object.latest_payment_attempt.payment_method.applepay.tokenized_card.bin')), '****', JSON_UNQUOTE(JSON_EXTRACT(raw_data,
                                                                                                                                                                                     '$.data.object.latest_payment_attempt.payment_method.applepay.tokenized_card.last4')))
          ELSE CONCAT('********', '****', JSON_UNQUOTE(JSON_EXTRACT(raw_data, '$.data.object.latest_payment_attempt.payment_method.applepay.tokenized_card.last4')))
          END
    END
WHERE status = 'SUCCEEDED'
  AND raw_data IS NOT NULL
  AND card_number IS NULL
  AND paymentMethod IN ('card', 'googlepay', 'applepay')

-- 在告警表添加唯一索引，防止重复告警
ALTER TABLE bank_card_alerts
    ADD COLUMN created_date DATE AS (DATE(created_at)) STORED,
ADD UNIQUE INDEX uk_card_alert_daily (
    card_number,
    alert_type,
    created_date
);
