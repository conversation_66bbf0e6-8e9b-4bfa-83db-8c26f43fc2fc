package com.ruoyi.app.api.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.app.api.service.IpInfoService;
import com.ruoyi.app.api.service.UserApiService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.domain.ShortUserCollectAndHistory;
import com.ruoyi.mapper.ShortUserCollectAndHistoryMapper;
import com.ruoyi.vo.SubscribeRecordVO;
import com.ruoyi.common.core.domain.model.TokenPair;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.domain.ShortOrder;
import com.ruoyi.domain.ShortUser;
import com.ruoyi.domain.ShortUserActivityLog;
import com.ruoyi.dto.CheckShortUserDTO;
import com.ruoyi.framework.web.service.token.JwtTokenService;
import com.ruoyi.mapper.ShortOrderMapper;
import com.ruoyi.mapper.ShortUserMapper;
import com.ruoyi.service.IShortUserActivityLogService;
import com.ruoyi.service.IShortUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.net.*;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.X509EncodedKeySpec;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户API服务实现
 */
@Slf4j
@Service
public class UserApiServiceImpl implements UserApiService {

    @Autowired
    private IShortUserService shortUserService;

    @Autowired
    private JwtTokenService jwtTokenService;

    @Autowired
    private ShortUserMapper shortUserMapper;

    @Resource
    private IpInfoService ipInfoService;

    @Resource
    private IShortUserActivityLogService shortUserActivityLogService;
    @Autowired
    private ShortOrderMapper shortOrderMapper;
    @Autowired
    private ShortUserCollectAndHistoryMapper shortUserCollectAndHistoryMapper;

    /**
     * 通用方法：处理用户ID
     */
    private String processUserId(String userId) {
        if (userId != null && userId.length() >= 8 && userId.matches("\\d+")) {
            // 如果长度>=8且为纯数字，认为是hash_id格式（4位随机数+实际ID），截取前4位后的部分
            return userId.substring(4);
        }
        // 否则直接返回原值（可能已经是实际ID）
        return userId;
    }

    /**
     * 通用方法：查找用户
     */
    private ShortUser findUser(String userId, String appId, String token) {
        ShortUser user = null;

        // 优先使用token查询用户，没有token时使用userId
        if (token != null && !token.isEmpty()) {
            // 通过token查询用户
            ShortUser queryUser = new ShortUser();
            queryUser.setToken(token);
            if (appId != null && !appId.isEmpty()) {
                try {
                    queryUser.setAppId(Long.valueOf(appId));
                } catch (NumberFormatException e) {
                    System.err.println("[查找用户] AppID 格式错误: " + appId);
                    return null; // 或者抛出异常
                }
            } else {
                System.err.println("[查找用户] AppID 为空，无法查询");
                return null;
            }
            List<ShortUser> users = shortUserService.selectShortUserList(queryUser);
            if (users != null && !users.isEmpty()) {
                user = users.get(0);
            }
        } else if (userId != null && !userId.isEmpty()) {
            // 通过userId查询用户
            String processedUserId = processUserId(userId);
            try {
                user = shortUserService.selectShortUserById(Long.valueOf(processedUserId));
            } catch (NumberFormatException e) {
                System.err.println("[查找用户] UserID 格式错误: " + processedUserId);
            }
        }
        return user;
    }

    /**
     * 通用方法：验证用户存在
     */
    private ShortUser validateUser(String userId, String appId, String token) {
        ShortUser user = findUser(userId, appId, token);
        if (user == null) {
            throw new ServiceException("用户不存在", 400);
        }
        return user;
    }

    /**
     * 通用方法：将用户转换为Map
     */
    private Map<String, Object> convertUserToMap(ShortUser user) {
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("id", user.getHashId());  // 使用hash_id
        userInfo.put("username", user.getUsername());
        userInfo.put("unsub", user.getUnsub());

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (null != user.getUnsubTime())
            userInfo.put("unsubTime", sdf.format(user.getUnsubTime()));
        else
            userInfo.put("unsubTime", user.getUnsubTime());

        // 设置默认头像
        String avatar = "https://imagedelivery.net/HFyDYEfL0zSd_D8py7HTJg/35f4bb66-6225-493b-1a0b-276cde1cd800/public";
        userInfo.put("avatar", avatar);
        userInfo.put("coin", user.getCoin() != null ? user.getCoin().intValue() : 0);
        userInfo.put("state", user.getState() != null ? user.getState() : "0"); // 保持为字符串格式


        // 处理过期时间格式，只保留年月日
        if (user.getExpireTime() != null) {

            // 获取当前时间
            Date currentTime = new Date();

            // 判断是否过期
            if (user.getExpireTime().before(currentTime)) {
                user.setIsSubscriber("0");
            }

            userInfo.put("expire_time", user.getExpireTime());
        } else {
            userInfo.put("expire_time", null);
        }

        userInfo.put("is_subscriber", user.getIsSubscriber() != null && user.getIsSubscriber().equals("1"));

        // 获取会员类型
//        String subscriptionType = null;
//        if (user.getVipId() != null) {
//            subscriptionType = shortUserService.getVipSubscriptionType(user.getVipId());
//        }
        String subscriptionType = shortOrderMapper.selectBySubscriptionTypeBy(user.getId());
        if (StringUtils.isEmpty(subscriptionType) && StringUtils.isNotEmpty(user.getEmail())) {
            List<Long> ids = shortUserMapper.getAllIdsById(user.getId());
            for (Long id : ids) {
                String subscriptionTypeResult = shortOrderMapper.selectBySubscriptionTypeBy(id);
                if (StringUtils.isNotEmpty(subscriptionTypeResult)) {
                    subscriptionType = subscriptionTypeResult;
                    break;
                }
            }
        }

        userInfo.put("now_sub_vip", subscriptionType);
        userInfo.put("email", user.getEmail());
        userInfo.put("remark", user.getRemark());
        userInfo.put("source", user.getSource());
        return userInfo;
    }

    @Override
    public Map<String, Object> getUserInfo(String userId, String appId, String token) {
        // 验证用户是否存在
        ShortUser user = validateUser(userId, appId, token);
        if (/*null != user.getExpireTime() && !user.getExpireTime().after(new Date()) &&*/ StringUtils.isNotEmpty(user.getEmail()) && user.getSubscriptionOldUserId() == null) {
            ShortUser afterUser = shortUserService.getAfterExpireTime(user.getEmail());
            if (afterUser != null) {
//                shortUserService.setIsSubscriber(user.getId());
//                shortUserService.updateExpireTimeById(user.getId(),afterExpireTime);
                user.setExpireTime(afterUser.getExpireTime());
                user.setIsSubscriber("1");
                user.setUnsub(afterUser.getUnsub());
                if(afterUser.getUnsub().equals(1)){
                    user.setUnsubSource("同邮箱同步订阅状态");
                    user.setUnsubTime(DateUtils.getNowDate());
                }
                shortUserService.updateShortUser(user);
            }/*else{
                ShortUser lastUser = shortUserService.getLastUser(user.getEmail());
                if(null != lastUser){
                    user.setExpireTime(lastUser.getExpireTime());
                    user.setIsSubscriber(lastUser.getIsSubscriber());
                    user.setUnsub(lastUser.getUnsub());
                    shortUserService.updateShortUser(user);
                }
            }*/
        }
        // 构建用户信息Map
        return convertUserToMap(user);
    }

    /**
     * 用户退出登录
     *
     * @param userId 用户ID
     * @param appId  应用ID
     * @return 结果Map
     */
    public Map<String, Object> logout(String userId, String appId) {
        if (userId == null || userId.isEmpty()) {
            throw new ServiceException("未授权", 400);
        }
        if (appId == null || appId.isEmpty()) {
            throw new ServiceException("缺少应用ID", 400);
        }

        // 处理userId
        String processedUserId = processUserId(userId);
        Long userLongId = null;
        try {
            userLongId = Long.valueOf(processedUserId);
        } catch (NumberFormatException e) {
            throw new ServiceException("无效的用户ID格式", 400);
        }

        // 创建查询对象
        ShortUser queryUser = new ShortUser();
        queryUser.setId(userLongId);
        queryUser.setAppId(Long.valueOf(appId));

        // 查询用户信息列表
        List<ShortUser> users = shortUserService.selectShortUserList(queryUser);
        ShortUser user = null;
        if (users != null && !users.isEmpty()) {
            user = users.get(0); // 获取第一个匹配的用户
        }

        if (user == null) {
            throw new ServiceException("用户不存在", 400);
        }

        // 清除用户token
        String token = user.getToken();
        if (token != null && !token.isEmpty()) {
            // 清除token
            jwtTokenService.clearToken(token);

            // 从用户信息中移除token
            user.setToken(null);
            shortUserService.updateShortUser(user);
        }

        // 返回成功结果
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("msg", "退出成功");
        return result;
    }

    @Override
    public Map<String, Object> deleteUser(String userId, String appId) {
        if (userId == null || userId.isEmpty()) {
            throw new ServiceException("未授权", 400);
        }

        // 处理userId
        String processedUserId = processUserId(userId);

        // 查询用户信息
        ShortUser user = shortUserService.selectShortUserById(Long.valueOf(processedUserId));
        if (user == null) {
            throw new ServiceException("用户不存在", 400);
        }

        // 清除用户token
        String token = user.getToken();
        if (token != null && !token.isEmpty()) {
            // 清除Redis中的token
            jwtTokenService.clearToken(token);

            // 从数据库用户信息中移除token
            user.setToken(null);
        }

        // 逻辑删除用户，将状态设置为0(禁用)
        user.setState("0");
        int rows = shortUserService.updateShortUser(user);

        // 返回结果
        Map<String, Object> result = new HashMap<>();
        if (rows > 0) {
            result.put("code", 200);
            result.put("msg", "删除成功");
        } else {
            result.put("code", 400);
            result.put("msg", "删除失败");
        }

        return result;
    }

    @Override
    public Map<String, Object> registerOrLogin(Map<String, Object> registrationData) {
        log.info("register=================== - {}", registrationData);
        String uid = (String) registrationData.get("uid");
        String idToken = (String) registrationData.get("id_token");
        String source = (String) registrationData.get("source");
        String phoneVersion = (String) registrationData.get("phone_version");
        String systemVersion = (String) registrationData.get("system_version");
        String language = (String) registrationData.get("language");
        String appVersion = (String) registrationData.get("app_version");
        String accountType = (String) registrationData.get("account_type");
        String uniqueId = (String) registrationData.get("unique_id");
        String ip = (String) registrationData.get("ip");
        String country = null;
        String appId = (String) registrationData.get("appId");
        Long pixelId = (Long) registrationData.get("pixelId");
        String packageName = (String) registrationData.get("packageName");


        // 根据IP地址获取国家信息
        if (ip != null && !ip.isEmpty()) {
            try {
                Map<String, String> countryInfo = ipInfoService.getCountryByIp(ip);
                country = countryInfo.get("countryName");
                if (country == null) {
                    country = "Unknown";
                }
            } catch (Exception e) {
                country = "Unknown";
            }
        } else {
            country = "Unknown";
        }

        String subId = null;
        String email = null;
        String username = null;

        // 处理不同账户类型的验证
        if ("apple".equals(accountType)) {
            try {
                // 获取Apple公钥并验证token
                String appleKeysJson = getApplePublicKeys();
                Map<String, Object> appleKeys = parseJsonToMap(appleKeysJson);

                // 解析JWT头部，获取kid
                Map<String, Object> header = getJwtHeader(idToken);
                String kid = (String) header.get("kid");

                // 从Apple公钥列表中匹配正确的kid
                Map<String, Object> key = findKeyByKid(appleKeys, kid);
                if (key == null) {
                    System.err.println("[注册或登录] 无法根据 kid 找到匹配的 Apple 公钥");
                    Map<String, Object> errorResponse = new HashMap<>();
                    errorResponse.put("code", 400);
                    errorResponse.put("msg", "无效的请求");
                    return errorResponse;
                }

                // 验证Apple的token
                String expectedAudience = "com.shortplay.flareshort"; // 默认值
                if (packageName != null && !packageName.isEmpty()) {
                    expectedAudience = packageName;
                }
                Map<String, Object> tokenData = verifyAppleToken(idToken, key, expectedAudience);
                subId = (String) tokenData.get("sub");
                email = (String) tokenData.get("email");

                // Apple登录不返回用户名，使用email前缀或subId作为用户名
                if (email != null && !email.isEmpty()) {
                    username = email.split("@")[0]; // 使用邮箱前缀作为用户名
                } else {
                    // 如果没有邮箱，使用subId的一部分作为用户名
                    username = subId != null && subId.length() >= 8 ?
                            "apple_" + subId.substring(subId.length() - 8) : "apple_user";
                }
            } catch (Exception e) {
                System.err.println("[注册或登录] Apple Token 验证失败: " + e.getMessage());
                e.printStackTrace(); // 打印详细堆栈
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("code", 400);
                errorResponse.put("msg", "无效的请求");
                return errorResponse;
            }
        } else if ("google".equals(accountType)) {
            try {
                // 使用ID Token向Google的tokeninfo发起请求
                String url = "https://oauth2.googleapis.com/tokeninfo?id_token=" + idToken;
                String response = httpGet(url);
                Map<String, Object> tokenInfo = parseJsonToMap(response);
                subId = (String) tokenInfo.get("sub");
                email = (String) tokenInfo.get("email");
                username = (String) tokenInfo.get("name");

                if (subId == null) {
                    System.err.println("[注册或登录] Google Token 验证后 subId 为空");
                    Map<String, Object> errorResponse = new HashMap<>();
                    errorResponse.put("code", 400);
                    errorResponse.put("msg", "无效的请求");
                    return errorResponse;
                }
            } catch (Exception e) {
                System.err.println("[注册或登录] Google Token 验证失败: " + e.getMessage());
                e.printStackTrace(); // 打印详细堆栈
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("code", 400);
                errorResponse.put("msg", "无效的请求");
                return errorResponse;
            }
        } else {
            System.err.println("[注册或登录] 不支持的账户类型: " + accountType);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", 400);
            errorResponse.put("msg", "无效的请求");
            return errorResponse;
        }

        // 查找用户
        ShortUser user = null;
        // 先按subId查找
        if (subId != null) {
            ShortUser queryUser = new ShortUser();
            queryUser.setSubId(subId);
            queryUser.setAppId(Long.valueOf(appId)); // 确保同时匹配 App ID
            List<ShortUser> userList = shortUserService.selectShortUserList(queryUser);
            if (userList != null && !userList.isEmpty()) {
                user = userList.get(0);  // 直接获取第一条结果
            }
        }

        // 如果没找到，尝试按uid查找
        if (user == null && uid != null && !uid.isEmpty()) {
            String uid_str = uid.toString();
            if (uid_str.length() > 4 && uid_str.matches("\\d+")) {
                uid = uid_str.substring(4);
            } else {
                uid = uid_str;
            }

            try {
                user = shortUserService.selectShortUserById(Long.valueOf(uid));
                if (user != null) {
                    // 检查找到的用户AppID是否匹配
                    if (!user.getAppId().equals(Long.valueOf(appId))) {
                        System.err.println("[注册或登录] 警告：通过 UID 找到的用户 AppID 不匹配! 请求AppID: " + appId + ", 用户AppID: " + user.getAppId());
                        user = null; // AppID不匹配，视为未找到
                    }
                }
            } catch (NumberFormatException | NullPointerException e) {
                System.err.println("[注册或登录] 使用 uid 查询用户时出现异常: " + e.getMessage());
                // 记录异常但继续处理，将user保持为null
            }
        }

        Map<String, Object> result = new HashMap<>();

        // 如果用户存在则更新，否则新建
        if (user != null) {
            if (user.getState() != null && "0".equals(user.getState())) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("code", 1);
                errorResponse.put("msg", "用户已注销，请联系客服");
                return errorResponse;
            }

            // 更新用户信息
            user.setUsername(username);
            user.setEmail(email);
            user.setUniqueId(uniqueId);
            user.setPhoneVersion(phoneVersion);
            user.setSystemVersion(systemVersion);
            user.setLanguage(language);
            user.setAppVersion(appVersion);
            user.setSubId(subId); // 确保更新 subId
            user.setAccountType(accountType); // 确保更新账户类型
            user.setIp(ip);
            user.setCountry(country);
            user.setPixelId(pixelId);
            // 生成token
            TokenPair tokenPair = jwtTokenService.createTokenPair(user.getId().toString());
            user.setToken(tokenPair.getAccessToken());
            shortUserService.updateShortUser(user);

            result.put("code", 200);
            result.put("msg", "登陆成功");

            Map<String, Object> data = new HashMap<>();
            data.put("username", user.getUsername());
            data.put("token", tokenPair.getAccessToken());
            // 确保返回正确的头像
            String avatar = user.getAvatar();
            if (avatar == null || avatar.isEmpty() || avatar.equals("/media/none.png")) {
                Object googleAvatar = registrationData.get("picture");
                if (googleAvatar instanceof String && !((String) googleAvatar).isEmpty()) {
                    avatar = (String) googleAvatar;
                    user.setAvatar(avatar);
                } else {
                    avatar = "https://imagedelivery.net/HFyDYEfL0zSd_D8py7HTJg/35f4bb66-6225-493b-1a0b-276cde1cd800/public"; // 最终默认
                }
            }
            data.put("avatar", avatar);
            result.put("data", data);
        } else {
            // 创建新用户
            user = new ShortUser();
            try {
                user.setAppId(Long.valueOf(appId));
            } catch (NumberFormatException e) {
                System.err.println("[注册或登录] [注册] AppID 格式错误: " + appId);
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("code", 400);
                errorResponse.put("msg", "无效的应用ID");
                return errorResponse;
            }
            user.setEmail(email);
            user.setUsername(username);
            user.setSource("自主注册"); // 或者使用传入的 source?
            user.setPhoneVersion(phoneVersion);
            user.setSystemVersion(systemVersion);
            user.setLanguage(language);
            user.setAppVersion(appVersion);
            user.setAccountType(accountType);
            user.setUniqueId(uniqueId);
            user.setIp(ip);
            user.setCountry(country);
            user.setSubId(subId);
            user.setState("1"); // 新用户状态为 1
            user.setPixelId(pixelId);
            // 头像处理
            String avatar = null;
            Object googleAvatar = registrationData.get("picture");
            if (googleAvatar instanceof String && !((String) googleAvatar).isEmpty()) {
                avatar = (String) googleAvatar;
            } else {
                avatar = "/media/none.png"; // 数据库默认图
            }
            user.setAvatar(avatar);
            shortUserService.insertShortUser(user);

            // 生成token
            TokenPair tokenPair = jwtTokenService.createTokenPair(user.getId().toString());
            user.setToken(tokenPair.getAccessToken());
            shortUserService.updateShortUser(user); // 更新新用户的token

            result.put("code", 200);
            result.put("msg", "登陆成功");

            Map<String, Object> data = new HashMap<>();
            data.put("username", user.getUsername());
            data.put("token", tokenPair.getAccessToken());
            // 注册成功也返回头像
            data.put("avatar", user.getAvatar()); // 返回数据库中设置的头像 (/media/none.png 或 google 头像)
            result.put("data", data);
        }
        return result;
    }

    /**
     * 获取Apple公钥
     */
    private String getApplePublicKeys() throws Exception {
        String url = "https://appleid.apple.com/auth/keys";
        return httpGet(url);
    }

    /**
     * 发送HTTP GET请求
     */
    private String httpGet(String urlString) throws Exception {
        URL url = new URL(urlString);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");
        connection.setConnectTimeout(15000);
        connection.setReadTimeout(15000);

        int responseCode = connection.getResponseCode();
        if (responseCode != 200) {
            throw new Exception("HTTP错误码: " + responseCode);
        }

        try (java.io.BufferedReader reader = new java.io.BufferedReader(
                new java.io.InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
            return response.toString();
        }
    }

    /**
     * 发送HTTP POST请求
     */
    private String httpPost(String urlString) throws Exception {
        URL url = new URL(urlString);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("POST");
        connection.setConnectTimeout(5000);
        connection.setReadTimeout(5000);

        int responseCode = connection.getResponseCode();
        if (responseCode != 200) {
            throw new Exception("HTTP错误码: " + responseCode);
        }

        try (java.io.BufferedReader reader = new java.io.BufferedReader(
                new java.io.InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
            return response.toString();
        }
    }

    /**
     * 解析JSON字符串为Map
     */
    private Map<String, Object> parseJsonToMap(String jsonStr) throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        return mapper.readValue(jsonStr, new TypeReference<Map<String, Object>>() {
        });
    }

    /**
     * 获取JWT头部信息
     */
    private Map<String, Object> getJwtHeader(String token) throws Exception {
        // JWT格式: header.payload.signature
        String[] parts = token.split("\\.");
        if (parts.length != 3) {
            throw new Exception("无效的JWT格式");
        }

        // 解码Base64头部
        String decodedHeader = new String(java.util.Base64.getUrlDecoder().decode(parts[0]));
        return parseJsonToMap(decodedHeader);
    }

    /**
     * 在Apple公钥列表中查找匹配的kid
     */
    private Map<String, Object> findKeyByKid(Map<String, Object> appleKeys, String kid) {
        if (appleKeys == null || !appleKeys.containsKey("keys")) {
            return null;
        }

        List<Map<String, Object>> keys = (List<Map<String, Object>>) appleKeys.get("keys");
        for (Map<String, Object> key : keys) {
            if (kid.equals(key.get("kid"))) {
                return key;
            }
        }

        return null;
    }

    /**
     * 验证Apple ID Token
     */
    private Map<String, Object> verifyAppleToken(String token, Map<String, Object> key, String expectedAudience) throws Exception {
        // 解析PEM格式公钥
        String publicKeyPem = convertJwkToPem(key);

        // 使用公钥验证Token并获取Payload
        return verifyAppleTokenAndDecodePayload(token, publicKeyPem, expectedAudience);
    }

    /**
     * 将JWK转换为PEM格式公钥
     */
    private String convertJwkToPem(Map<String, Object> jwk) throws Exception {
        // 获取RSA参数
        String n = (String) jwk.get("n"); // modulus
        String e = (String) jwk.get("e"); // exponent

        // 创建RSA公钥规范
        java.security.spec.RSAPublicKeySpec spec = new java.security.spec.RSAPublicKeySpec(
                new java.math.BigInteger(1, java.util.Base64.getUrlDecoder().decode(n)),
                new java.math.BigInteger(1, java.util.Base64.getUrlDecoder().decode(e))
        );

        // 生成RSA公钥
        java.security.KeyFactory factory = java.security.KeyFactory.getInstance("RSA");
        java.security.PublicKey publicKey = factory.generatePublic(spec);

        // 转换为PEM格式
        byte[] encoded = publicKey.getEncoded();
        String base64 = java.util.Base64.getEncoder().encodeToString(encoded);

        // 构建PEM格式
        StringBuilder pem = new StringBuilder();
        pem.append("-----BEGIN PUBLIC KEY-----\n");
        // 每64个字符添加一个换行符
        for (int i = 0; i < base64.length(); i += 64) {
            pem.append(base64.substring(i, Math.min(i + 64, base64.length()))).append("\n");
        }
        pem.append("-----END PUBLIC KEY-----");

        return pem.toString();
    }

    /**
     * 验证Apple ID Token 并解码 Payload
     *
     * @param token        Apple ID Token
     * @param publicKeyPem PEM 格式的公钥
     * @return 解码后的 Payload Map
     * @throws Exception 验证失败或处理错误时抛出异常
     */
    private Map<String, Object> verifyAppleTokenAndDecodePayload(String token, String publicKeyPem, String expectedAudience) throws Exception {
        // 解析PEM格式公钥
        String publicKeyContent = publicKeyPem
                .replace("-----BEGIN PUBLIC KEY-----", "")
                .replace("-----END PUBLIC KEY-----", "")
                .replaceAll("\\s", "");

        byte[] publicKeyBytes = Base64.getDecoder().decode(publicKeyContent);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(publicKeyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PublicKey publicKey = keyFactory.generatePublic(keySpec);

        // 分割JWT
        String[] parts = token.split("\\.");
        if (parts.length != 3) {
            throw new Exception("无效的JWT格式");
        }
        String header = parts[0];
        String payload = parts[1];
        String signature = parts[2];
        String signedData = header + "." + payload; // 待验证的数据

        // 验证签名
        Signature sig = Signature.getInstance("SHA256withRSA");
        sig.initVerify(publicKey);
        sig.update(signedData.getBytes(StandardCharsets.UTF_8));

        if (!sig.verify(Base64.getUrlDecoder().decode(signature))) {
            throw new Exception("JWT 签名无效");
        }

        // 解码Base64负载
        String decodedPayload = new String(Base64.getUrlDecoder().decode(payload), StandardCharsets.UTF_8);
        Map<String, Object> payloadMap = parseJsonToMap(decodedPayload);

        // 验证颁发者 (iss)
        String iss = (String) payloadMap.get("iss");
        if (!"https://appleid.apple.com".equals(iss)) {
            throw new Exception("无效的 issuer: " + iss);
        }

        // 验证受众 (aud)
        String aud = (String) payloadMap.get("aud");
        if (!expectedAudience.equals(aud)) {
            throw new Exception("无效的 audience: " + aud + ", 期望值: " + expectedAudience);
        }

        // 验证过期时间 (exp)
        Long exp = ((Number) payloadMap.get("exp")).longValue();
        Date expirationDate = new Date(exp * 1000); // exp 是秒数，需要转换为毫秒
        if (expirationDate.before(new Date())) {
            throw new Exception("JWT 已过期: " + expirationDate);
        }
        // 返回负载数据
        return payloadMap;
    }

    @Override
    public Map<String, Object> signIn(String userId, String appId) {
        Map<String, Object> result = new HashMap<>();

        if (userId == null || userId.isEmpty()) {
            result.put("code", 400);
            result.put("msg", "未授权");
            return result;
        }

        ShortUser user = shortUserService.selectShortUserById(Long.valueOf(userId));
        if (user == null) {
            result.put("code", 400);
            result.put("msg", "用户不存在");
            return result;
        }

        Date currentDate = new Date();
        Date lastSignInDate = user.getLastSignIn();
        Map<String, Object> data = new HashMap<>();

        // 检查是否今日已签到
        if (lastSignInDate != null && isSameDay(currentDate, lastSignInDate)) {
            data.put("sign_in_days", user.getSignInDays());
            data.put("coin", user.getCoin());

            result.put("code", 200);
            result.put("msg", "success");
            result.put("data", data);
            return result;
        }

        // 计算是否连续签到
        boolean isContinuous = false;
        if (lastSignInDate != null) {
            Calendar cal1 = Calendar.getInstance();
            cal1.setTime(lastSignInDate);
            Calendar cal2 = Calendar.getInstance();
            cal2.setTime(currentDate);
            isContinuous = cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
                    cal1.get(Calendar.DAY_OF_YEAR) + 1 == cal2.get(Calendar.DAY_OF_YEAR);
        }

        // 更新签到信息
        Long signInDays = user.getSignInDays() != null ? user.getSignInDays() : 0L;
        long addCoins = 0;

        if (isContinuous) {
            signInDays++;
            if (signInDays > 7) {
                signInDays = 1L;
            }

            if (signInDays == 1 || signInDays == 2 || signInDays == 4 || signInDays == 5) {
                addCoins = 3;
            } else if (signInDays == 3 || signInDays == 6) {
                addCoins = 5;
            } else if (signInDays == 7) {
                addCoins = 7;
            }
        } else {
            signInDays = 1L;
            addCoins = 3;
        }

        user.setSignInDays(signInDays);
        user.setCoin(user.getCoin() != null ? user.getCoin() + addCoins : addCoins);
        user.setLastSignIn(currentDate);
        shortUserService.updateShortUser(user);

        data.put("sign_in_days", user.getSignInDays());
        data.put("coin", user.getCoin());

        result.put("code", 200);
        result.put("msg", "success");
        result.put("data", data);
        return result;
    }


    @Override
    public Map<String, Object> getSignInInfo(String appId, String token) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 只通过token查询用户
            ShortUser user = null;
            if (token != null && !token.isEmpty()) {
                ShortUser queryUser = new ShortUser();
                queryUser.setToken(token);
                queryUser.setAppId(Long.valueOf(appId));
                List<ShortUser> users = shortUserService.selectShortUserList(queryUser);

                if (users != null && !users.isEmpty()) {
                    user = users.get(0);
                }
            }

            // 如果找不到用户，返回未授权错误
            if (user == null) {
                result.put("code", 400);
                result.put("msg", "未授权");
                return result;
            }

            Map<String, Object> data = new HashMap<>();

            // 获取当前日期
            Date currentDate = new Date();

            // 获取最后签到日期
            Date lastSignDate = user.getLastSignIn();

            boolean hasSignedToday = false;
            if (lastSignDate != null) {
                hasSignedToday = isSameDay(currentDate, lastSignDate);
            }

            data.put("state", hasSignedToday);
            data.put("sign_in_days", user.getSignInDays() != null ? user.getSignInDays() : 0);

            result.put("code", 200);
            result.put("msg", "success");
            result.put("data", data);
            return result;

        } catch (Exception e) {
            // 处理异常
            Map<String, Object> data = new HashMap<>();
            data.put("state", false);
            data.put("sign_in_days", 0);

            result.put("code", 200);
            result.put("msg", "success");
            result.put("data", data);
            return result;
        }
    }

    @Override
    public void refreshCountry(Integer flag) {
        List<ShortUser> list = shortUserMapper.selectShortUserList(new ShortUser());
        if (1 == flag) {
            //截取前100条
            list = list.subList(0, 100);
        }
        for (ShortUser shortUserDo : list) {
            String ip = shortUserDo.getIp();
            if (StringUtils.isNotEmpty(ip)) {
                Map<String, String> result = ipInfoService.getCountryByIp(ip);
                String countryName = result.get("countryName");
                shortUserDo.setCountry(countryName);
                log.info("更新国家信息：{}", countryName);
                shortUserMapper.updateUserCountry(shortUserDo);
            }
        }
    }

    @Override
    public Map<String, Object> registerByEmail(Map<String, Object> registrationData) {
        Map<String, Object> result = new HashMap<>();
        String appId = (String) registrationData.get("appId");
        String email = (String) registrationData.get("email");
        String packageName = (String) registrationData.get("packageName");
        String userId = (String) registrationData.get("userId");
        //根据邮箱和appId查询用户
        ShortUser shortUser = shortUserMapper.selectShortUserById(Long.valueOf(userId));
        if (null != shortUser) {
            if (shortUser.getState() != null && "0".equals(shortUser.getState())) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("code", 1);
                errorResponse.put("msg", "用户已注销，请联系客服");
                return errorResponse;
            }
            shortUser.setEmail(email);
            //用户已存在直接发放token
            TokenPair tokenPair = jwtTokenService.createTokenPair(shortUser.getId().toString());
            shortUser.setToken(tokenPair.getAccessToken());
            shortUserService.updateShortUser(shortUser); // 更新用户的token
            shortUser.setToken(tokenPair.getAccessToken());
            result.put("code", 200);
            result.put("msg", "登陆成功");

            Map<String, Object> data = new HashMap<>();
            data.put("username", shortUser.getUsername());
            data.put("token", tokenPair.getAccessToken());
            result.put("data", data);
        } else {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", 1);
            errorResponse.put("msg", "用户不存在");
            return errorResponse;
        }
        return result;
    }

    @Override
    public ShortUser getUserByLinkIdAndIp(CheckShortUserDTO shortUserDTO) {
        Long linkId = shortUserDTO.getLinkId();
        Long appId = shortUserDTO.getAppId();
        String ip = shortUserDTO.getIp();
        String linkUrl = shortUserDTO.getLinkUrl();
        if (null != linkId && StringUtils.isNotEmpty(ip) && StringUtils.isNotEmpty(linkUrl) && null != appId) {
            ShortUser shortUser = new ShortUser();
            shortUser.setLinkidId(linkId);
            shortUser.setIp(ip);
            shortUser.setAppId(appId);
            List<ShortUser> shortUserList = shortUserService.selectShortUserList(shortUser);
            //筛选出创建时间距离当前5分钟内的用户 根据时间倒序排列
            if (CollectionUtil.isNotEmpty(shortUserList)) {
                List<Long> userIdList = shortUserList.stream().map(ShortUser::getId).collect(Collectors.toList());
                //查询这些用户近五分钟内的活动日志记录
                List<ShortUserActivityLog> shortUserActivityLogList = shortUserActivityLogService.selectByUserIdList(userIdList);
                if (CollectionUtil.isNotEmpty(shortUserActivityLogList)) {
                    List<ShortUserActivityLog> userActivityLogList = shortUserActivityLogList.stream()
                            .filter(item -> DateUtils.differentMinuteByMillisecond(item.getCreateTime(), DateUtils.getNowDate()) <= 5)
                            .sorted(Comparator.comparing(ShortUserActivityLog::getCreateTime).reversed())
                            .collect(Collectors.toList());
                    // content 先进行url解码 然后判断是否包含 linkUrl
                    List<ShortUserActivityLog> list = new ArrayList<>();
                    for (ShortUserActivityLog shortUserActivityLog : userActivityLogList) {
                        String content = null;
                        try {
                            content = URLDecoder.decode(shortUserActivityLog.getContent(), "UTF-8");
                        } catch (UnsupportedEncodingException e) {
                            throw new RuntimeException(e);
                        }
                        if (content.contains(linkUrl)) {
                            list.add(shortUserActivityLog);
                        }
                    }
                    if (CollectionUtil.isNotEmpty(list)) {
                        List<Long> existUserIds = list.stream().map(ShortUserActivityLog::getUserId).distinct().collect(Collectors.toList());
                        List<ShortUser> existUserList = shortUserList.stream().filter(item -> existUserIds.contains(item.getId()))
                                .collect(Collectors.toList());
                        return existUserList.get(0);
                    }
                }
            }
        }
        return null;
    }

    @Override
    public Map<String, Object> subscribeRecord(String email) {
        List<ShortUser> shortUsers = shortUserMapper.selectShortUserByEmail(email);
        List<Long> userIds = shortUsers.stream().map(ShortUser::getId).collect(Collectors.toList());
        List<SubscribeRecordVO> orders = shortOrderMapper.selectUserSubscribeRecord(userIds);

        List<SubscribeRecordVO> recordVOs = orders.stream().map(order -> {
            // 计算到期时间
            if (order.getPayTime() != null && order.getSubscriptionType() != null) {
                Calendar cal = Calendar.getInstance();
                cal.setTime(order.getPayTime());
                cal.add(Calendar.DATE, Integer.parseInt(order.getSubscriptionType()));
                order.setSubscriptionEndTime(cal.getTime());
                Calendar next = Calendar.getInstance();
                next.setTime(order.getPayTime());
                next.add(Calendar.DATE, Integer.parseInt(order.getSubscriptionType()) - 1);
                order.setNextChargeTime(next.getTime());
            } else {
                order.setSubscriptionEndTime(null);
            }
            return order;
        }).collect(Collectors.toList());

        if (!recordVOs.isEmpty()) {
            SubscribeRecordVO firstRecord = recordVOs.get(0);

            if (firstRecord.getExpireTime().after(DateUtils.getNowDate())) {
                // 转换为LocalDateTime
                LocalDateTime localDateTime = firstRecord.getExpireTime().toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDateTime();

                // 减去一天
                LocalDateTime newDateTime = localDateTime.minusDays(1);

                // 转换回Date
                firstRecord.setNextChargeTime(Date.from(newDateTime.atZone(ZoneId.systemDefault()).toInstant()));
            }


            firstRecord.setSubscriptionEndTime(firstRecord.getExpireTime());
            if (null == firstRecord.getExpireTime())
                firstRecord.setNextChargeTime(null);
        }

        return Collections.singletonMap("data", recordVOs);
    }

    @Override
    public AjaxResult syncUserData(String userId) {
        ShortUser newUser = shortUserMapper.selectShortUserByUserId(Long.valueOf(userId));
        Long subscriptionOldUserId = newUser.getSubscriptionOldUserId();
        if (subscriptionOldUserId != null && subscriptionOldUserId > 0L) {
            ShortUser oldUser = shortUserMapper.selectShortUserById(subscriptionOldUserId);
            newUser.setIsSubscriber(oldUser.getIsSubscriber());
            newUser.setExpireTime(oldUser.getExpireTime());
            shortUserMapper.updateShortUser(newUser);
        }
        return AjaxResult.success();
    }

    @Override
    public AjaxResult userCollectHistory(String userId, String type) {
        ShortUserCollectAndHistory shortUserCollectAndHistory = new ShortUserCollectAndHistory();
        shortUserCollectAndHistory.setUserId(Long.valueOf(userId));
        shortUserCollectAndHistory.setType(type);
        List<ShortUserCollectAndHistory> shortUserCollectAndHistories = shortUserCollectAndHistoryMapper.selectShortUserCollectAndHistoryList(shortUserCollectAndHistory);
        if(CollectionUtil.isNotEmpty(shortUserCollectAndHistories)){
            shortUserCollectAndHistories.sort((o1, o2) -> o2.getUpdateTime().compareTo(o1.getUpdateTime()));
        }
        return AjaxResult.success(shortUserCollectAndHistories);
    }

    @Override
    public AjaxResult addUserCollectHistory(ShortUserCollectAndHistory shortUserCollectAndHistory) {
        int i = 0;
        if ("history".equals(shortUserCollectAndHistory.getType())) {
            ShortUserCollectAndHistory query = new ShortUserCollectAndHistory();
            query.setUserId(shortUserCollectAndHistory.getUserId());
            query.setMovieId(shortUserCollectAndHistory.getMovieId());
            query.setType("history");
            ShortUserCollectAndHistory shortUserCollectAndHistory1 = shortUserCollectAndHistoryMapper.selectShortUserCollectAndHistoryList(query).stream().findFirst().orElse(null);
            if (shortUserCollectAndHistory1 != null) {
                shortUserCollectAndHistory1.setUrl(shortUserCollectAndHistory.getUrl());
                shortUserCollectAndHistory1.setCurrentEp(shortUserCollectAndHistory.getCurrentEp());
                shortUserCollectAndHistory1.setAllEp(shortUserCollectAndHistory.getAllEp());
                shortUserCollectAndHistory1.setTitle(shortUserCollectAndHistory.getTitle());
                shortUserCollectAndHistory1.setUpdateTime(new Date());
                i = shortUserCollectAndHistoryMapper.updateShortUserCollectAndHistory(shortUserCollectAndHistory1);
            } else {
                shortUserCollectAndHistory.setCreateTime(new Date());
                shortUserCollectAndHistory.setUpdateTime(new Date());
                i = shortUserCollectAndHistoryMapper.insertShortUserCollectAndHistory(shortUserCollectAndHistory);
            }
        } else {
            shortUserCollectAndHistory.setCreateTime(new Date());
            shortUserCollectAndHistory.setUpdateTime(new Date());
            i = shortUserCollectAndHistoryMapper.insertShortUserCollectAndHistory(shortUserCollectAndHistory);
        }
        return i > 0 ? AjaxResult.success() : AjaxResult.error();
    }

    @Override
    public AjaxResult deleteUserCollectHistory(String id) {
        int i = shortUserCollectAndHistoryMapper.deleteShortUserCollectAndHistoryById(Long.valueOf(id));
        return i > 0 ? AjaxResult.success() : AjaxResult.error();
    }

    @Override
    public AjaxResult isCollect(Long userId, String movieId) {
        ShortUserCollectAndHistory shortUserCollectAndHistory = new ShortUserCollectAndHistory();
        shortUserCollectAndHistory.setUserId(userId);
        shortUserCollectAndHistory.setMovieId(Long.valueOf(movieId));
        shortUserCollectAndHistory.setType("collect");
        List<ShortUserCollectAndHistory> shortUserCollectAndHistories = shortUserCollectAndHistoryMapper.selectShortUserCollectAndHistoryList(shortUserCollectAndHistory);
        if (CollectionUtil.isNotEmpty(shortUserCollectAndHistories)) {
            return AjaxResult.success(shortUserCollectAndHistories.get(0));
        } else {
            return AjaxResult.success();
        }
    }

    private boolean isSameDay(Date date1, Date date2) {
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);
        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);
        return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
                cal1.get(Calendar.DAY_OF_YEAR) == cal2.get(Calendar.DAY_OF_YEAR);
    }

    /**
     * 根据IP地址获取国家信息
     */
    private Map<String, String> getCountryByIp(String ip) {
        Map<String, String> result = new HashMap<>();

        // 默认值
        result.put("countryName", null);
        result.put("countryCode", null);

        if (ip == null || ip.isEmpty()) {
            result.put("countryName", "Unknown");
            result.put("countryCode", "UN");
            return result;
        }

        try {
            // 对于特殊IP地址进行处理
            if (ip.startsWith("192.168.") || ip.startsWith("10.") || ip.equals("127.0.0.1") || ip.equals("0:0:0:0:0:0:0:1") || ip.equals("::1")) {
                result.put("countryName", "Local");
                result.put("countryCode", "LO");
                return result;
            }

            // 判断IP类型并查询相应表
            String countryName = null;
            if (ip.contains(":")) {
                // IPv6
                try {
                    BigDecimal ipDecimal = ipv6ToDecimal(ip);
                    System.out.println("[getCountryByIp] Converted IPv6 " + ip + " to Decimal: " + ipDecimal); // 调试输出
                    countryName = shortUserMapper.getCountryByIpv6Decimal(ipDecimal);
                    System.out.println("[getCountryByIp] Mapper result for IPv6 Decimal: " + countryName); // 调试输出
                } catch (UnknownHostException | IllegalArgumentException e) {
                    System.err.println("[getCountryByIp] Error converting IPv6 to Decimal: " + ip + ", Error: " + e.getMessage());
                    // 转换失败，无法查询
                    countryName = null;
                }
            } else {
                // IPv4
                System.out.println("[getCountryByIp] Querying IPv4: " + ip); // 调试输出
                countryName = shortUserMapper.getCountryByIpv4(ip);
                System.out.println("[getCountryByIp] Mapper result for IPv4: " + countryName); // 调试输出
            }

            if (countryName != null && !countryName.isEmpty()) {
                result.put("countryName", countryName);
            } else {
                System.out.println("[getCountryByIp] No country found in DB for IP: " + ip + ". Setting to Unknown."); // 调试输出
                // 如果数据库查询无结果或转换失败，则标记为未知
                result.put("countryName", "Unknown");
                result.put("countryCode", "UN");
            }
        } catch (Exception e) {
            // 记录错误日志会更好
            System.err.println("[getCountryByIp] General Error getting country by IP: " + ip + ", Error: " + e.getMessage());
            e.printStackTrace(); // 打印完整堆栈跟踪
            result.put("countryName", "Unknown");
            result.put("countryCode", "UN");
        }

        System.out.println("[getCountryByIp] Final result for IP " + ip + ": " + result); // 调试输出
        return result;
    }

    /**
     * Converts an IPv6 string to its BigDecimal representation.
     *
     * @param ipv6Str The IPv6 address string.
     * @return The BigDecimal representation.
     * @throws UnknownHostException     If the IP address format is invalid.
     * @throws IllegalArgumentException If the resolved address is not IPv6.
     */
    private BigDecimal ipv6ToDecimal(String ipv6Str) throws UnknownHostException, IllegalArgumentException {
        InetAddress inetAddress = InetAddress.getByName(ipv6Str);
        byte[] bytes = inetAddress.getAddress();
        // Ensure it's IPv6 (16 bytes)
        if (bytes.length != 16) {
            throw new IllegalArgumentException("Address is not IPv6: " + ipv6Str);
        }
        // new BigInteger(1, bytes) interprets bytes as a positive magnitude two's complement BigInteger
        return new BigDecimal(new BigInteger(1, bytes));
    }

}
