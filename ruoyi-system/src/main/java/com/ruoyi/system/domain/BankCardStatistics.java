package com.ruoyi.system.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;

/**
 * 银行卡统计对象 bank_card_statistics
 */
public class BankCardStatistics extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 银行卡号（脱敏） */
    @Excel(name = "银行卡号")
    private String cardNumber;

    /** 主绑定用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 历史关联用户ID列表 */
    private String historyUserIds;

    /** 关联用户数量 */
    @Excel(name = "关联用户数")
    private Integer relatedUsers;

    /** 卡状态 */
    @Excel(name = "状态")
    private String status;

    /** 订阅累计金额 */
    @Excel(name = "订阅累计金额")
    private BigDecimal subTotalAmount;

    /** 订阅累计次数 */
    @Excel(name = "订阅累计次数")
    private Integer subTotalCount;

    /** 充值累计金额 */
    @Excel(name = "充值累计金额")
    private BigDecimal recTotalAmount;

    /** 充值累计次数 */
    @Excel(name = "充值累计次数")
    private Integer recTotalCount;

    public BankCardStatistics()
    {
        this.relatedUsers = 1;
        this.subTotalAmount = BigDecimal.ZERO;
        this.subTotalCount = 0;
        this.recTotalAmount = BigDecimal.ZERO;
        this.recTotalCount = 0;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }

    public void setCardNumber(String cardNumber)
    {
        this.cardNumber = cardNumber;
    }

    public String getCardNumber()
    {
        return cardNumber;
    }

    public void setUserId(Long userId)
    {
        this.userId = userId;
    }

    public Long getUserId()
    {
        return userId;
    }

    public void setHistoryUserIds(String historyUserIds)
    {
        this.historyUserIds = historyUserIds;
    }

    public String getHistoryUserIds()
    {
        return historyUserIds;
    }

    public void setRelatedUsers(Integer relatedUsers)
    {
        this.relatedUsers = relatedUsers;
    }

    public Integer getRelatedUsers()
    {
        return relatedUsers;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }

    public void setSubTotalAmount(BigDecimal subTotalAmount)
    {
        this.subTotalAmount = subTotalAmount;
    }

    public BigDecimal getSubTotalAmount()
    {
        return subTotalAmount;
    }

    public void setSubTotalCount(Integer subTotalCount)
    {
        this.subTotalCount = subTotalCount;
    }

    public Integer getSubTotalCount()
    {
        return subTotalCount;
    }

    public void setRecTotalAmount(BigDecimal recTotalAmount)
    {
        this.recTotalAmount = recTotalAmount;
    }

    public BigDecimal getRecTotalAmount()
    {
        return recTotalAmount;
    }

    public void setRecTotalCount(Integer recTotalCount)
    {
        this.recTotalCount = recTotalCount;
    }

    public Integer getRecTotalCount()
    {
        return recTotalCount;
    }

    /**
     * 获取总金额
     */
    public BigDecimal getTotalAmount() {
        return this.subTotalAmount.add(this.recTotalAmount);
    }

    /**
     * 获取总次数
     */
    public Integer getTotalCount() {
        return this.subTotalCount + this.recTotalCount;
    }

    /**
     * 增加订阅统计
     */
    public void addSubscriptionStats(BigDecimal amount) {
        this.subTotalAmount = this.subTotalAmount.add(amount);
        this.subTotalCount++;
    }

    /**
     * 增加充值统计
     */
    public void addRechargeStats(BigDecimal amount) {
        this.recTotalAmount = this.recTotalAmount.add(amount);
        this.recTotalCount++;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("cardNumber", getCardNumber())
            .append("userId", getUserId())
            .append("historyUserIds", getHistoryUserIds())
            .append("relatedUsers", getRelatedUsers())
            .append("status", getStatus())
            .append("subTotalAmount", getSubTotalAmount())
            .append("subTotalCount", getSubTotalCount())
            .append("recTotalAmount", getRecTotalAmount())
            .append("recTotalCount", getRecTotalCount())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}