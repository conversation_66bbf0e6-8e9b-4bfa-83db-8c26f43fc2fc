package com.ruoyi.system.service;

import com.ruoyi.system.domain.BankCardStatistics;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 银行卡统计Service接口
 */
public interface IBankCardStatisticsService
{
    /**
     * 查询银行卡统计
     *
     * @param id 银行卡统计主键
     * @return 银行卡统计
     */
    public BankCardStatistics selectBankCardStatisticsById(Long id);

    /**
     * 根据银行卡号查询统计信息
     */
    public BankCardStatistics selectBankCardStatisticsByCardNumber(String cardNumber);

    /**
     * 查询银行卡统计列表
     *
     * @param bankCardStatistics 银行卡统计
     * @return 银行卡统计集合
     */
    public List<BankCardStatistics> selectBankCardStatisticsList(BankCardStatistics bankCardStatistics);

    /**
     * 新增银行卡统计
     *
     * @param bankCardStatistics 银行卡统计
     * @return 结果
     */
    public int insertBankCardStatistics(BankCardStatistics bankCardStatistics);

    /**
     * 修改银行卡统计
     *
     * @param bankCardStatistics 银行卡统计
     * @return 结果
     */
    public int updateBankCardStatistics(BankCardStatistics bankCardStatistics);

    /**
     * 批量删除银行卡统计
     *
     * @param ids 需要删除的银行卡统计主键集合
     * @return 结果
     */
    public int deleteBankCardStatisticsByIds(Long[] ids);

    /**
     * 删除银行卡统计信息
     *
     * @param id 银行卡统计主键
     * @return 结果
     */
    public int deleteBankCardStatisticsById(Long id);

    /**
     * 获取或创建银行卡统计记录
     */
    public BankCardStatistics getOrCreateStatistics(String cardNumber);

    /**
     * 更新用户关联信息
     */
    public boolean updateUserAssociation(String cardNumber, Long userId, List<Long> allUserIds);

    /**
     * 增加订阅统计
     */
    public boolean addSubscriptionStats(String cardNumber, Integer count, BigDecimal amount);

    /**
     * 增加充值统计
     */
    public boolean addRechargeStats(String cardNumber, Integer count, BigDecimal amount);

    /**
     * 获取指定日期有订单的银行卡列表
     */
    public List<String> getActiveCardNumbers(LocalDate targetDate);

    /**
     * 获取用户订阅开始时间
     */
    public LocalDate getUserSubscriptionStartDate(Long userId);

    /**
     * 获取多用户使用的银行卡列表
     */
    public List<BankCardStatistics> getMultiUserCards();

    /**
     * 按状态统计银行卡数量
     */
    public List<Object[]> countByStatus();

    /**
     * 查询指定银行卡在指定日期的订单数据
     */
    public List<Map<String, Object>> selectCardOrdersByDate(String cardNumber, LocalDate targetDate);

    /**
     * 获取银行卡首次使用时间
     *
     * @param cardNumber 银行卡号
     * @return 首次使用日期
     */
    public LocalDate getCardFirstUseDate(String cardNumber);

    /**
     * 获取银行卡在指定日期范围内的统计数据
     *
     * @param cardNumber 银行卡号
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计数据Map，包含count和amount
     */
    public Map<String, Object> getCardStatsByDateRange(String cardNumber, LocalDate startDate, LocalDate endDate);

    /**
     * 获取银行卡在指定日期范围内有订阅订单的用户ID列表
     *
     * @param cardNumber 银行卡号
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 用户ID列表
     */
    public List<Long> getCardSubscriptionUsersByDateRange(String cardNumber, LocalDate startDate, LocalDate endDate);

    /**
     * 获取银行卡的所有订阅续费用户ID列表
     *
     * @param cardNumber 银行卡号
     * @return 订阅续费用户ID列表
     */
    public List<Long> getCardSubscriptionRenewalUsers(String cardNumber);
}