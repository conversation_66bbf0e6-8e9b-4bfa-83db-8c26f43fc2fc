package com.ruoyi.service;

import java.util.List;

import com.ruoyi.domain.ShortUserLinkMovieBind;

/**
 * 【请填写功能名称】Service接口
 *
 * <AUTHOR>
 * @date 2025-09-22
 */
public interface IShortUserLinkMovieBindService {
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public ShortUserLinkMovieBind selectShortUserLinkMovieBindById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param shortUserLinkMovieBind 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<ShortUserLinkMovieBind> selectShortUserLinkMovieBindList(ShortUserLinkMovieBind shortUserLinkMovieBind);

    /**
     * 新增【请填写功能名称】
     *
     * @param shortUserLinkMovieBind 【请填写功能名称】
     * @return 结果
     */
    public int insertShortUserLinkMovieBind(ShortUserLinkMovieBind shortUserLinkMovieBind);

    /**
     * 修改【请填写功能名称】
     *
     * @param shortUserLinkMovieBind 【请填写功能名称】
     * @return 结果
     */
    public int updateShortUserLinkMovieBind(ShortUserLinkMovieBind shortUserLinkMovieBind);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键集合
     * @return 结果
     */
    public int deleteShortUserLinkMovieBindByIds(Long[] ids);

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteShortUserLinkMovieBindById(Long id);
}
