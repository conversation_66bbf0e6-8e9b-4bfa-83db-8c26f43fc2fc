package com.ruoyi.app.api.service;

import com.alipay.global.api.model.Result;
import com.ruoyi.app.api.dto.PaymentDisputeDTO;
import com.ruoyi.app.api.dto.PaymentNotifyDTO;
import com.ruoyi.app.api.request.PayOrderRequest;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.domain.ShortOrder;

import java.util.Map;

/**
 * 支付相关API服务接口
 */
public interface PaymentApiService {

    /**
     * 创建支付意图
     *
     * @param payOrderRequest 请求参数
     * @return 支付意图信息
     */
    Map<String, Object> createIntent(PayOrderRequest payOrderRequest);

    /**
     * 获取订单状态
     *
     * @param merchantOrderId 商户订单ID
     * @return 订单状态信息
     */
    Map<String, Object> getOrderStatus(String merchantOrderId);

    /**
     * 处理支付回调
     *
     * @param callbackData 回调数据
     * @return 处理结果
     */
    Map<String, Object> handlePaymentCallback(Map<String, Object> callbackData);

    /**
     * 获取VIP类型数据
     *
     * @param templateId 充值模板ID
     * @return 处理结果
     */
    Map<String, Object> getVipList(String templateId,Boolean flag, String userId, String linkAppId, String linkId);

    /**
     * 获取金币消费记录
     *
     * @param token 用户令牌
     * @param uid 用户ID
     * @return 处理结果
     */
    Map<String, Object> getCoinRecords(String token, String uid);

    /**
     * 扣除用户金币
     *
     * @param token 用户令牌
     * @param appId 应用ID
     * @param uid 用户ID
     * @param deductData 扣除数据
     * @return 处理结果
     */
    Map<String, Object> deductUserCoin(String token, String appId, String uid, Map<String, Object> deductData);

    /**
     * 处理支付宝支付结果通知
     */
    Result handleAliPaymentNotify(Map<String, Object> notifyData) throws Exception;

    AjaxResult handleAcceptDispute(Long orderId);

    /**
     * 处理争议用户 - 例如拉黑
     * @param userId 用户ID
     * @param disputeId 争议ID
     * @param disputeResult 争议结果
     */
    void processDisputeUser(Long userId, String disputeId, String disputeResult);

     void handleOrderRecharge(ShortOrder order, PaymentNotifyDTO notifyDTO);

     void handleOrderSubscribe(ShortOrder order, PaymentNotifyDTO notifyDTO) throws Exception;

    Map<String, Object> getCoinTemplate(Long payTemplateId);
}
