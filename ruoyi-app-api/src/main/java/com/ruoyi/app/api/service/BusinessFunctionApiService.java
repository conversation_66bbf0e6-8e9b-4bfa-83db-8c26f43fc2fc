package com.ruoyi.app.api.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.ruoyi.app.api.dto.EmailLoginDTO;
import com.ruoyi.app.api.dto.InitUserDTO;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.dto.EmailDTO;
import com.ruoyi.domain.ShortMovie;
import com.ruoyi.domain.ShortOrder;
import com.ruoyi.domain.ShortUser;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * 业务功能相关接口服务
 */
public interface BusinessFunctionApiService {

    /**
     * 添加反馈信息
     *
     * @param token        认证token
     * @param appId        应用ID
     * @param feedbackData 反馈数据
     * @return 操作结果
     */
    Map<String, Object> addFeedback(String token, String appId, Map<String, Object> feedbackData);

    /**
     * 获取每日签到赠送金币的活动
     *
     * @param token    认证token
     * @param appId    应用ID
     * @param uid      用户ID
     * @param activity 活动ID
     * @return 活动信息
     */
    Map<String, Object> getDailySign(String token, String appId, String uid, Integer activity);

    /**
     * 获取程序开关数据
     *
     * @param appId       应用ID
     * @param type        类型
     * @param versionData 版本数据
     * @return 开关数据
     */
    Map<String, Object> getVersion(String appId, String type, Map<String, Object> versionData);

    /**
     * 存储设备信息
     *
     * @param platform       平台
     * @param screenWidth    屏幕宽度
     * @param screenHeight   屏幕高度
     * @param systemTimeZone 系统时区
     * @param ip             IP地址
     * @param linkUrl        深链地址
     * @param appId          应用ID
     * @return 操作结果
     */
    Map<String, Object> storeDeviceInfo(String platform, String screenWidth, String screenHeight,
                                        String systemTimeZone, String ip, String linkUrl, String appId);

    /**
     * 匹配设备信息
     *
     * @param platform       平台
     * @param screenWidth    屏幕宽度
     * @param screenHeight   屏幕高度
     * @param systemTimeZone 系统时区
     * @param ip             IP地址
     * @param appId          应用ID
     * @return 匹配结果
     */
    Map<String, Object> matchDeviceInfo(String platform, String screenWidth, String screenHeight,
                                        String systemTimeZone, String ip, String appId);

    /**
     * 初始化用户
     *
     * @param appId       应用ID
     * @param initUserDTO 用户初始化DTO
     * @return 初始化结果
     */
    Map<String, Object> initUser(String appId, InitUserDTO initUserDTO);

    /**
     * 查询用户深链关联的视频
     *
     * @param appId  应用ID
     * @param hashId 用户临时ID
     * @return 关联视频列表
     */
    Map<String, Object> getLinkVideos(String appId, String hashId);

    /**
     * 根据推广链接ID获取影片ID和其他相关参数
     *
     * @param appId     应用ID
     * @param linkId    推广链接ID
     * @param emailFlag 是否邮件点击进入
     * @param userId    用户id
     * @return 包含影片ID、渠道、PID、KID、Pixel ID等的结果Map
     */
    Map<String, Object> getMovieIdByLinkId(String appId, Long linkId, boolean emailFlag, Long userId);

    ShortUser filterUser(InitUserDTO initUserDTO) throws Exception;

    /**
     * 发送Facebook事件，支持多种事件类型，并可指定是否为手动回传
     *
     * @param userId         用户ID
     * @param eventName      事件名称(CompleteRegistration/Purchase等)
     * @param orderId        订单ID (仅在Purchase事件时相关)
     * @param async          是否异步执行
     * @param isManualUpload 是否为手动回传（手动回传时绕过已回传和首单检查）
     * @return 是否成功发送
     */
    boolean sendFacebookEvent(Long userId, String eventName, Long orderId, boolean async, boolean isManualUpload);

    AjaxResult sendEmail(String publicKey);

    AjaxResult sendAwsEmail(EmailDTO emailDTO);

    /**
     * 触发Facebook像素回传
     *
     * @param user 用户信息
     */
    void triggerFacebookPixel(ShortUser user);

    AjaxResult sendNoticeEmail(String publicKey);

    AjaxResult sendEmailRenewalByUserIds(String publicKey);

    AjaxResult sendForwardEmail(String publicKey);

    AjaxResult sendBackRegEmail(String publicKey);

    AjaxResult sendNullUserEmailBrevo(String publicKey);

    AjaxResult sendNullUserEmailForward(String publicKey);

    AjaxResult sendWelcomeEmailForward(InitUserDTO shortBrevo, ShortMovie shortMovie, String brevo) throws Exception;

    AjaxResult sendSubEmailForward(InitUserDTO shortBrevo, ShortMovie shortMovie, String brevo, ShortOrder order) throws Exception;

    AjaxResult sendSubEmailForward1(InitUserDTO shortBrevo, ShortMovie shortMovie, String brevo, ShortOrder order) throws Exception;

    AjaxResult sendUnSubEmailForward(InitUserDTO shortBrevo, ShortMovie shortMovie, String brevo) throws Exception;

    /**
     * 邮箱登录
     *
     * @param emailLoginDTO 邮箱登录DTO
     * @return 登录结果
     */
    Map<String, Object> emailLogin(EmailLoginDTO emailLoginDTO) throws JsonProcessingException;

    AjaxResult sendWeeklyEmail(String publicKey) throws Exception;

    void sendEmailForInType(InitUserDTO shortBrevo, List<ShortMovie> shortMovieList, String type, String inType) throws Exception;

    AjaxResult sendMonitorEmail(String publicKey);

    void createCustomers(ShortUser shortBrevo);

    AjaxResult toSend(InitUserDTO shortBrevo, HttpServletRequest request) throws Exception;

    AjaxResult updateUserIsSubscriber(String publicKey);

    /**
     * 搜索老用户
     *
     * @param shortBrevo 短用户信息
     * @param request    HTTP请求
     * @return 搜索结果
     */
    AjaxResult searchOldUserByEmail(InitUserDTO shortBrevo, HttpServletRequest request) throws Exception;

    /**
     * 解锁电影
     *
     * @param userId 用户ID
     * @return 解锁结果
     */
    AjaxResult unlockLinkMovieApi(Long userId);

    AjaxResult sendEmailRenewalRecord(String publicKey);
}