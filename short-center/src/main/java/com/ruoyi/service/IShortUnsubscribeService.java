package com.ruoyi.service;

import com.ruoyi.domain.ShortUnsubscribe;

import java.util.List;

/**
 * 退订Service接口
 *
 * <AUTHOR>
 * @date 2025-09-22
 */
public interface IShortUnsubscribeService
{
    /**
     * 查询退订
     *
     * @param id 退订主键
     * @return 退订
     */
    public ShortUnsubscribe selectShortUnsubscribeById(Long id);

    /**
     * 查询退订列表
     *
     * @param shortUnsubscribe 退订
     * @return 退订集合
     */
    public List<ShortUnsubscribe> selectShortUnsubscribeList(ShortUnsubscribe shortUnsubscribe);

    /**
     * 新增退订
     *
     * @param shortUnsubscribe 退订
     * @return 结果
     */
    public int insertShortUnsubscribe(ShortUnsubscribe shortUnsubscribe);

    /**
     * 修改退订
     *
     * @param shortUnsubscribe 退订
     * @return 结果
     */
    public int updateShortUnsubscribe(ShortUnsubscribe shortUnsubscribe);

    /**
     * 批量删除退订
     *
     * @param ids 需要删除的退订主键集合
     * @return 结果
     */
    public int deleteShortUnsubscribeByIds(Long[] ids);

    /**
     * 删除退订信息
     *
     * @param id 退订主键
     * @return 结果
     */
    public int deleteShortUnsubscribeById(Long id);

    /**
     * 检查邮箱在指定应用中是否存在退订记录
     *
     * @param email 邮箱
     * @param appId 应用ID
     * @return 是否存在
     */
    public boolean existsByEmailAndAppId(String email, Long appId);

    /**
     * 批量检查邮箱在指定应用中是否存在退订记录
     *
     * @param emails 邮箱列表
     * @param appId 应用ID
     * @return 存在的邮箱列表
     */
    public List<String> batchExistsByEmailAndAppId(List<String> emails, Long appId);
}
