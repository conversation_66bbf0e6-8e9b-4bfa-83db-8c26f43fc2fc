package com.ruoyi.domain;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 轮播图数据对象 short_banner
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
public class ShortBanner extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 轮播图图片
     */
    @Excel(name = "轮播图图片")
    private String image;

    /**
     * 选择影片
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "选择影片", width = 30, dateFormat = "yyyy-MM-dd")
    private Date addtime;

    /**
     * 应用ID
     */
    @Excel(name = "应用ID")
    private Long appId;

    /**
     * 影片ID
     */
    @Excel(name = "影片ID")
    private Long movieId;

    @Excel(name = "应用名称")
    private String appName;

    @Excel(name = "影片名称")
    private String movieName;

    @Excel(name = "语言编码")
    private String languageCode;

    private String language;

    private String movieDescription;

    private Integer movieNum;

    /**
     * 原始图片ID
     */
    private String originalImage;

    public String getOriginalImage() {
        return originalImage;
    }

    public void setOriginalImage(String originalImage) {
        this.originalImage = originalImage;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getLanguageCode() {
        return languageCode;
    }

    public void setLanguageCode(String languageCode) {
        this.languageCode = languageCode;
    }

    public Integer getMovieNum() {
        return movieNum;
    }

    public void setMovieNum(Integer movieNum) {
        this.movieNum = movieNum;
    }

    public String getMovieDescription() {
        return movieDescription;
    }

    public void setMovieDescription(String movieDescription) {
        this.movieDescription = movieDescription;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public void setMovieName(String movieName) {
        this.movieName = movieName;
    }

    public String getAppName() {
        return appName;
    }

    public String getMovieName() {
        return movieName;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public String getImage() {
        return image;
    }

    public void setAddtime(Date addtime) {
        this.addtime = addtime;
    }

    public Date getAddtime() {
        return addtime;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public Long getAppId() {
        return appId;
    }

    public void setMovieId(Long movieId) {
        this.movieId = movieId;
    }

    public Long getMovieId() {
        return movieId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("image", getImage())
                .append("addtime", getAddtime())
                .append("appId", getAppId())
                .append("movieId", getMovieId())
                .toString();
    }
}
