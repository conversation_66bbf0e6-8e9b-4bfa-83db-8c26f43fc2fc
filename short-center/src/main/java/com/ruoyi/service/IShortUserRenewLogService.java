package com.ruoyi.service;

import com.ruoyi.domain.ShortUserRenewLog;

import java.util.List;

/**
 * 订阅续费日志记录Service接口
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
public interface IShortUserRenewLogService
{
    /**
     * 查询订阅续费日志记录
     *
     * @param id 订阅续费日志记录主键
     * @return 订阅续费日志记录
     */
    public ShortUserRenewLog selectShortUserRenewLogById(Long id);

    /**
     * 查询订阅续费日志记录列表
     *
     * @param shortUserRenewLog 订阅续费日志记录
     * @return 订阅续费日志记录集合
     */
    public List<ShortUserRenewLog> selectShortUserRenewLogList(ShortUserRenewLog shortUserRenewLog);

    /**
     * 新增订阅续费日志记录
     *
     * @param shortUserRenewLog 订阅续费日志记录
     * @return 结果
     */
    public int insertShortUserRenewLog(ShortUserRenewLog shortUserRenewLog);

    /**
     * 修改订阅续费日志记录
     *
     * @param shortUserRenewLog 订阅续费日志记录
     * @return 结果
     */
    public int updateShortUserRenewLog(ShortUserRenewLog shortUserRenewLog);

    /**
     * 批量删除订阅续费日志记录
     *
     * @param ids 需要删除的订阅续费日志记录主键集合
     * @return 结果
     */
    public int deleteShortUserRenewLogByIds(Long[] ids);

    /**
     * 删除订阅续费日志记录信息
     *
     * @param id 订阅续费日志记录主键
     * @return 结果
     */
    public int deleteShortUserRenewLogById(Long id);

    List<Long> selectShortUserCodeIs51(List<Long> userIds);

    String selectShortUserCodeByUserId(Long id);

    List<Long> getByIds(List<Long> mergedList);

    String getLastCodeByUserId(Long id);
}
