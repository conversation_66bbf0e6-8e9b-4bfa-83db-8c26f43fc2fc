package com.ruoyi.service;

import java.util.List;
import com.ruoyi.domain.ShortUserUnlockVideo;
import org.apache.ibatis.annotations.Param;

/**
 * 用户已解锁的视频Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
public interface IShortUserUnlockVideoService 
{
    /**
     * 查询用户已解锁的视频
     * 
     * @param id 用户已解锁的视频主键
     * @return 用户已解锁的视频
     */
    public ShortUserUnlockVideo selectShortUserUnlockVideoById(Long id);

    /**
     * 查询用户已解锁的视频列表
     * 
     * @param shortUserUnlockVideo 用户已解锁的视频
     * @return 用户已解锁的视频集合
     */
    public List<ShortUserUnlockVideo> selectShortUserUnlockVideoList(ShortUserUnlockVideo shortUserUnlockVideo);

    /**
     * 新增用户已解锁的视频
     * 
     * @param shortUserUnlockVideo 用户已解锁的视频
     * @return 结果
     */
    public int insertShortUserUnlockVideo(ShortUserUnlockVideo shortUserUnlockVideo);

    /**
     * 修改用户已解锁的视频
     * 
     * @param shortUserUnlockVideo 用户已解锁的视频
     * @return 结果
     */
    public int updateShortUserUnlockVideo(ShortUserUnlockVideo shortUserUnlockVideo);

    /**
     * 批量删除用户已解锁的视频
     * 
     * @param ids 需要删除的用户已解锁的视频主键集合
     * @return 结果
     */
    public int deleteShortUserUnlockVideoByIds(Long[] ids);

    /**
     * 删除用户已解锁的视频信息
     * 
     * @param id 用户已解锁的视频主键
     * @return 结果
     */
    public int deleteShortUserUnlockVideoById(Long id);

    List<Integer> findVideoIdByMovieIdAndUser(Integer movieId, String userId);

    void insertShortUserUnlockVideoList(List<ShortUserUnlockVideo> unlockVideos);

    void deleteShortUserUnlockVideoByUserIdAndMovieId(Long userId, Long movieId);
}
