package com.ruoyi.app.api.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.ruoyi.app.api.dto.InitUserDTO;
import com.ruoyi.app.api.service.BusinessFunctionApiService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.domain.ShortMovie;
import com.ruoyi.domain.ShortUser;
import com.ruoyi.domain.ShortUserLinkMovieBind;
import com.ruoyi.mapper.ShortUserLinkMovieBindMapper;
import com.ruoyi.service.IShortMovieI18nService;
import com.ruoyi.service.IShortMovieService;
import com.ruoyi.service.IShortSemLinkService;
import com.ruoyi.service.IShortUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * brevo邮件管理Controller
 */
@Api("brevo邮件管理")
@RestController
@RequestMapping("/api/brevo")
public class BrevoController extends BaseController {

    @Autowired
    private BusinessFunctionApiService businessFunctionApiService;

    @Autowired
    private IShortMovieService shortMovieService;

    @Autowired
    private IShortUserService shortUserService;

    @Autowired
    private IShortMovieI18nService shortMovieI18nService;

    @Autowired
    private IShortSemLinkService shortSemLinkService;

    @Autowired(required = false)
    private RedisTemplate<String, String> redisTemplate;

    /**
     * 新增App管理
     */
    @ApiOperation("发送邮件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "receiveEmail", value = "收件人邮箱", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "userId", value = "收件人名称", dataType = "String", dataTypeClass = String.class),
    })
    @PostMapping("/send")
    public AjaxResult send(@RequestBody InitUserDTO shortBrevo, HttpServletRequest request) throws Exception {
//        ShortUser user = shortUserService.selectShortUserById(Long.valueOf(840679));
//        shortBrevo.setReceiveEmail(user.getEmail());
//        shortBrevo.setAppId(user.getAppId());
//        if(null == shortBrevo.getLanguageCode())
//            shortBrevo.setLanguageCode("en-US");
////                        ShortSemLink shortSemLink = shortSemLinkService.selectShortSemLinkById(su.getLinkidId());
//        shortBrevo.setUserId(String.valueOf(user.getId()));
//        shortBrevo.setUniqueId(user.getUniqueId());
//        shortBrevo.setExpireTime(user.getExpireTime());
//        ShortMovie shortMovie1 = shortMovieService.selectShortMovieById(59L);
//        businessFunctionApiService.sendSubEmailForward(shortBrevo, shortMovie1, "brevo");


        return businessFunctionApiService.toSend(shortBrevo, request);

    }

    /**
     * 新增App管理 biliTV 用
     */
    @ApiOperation("发送邮件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "receiveEmail", value = "收件人邮箱", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "userId", value = "收件人名称", dataType = "String", dataTypeClass = String.class),
    })
    @PostMapping("/send/verify")
    public AjaxResult sendVerify(@RequestBody InitUserDTO shortBrevo, HttpServletRequest request) throws Exception {
//        ShortUser user = shortUserService.selectShortUserById(Long.valueOf(840679));
//        shortBrevo.setReceiveEmail(user.getEmail());
//        shortBrevo.setAppId(user.getAppId());
//        if(null == shortBrevo.getLanguageCode())
//            shortBrevo.setLanguageCode("en-US");
////                        ShortSemLink shortSemLink = shortSemLinkService.selectShortSemLinkById(su.getLinkidId());
//        shortBrevo.setUserId(String.valueOf(user.getId()));
//        shortBrevo.setUniqueId(user.getUniqueId());
//        shortBrevo.setExpireTime(user.getExpireTime());
//        ShortMovie shortMovie1 = shortMovieService.selectShortMovieById(59L);
//        businessFunctionApiService.sendSubEmailForward(shortBrevo, shortMovie1, "brevo");
        if (StringUtils.isEmpty(shortBrevo.getReceiveEmail()))
            return AjaxResult.error("Please fill in the recipient's email address!");

        //验证码为空，不校验
        if (StringUtils.isNotEmpty(shortBrevo.getEmailCode())) {
            String emailCode = shortBrevo.getEmailCode();
            // 从Redis中获取验证码并与用户输入的验证码进行比对
            String storedCode = redisTemplate.opsForValue().get("email:login:" + shortBrevo.getReceiveEmail());
            if (storedCode == null || !storedCode.equalsIgnoreCase(emailCode)) {
                return error("The verification code is incorrect or has expired！");
            }
            // 删除Redis中的验证码
            redisTemplate.delete("email:login:" + shortBrevo.getReceiveEmail());
        }

        shortBrevo.setSendType(1);

        // 如果存在订单号，则执行新的逻辑
        String merchantOrderId = shortBrevo.getMerchantOrderId();
        if (StringUtils.isNotEmpty(merchantOrderId)) {
            return businessFunctionApiService.searchOldUserByEmail(shortBrevo, request);
        }
        return businessFunctionApiService.toSend(shortBrevo, request);

    }

    /**
     * 绑定邮箱
     */
    @ApiOperation("绑定邮箱")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "receiveEmail", value = "收件人邮箱", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "userId", value = "收件人名称", dataType = "String", dataTypeClass = String.class),
    })
    @PostMapping("/bind_email")
    public AjaxResult bindEmail(@RequestBody InitUserDTO shortBrevo, HttpServletRequest request) throws Exception {

        String emailCode = shortBrevo.getEmailCode();
        if (StringUtils.isEmpty(emailCode))
            return error("Please fill in the verification code!");

        // 从Redis中获取验证码并与用户输入的验证码进行比对
        String storedCode = redisTemplate.opsForValue().get("email:code:" + shortBrevo.getReceiveEmail());
        if (storedCode == null || !storedCode.equalsIgnoreCase(emailCode)) {
            return error("The verification code is incorrect or has expired！");
        }
        // 删除Redis中的验证码
        redisTemplate.delete("email:code:" + shortBrevo.getReceiveEmail());


        if (StringUtils.isEmpty(shortBrevo.getReceiveEmail()))
            return error("Please fill in the recipient's email!");

//        int res =shortUserService.countEmail(shortBrevo.getReceiveEmail());
//        if(res>0)
//            return error("Account already exists!");

        if ("".equals(shortBrevo.getReceiveName()))
            shortBrevo.setReceiveName(null);
        if ("".equals(shortBrevo.getSubject()))
            shortBrevo.setSubject(null);
        if (null == shortBrevo.getLanguageCode())
            shortBrevo.setLanguageCode("en-US");
        String appId = request.getHeader("NID");
        String uid = request.getHeader("UID");
        if (StringUtils.isNotEmpty(uid)) {
            // 处理uid前缀
            if (uid.length() > 4) {
                uid = uid.substring(4);
            }
        }
        shortBrevo.setUserId(uid);
        shortBrevo.setAppId(Long.valueOf(appId));
        ShortUser shortUser = shortUserService.selectShortUserById(Long.valueOf(shortBrevo.getUserId()));
        if (StringUtils.isNotEmpty(shortBrevo.getReceiveEmail())) {
            shortUser.setEmail(shortBrevo.getReceiveEmail());
        }
        if (StringUtils.isNotEmpty(shortBrevo.getPassword())) {
            shortUser.setPassword(shortBrevo.getPassword());
        }
        shortUserService.updateShortUser(shortUser);
        ShortMovie shortMovie = shortMovieService.selectShortMovieById(59L);
        shortBrevo.setAppId(shortUser.getAppId());
        shortBrevo.setUniqueId(shortUser.getUniqueId());

        ShortUser tempUser = shortUser;
        if (StringUtils.isNotEmpty(shortBrevo.getReceiveEmail())) {
            tempUser.setEmail(shortBrevo.getReceiveEmail());
        }
        businessFunctionApiService.createCustomers(tempUser);


        return businessFunctionApiService.sendWelcomeEmailForward(shortBrevo, shortMovie, "brevo");
    }

    @ApiOperation("重发")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "receiveEmail", value = "收件人邮箱", dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "userId", value = "收件人名称", dataType = "String", dataTypeClass = String.class),
    })
    @PostMapping("/reSend")
    public AjaxResult reSend(@RequestBody InitUserDTO shortBrevo, HttpServletRequest request) throws Exception {
        if (StringUtils.isEmpty(shortBrevo.getReceiveEmail()))
            return error("请填写收件人邮箱！");
        if ("".equals(shortBrevo.getReceiveName()))
            shortBrevo.setReceiveName(null);
        if ("".equals(shortBrevo.getSubject()))
            shortBrevo.setSubject(null);
        if (null == shortBrevo.getLanguageCode())
            shortBrevo.setLanguageCode("en-US");
        ShortUser shortUser = shortUserService.selectShortUserById(Long.valueOf(shortBrevo.getUserId()));
        if (null == shortUser)
            return error("没有该用户信息");
        shortBrevo.setAppId(shortUser.getAppId());
        shortBrevo.setUser_name(shortUser.getUsername());
        shortBrevo.setUniqueId(shortUser.getUniqueId());

        ShortMovie shortMovie = shortMovieService.getMovie1(shortBrevo.getAppId());
        String resLanguageCode = shortSemLinkService.getCanguageCodeByUserId(shortBrevo.getUserId());
        if (null != resLanguageCode)
            shortBrevo.setLanguageCode(resLanguageCode);
        return businessFunctionApiService.sendWelcomeEmailForward(shortBrevo, shortMovie, "brevo");

    }


    //营销发送邮件--定时   ----- 废弃
//    @GetMapping("/sendEmail")
//    public AjaxResult sendEmail(@RequestParam String publicKey) throws InterruptedException {
//        return businessFunctionApiService.sendEmail(publicKey);
//    }

    //订阅通知邮件--定时 -----Forward
    @GetMapping("/sendForwardEmail")
    public AjaxResult sendForwardEmail(@RequestParam String publicKey) throws InterruptedException {
        return businessFunctionApiService.sendForwardEmail(publicKey);
    }

    //营销发送邮件--定时
    @GetMapping("/sendBackRegEmail")
    public AjaxResult sendBackRegEmail(@RequestParam String publicKey) throws InterruptedException {
        return businessFunctionApiService.sendBackRegEmail(publicKey);
    }


    //订阅通知邮件--定时 -----brevo
    @GetMapping("/sendNoticeEmail")
    public AjaxResult sendNoticeEmail(@RequestParam String publicKey) throws JsonProcessingException {
        return businessFunctionApiService.sendNoticeEmail(publicKey);
    }

    //8.18号发送的，1897个续订通知邮件的
    //8.19改了下模板
    @GetMapping("/sendEmailRenewalByUserIds")
    public AjaxResult sendEmailRenewalByUserIds(@RequestParam String publicKey) throws JsonProcessingException {
        return businessFunctionApiService.sendEmailRenewalByUserIds(publicKey);
    }


    //自动回复无账户邮件--定时 -----brevo
    @GetMapping("/sendNullUserEmailBrevo")
    public AjaxResult sendNullUserEmailBrevo(@RequestParam String publicKey) throws JsonProcessingException {
        return businessFunctionApiService.sendNullUserEmailBrevo(publicKey);
    }

    //自动回复无账户邮件--定时 -----Forward
    @GetMapping("/sendNullUserEmailForward")
    public AjaxResult sendNullUserEmailForward(@RequestParam String publicKey) throws InterruptedException {
        return businessFunctionApiService.sendNullUserEmailForward(publicKey);
    }


    //每周一次的新剧上新邮件推送，
    @GetMapping("/sendWeeklyEmail")
    public AjaxResult sendWeeklyEmail(@RequestParam String publicKey) throws Exception {
        return businessFunctionApiService.sendWeeklyEmail(publicKey);
    }

    //订阅到期更改订阅状态
    @GetMapping("/updateUserIsSubscriber")
    public AjaxResult updateUserIsSubscriber(@RequestParam String publicKey) throws Exception {
        return businessFunctionApiService.updateUserIsSubscriber(publicKey);
    }

    //    监控发送邮件
    @GetMapping("/sendMonitorEmail")
    public AjaxResult sendMonitorEmail(@RequestParam String publicKey) throws Exception {
        return businessFunctionApiService.sendMonitorEmail(publicKey);
    }


    //新逻辑发送邮件，定时
    @GetMapping("/sendEmailRenewalRecord")
    public AjaxResult sendEmailRenewalRecord(@RequestParam String publicKey) throws JsonProcessingException {
        return businessFunctionApiService.sendEmailRenewalRecord(publicKey);
    }


}
