# **银行卡订单位数据统计 & 异常预警改造方案 (v3.0 简化版)**

## 一、功能点 & 需求总结

### 1. 数据统计

* **维度**：银行卡（脱敏格式存储，如 1234****5678）
* **统计指标**：
    * **会员第n周**累计消费：**次数 > 1 或 金额 > 40 美金 → 预警**
    * **会员第n月**累计消费：**次数 > 5 或 金额 > 100 美金 → 预警**
    * **注意**：基于该银行卡订阅开始时间动态计算周期，非自然周/月
* **绑定关系**：
    * 单卡多用户 → 预警
    * 单用户多卡 → 允许

### 2. 数据存储策略

* **最小化改动**：只修改 `short_order` 表添加卡号字段
* **新增3个独立统计表**：
    * 银行卡统计表：存储银行卡维度的累计数据
    * 告警表：存储预警记录
    * 阈值配置表：动态配置预警规则
* **异步处理机制**：统计数据通过定时任务从订单表同步更新

### 3. 异常识别

* **正常**：单卡单用户，会员周期内金额/次数在阈值内
* **异常**：
    * 单卡多用户绑定
    * 会员第n周/月统计超过动态阈值

### 4. 增量统计任务

* **执行频率**：每日 0:30（UTC+08:00）执行增量统计
* **处理策略**：
    * 从 `short_order` 表读取前一日订单数据
    * 动态计算「会员第n周/月」并更新统计表
    * 异步生成预警数据，支持重试机制

---

## 二、数据库设计（按原有结构）

### 1. 修改现有表：`short_order`（订单表）

```sql
ALTER TABLE short_order ADD COLUMN card_number VARCHAR(64) COMMENT '脱敏银行卡号';
```

### 2. 新建表：`bank_card_statistics`（银行卡统计表）

```sql
CREATE TABLE bank_card_statistics (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    card_number VARCHAR(64) NOT NULL COMMENT '脱敏卡号，例如 1234****5678',

    -- 主绑定用户
    user_id BIGINT COMMENT '用户ID',
    history_user_ids JSON COMMENT '使用过该卡的所有用户ID数组',
    related_users INT DEFAULT 1 COMMENT '历史累计的不同 user_id 数',
    status VARCHAR(20) DEFAULT 'active' COMMENT 'active / suspicious / blocked',

    -- 订阅（subscription）累计
    sub_total_amount DECIMAL(14,2) DEFAULT 0 COMMENT '订阅累计额',
    sub_total_count INT DEFAULT 0 COMMENT '订阅累计次数',

    -- 充值（recharge）累计
    rec_total_amount DECIMAL(14,2) DEFAULT 0 COMMENT '充值累计额',
    rec_total_count INT DEFAULT 0 COMMENT '充值累计次数',
    created_at DATETIME COMMENT '创建时间',

    last_update DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_card_number(card_number),
    INDEX idx_user_id(user_id)
);
```

### 3. 新建表：`bank_card_alerts`（告警存储）

```sql
CREATE TABLE bank_card_alerts (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    card_number VARCHAR(64) COMMENT '脱敏银行卡号，例如 1234****5678',
    alert_type VARCHAR(50) COMMENT '告警类型：week_overuse=周累计超限，month_overuse=月累计超限，multi_user=多用户共用',
    alert_detail TEXT COMMENT '告警详情，描述具体的超限情况，如累计次数、累计金额等',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '告警生成时间',
    processed_flag TINYINT DEFAULT 0 COMMENT '是否已处理：0=未处理，1=已处理',
    processed_at DATETIME COMMENT '处理时间',
    processed_by BIGINT COMMENT '处理人ID（后台管理员）'
);
```

### 4. 新建表：`bank_card_rules`（统计配置表）

```sql
CREATE TABLE bank_card_rules (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    rule_type VARCHAR(50) NOT NULL COMMENT '规则类型: week / month / multi_user',
    threshold_count INT DEFAULT NULL COMMENT '次数阈值，例如周>1次、月>5次',
    threshold_amount DECIMAL(14,2) DEFAULT NULL COMMENT '金额阈值，例如周>40美金、月>100美金',
    description VARCHAR(255) COMMENT '规则描述',
    is_active TINYINT DEFAULT 1 COMMENT '是否启用：1=启用，0=停用',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_rule_type(rule_type)
);

INSERT INTO bank_card_rules (rule_type, threshold_count, threshold_amount, description) VALUES
('week', 1, 40.00, '单周超过1次或40美金'),
('month', 5, 100.00, '单月超过5次或100美金'),
('multi_user', 0, NULL, '同一银行卡绑定多个用户');
```

---

## 三、核心逻辑实现

### 1. 会员周期动态计算器

```java
@Service
public class MemberCycleCalculator {

    /**
     * 计算用户当前是订阅的第几周
     */
    public int calculateCurrentWeek(Long userId, LocalDate targetDate) {
        LocalDate subscriptionStartDate = userService.getSubscriptionStartDate(userId);
        if (subscriptionStartDate == null) {
            return 1; // 新用户默认第1周
        }

        long daysBetween = ChronoUnit.DAYS.between(subscriptionStartDate, targetDate);
        return (int) (daysBetween / 7) + 1;
    }

    /**
     * 计算用户当前是订阅的第几月
     */
    public int calculateCurrentMonth(Long userId, LocalDate targetDate) {
        LocalDate subscriptionStartDate = userService.getSubscriptionStartDate(userId);
        if (subscriptionStartDate == null) {
            return 1;
        }

        return (int) ChronoUnit.MONTHS.between(subscriptionStartDate, targetDate) + 1;
    }
}
```

### 2. 每日增量统计任务

```java
@Component
public class BankCardDailyStatisticsTask {

    @Scheduled(cron = "0 30 0 * * ?") // 每日 0:30 执行
    public void executeDailyStatistics() {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        log.info("开始执行银行卡增量统计任务，目标日期: {}", yesterday);

        try {
            // 1. 从订单表获取昨日有银行卡号的订单
            List<Order> yesterdayOrders = orderService.getOrdersWithCardByDate(yesterday);

            if (yesterdayOrders.isEmpty()) {
                log.info("昨日无银行卡订单，跳过统计");
                return;
            }

            // 2. 按银行卡号分组处理
            Map<String, List<Order>> cardOrderMap = yesterdayOrders.stream()
                .filter(order -> StringUtils.isNotBlank(order.getCardNumber()))
                .collect(Collectors.groupingBy(Order::getCardNumber));

            // 3. 处理每张银行卡的统计
            for (Map.Entry<String, List<Order>> entry : cardOrderMap.entrySet()) {
                String cardNumber = entry.getKey();
                List<Order> cardOrders = entry.getValue();

                processCardStatistics(cardNumber, cardOrders, yesterday);
            }

            log.info("银行卡增量统计任务完成，处理银行卡数量: {}", cardOrderMap.size());

        } catch (Exception e) {
            log.error("银行卡增量统计任务执行失败", e);
        }
    }

    /**
     * 处理单张银行卡的统计更新
     */
    @Transactional
    private void processCardStatistics(String cardNumber, List<Order> orders, LocalDate targetDate) {
        // 1. 获取或创建银行卡统计记录
        BankCardStatistics cardStats = getOrCreateCardStatistics(cardNumber);

        // 2. 按用户分组处理（一张卡可能被多个用户使用）
        Map<Long, List<Order>> userOrderMap = orders.stream()
            .collect(Collectors.groupingBy(Order::getUserId));

        // 3. 更新用户关联信息
        updateUserAssociation(cardStats, userOrderMap.keySet());

        // 4. 按订单类型累计统计
        for (Order order : orders) {
            String orderType = order.getOrderType(); // subscription 或 recharge
            BigDecimal amount = order.getAmount();

            if ("subscription".equals(orderType)) {
                cardStats.setSubTotalCount(cardStats.getSubTotalCount() + 1);
                cardStats.setSubTotalAmount(cardStats.getSubTotalAmount().add(amount));
            } else if ("recharge".equals(orderType)) {
                cardStats.setRecTotalCount(cardStats.getRecTotalCount() + 1);
                cardStats.setRecTotalAmount(cardStats.getRecTotalAmount().add(amount));
            }
        }

        // 5. 更新最后更新时间
        cardStats.setLastUpdate(LocalDateTime.now());

        // 6. 保存统计数据
        cardStatisticsService.updateStatistics(cardStats);

        // 7. 检查预警阈值（基于会员周期）
        checkAndCreateAlerts(cardNumber, cardStats, targetDate);
    }

    /**
     * 更新银行卡关联用户信息
     */
    private void updateUserAssociation(BankCardStatistics cardStats, Set<Long> userIds) {
        // 解析历史用户ID列表
        Set<Long> historyUserIds = parseUserIds(cardStats.getHistoryUserIds());

        // 添加新用户ID
        historyUserIds.addAll(userIds);

        // 更新统计信息
        cardStats.setHistoryUserIds(JSON.toJSONString(historyUserIds));
        cardStats.setRelatedUsers(historyUserIds.size());

        // 如果是首次使用，设置主用户
        if (cardStats.getUserId() == null && !userIds.isEmpty()) {
            cardStats.setUserId(userIds.iterator().next());
        }
    }

    /**
     * 解析用户ID列表
     */
    private Set<Long> parseUserIds(String historyUserIdsJson) {
        if (StringUtils.isBlank(historyUserIdsJson)) {
            return new HashSet<>();
        }

        try {
            return JSON.parseArray(historyUserIdsJson, Long.class)
                .stream().collect(Collectors.toSet());
        } catch (Exception e) {
            log.warn("解析用户ID列表失败: {}", historyUserIdsJson, e);
            return new HashSet<>();
        }
    }
}
```

### 3. 预警检测引擎

```java
@Service
public class BankCardAlertService {

    /**
     * 检查银行卡是否触发预警
     */
    public void checkAndCreateAlerts(String cardNumber, BankCardStatistics cardStats, LocalDate targetDate) {

        // 1. 检查多用户绑定预警
        checkMultiUserAlert(cardNumber, cardStats);

        // 2. 检查会员周期阈值（基于主用户的订阅周期）
        if (cardStats.getUserId() != null) {
            checkMemberCycleAlerts(cardNumber, cardStats, targetDate);
        }
    }

    /**
     * 检查多用户绑定预警
     */
    private void checkMultiUserAlert(String cardNumber, BankCardStatistics cardStats) {
        if (cardStats.getRelatedUsers() > 1) {
            String alertDetail = String.format("银行卡 %s 被 %d 个用户使用：%s",
                cardNumber, cardStats.getRelatedUsers(), cardStats.getHistoryUserIds());

            createAlert(cardNumber, "multi_user", alertDetail);
        }
    }

    /**
     * 检查会员周期阈值
     */
    private void checkMemberCycleAlerts(String cardNumber, BankCardStatistics cardStats, LocalDate targetDate) {
        Long primaryUserId = cardStats.getUserId();

        // 计算当前会员周期
        int currentWeek = cycleCalculator.calculateCurrentWeek(primaryUserId, targetDate);
        int currentMonth = cycleCalculator.calculateCurrentMonth(primaryUserId, targetDate);

        // 获取该会员周期内的统计数据
        WeekMonthStatistics weekStats = calculateWeekStatistics(cardNumber, primaryUserId, currentWeek, targetDate);
        WeekMonthStatistics monthStats = calculateMonthStatistics(cardNumber, primaryUserId, currentMonth, targetDate);

        // 检查周阈值
        checkWeekThreshold(cardNumber, currentWeek, weekStats);

        // 检查月阈值
        checkMonthThreshold(cardNumber, currentMonth, monthStats);
    }

    /**
     * 计算指定会员周的统计数据
     */
    private WeekMonthStatistics calculateWeekStatistics(String cardNumber, Long userId, int weekNumber, LocalDate targetDate) {
        // 计算会员第n周的日期范围
        LocalDate subscriptionStart = userService.getSubscriptionStartDate(userId);
        LocalDate weekStart = subscriptionStart.plusDays((weekNumber - 1) * 7L);
        LocalDate weekEnd = weekStart.plusDays(6);

        // 从订单表统计该周期内的数据
        return orderService.getStatisticsByCardAndDateRange(cardNumber, weekStart,
            weekEnd.isAfter(targetDate) ? targetDate : weekEnd);
    }

    /**
     * 计算指定会员月的统计数据
     */
    private WeekMonthStatistics calculateMonthStatistics(String cardNumber, Long userId, int monthNumber, LocalDate targetDate) {
        // 计算会员第n月的日期范围
        LocalDate subscriptionStart = userService.getSubscriptionStartDate(userId);
        LocalDate monthStart = subscriptionStart.plusMonths(monthNumber - 1);
        LocalDate monthEnd = monthStart.plusMonths(1).minusDays(1);

        // 从订单表统计该月期内的数据
        return orderService.getStatisticsByCardAndDateRange(cardNumber, monthStart,
            monthEnd.isAfter(targetDate) ? targetDate : monthEnd);
    }

    /**
     * 检查周阈值
     */
    private void checkWeekThreshold(String cardNumber, int weekNumber, WeekMonthStatistics weekStats) {
        BankCardRule weekRule = ruleService.getRuleByType("week");
        if (weekRule == null || !weekRule.getIsActive()) return;

        boolean countExceeded = weekStats.getOrderCount() > weekRule.getThresholdCount();
        boolean amountExceeded = weekStats.getTotalAmount().compareTo(weekRule.getThresholdAmount()) > 0;

        if (countExceeded || amountExceeded) {
            String alertDetail = String.format(
                "会员第%d周超限：实际次数=%d(阈值=%d)，实际金额=%.2f(阈值=%.2f)",
                weekNumber, weekStats.getOrderCount(), weekRule.getThresholdCount(),
                weekStats.getTotalAmount(), weekRule.getThresholdAmount()
            );

            createAlert(cardNumber, "week_overuse", alertDetail);
        }
    }

    /**
     * 检查月阈值
     */
    private void checkMonthThreshold(String cardNumber, int monthNumber, WeekMonthStatistics monthStats) {
        BankCardRule monthRule = ruleService.getRuleByType("month");
        if (monthRule == null || !monthRule.getIsActive()) return;

        boolean countExceeded = monthStats.getOrderCount() > monthRule.getThresholdCount();
        boolean amountExceeded = monthStats.getTotalAmount().compareTo(monthRule.getThresholdAmount()) > 0;

        if (countExceeded || amountExceeded) {
            String alertDetail = String.format(
                "会员第%d月超限：实际次数=%d(阈值=%d)，实际金额=%.2f(阈值=%.2f)",
                monthNumber, monthStats.getOrderCount(), monthRule.getThresholdCount(),
                monthStats.getTotalAmount(), monthRule.getThresholdAmount()
            );

            createAlert(cardNumber, "month_overuse", alertDetail);
        }
    }

    /**
     * 创建告警记录
     */
    private void createAlert(String cardNumber, String alertType, String alertDetail) {
        // 避免重复告警（24小时内同类型告警只生成一次）
        if (alertExists(cardNumber, alertType, LocalDateTime.now().minusHours(24))) {
            return;
        }

        BankCardAlert alert = new BankCardAlert();
        alert.setCardNumber(cardNumber);
        alert.setAlertType(alertType);
        alert.setAlertDetail(alertDetail);
        alert.setCreatedAt(LocalDateTime.now());
        alert.setProcessedFlag(0);

        alertService.createAlert(alert);

        // 发送实时通知
        notificationService.sendAlertNotification(alert);
    }

    /**
     * 检查告警是否已存在
     */
    private boolean alertExists(String cardNumber, String alertType, LocalDateTime since) {
        return alertService.existsAlert(cardNumber, alertType, since);
    }
}
```

### 4. 统计数据结构

```java
// 周/月统计数据结构
@Data
public class WeekMonthStatistics {
    private int orderCount;           // 订单次数
    private BigDecimal totalAmount;   // 总金额
    private int subscriptionCount;    // 订阅次数
    private BigDecimal subscriptionAmount; // 订阅金额
    private int rechargeCount;        // 充值次数
    private BigDecimal rechargeAmount;     // 充值金额
}

// 银行卡统计实体
@Data
@Entity
@Table(name = "bank_card_statistics")
public class BankCardStatistics {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "card_number")
    private String cardNumber;

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "history_user_ids")
    private String historyUserIds;

    @Column(name = "related_users")
    private Integer relatedUsers;

    @Column(name = "status")
    private String status;

    @Column(name = "sub_total_amount")
    private BigDecimal subTotalAmount;

    @Column(name = "sub_total_count")
    private Integer subTotalCount;

    @Column(name = "rec_total_amount")
    private BigDecimal recTotalAmount;

    @Column(name = "rec_total_count")
    private Integer recTotalCount;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "last_update")
    private LocalDateTime lastUpdate;
}
```

---

## 四、实施计划（最小化改动版）

### **第1周：数据库改造**
1. 修改 `short_order` 表添加 `card_number` 字段
2. 创建3个新表：统计表、告警表、阈值配置表
3. 支付回调集成银行卡号写入

### **第2周：统计逻辑开发**
1. 开发会员周期动态计算器
2. 开发每日增量统计任务
3. 开发预警检测引擎

### **第3周：前端 & 测试**
1. 开发银行卡监控页面
2. 开发预警管理界面
3. 功能测试和性能测试

### **第4周：上线部署**
1. 数据迁移和初始化
2. 生产环境部署
3. 监控和调优

---

## 五、成功标准

### **功能标准**
- ✅ 支持基于用户订阅时间的会员第n周/月统计
- ✅ 每日增量统计任务稳定运行
- ✅ 预警系统准确识别异常行为
- ✅ 最小化对现有系统的影响

### **性能标准**
- ✅ 支付回调处理时间增加 < 10ms
- ✅ 每日统计任务执行时间 < 10分钟
- ✅ 统计数据准确率 > 99.9%

**Leo，这个v3.0版本按你的要求：最小化数据库改动，只新增3个表+订单表添加卡号字段。核心逻辑简化，通过定时任务从现有订单表同步统计数据。这样改动最小，风险最低！**