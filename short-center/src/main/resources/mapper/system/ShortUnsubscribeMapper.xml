<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.ShortUnsubscribeMapper">

    <resultMap type="ShortUnsubscribe" id="ShortUnsubscribeResult">
        <result property="id"    column="id"    />
        <result property="appId"    column="app_id"    />
        <result property="userId"    column="user_id"    />
        <result property="content"    column="content"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="addtime"    column="addtime"    />
        <result property="email"    column="email"    />
        <result property="type"    column="type"    />
        <result property="replyType"    column="reply_type"    />
    </resultMap>

    <sql id="selectShortUnsubscribeVo">
        select id, app_id, user_id, content, status, create_by, create_time, update_by, update_time, remark, addtime, email, type, reply_type from short_unsubscribe
    </sql>

    <select id="selectShortUnsubscribeList" parameterType="ShortUnsubscribe" resultMap="ShortUnsubscribeResult">
        SELECT
        sf.id,
        sf.app_id,
        sf.user_id,
        sf.content,
        sf.STATUS,
        sf.create_by,
        sf.create_time,
        sf.update_by,
        sf.update_time,
        sf.remark,
        sf.addtime,
        sf.email,
        sf.type,
        sf.reply_type,
        se.domain,
        su.is_subscriber isSubscriber
        FROM
        short_unsubscribe sf
        LEFT JOIN short_user su on su.id = sf.user_id
        LEFT JOIN short_email_domain se on se.app_id = sf.app_id
        <where>
            <if test="appId != null "> and sf.app_id = #{appId}</if>
            <if test="userId != null "> and sf.user_id = #{userId}</if>
            <if test="content != null  and content != ''"> and sf.content = #{content}</if>
            <if test="status != null  and status != ''"> and sf.status = #{status}</if>
            <if test="addtime != null "> and sf.addtime = #{addtime}</if>
            <if test="email != null  and email != ''"> and sf.email = #{email}</if>
            <if test="type != null  and type != ''"> and sf.type = #{type}</if>
            <if test="replyType != null "> and sf.reply_type = #{replyType}</if>
        </where>
    </select>

    <select id="selectShortUnsubscribeById" parameterType="Long" resultMap="ShortUnsubscribeResult">
        <include refid="selectShortUnsubscribeVo"/>
        where id = #{id}
    </select>

    <insert id="insertShortUnsubscribe" parameterType="ShortUnsubscribe" useGeneratedKeys="true" keyProperty="id">
        insert into short_unsubscribe
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appId != null">app_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="content != null">content,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="addtime != null">addtime,</if>
            <if test="email != null">email,</if>
            <if test="type != null">type,</if>
            <if test="replyType != null">reply_type,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appId != null">#{appId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="content != null">#{content},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="addtime != null">#{addtime},</if>
            <if test="email != null">#{email},</if>
            <if test="type != null">#{type},</if>
            <if test="replyType != null">#{replyType},</if>
        </trim>
    </insert>

    <update id="updateShortUnsubscribe" parameterType="ShortUnsubscribe">
        update short_unsubscribe
        <trim prefix="SET" suffixOverrides=",">
            <if test="appId != null">app_id = #{appId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="content != null">content = #{content},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="addtime != null">addtime = #{addtime},</if>
            <if test="email != null">email = #{email},</if>
            <if test="type != null">type = #{type},</if>
            <if test="replyType != null">reply_type = #{replyType},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteShortUnsubscribeById" parameterType="Long">
        delete from short_unsubscribe where id = #{id}
    </delete>

    <delete id="deleteShortUnsubscribeByIds" parameterType="String">
        delete from short_unsubscribe where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>