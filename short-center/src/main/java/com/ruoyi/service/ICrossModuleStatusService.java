package com.ruoyi.service;

import com.ruoyi.domain.dto.CrossModuleStatusDto;

import java.util.List;
import java.util.Map;

/**
 * 跨模块状态查询服务接口
 *
 * <AUTHOR>
 * @date 2025-09-27
 */
public interface ICrossModuleStatusService {

    /**
     * 根据邮箱和应用ID获取跨模块状态
     *
     * @param email 邮箱
     * @param appId 应用ID
     * @return 跨模块状态
     */
    CrossModuleStatusDto getCrossModuleStatus(String email, Long appId);

    /**
     * 批量获取跨模块状态（性能优化版本）
     *
     * @param emails 邮箱列表
     * @param appId 应用ID
     * @return 邮箱与状态的映射
     */
    Map<String, CrossModuleStatusDto> getBatchCrossModuleStatus(List<String> emails, Long appId);
}