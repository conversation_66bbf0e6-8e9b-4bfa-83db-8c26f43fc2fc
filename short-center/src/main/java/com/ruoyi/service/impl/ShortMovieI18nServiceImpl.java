package com.ruoyi.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.*;
import com.ruoyi.domain.*;
import com.ruoyi.dto.ShortAppLanguageDTO;
import com.ruoyi.dto.ShortMovieI18nDTO;
import com.ruoyi.dto.ShortMovieI18nIconDTO;
import com.ruoyi.dto.ShortMovieI18nInDTO;
import com.ruoyi.mapper.*;
import com.ruoyi.service.IShortMovieI18nService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * 影片多语言Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Service
public class ShortMovieI18nServiceImpl implements IShortMovieI18nService {

    protected final Logger logger = LoggerFactory.getLogger(ShortMovieI18nServiceImpl.class);

    @Autowired
    private ShortMovieI18nMapper shortMovieI18nMapper;

    @Autowired
    private ShortMovieMapper shortMovieMapper;

    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Autowired
    private ShortAppLanguageMapper shortAppLanguageMapper;

    @Autowired
    private ShortVideoMapper shortVideoMapper;

    @Autowired
    private ShortVideoI18nMapper shortVideoI18nMapper;

    /**
     * 查询影片多语言
     *
     * @param id 影片多语言主键
     * @return 影片多语言
     */
    @Override
    public ShortMovieI18n selectShortMovieI18nById(Long id) {
        return shortMovieI18nMapper.selectShortMovieI18nById(id);
    }

    /**
     * 查询影片多语言列表
     *
     * @param shortMovieI18n 影片多语言
     * @return 影片多语言
     */
    @Override
    public List<ShortMovieI18n> selectShortMovieI18nList(ShortMovieI18n shortMovieI18n) {
        return shortMovieI18nMapper.selectShortMovieI18nList(shortMovieI18n);
    }

    /**
     * 新增影片多语言
     *
     * @param shortMovieI18n 影片多语言
     * @return 结果
     */
    @Override
    public int insertShortMovieI18n(ShortMovieI18n shortMovieI18n) {
        shortMovieI18n.setCreateTime(DateUtils.getNowDate());
        return shortMovieI18nMapper.insertShortMovieI18n(shortMovieI18n);
    }

    /**
     * 修改影片多语言
     *
     * @param shortMovieI18n 影片多语言
     * @return 结果
     */
    @Override
    public int updateShortMovieI18n(ShortMovieI18n shortMovieI18n) {
        shortMovieI18n.setUpdateTime(DateUtils.getNowDate());
        return shortMovieI18nMapper.updateShortMovieI18n(shortMovieI18n);
    }

    /**
     * 批量删除影片多语言
     *
     * @param ids 需要删除的影片多语言主键
     * @return 结果
     */
    @Override
    public int deleteShortMovieI18nByIds(Long[] ids) {
        return shortMovieI18nMapper.deleteShortMovieI18nByIds(ids);
    }

    /**
     * 删除影片多语言信息
     *
     * @param id 影片多语言主键
     * @return 结果
     */
    @Override
    public int deleteShortMovieI18nById(Long id) {
        return shortMovieI18nMapper.deleteShortMovieI18nById(id);
    }

    @Async
    @Override
    public void translateMovie(ShortMovieI18nDTO shortMovieI18nDTO) throws RuntimeException {
        ShortMovieI18n shortMovieI18n = new ShortMovieI18n();
        shortMovieI18n.setMovieId(shortMovieI18nDTO.getMovieId());
        shortMovieI18n.setLanguageCode(shortMovieI18nDTO.getLanguageCode());
        List<ShortMovieI18n> shortMovieI18ns = shortMovieI18nMapper.selectShortMovieI18nList(shortMovieI18n);
        if (CollectionUtil.isNotEmpty(shortMovieI18ns)) {
            logger.info("该短剧{}已翻译", shortMovieI18nDTO.getLanguage());
            return;
        }
        try {
            //查询短剧
            ShortMovie shortMovie = shortMovieMapper.selectShortMovieById(shortMovieI18nDTO.getMovieId());
            String language = shortMovieI18nDTO.getLanguage();
            if (null != shortMovie) {
                shortMovieI18n.setMovieId(shortMovie.getId());
                String[] keys = new String[]{"name", "director", "actors", "description", "content"};
                CountDownLatch countDownLatch = new CountDownLatch(keys.length);
                for (String key : keys) {
                    threadPoolTaskExecutor.execute(() -> {
                        logger.info(Thread.currentThread().getName() + "线程：开始翻译：{}", key);
                        try {
                            String translate = getI18N(shortMovie.get(key), language);
                            //如果翻译 translate 为空则 重试3次
                            int count = 3;
                            while (StringUtils.isEmpty(translate) && count > 0) {
                                translate = getI18N(shortMovie.get(key), language);
                                //重试之后休眠200毫秒
                                Thread.sleep(200);
                                count--;
                            }
                            shortMovieI18n.set(key, translate);
                        } catch (Exception e) {
                            e.printStackTrace();
                        } finally {
                            countDownLatch.countDown();
                        }
                    });
                }
                countDownLatch.await();
                shortMovieI18n.setLanguageCode(shortMovieI18nDTO.getLanguageCode());
                shortMovieI18n.setCreateTime(DateUtils.getNowDate());
                shortMovieI18n.setUpdateTime(DateUtils.getNowDate());
                shortMovieI18n.setCreateBy(String.valueOf(shortMovieI18nDTO.getUserId()));
                if ("en-US".equals(shortMovieI18n.getLanguageCode())) {
                    shortMovieI18n.setIcon(shortMovie.getIcon());
                }else {
                    shortMovieI18n.setIcon(shortMovie.getIconNoWord());
                }
                int i = shortMovieI18nMapper.insertShortMovieI18n(shortMovieI18n);
                if (i > 0) {
                    logger.info("短剧ID: {} 的 {} 翻译已完成", shortMovieI18nDTO.getMovieId(), shortMovieI18nDTO.getLanguage());
                    SseEmitterUtils.sendMessageToUser(shortMovieI18nDTO.getUserId(), "短剧ID: " + shortMovieI18nDTO.getMovieId() + " 的 " + shortMovieI18nDTO.getLanguage() + " 翻译已完成");
                }
            } else {
                throw new RuntimeException("短剧不存在，请联系开发人员");
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public List<ShortMovieI18n> queryTranslateMovieList(Long movieId, String languageCode) {
        ShortMovieI18n query = new ShortMovieI18n();
        query.setMovieId(movieId);
        query.setLanguageCode(languageCode);
        return shortMovieI18nMapper.selectShortMovieI18nList(query);
    }

    @Async
    @Override
    public void translateAllMovie() {
        //查询所有剧
        List<ShortMovie> shortMovies = shortMovieMapper.selectShortMovieList(new ShortMovie());
        //翻译为各种语言的剧
        List<ShortAppLanguage> langList = shortAppLanguageMapper.selectShortAppLanguageList(new ShortAppLanguage());
        //嵌套循环翻译剧的相关属性
        for (ShortMovie shortMovie : shortMovies) {
            for (ShortAppLanguage shortAppLanguage : langList) {
                ShortMovieI18nDTO shortMovieI18nDTO = new ShortMovieI18nDTO();
                shortMovieI18nDTO.setMovieId(shortMovie.getId());
                shortMovieI18nDTO.setLanguageCode(shortAppLanguage.getCode());
                shortMovieI18nDTO.setLanguage(shortAppLanguage.getZhName());
                shortMovieI18nDTO.setUserId(1L);
                translateMovie(shortMovieI18nDTO);
            }
        }
    }

    @Override
    public AjaxResult updateState(ShortMovieI18n shortMovieI18n) {
        //只有是通过状态才校验  查询短剧下这个语种的视频字母是否翻译完成
        if (shortMovieI18n.getStatus().equals("1")) {
            Long movieId = shortMovieI18n.getId();
            ShortMovieI18n shortMovieI18nQuery = new ShortMovieI18n();
            shortMovieI18nQuery.setMovieId(movieId);
            shortMovieI18nQuery.setLanguageCode(shortMovieI18n.getLanguageCode());
            List<ShortMovieI18n> shortMovieI18nList = shortMovieI18nMapper.selectShortMovieI18nList(shortMovieI18nQuery);
            List<ShortAppLanguage> langList = shortAppLanguageMapper.selectShortAppLanguageList(new ShortAppLanguage());
            Map<String, String> languageCodeMap = langList.stream().collect(Collectors.toMap(ShortAppLanguage::getCode, ShortAppLanguage::getZhName));
            if (CollectionUtil.isNotEmpty(shortMovieI18nList)) {
                for (ShortMovieI18n movieI18n : shortMovieI18nList) {
                    if (StringUtils.isEmpty(movieI18n.getIcon())) {
                        throw new RuntimeException("短剧" + languageCodeMap.get(movieI18n.getLanguageCode()) + "版本封面未上传");
                    }
                }
            }
            List<String> languageCodeCollect = shortMovieI18nList.stream().map(ShortMovieI18n::getLanguageCode).collect(Collectors.toList());
            ShortVideo shortVideoQuery = new ShortVideo();
            shortVideoQuery.setMovieId(movieId);
            //原视频
            List<ShortVideo> shortVideoList = shortVideoMapper.selectShortVideoList(shortVideoQuery);
            List<Long> videoIdList = shortVideoList.stream().map(ShortVideo::getId).collect(Collectors.toList());
            //查询翻译后的视频字幕表
            List<ShortVideoI18n> shortVideoI18nList = shortVideoI18nMapper.selectShortVideoI18nByVideoIds(videoIdList, languageCodeCollect);
            int num = videoIdList.size();
            if (CollectionUtil.isEmpty(shortVideoI18nList)) {
                throw new RuntimeException("短剧" + languageCodeMap.get(shortMovieI18nList.iterator().next().getLanguageCode()) + "版本字幕未上传");
            }
            //判断某个语言的数量和原视频不一致则抛异常
            if (num != shortVideoI18nList.size()) {
                List<Integer> videoNumList = new ArrayList<>();
                Map<Long, Integer> videoNumMap = shortVideoList.stream().collect(Collectors.toMap(ShortVideo::getId, ShortVideo::getNum));
                List<Long> videoI18nVideoIdList = shortVideoI18nList.stream().map(ShortVideoI18n::getVideoId).collect(Collectors.toList());
                videoNumMap.forEach((videoId, videoNum) -> {
                    if (!videoI18nVideoIdList.contains(videoId)) {
                        videoNumList.add(videoNumMap.get(videoId));
                    }
                });
                //将 videoNumList 转为字符串用 , 拼接
                String videoNumStr = videoNumList.stream().map(String::valueOf).collect(Collectors.joining(","));
                throw new RuntimeException("短剧的字幕" + languageCodeMap.get(shortMovieI18nList.iterator().next().getLanguageCode()) + "版本翻译的第" + videoNumStr + "集未完成");
            }
        }
        //根据videoId查询 已翻译的短句字幕
        ShortMovieI18n update = new ShortMovieI18n();
        update.setMovieId(shortMovieI18n.getId());
        update.setLanguageCode(shortMovieI18n.getLanguageCode());
        update.setStatus(shortMovieI18n.getStatus());
        update.setUpdateBy(String.valueOf(SecurityUtils.getUserId()));
        update.setUpdateTime(DateUtils.getNowDate());
        int i = shortMovieI18nMapper.updateShortMovieI18nByMovieId(update);
        return i > 0 ? AjaxResult.success() : AjaxResult.error();
    }

    @Override
    public List<ShortMovieI18n> queryTranslateMovieList(List<Long> movieIdList, String languageCode) {
        if (CollectionUtil.isEmpty(movieIdList) || StringUtils.isEmpty(languageCode)) {
            return Collections.emptyList();
        }
        return shortMovieI18nMapper.selectByMovieIdListAndLanguageCode(movieIdList, languageCode);
    }

    @Override
    public int deleteShortMovieI18n(Long movieId, String languageCode) {
        return shortMovieI18nMapper.deleteShortMovieI18n(movieId, languageCode);
    }

    @Override
    public List<ShortAppLanguage> queryAccessTranslateLanguageList(Long movieId) {
        List<ShortAppLanguage> langList = shortAppLanguageMapper.selectShortAppLanguageList(new ShortAppLanguage());
        List<String> languageCodeCollect = langList.stream().map(ShortAppLanguage::getCode).collect(Collectors.toList());
        ShortVideo shortVideoQuery = new ShortVideo();
        shortVideoQuery.setMovieId(movieId);
        //原视频
        List<ShortVideo> shortVideoList = shortVideoMapper.selectShortVideoList(shortVideoQuery);
        List<Long> videoIdList = shortVideoList.stream().map(ShortVideo::getId).collect(Collectors.toList());
        //查询翻译后的视频字幕表
        List<ShortVideoI18n> shortVideoI18nList = shortVideoI18nMapper.selectShortVideoI18nByVideoIds(videoIdList, languageCodeCollect);
        if (CollectionUtil.isNotEmpty(shortVideoI18nList)) {
            List<String> collect = shortVideoI18nList.stream().map(ShortVideoI18n::getLanguageCode).distinct().collect(Collectors.toList());
            return langList.stream().filter(item -> collect.contains(item.getCode())).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Async
    @Override
    public void batchTranslateMovie(ShortMovieI18nInDTO shortMovieI18nInDTO) {
        //翻译为各种语言的剧
        List<ShortAppLanguageDTO> languageCodeList = shortMovieI18nInDTO.getLanguageList();
        if (CollectionUtil.isNotEmpty(languageCodeList)) {
            //嵌套循环翻译剧的相关属性
            try {
                for (ShortAppLanguageDTO shortAppLanguage : languageCodeList) {
                    ShortMovieI18nDTO shortMovieI18nDTO = new ShortMovieI18nDTO();
                    shortMovieI18nDTO.setMovieId(shortMovieI18nInDTO.getMovieId());
                    shortMovieI18nDTO.setLanguageCode(shortAppLanguage.getLanguageCode());
                    shortMovieI18nDTO.setLanguage(shortAppLanguage.getLanguage());
                    shortMovieI18nDTO.setUserId(shortMovieI18nInDTO.getUserId());
                    translateMovie(shortMovieI18nDTO);
                    //每次执行完一部剧的翻译分发 休眠1秒
                    Thread.sleep(1000);
                }
            } catch (Exception e) {
                logger.error("批量翻译出错", e);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult batchUpdateState(Long movieId) {
        //根据movieId查询所有已分发的剧
        ShortMovieI18n query = new ShortMovieI18n();
        query.setMovieId(movieId);
        query.setStatus("0");
        List<ShortMovieI18n> shortMovieI18nList = shortMovieI18nMapper.selectShortMovieI18nList(query);
        if (CollectionUtil.isNotEmpty(shortMovieI18nList)) {
            shortMovieI18nList.forEach(item -> {
                item.setStatus("1");
                //当时单个审批时 id 设置的是movieId
                item.setId(movieId);
                AjaxResult ajaxResult = this.updateState(item);
                if (!ajaxResult.isSuccess()) {
                    throw new RuntimeException("批量更新状态失败");
                }
            });
        }
        return AjaxResult.success();
    }

    @Override
    public void batchUpdateIcon(List<ShortMovieI18nIconDTO> iconDTOS, Long movieId) {
        List<ShortMovieI18n> shortMovieI18ns = shortMovieI18nMapper.selectByMovieIds(Collections.singletonList(movieId));
        for (ShortMovieI18nIconDTO iconDTO : iconDTOS) {
            shortMovieI18ns.stream()
                    .filter(i18n -> i18n.getLanguageCode().equals(iconDTO.getLanguageCode()))
                    .forEach(i18n -> {
                                i18n.setIcon(iconDTO.getIcon());
                                i18n.setUpdateBy(String.valueOf(SecurityUtils.getUserId()));
                                i18n.setUpdateTime(new Date());
                            }
                    );
        }
        shortMovieI18nMapper.batchUpdateShortMovieI18n(shortMovieI18ns);
    }

    @Override
    public List<ShortMovieI18n> selectByMovieIdList(List<Long> movieIdList) {
        if (CollectionUtil.isEmpty(movieIdList)) {
            return Collections.emptyList();
        }
        List<ShortMovieI18n> shortMovieI18ns = shortMovieI18nMapper.selectByMovieIds(movieIdList);
        if (CollectionUtil.isNotEmpty(shortMovieI18ns)) {
            //杀你选出已完成审核的剧
            return shortMovieI18ns.stream().filter(item -> item.getStatus().equals("1")).collect(Collectors.toList());
        }
        return shortMovieI18ns;
    }

    private static String getI18N(String text, String language) throws IOException {
        return StringUtils.isNotEmpty(text) ? Gemini25FlashUtils.translate(text, language) : text;
    }
}
