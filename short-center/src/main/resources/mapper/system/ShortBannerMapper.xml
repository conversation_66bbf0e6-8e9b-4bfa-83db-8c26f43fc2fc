<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.ShortBannerMapper">
    
    <resultMap type="ShortBanner" id="ShortBannerResult">
        <result property="id"    column="id"    />
        <result property="image"    column="image"    />
        <result property="addtime"    column="addtime"    />
        <result property="appId"    column="app_id"    />
        <result property="movieId"    column="movie_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="languageCode" column="language_code"/>
        <result property="movieName" column="movieName"/>
        <result property="language" column="language"/>
        <result property="originalImage" column="original_image"/>
    </resultMap>

    <sql id="selectShortBannerVo">
        select id, image, addtime, app_id, movie_id, create_by, create_time, update_by, update_time, remark,language_code,original_image  from short_banner
    </sql>

    <select id="selectShortBannerList" parameterType="ShortBanner" resultMap="ShortBannerResult">
        SELECT
        sb.id,
        sb.image,
        sb.addtime,
        sb.app_id,
        sb.movie_id,
        sb.original_image,
        sa.`name` appName,
        sm.`name` movieName,
        sb.create_by ,
        sb.create_time ,
        sb.update_by  ,
        sb.update_time ,
        sb.remark,
        sb.language_code,
        sl.zh_name as language
        FROM
        short_banner sb
        LEFT JOIN short_app sa on sb.app_id =sa.id
        LEFT JOIN short_movie sm on sb.movie_id = sm.id
        left join short_app_language sl on sb.language_code = sl.code
        <where>  
            <if test="image != null  and image != ''"> and sb.image = #{image}</if>
            <if test="addtime != null "> and sb.addtime = #{addtime}</if>
            <if test="appId != null "> and sb.app_id = #{appId}</if>
            <if test="movieId != null "> and sb.movie_id = #{movieId}</if>
            <if test="languageCode != null  and languageCode != ''"> and sb.language_code = #{languageCode}</if>
        </where>
        ORDER BY sb.id DESC
    </select>
    
    <select id="selectShortBannerById" parameterType="Long" resultMap="ShortBannerResult">
        <include refid="selectShortBannerVo"/>
        where id = #{id}
    </select>
    <select id="getBannerListByAppId" resultType="com.ruoyi.domain.ShortBanner">
        SELECT
            sb.id,
            sb.image,
            sb.addtime,
            sb.app_id,
            sb.movie_id,
            sb.create_by,
            sb.create_time,
            sb.update_by,
            sb.update_time,
            sb.remark,
            sb.language_code,
            sb.original_image,
            sm.name movieName,
            sm.description movieDescription,
            sm.num movieNum
        FROM
            short_banner sb LEFT JOIN short_movie sm on sb.movie_id = sm.id
        where sb.app_id = #{appId}
    </select>
    <select id="getBannerListByAppIdAndLanguage" resultType="com.ruoyi.domain.ShortBanner">
        SELECT
            sb.id,
            sb.image,
            sb.addtime,
            sb.app_id,
            sb.movie_id,
            sb.create_by,
            sb.create_time,
            sb.update_by,
            sb.update_time,
            sb.remark,
            sb.language_code,
            sb.original_image,
            sm.NAME movieName,
            sm.description movieDescription,
            sm.num movieNum
        FROM
            short_banner sb
                LEFT JOIN short_movie sm ON sb.movie_id = sm.id
        WHERE
            sb.app_id = #{appId}
           AND sb.language_code = #{languageCode}
    </select>

    <insert id="insertShortBanner" parameterType="ShortBanner" useGeneratedKeys="true" keyProperty="id">
        insert into short_banner
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="image != null">image,</if>
            <if test="addtime != null">addtime,</if>
            <if test="appId != null">app_id,</if>
            <if test="movieId != null">movie_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="languageCode != null">language_code,</if>
            <if test="originalImage != null">original_image,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="image != null">#{image},</if>
            <if test="addtime != null">#{addtime},</if>
            <if test="appId != null">#{appId},</if>
            <if test="movieId != null">#{movieId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="languageCode != null">#{languageCode},</if>
            <if test="originalImage != null">#{originalImage},</if>
         </trim>
    </insert>
    <insert id="insertShortBannerList">
        insert into short_banner (image,addtime,app_id,movie_id,create_by,create_time,update_by,update_time,remark,language_code,original_image) values
        <foreach collection="list" item="item" separator=",">
            (#{item.image},#{item.addtime},#{item.appId},#{item.movieId},#{item.createBy},#{item.createTime},#{item.updateBy},#{item.updateTime},#{item.remark},#{item.languageCode},#{item.originalImage})
        </foreach>
    </insert>

    <update id="updateShortBanner" parameterType="ShortBanner">
        update short_banner
        <trim prefix="SET" suffixOverrides=",">
            <if test="image != null">image = #{image},</if>
            <if test="addtime != null">addtime = #{addtime},</if>
            <if test="appId != null">app_id = #{appId},</if>
            <if test="movieId != null">movie_id = #{movieId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="languageCode != null">language_code = #{languageCode},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="batchUpdateShortBanner">
        <foreach item="item" collection="list" separator=";">
            update short_banner
            <set>
                <if test="item.image != null">image = #{item.image},</if>
                <if test="item.appId != null">app_id = #{item.appId},</if>
                <if test="item.movieId != null">movie_id = #{item.movieId},</if>
                <if test="item.updateTime != null">update_time = #{item.updateTime},</if>
                <if test="item.updateBy != null">update_by = #{item.updateBy},</if>
                <if test="item.originalImage != null">original_image = #{item.originalImage},</if>
                <if test="item.languageCode != null">language_code = #{item.languageCode},</if>
            </set>
            where id = #{item.id}
        </foreach>
    </update>

    <delete id="deleteShortBannerById" parameterType="Long">
        delete from short_banner where id = #{id}
    </delete>

    <delete id="deleteShortBannerByIds" parameterType="String">
        delete from short_banner where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteByMovieId">
        delete from short_banner where movie_id = #{movieId} and app_id = #{appId}
    </delete>
</mapper>