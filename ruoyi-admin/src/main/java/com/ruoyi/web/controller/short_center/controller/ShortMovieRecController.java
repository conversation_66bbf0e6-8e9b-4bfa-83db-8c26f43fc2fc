package com.ruoyi.web.controller.short_center.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.domain.ShortMovieRec;
import com.ruoyi.dto.ShortMovieRecDTO;
import com.ruoyi.dto.ShortMovieRecDataDTO;
import com.ruoyi.service.IShortMovieRecService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 影片推荐Controller
 *
 * <AUTHOR>
 * @date 2025-09-08
 */
@Api("影片推荐Controller")
@RestController
@RequestMapping("/short_center/movieRec")
public class ShortMovieRecController extends BaseController
{
    @Autowired
    private IShortMovieRecService shortMovieRecService;

    /**
     * 查询影片推荐列表
     */
    @ApiOperation("查询影片推荐列表")
    @PreAuthorize("@ss.hasPermi('system:rec:list')")
    @GetMapping("/list")
    public TableDataInfo list(ShortMovieRec shortMovieRec)
    {
        startPage();
        List<ShortMovieRec> list = shortMovieRecService.selectShortMovieRecList(shortMovieRec);
        return getDataTable(list);
    }

    /**
     * 导出影片推荐列表
     */
    @ApiOperation("导出影片推荐列表")
    @PreAuthorize("@ss.hasPermi('system:rec:export')")
    @Log(title = "影片推荐", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ShortMovieRec shortMovieRec)
    {
        List<ShortMovieRec> list = shortMovieRecService.selectShortMovieRecList(shortMovieRec);
        ExcelUtil<ShortMovieRec> util = new ExcelUtil<ShortMovieRec>(ShortMovieRec.class);
        util.exportExcel(response, list, "影片推荐数据");
    }

    /**
     * 获取影片推荐详细信息
     */
    @ApiOperation("获取影片推荐详细信息")
    @PreAuthorize("@ss.hasPermi('system:rec:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(shortMovieRecService.selectShortMovieRecById(id));
    }

    /**
     * 新增影片推荐
     */
    @ApiOperation("新增影片推荐")
    @PreAuthorize("@ss.hasPermi('system:rec:add')")
    @Log(title = "影片推荐", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ShortMovieRec shortMovieRec)
    {
        return toAjax(shortMovieRecService.insertShortMovieRec(shortMovieRec));
    }

    /**
     * 批量新增影片推荐
     */
    @ApiOperation("新增影片推荐")
    @PreAuthorize("@ss.hasPermi('system:rec:add')")
    @Log(title = "影片推荐", businessType = BusinessType.INSERT)
    @PostMapping("/addList")
    public AjaxResult addList(@RequestBody ShortMovieRec shortMovieRec)
    {
        return toAjax(shortMovieRecService.insertShortMovieRecList(shortMovieRec.getMovieRecList()));
    }


    /**
     * 修改影片推荐
     */
    @ApiOperation("修改影片推荐")
    @PreAuthorize("@ss.hasPermi('system:rec:edit')")
    @Log(title = "影片推荐", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ShortMovieRec shortMovieRec)
    {
        return toAjax(shortMovieRecService.updateShortMovieRec(shortMovieRec));
    }

    /**
     * 删除影片推荐
     */
    @ApiOperation("删除影片推荐")
    @PreAuthorize("@ss.hasPermi('system:rec:remove')")
    @Log(title = "影片推荐", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(shortMovieRecService.deleteShortMovieRecByIds(ids));
    }

    @ApiOperation("app批量新增影片推荐")
    @PostMapping("/batchAddRec")
    public AjaxResult batchAddRec(@RequestBody ShortMovieRecDTO shortMovieRecDTO)
    {
        return AjaxResult.success(shortMovieRecService.batchAddRec(shortMovieRecDTO));
    }

    @ApiOperation("修改影片推荐状态")
    @PostMapping("/updateStatus")
    public AjaxResult updateStatus(@RequestBody ShortMovieRec shortMovieRec){
        return toAjax(shortMovieRecService.updateStatus(shortMovieRec));
    }

    @ApiOperation("查询影片推荐列表")
    @PreAuthorize("@ss.hasPermi('system:rec:list')")
    @GetMapping("/pageQuery")
    public TableDataInfo pageQuery(ShortMovieRec shortMovieRec)
    {
        startPage();
        List<ShortMovieRecDataDTO> list = shortMovieRecService.pageQuery(shortMovieRec);
        return getDataTable(list);
    }

    @ApiOperation("删除影片推荐状态")
    @PostMapping("/deleteMovieRec")
    public AjaxResult deleteMovieRec(@RequestBody ShortMovieRec shortMovieRec){
        return toAjax(shortMovieRecService.deleteMovieRec(shortMovieRec));
    }
}
