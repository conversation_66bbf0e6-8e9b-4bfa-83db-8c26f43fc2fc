<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.ShortMovieMapper">

    <resultMap type="ShortMovie" id="ShortMovieResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="oldname"    column="oldname"    />
        <result property="icon"    column="icon"    />
        <result property="iconNoWord"    column="icon_no_word"    />
        <result property="director"    column="director"    />
        <result property="actors"    column="actors"    />
        <result property="upTime"    column="up_time"    />
        <result property="time"    column="time"    />
        <result property="num"    column="num"    />
        <result property="rating"    column="rating"    />
        <result property="description"    column="description"    />
        <result property="content"    column="content"    />
        <result property="vipNum"    column="vip_num"    />
        <result property="isVip"    column="is_vip"    />
        <result property="source"    column="source"    />
        <result property="rec"    column="rec"    />
        <result property="state"    column="state"    />
        <result property="addtime"    column="addtime"    />
        <result property="updatetime"    column="updatetime"    />
        <result property="cateId"    column="cate_id"    />
        <result property="langId"    column="lang_id"    />
        <result property="appId"    column="app_id"    />
        <result property="unitCoin"    column="unit_coin"    />
        <result property="checkState" column="check_state"/>
        <result property="checkLog" column="check_log"/>
        <result property="status"    column="status"    />
        <result property="releasetime"    column="releasetime"    />
    </resultMap>

    <sql id="selectShortMovieVo">
        select id, name, oldname, icon, icon_no_word, director, actors, up_time, time, num, rating, description, content, vip_num, is_vip, source, rec, state, addtime, updatetime, cate_id, lang_id, app_id, unit_coin, check_state, check_log,status,releasetime from short_movie
    </sql>

    <select id="selectShortMovieList" parameterType="ShortMovie" resultMap="ShortMovieResult">
        SELECT
        distinct sm.id,
        sm.NAME,
        sm.oldname,
        sm.icon,
        sm.icon_no_word,
        sm.director,
        sm.actors,
        sm.up_time,
        sm.TIME,
        sm.num,
        sm.rating,
        sm.description,
        sm.content,
        sm.vip_num,
        sm.is_vip,
        sm.source,
        sm.rec,
        sm.state,
        sm.addtime,
        sm.updatetime,
        sm.cate_id,
        sm.lang_id,
        sm.app_id,
        sm.unit_coin,
        sl.cname languageName,
        sa.`name` appName,
        sm.check_state,
        sm.check_log,
        sm.status,
        sm.releasetime,
        sm.create_time,
        MAX(CASE WHEN scr.id IS NOT NULL THEN 1 ELSE 0 END) AS hasCoinRule
        FROM
        short_movie sm
        LEFT JOIN short_language sl on sm.lang_id = sl.id
        LEFT JOIN short_movie_app sma on sma.movie_id = sm.id
        LEFT JOIN short_app sa on sa.id = sm.app_id
        LEFT JOIN short_coin_rule scr on scr.movie_id = sm.id
        LEFT JOIN short_movie_i18n smi on smi.movie_id = sm.id
        LEFT JOIN short_movie_rec mr on mr.movie_id = sm.id
        <where>
            sm.status = 1
            <if test=" id != null "> and sm.id = #{id}</if>
            <if test="name != null  and name != ''"> and (sm.name like concat('%', #{name}, '%') or sm.oldname like concat('%', #{name}, '%'))</if>
            <if test="icon != null  and icon != ''"> and sm.icon = #{icon}</if>
            <if test="iconNoWord != null  and iconNoWord != ''"> and sm.icon_no_word = #{iconNoWord}</if>
            <if test="director != null  and director != ''"> and sm.director like concat('%', #{director}, '%')</if>
            <if test="actors != null  and actors != ''"> and sm.actors like concat('%', #{actors}, '%')</if>
            <if test="upTime != null "> and sm.up_time = date_format(#{upTime}, '%Y-%m-%d')</if>
            <if test="time != null  and time != ''"> and sm.time = #{time}</if>
            <if test="num != null "> and sm.num = #{num}</if>
            <if test="rating != null "> and sm.rating = #{rating}</if>
            <if test="description != null  and description != ''"> and sm.description = #{description}</if>
            <if test="content != null  and content != ''"> and sm.content = #{content}</if>
            <if test="vipNum != null  and vipNum != ''"> and sm.vip_num = #{vipNum}</if>
            <if test="isVip != null "> and sm.is_vip = #{isVip}</if>
            <if test="source != null  and source != ''"> and sm.source = #{source}</if>
            <if test="rec != null  and rec != ''"> and  mr.rec= #{rec} and mr.status = '0'</if>
            <if test="state != null  and state != ''"> and sm.state = #{state}</if>
            <if test="addtime != null "> and sm.addtime = #{addtime}</if>
            <if test="updatetime != null "> and sm.updatetime = #{updatetime}</if>
            <if test="cateId != null "> and sm.cate_id = #{cateId}</if>
            <if test="langId != null "> and sm.lang_id = #{langId}</if>
            <if test="appId != null "> and (sm.app_id = #{appId} or sma.app_id = #{appId}) </if>
            <if test="appIds != null and appIds.size() > 0">
                AND (sm.app_id IN
                <foreach item="item" index="index" collection="appIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
                or sma.app_id IN
                <foreach item="item1" index="index" collection="appIds" open="(" separator="," close=")">
                    #{item1}
                </foreach>
                )
            </if>
            <if test="movieIds != null and movieIds.size() > 0">
                AND sm.id IN
                <foreach item="item" index="index" collection="movieIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="unitCoin != null "> and sm.unit_coin = #{unitCoin}</if>
            <if test="checkState != null "> and sm.check_state = #{checkState}</if>
            <if test="createBy != null "> and scr.create_by = #{createBy}</if>
            <if test="languageCodes != null and languageCodes.size > 0">
                AND smi.language_code IN
                <foreach item="item" index="index" collection="languageCodes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <!--<if test="status != null  and status != ''"> and sm.status = #{status}</if>-->

        </where>
        GROUP BY sm.id
        ORDER BY sm.id DESC
    </select>

    <select id="selectShortMovieById" parameterType="Long" resultMap="ShortMovieResult">
        <include refid="selectShortMovieVo"/>
        where id = #{id}
    </select>

    <select id="selectShortMovieByName" parameterType="String" resultMap="ShortMovieResult">
        <include refid="selectShortMovieVo"/>
        where name = #{name} and status = 1
    </select>
    <select id="selectShortMovieByIds" resultType="com.ruoyi.domain.ShortMovie">
        <include refid="selectShortMovieVo"/>
         where  status = 1 and id in
        <foreach item="item" index="index" collection="list"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="getMovies" resultType="com.ruoyi.domain.ShortMovie">
        <include refid="selectShortMovieVo"/>
        where
        state = 1
        and status = 1
        and app_id = #{nid}
        <if test="type > 0">and rec = #{type}</if>
        <if test="ifApp > 0">and id &lt; 60 </if>
        <if test="exTime != null">and releasetime &lt; #{exTime} </if>
        ORDER BY rec
    </select>
    <select id="findBySourceAndStateAndIdNotIn" resultType="com.ruoyi.domain.ShortMovie">
        <include refid="selectShortMovieVo"/>
        WHERE state = #{state}
        and source= #{source}
        and status = 1
        and rec not in
        <foreach item="id" collection="recIdList" open="(" separator="," close=")">
            #{id}
        </foreach>
        ORDER BY id desc
        LIMIT #{limitNum}
    </select>
    <select id="findByIdInAndAppId" resultType="com.ruoyi.domain.ShortMovie">
        <include refid="selectShortMovieVo"/>
        WHERE state = 1
        and status = 1
        <if test="nid != null ">
            and app_id = #{nid}
        </if>
        and id in
        <foreach item="id" collection="midList" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="findByName" resultType="com.ruoyi.domain.ShortMovie">
        select id, name, oldname, icon, icon_no_word, director, actors, up_time, time, num, rating, description, content, vip_num, is_vip, source, rec, state, addtime, updatetime, cate_id, lang_id, app_id, unit_coin,status,releasetime from short_movie
        WHERE `name` = #{name} and status = 1
    </select>
    <select id="getMoviesByApps" resultType="com.ruoyi.domain.ShortMovie">
        SELECT
            sm.id,
            sm.NAME,
            sm.oldname,
            sm.icon,
            sm.icon_no_word,
            sm.director,
            sm.actors,
            sm.up_time,
            sm.TIME,
            sm.num,
            sm.rating,
            sm.description,
            sm.content,
            sm.vip_num,
            sm.is_vip,
            sm.source,
            sm.rec,
            sm.state,
            sm.addtime,
            sm.updatetime,
            sm.cate_id,
            sm.lang_id,
            sm.app_id,
            sm.unit_coin,
            sm.status,
            sm.releasetime
        FROM
            short_movie sm
                LEFT JOIN short_movie_app sma on sm.id = sma.movie_id
        WHERE
            sm.state = 1
        and sm.status = 1
          and sma.app_id = #{nid}
        <if test="type > 0">and sm.rec = #{type}</if>
        <if test="ifApp > 0">and sm.id &lt; 60 </if>
        <if test="exTime != null">and sm.releasetime &lt; #{exTime} </if>
        GROUP BY sm.id
        ORDER BY
            sm.rec
    </select>
    <select id="findByIdInAndAppIds" resultType="com.ruoyi.domain.ShortMovie">
        SELECT
        sm.id,
        sm.NAME,
        sm.oldname,
        sm.icon,
        sm.icon_no_word,
        sm.director,
        sm.actors,
        sm.up_time,
        sm.TIME,
        sm.num,
        sm.rating,
        sm.description,
        sm.content,
        sm.vip_num,
        sm.is_vip,
        sm.source,
        sm.rec,
        sm.state,
        sm.addtime,
        sm.updatetime,
        sm.cate_id,
        sm.lang_id,
        sm.app_id,
        sm.unit_coin,
        sm.status,
        sm.releasetime
        FROM
        short_movie sm
        LEFT JOIN short_movie_app sma on sm.id = sma.movie_id
        WHERE
        sm.state = 1
        and sm.status = 1
        <if test="nid != null ">
            and sma.app_id = #{nid}
        </if>
        <if test="midList.size > 0 ">
            and sm.id in
            <foreach item="id" collection="midList" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        GROUP BY sm.id

    </select>
    <select id="getAll" resultType="com.ruoyi.domain.ShortMovie">
        select id, name, oldname, icon, icon_no_word, director, actors, up_time, time, num, rating, description, content, vip_num, is_vip, source, rec, state, addtime, updatetime, cate_id, lang_id, app_id, unit_coin, check_state, check_log,status,releasetime from short_movie
    </select>
    <select id="findByIdInAndAppIds1" resultType="com.ruoyi.domain.ShortMovie">
        SELECT
        sm.id,
        sm.NAME,
        sm.oldname,
        sm.icon,
        sm.icon_no_word,
        sm.director,
        sm.actors,
        sm.up_time,
        sm.TIME,
        sm.num,
        sm.rating,
        sm.description,
        sm.content,
        sm.vip_num,
        sm.is_vip,
        sm.source,
        sm.rec,
        sm.state,
        sm.addtime,
        sm.updatetime,
        sm.cate_id,
        sm.lang_id,
        sm.app_id,
        sm.unit_coin,
        sm.status,
        sm.releasetime,
        MAX(CASE WHEN scr.id IS NOT NULL THEN 1 ELSE 0 END) AS hasCoinRule
        FROM
        short_movie sm
        LEFT JOIN short_movie_app sma on sm.id = sma.movie_id
        LEFT JOIN short_coin_rule scr on scr.movie_id = sm.id
        WHERE
        sm.state = 1
and sm.status = 1
        <!--        <if test="nid != null ">-->
<!--            and sma.app_id = #{nid}-->
<!--        </if>-->
        <if test="nids != null and nids.size() > 0">
            AND sma.app_id IN
            <foreach item="item" index="index" collection="nids" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="createBy != null "> and scr.create_by = #{createBy}</if>
        <if test="midList.size > 0 ">
            and sm.id in
            <foreach item="id" collection="midList" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        GROUP BY sm.id
    </select>
    <select id="getByRand4" resultType="com.ruoyi.domain.ShortMovie">
        SELECT
            *
        FROM
            (
                SELECT
                    sm.*
                FROM
                    short_movie sm
                        INNER JOIN short_movie_app sma ON sm.id = sma.movie_id
                WHERE
                    (sm.app_id = #{appId} OR sma.app_id = #{appId})
                GROUP BY
                    sm.id
                ORDER BY
                    sm.releasetime DESC
                    LIMIT 10
            ) AS latest_movies
        ORDER BY
            RAND()
            LIMIT 6
    </select>
    <select id="selectOldMovie" resultType="com.ruoyi.domain.ShortMovie">
        SELECT * from short_movie  WHERE id &lt;= 60
    </select>
    <select id="getByRand4I18n" resultType="com.ruoyi.domain.ShortMovie">
        SELECT
            *
        FROM
            (
                SELECT
                    si.movie_id id,
                    MAX(si.icon) icon,
                    MAX(si.`name`) NAME
                FROM
                    short_movie sm
                        INNER JOIN short_movie_app sma ON sm.id = sma.movie_id
                        INNER JOIN short_movie_i18n si on si.movie_id = sm.id
                WHERE
                    (sm.app_id = #{appId} OR sma.app_id =#{appId})
                  and si.language_code = #{language}
                GROUP BY
                    sm.id
                ORDER BY
                    sm.releasetime DESC
                    LIMIT 10
            ) AS latest_movies
        ORDER BY
            RAND()
            LIMIT 6
    </select>
    <select id="getMovie1" resultType="com.ruoyi.domain.ShortMovie">
        SELECT
            sm.*
        FROM
            short_movie sm
                INNER JOIN short_movie_app sma ON sm.id = sma.movie_id
        WHERE
            (sm.app_id = #{appId} OR sma.app_id =#{appId})
        GROUP BY
            sm.id
        ORDER BY
            sm.releasetime DESC
            LIMIT 1
    </select>
    <select id="selectByReleasetime" resultType="java.util.Date">
        SELECT releasetime from short_movie WHERE id = #{id}
    </select>
    <select id="getMovieNameByUserId" resultType="com.ruoyi.domain.ShortMovie">
        SELECT
            sm.*
        FROM
            short_order so
                INNER JOIN short_sem_link se on se.id = so.link_id
                INNER JOIN short_movie sm on sm.id = se.movie_id
        WHERE
            so.user_id = #{id}
          AND so.`status` = 'SUCCEEDED'
        ORDER BY
            so.create_time ASC
            LIMIT 1
    </select>
    <select id="getByRand10" resultType="com.ruoyi.domain.ShortMovie">
        SELECT
            sm.*
        FROM
            short_movie sm
                INNER JOIN short_movie_app sma ON sm.id = sma.movie_id
        WHERE
            (sm.app_id = #{appId} OR sma.app_id = #{appId})
        GROUP BY
            sm.id
        ORDER BY
            sm.releasetime DESC
            LIMIT #{limit}
    </select>
    <select id="getMoviesI18n" resultType="com.ruoyi.domain.ShortMovie">
        SELECT
        sm.id,
        sm.NAME,
        sm.oldname,
        sm.icon,
        sm.icon_no_word,
        sm.director,
        sm.actors,
        sm.up_time,
        sm.TIME,
        sm.num,
        sm.rating,
        sm.description,
        sm.content,
        sm.vip_num,
        sm.is_vip,
        sm.source,
        sm.rec,
        sm.state,
        sm.addtime,
        sm.updatetime,
        sm.cate_id,
        sm.lang_id,
        sm.app_id,
        sm.unit_coin,
        sm.check_state,
        sm.check_log,
        sm.STATUS,
        sm.releasetime
        FROM
        short_movie sm
        INNER JOIN short_movie_i18n mi on mi.movie_id = sm.id
        where
        sm.state = 1
        and sm.status = 1
        and sm.app_id = #{nid}
        <if test="languageCode != null and languageCode != 'en-US'">and mi.language_code = #{languageCode} </if>
        <if test="type > 0">and sm.rec = #{type}</if>
        <if test="ifApp > 0">and sm.id &lt; 60 </if>
        <if test="exTime != null">and sm.releasetime &lt; #{exTime} </if>
        ORDER BY sm.rec
    </select>
    <select id="getMoviesByAppsI18n" resultType="com.ruoyi.domain.ShortMovie">
        SELECT
        sm.id,
        sm.NAME,
        sm.oldname,
        sm.icon,
        sm.icon_no_word,
        sm.director,
        sm.actors,
        sm.up_time,
        sm.TIME,
        sm.num,
        sm.rating,
        sm.description,
        sm.content,
        sm.vip_num,
        sm.is_vip,
        sm.source,
        sm.rec,
        sm.state,
        sm.addtime,
        sm.updatetime,
        sm.cate_id,
        sm.lang_id,
        sm.app_id,
        sm.unit_coin,
        sm.status,
        sm.releasetime
        FROM
        short_movie sm
        LEFT JOIN short_movie_app sma on sm.id = sma.movie_id
        LEFT JOIN short_movie_i18n mi on mi.movie_id = sm.id
        WHERE
        sm.state = 1
        and sm.status = 1
        and sma.app_id = #{nid}
        <if test="languageCode != null and languageCode != 'en-US'">and mi.language_code = #{languageCode} </if>
        <if test="type > 0">and sm.rec = #{type}</if>
        <if test="ifApp > 0">and sm.id &lt; 60 </if>
        <if test="exTime != null">and sm.releasetime &lt; #{exTime} </if>
        GROUP BY sm.id
        ORDER BY
        sm.rec
    </select>


    <insert id="insertShortMovie" parameterType="ShortMovie" useGeneratedKeys="true" keyProperty="id">
        insert into short_movie
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="oldname != null">oldname,</if>
            <if test="icon != null">icon,</if>
            <if test="iconNoWord != null">icon_no_word,</if>
            <if test="director != null">director,</if>
            <if test="actors != null">actors,</if>
            <if test="upTime != null">up_time,</if>
            <if test="time != null">time,</if>
            <if test="num != null">num,</if>
            <if test="rating != null">rating,</if>
            <if test="description != null">description,</if>
            <if test="content != null">content,</if>
            <if test="vipNum != null">vip_num,</if>
            <if test="isVip != null">is_vip,</if>
            <if test="source != null">source,</if>
            <if test="rec != null">rec,</if>
            <if test="state != null">state,</if>
            <if test="addtime != null">addtime,</if>
            <if test="updatetime != null">updatetime,</if>
            <if test="cateId != null">cate_id,</if>
            <if test="langId != null">lang_id,</if>
            <if test="appId != null">app_id,</if>
            <if test="unitCoin != null">unit_coin,</if>
            <if test="checkState != null"> check_state,</if>
            <if test="checkLog != null"> check_log,</if>
            <if test="status != null"> status,</if>
            <if test="releasetime != null"> releasetime,</if>

         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="oldname != null">#{oldname},</if>
            <if test="icon != null">#{icon},</if>
            <if test="iconNoWord != null">#{iconNoWord},</if>
            <if test="director != null">#{director},</if>
            <if test="actors != null">#{actors},</if>
            <if test="upTime != null">#{upTime},</if>
            <if test="time != null">#{time},</if>
            <if test="num != null">#{num},</if>
            <if test="rating != null">#{rating},</if>
            <if test="description != null">#{description},</if>
            <if test="content != null">#{content},</if>
            <if test="vipNum != null">#{vipNum},</if>
            <if test="isVip != null">#{isVip},</if>
            <if test="source != null">#{source},</if>
            <if test="rec != null">#{rec},</if>
            <if test="state != null">#{state},</if>
            <if test="addtime != null">#{addtime},</if>
            <if test="updatetime != null">#{updatetime},</if>
            <if test="cateId != null">#{cateId},</if>
            <if test="langId != null">#{langId},</if>
            <if test="appId != null">#{appId},</if>
            <if test="unitCoin != null">#{unitCoin},</if>
            <if test="checkState != null">#{checkState},</if>
            <if test="checkLog != null">#{checkLog},</if>
            <if test="status != null">#{status},</if>
            <if test="releasetime != null">#{releasetime},</if>

         </trim>
    </insert>

    <update id="updateShortMovie" parameterType="ShortMovie">
        update short_movie
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="oldname != null">oldname = #{oldname},</if>
            <if test="icon != null">icon = #{icon},</if>
            <if test="iconNoWord != null">icon_no_word = #{iconNoWord},</if>
            <if test="director != null">director = #{director},</if>
            <if test="actors != null">actors = #{actors},</if>
            <if test="upTime != null">up_time = #{upTime},</if>
            <if test="time != null">time = #{time},</if>
            <if test="num != null">num = #{num},</if>
            <if test="rating != null">rating = #{rating},</if>
            <if test="description != null">description = #{description},</if>
            <if test="content != null">content = #{content},</if>
            <if test="vipNum != null">vip_num = #{vipNum},</if>
            <if test="isVip != null">is_vip = #{isVip},</if>
            <if test="source != null">source = #{source},</if>
            <if test="rec != null">rec = #{rec},</if>
            <if test="state != null">state = #{state},</if>
            <if test="addtime != null">addtime = #{addtime},</if>
            <if test="updatetime != null">updatetime = #{updatetime},</if>
            <if test="cateId != null">cate_id = #{cateId},</if>
            <if test="langId != null">lang_id = #{langId},</if>
            <if test="appId != null">app_id = #{appId},</if>
            <if test="unitCoin != null">unit_coin = #{unitCoin},</if>
            <if test="checkState != null"> check_state = #{checkState},</if>
            <if test="checkLog != null"> check_log = #{checkLog},</if>
            <if test="status != null"> status = #{status},</if>
            <if test="releasetime != null"> releasetime = #{releasetime},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateIconById">
        update short_movie set icon = #{res} where id = #{id}
    </update>
    <update id="updateShortMovie1">
        update short_movie
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="oldname != null">oldname = #{oldname},</if>
            <if test="director != null">director = #{director},</if>
            <if test="actors != null">actors = #{actors},</if>
            <if test="upTime != null">up_time = #{upTime},</if>
            <if test="time != null">time = #{time},</if>
            <if test="num != null">num = #{num},</if>
            <if test="rating != null">rating = #{rating},</if>
            <if test="description != null">description = #{description},</if>
            <if test="content != null">content = #{content},</if>
            <if test="vipNum != null">vip_num = #{vipNum},</if>
            <if test="isVip != null">is_vip = #{isVip},</if>
            <if test="source != null">source = #{source},</if>
            <if test="rec != null">rec = #{rec},</if>
            <if test="state != null">state = #{state},</if>
            <if test="addtime != null">addtime = #{addtime},</if>
            <if test="updatetime != null">updatetime = #{updatetime},</if>
            <if test="cateId != null">cate_id = #{cateId},</if>
            <if test="langId != null">lang_id = #{langId},</if>
            <if test="appId != null">app_id = #{appId},</if>
            <if test="unitCoin != null">unit_coin = #{unitCoin},</if>
            <if test="checkState != null"> check_state = #{checkState},</if>
            <if test="checkLog != null"> check_log = #{checkLog},</if>
            <if test="status != null"> status = #{status},</if>
            <if test="releasetime != null"> releasetime = #{releasetime},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateNullById">
        update short_movie
        <trim prefix="SET" suffixOverrides=",">
            icon = null,
            director = null,
            actors = null,
            up_time = null,
            time = null,
            num = 0,
            rating = 0,
            description =null,
            content = null,
            vip_num = null,
            is_vip =0,
            source = null,
            rec = null,
            state = null,
            addtime = null,
            updatetime = null,
            cate_id = null,
            lang_id = null,
            app_id = null,
            unit_coin = 0,
            create_by=null,
            create_time=null,
            update_by=null,
            update_time=null,
            remark=null,
            check_state =0,
            check_log = null,
            status = 0,
            releasetime = null,
        </trim>
        where id = #{id}
    </update>
    <update id="updateIconNoWordById">
        update short_movie set icon_no_word = #{res} where id = #{id}
    </update>

    <delete id="deleteShortMovieById" parameterType="Long">
        delete from short_movie where id = #{id}
    </delete>

    <delete id="deleteShortMovieByIds" parameterType="String">
        delete from short_movie where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
