package com.ruoyi.domain;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.springframework.data.util.ReflectionUtils;

import javax.validation.constraints.NotBlank;

/**
 * 影片对象 short_movie
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
public class ShortMovie extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 短剧名称
     */
    @NotBlank(message = "短剧名称不能为空")
    @Excel(name = "短剧名称")
    private String name;

    /**
     * 短剧旧名称
     */
    @NotBlank(message = "短剧旧名称不能为空")
    @Excel(name = "短剧旧名称")
    private String oldname;

    /**
     * 封面图片
     */
    @NotBlank(message = "封面图片不能为空")
    @Excel(name = "封面图片")
    private String icon;

    @NotBlank(message = "无字封面图片不能为空")
    @Excel(name = "无字封面图片")
    private String iconNoWord;

    /**
     * 导演
     */
    @Excel(name = "导演")
    @NotBlank(message = "导演不能为空")
    private String director;

    /**
     * 主演
     */
    @Excel(name = "主演")
    @NotBlank(message = "主演不能为空")
    private String actors;

    /**
     * 上映日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "上映日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date upTime;

    /**
     * 时长(分钟)
     */
    @Excel(name = "时长(分钟)")
    @NotBlank(message = "时长不能为空")
    private String time;

    /**
     * 集数
     */
    @Excel(name = "集数")
    private Integer num;

    /**
     * 评分
     */
    @Excel(name = "评分")
    private BigDecimal rating;

    /**
     * 剧情简介
     */
    @Excel(name = "剧情简介")
    private String description;

    /**
     * 详细介绍
     */
    @Excel(name = "详细介绍")
    private String content;

    /**
     * 开始付费的集数
     */
    @Excel(name = "开始付费的集数")
    private String vipNum;

    /**
     * 是否仅VIP可见
     */
    @Excel(name = "是否仅VIP可见")
    private Boolean isVip;

    /**
     * 来源
     */
    @Excel(name = "来源")
    private String source;

    /**
     * 是否推荐
     */
    @Excel(name = "是否推荐")
    private String rec;


    private List<String> recList;

    /**
     * 状态 0待审核 1已发布
     */
    @Excel(name = "状态 0待审核 1已发布")
    private String state;

    /**
     * 添加时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "添加时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date addtime;

    /**
     * 自动更新修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "自动更新修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updatetime;


    /**
     * 分类
     */
    @Excel(name = "分类")
    private Long cateId;

    /**
     * 语言
     */
    @Excel(name = "语言")
    private Long langId;

    private List<ShortAppLanguage> languages;

    /**
     * 应用ID
     */
    @Excel(name = "应用ID")
    private Long appId;

    private int sumVideoCount;

    private List<Long> appIds;

    private List<Long> movieIds;

    private Date releasetime;

    public Date getReleasetime() {
        return releasetime;
    }

    public void setReleasetime(Date releasetime) {
        this.releasetime = releasetime;
    }

    public List<Long> getAppIds() {
        return appIds;
    }

    public void setAppIds(List<Long> appIds) {
        this.appIds = appIds;
    }

    public List<Long> getMovieIds() {
        return movieIds;
    }

    public void setMovieIds(List<Long> movieIds) {
        this.movieIds = movieIds;
    }

    public int getSumVideoCount() {
        return sumVideoCount;
    }

    public void setSumVideoCount(int sumVideoCount) {
        this.sumVideoCount = sumVideoCount;
    }

    public List<String> getRecList() {
        return recList;
    }

    public void setRecList(List<String> recList) {
        this.recList = recList;
    }

    /**
     * 单集金币
     */
    @Excel(name = "单集金币")
    private Integer unitCoin;

    @Excel(name = "语言名称")
    private String languageName;

    private List<Long> appList;

    private String appName;

    //m3u8文件检测状态  0-未检测 1-检测通过 2-检测失败 3-检测中
    @Excel(name = "m3u8文件检测状态")
    private Integer checkState;

    // 检测日志
    private String checkLog;

    private  int reInfo;

    private long sumTotal;

    private Integer hasCoinRule;

    private List<String> languageCodes;

    public List<String> getLanguageCodes() {
        return languageCodes;
    }

    public void setLanguageCodes(List<String> languageCodes) {
        this.languageCodes = languageCodes;
    }

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getHasCoinRule() {
        return hasCoinRule;
    }

    public void setHasCoinRule(Integer hasCoinRule) {
        this.hasCoinRule = hasCoinRule;
    }

    public long getSumTotal() {
        return sumTotal;
    }

    public void setSumTotal(long sumTotal) {
        this.sumTotal = sumTotal;
    }

    public int getReInfo() {
        return reInfo;
    }

    public void setReInfo(int reInfo) {
        this.reInfo = reInfo;
    }

    public Integer getCheckState() {
        return checkState;
    }

    public void setCheckState(Integer checkState) {
        this.checkState = checkState;
    }

    public String getCheckLog() {
        return checkLog;
    }

    public void setCheckLog(String checkLog) {
        this.checkLog = checkLog;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public List<Long> getAppList() {
        return appList;
    }

    public void setAppList(List<Long> appList) {
        this.appList = appList;
    }

    public String getLanguageName() {
        return languageName;
    }

    public void setLanguageName(String languageName) {
        this.languageName = languageName;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setOldname(String oldname) {
        this.oldname = oldname;
    }

    public String getOldname() {
        return oldname;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getIcon() {
        return icon;
    }

    public void setDirector(String director) {
        this.director = director;
    }

    public String getDirector() {
        return director;
    }

    public void setActors(String actors) {
        this.actors = actors;
    }

    public String getActors() {
        return actors;
    }

    public void setUpTime(Date upTime) {
        this.upTime = upTime;
    }

    public Date getUpTime() {
        return upTime;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public String getTime() {
        return time;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public Integer getNum() {
        return num;
    }

    public void setRating(BigDecimal rating) {
        this.rating = rating;
    }

    public BigDecimal getRating() {
        return rating;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getContent() {
        return content;
    }

    public void setVipNum(String vipNum) {
        this.vipNum = vipNum;
    }

    public String getVipNum() {
        return vipNum;
    }

    public void setIsVip(Boolean isVip) {
        this.isVip = isVip;
    }

    public Boolean getIsVip() {
        return isVip;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getSource() {
        return source;
    }

    public void setRec(String rec) {
        this.rec = rec;
    }

    public String getRec() {
        return rec;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getState() {
        return state;
    }

    public void setAddtime(Date addtime) {
        this.addtime = addtime;
    }

    public Date getAddtime() {
        return addtime;
    }

    public void setCateId(Long cateId) {
        this.cateId = cateId;
    }

    public Long getCateId() {
        return cateId;
    }

    public void setLangId(Long langId) {
        this.langId = langId;
    }

    public Long getLangId() {
        return langId;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public Long getAppId() {
        return appId;
    }

    public void setUnitCoin(Integer unitCoin) {
        this.unitCoin = unitCoin;
    }

    public Integer getUnitCoin() {
        return unitCoin;
    }

    public Date getUpdatetime() {
        return updatetime;
    }

    public void setUpdatetime(Date updatetime) {
        this.updatetime = updatetime;
    }

    public List<ShortAppLanguage> getLanguages() {
        return languages;
    }

    public void setLanguages(List<ShortAppLanguage> languages) {
        this.languages = languages;
    }

    public String getIconNoWord() {
        return iconNoWord;
    }

    public void setIconNoWord(String iconNoWord) {
        this.iconNoWord = iconNoWord;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("name", getName())
                .append("oldname", getOldname())
                .append("icon", getIcon())
                .append("director", getDirector())
                .append("actors", getActors())
                .append("upTime", getUpTime())
                .append("time", getTime())
                .append("num", getNum())
                .append("rating", getRating())
                .append("description", getDescription())
                .append("content", getContent())
                .append("vipNum", getVipNum())
                .append("isVip", getIsVip())
                .append("source", getSource())
                .append("rec", getRec())
                .append("state", getState())
                .append("addtime", getAddtime())
                .append("cateId", getCateId())
                .append("langId", getLangId())
                .append("appId", getAppId())
                .append("unitCoin", getUnitCoin())
                .toString();
    }

    public String get(String key) {
        try {
            // 获取当前类的 Field 对象
            Field field = this.getClass().getDeclaredField(key);
            // 设置可访问（即使是 private 属性）
            field.setAccessible(true);
            // 获取属性值
            Object value = field.get(this);
            // 如果值为 null，返回空字符串，否则返回字符串形式
            return value == null ? "" : value.toString();
        } catch (NoSuchFieldException | IllegalAccessException e) {
            // 如果属性不存在或访问失败，返回空字符串或抛出异常
            return "";
            // 或者抛出异常：
            // throw new IllegalArgumentException("Invalid field name: " + key, e);
        }
    }
}
