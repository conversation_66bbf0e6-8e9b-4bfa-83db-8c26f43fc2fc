package com.ruoyi.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.domain.*;
import com.ruoyi.dto.ShortMovieRecDTO;
import com.ruoyi.dto.ShortMovieRecDataDTO;
import com.ruoyi.mapper.ShortAppLanguageMapper;
import com.ruoyi.mapper.ShortMovieAppMapper;
import com.ruoyi.mapper.ShortMovieRecMapper;
import com.ruoyi.service.IShortMovieI18nService;
import com.ruoyi.service.IShortMovieRecService;
import com.ruoyi.service.IShortMovieService;
import com.ruoyi.system.service.ISysConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 影片推荐Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-09-08
 */
@Service
public class ShortMovieRecServiceImpl implements IShortMovieRecService {
    @Autowired
    private ShortMovieRecMapper shortMovieRecMapper;

    @Autowired
    private ShortAppLanguageMapper appLanguageMapper;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private IShortMovieI18nService shortMovieI18nService;

    @Autowired
    private ShortMovieAppMapper shortMovieAppMapper;

    @Autowired
    private IShortMovieService shortMovieService;

    /**
     * 查询影片推荐
     *
     * @param id 影片推荐主键
     * @return 影片推荐
     */
    @Override
    public ShortMovieRec selectShortMovieRecById(Long id) {
        return shortMovieRecMapper.selectShortMovieRecById(id);
    }

    /**
     * 查询影片推荐列表
     *
     * @param shortMovieRec 影片推荐
     * @return 影片推荐
     */
    @Override
    public List<ShortMovieRec> selectShortMovieRecList(ShortMovieRec shortMovieRec) {
        return shortMovieRecMapper.selectShortMovieRecList(shortMovieRec);
    }

    /**
     * 新增影片推荐
     *
     * @param shortMovieRec 影片推荐
     * @return 结果
     */
    @Override
    public int insertShortMovieRec(ShortMovieRec shortMovieRec) {
        shortMovieRec.setCreateTime(DateUtils.getNowDate());
        return shortMovieRecMapper.insertShortMovieRec(shortMovieRec);
    }

    /**
     * 修改影片推荐
     *
     * @param shortMovieRec 影片推荐
     * @return 结果
     */
    @Override
    public int updateShortMovieRec(ShortMovieRec shortMovieRec) {
        shortMovieRec.setUpdateTime(DateUtils.getNowDate());
        return shortMovieRecMapper.updateShortMovieRec(shortMovieRec);
    }

    /**
     * 批量删除影片推荐
     *
     * @param ids 需要删除的影片推荐主键
     * @return 结果
     */
    @Override
    public int deleteShortMovieRecByIds(Long[] ids) {
        return shortMovieRecMapper.deleteShortMovieRecByIds(ids);
    }

    /**
     * 删除影片推荐信息
     *
     * @param id 影片推荐主键
     * @return 结果
     */
    @Override
    public int deleteShortMovieRecById(Long id) {
        return shortMovieRecMapper.deleteShortMovieRecById(id);
    }

    @Override
    public int insertShortMovieRecList(List<ShortMovieRec> movieRecList) {
        for (ShortMovieRec rec:movieRecList){
            int check = shortMovieRecMapper.check(rec.getAppId(),rec.getMovieId(),rec.getLanguageCode(),rec.getRec());
            if (check == 0)
                shortMovieRecMapper.insertShortMovieRec(rec);
        }
        return 1;
    }

    @Override
    public List<ShortMovie> getMoviesByRec(Integer nid, String type, int ifApp, Date exTime, String languageCode) {
        return shortMovieRecMapper.getMoviesByRec(nid, type, ifApp, exTime, languageCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> batchAddRec(ShortMovieRecDTO shortMovieRecDTO) {
        List<String> result = new ArrayList<>();
        //查询app下配置的语言列表
        Long appId = shortMovieRecDTO.getAppId();
        List<Long> movieIdList = shortMovieRecDTO.getMovieIdList();
        List<ShortAppLanguage> list = appLanguageMapper.selectShortAppLanguageList(new ShortAppLanguage());
        String key = "sys.app.language." + appId;
        String value = configService.selectConfigByKey(key);
        if (StringUtils.isEmpty(value)) {
            throw new RuntimeException("未配置多语言");
        }
        List<Long> ids = JSONUtil.toList(value, Long.class);
        //拿到APP 配置的多语言
        List<ShortAppLanguage> languageCollect = list.stream().filter(item -> ids.contains(item.getId())).collect(Collectors.toList());
        //查询勾选的剧的多语言记录
        //筛选出这个APP 配置的语言
        List<ShortMovieI18n> movieI18nList = shortMovieI18nService.selectByMovieIdList(movieIdList).stream().filter(item -> languageCollect.stream().map(ShortAppLanguage::getCode).collect(Collectors.toList())
                .contains(item.getLanguageCode())).collect(Collectors.toList());
        //查询短剧信息
        List<ShortMovie> movieList = shortMovieService.selectShortMovieListByIds(movieIdList);
        Map<Long, String> movieNameMap = movieList.stream().collect(Collectors.toMap(ShortMovie::getId, ShortMovie::getOldname, (old, newData) -> newData));
        //校验已选中的剧是否存在多语言记录
        if (CollectionUtil.isNotEmpty(movieI18nList)) {
            //查出已分配且审核过的剧
            Map<Long, List<ShortMovieI18n>> movieMap = movieI18nList.stream().collect(Collectors.groupingBy(ShortMovieI18n::getMovieId));
            for (ShortMovie shortMovie : movieList) {
                if (!movieMap.containsKey(shortMovie.getId()) || CollectionUtil.isEmpty(movieMap.get(shortMovie.getId()))) {
                    result.add(shortMovie.getId() + "-" + shortMovie.getOldname());
                }
            }
           /* movieMap.forEach((movieId, data) -> {
                List<String> languageCodeList = data.stream().map(ShortMovieI18n::getLanguageCode).collect(Collectors.toList());
                // 判断 languageCollect 是否全部包含 languageCodeList  筛选出缺少的
                List<ShortAppLanguage> collect = languageCollect.stream().filter(item -> !languageCodeList.contains(item.getCode())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(collect)) {
                    if(movieNameMap.containsKey(movieId)){
                        result.put(movieId + "-" + movieNameMap.get(movieId), collect);
                    }else {
                        result.put( movieId.toString(), collect);
                    }
                }
            });*/
        } else {
            movieList.forEach(movie -> {
                result.add(movie.getId() + "-" + movieNameMap.get(movie.getId()));
            });
        }
        if (CollectionUtil.isNotEmpty(result)) {
            return result;
        } else {
            List<String> tmpLanguageList = Arrays.asList("en-US", "id");
            //检验完成之后构造数据入库
            List<ShortMovieRec> movieRecList = new ArrayList<>();
            Map<Long, List<ShortMovieI18n>> movieMap = movieI18nList.stream().collect(Collectors.groupingBy(ShortMovieI18n::getMovieId));
            movieMap.forEach((movieId, data) ->
            {
                //如果短剧多语言版本只包含 英语和印尼语 那么只推荐印尼语
                List<String> collect = data.stream().map(ShortMovieI18n::getLanguageCode).distinct().collect(Collectors.toList());
                if (data.size() == 2 && new HashSet<>(collect).containsAll(tmpLanguageList)) {
                    ShortMovieRec movieRec = new ShortMovieRec();
                    movieRec.setAppId(appId);
                    movieRec.setMovieId(movieId);
                    movieRec.setLanguageCode("id");
                    movieRec.setRec(shortMovieRecDTO.getRec());
                    movieRec.setStatus("0");
                    movieRec.setCreateTime(new Date());
                    movieRec.setUpdateTime(new Date());
                    movieRecList.add(movieRec);
                } else {
                    // 其他多语言语种 按照正常情况推荐
                    data.stream().filter(item -> languageCollect.stream().anyMatch(language -> language.getCode().equals(item.getLanguageCode()))).collect(Collectors.toList())
                            .forEach(item -> {
                                ShortMovieRec movieRec = new ShortMovieRec();
                                movieRec.setAppId(appId);
                                movieRec.setMovieId(movieId);
                                movieRec.setLanguageCode(item.getLanguageCode());
                                movieRec.setRec(shortMovieRecDTO.getRec());
                                movieRec.setStatus("0");
                                movieRec.setCreateTime(new Date());
                                movieRec.setUpdateTime(new Date());
                                movieRecList.add(movieRec);
                            });
                }
            });
            this.insertShortMovieRecList(movieRecList);
        }
        return result;
    }

    @Override
    public int updateStatus(ShortMovieRec shortMovieRec) {
        return shortMovieRecMapper.updateStatus(shortMovieRec);
    }

    @Override
    public List<ShortMovieRecDataDTO> pageQuery(ShortMovieRec shortMovieRec) {
        //查询配置的多语言信息
        //先进行分页条件查询
        List<ShortMovieRecDataDTO> movieRecList = shortMovieRecMapper.pageQuery(shortMovieRec);
        List<ShortAppLanguage> languageList = appLanguageMapper.selectShortAppLanguageList(new ShortAppLanguage());
        Map<String, ShortAppLanguage> languageMap = languageList.stream().collect(Collectors.toMap(ShortAppLanguage::getCode, shortAppLanguage -> shortAppLanguage));
        if (CollectionUtil.isNotEmpty(movieRecList)) {
            List<Long> movieIdList = movieRecList.stream().map(ShortMovieRecDataDTO::getMovieId).distinct().collect(Collectors.toList());
            List<ShortMovieRec> movieRec = shortMovieRecMapper.selectByMovieIds(movieIdList);
            // 根据 appId-movieId 进行分组
            Map<String, List<ShortMovieRec>> movieRecMap = movieRec.stream().collect(Collectors.groupingBy(movie -> movie.getAppId() + "-" + movie.getMovieId() + "-" + movie.getRec()));
            for (ShortMovieRecDataDTO shortMovieRecDataDTO : movieRecList) {
                if (movieRecMap.containsKey(shortMovieRecDataDTO.getAppId() + "-" + shortMovieRecDataDTO.getMovieId() + "-" + shortMovieRecDataDTO.getRec())) {
                    List<ShortMovieRec> movieRecTmpList = movieRecMap.get(shortMovieRecDataDTO.getAppId() + "-" + shortMovieRecDataDTO.getMovieId() + "-" + shortMovieRecDataDTO.getRec());
                    shortMovieRecDataDTO.setMovieRecList(movieRecTmpList);
                    //设置多语言信息
                    List<String> collect = movieRecTmpList.stream().map(ShortMovieRec::getLanguageCode).collect(Collectors.toList());
                    shortMovieRecDataDTO.setLanguageList(collect.stream().map(languageMap::get).collect(Collectors.toList()));
                }
            }
        }
        return movieRecList;
    }

    @Override
    public int deleteMovieRec(ShortMovieRec shortMovieRec) {
        return shortMovieRecMapper.deleteMovieRec(shortMovieRec);
    }
}
