<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.ShortUserCollectAndHistoryMapper">

    <resultMap type="ShortUserCollectAndHistory" id="ShortUserCollectAndHistoryResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="movieId"    column="movie_id"    />
        <result property="url"    column="url"    />
        <result property="title"    column="title"    />
        <result property="currentEp"    column="current_ep"    />
        <result property="allEp"    column="all_ep"    />
        <result property="kid"    column="kid"    />
        <result property="type"    column="type"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectShortUserCollectAndHistoryVo">
        select id, user_id, movie_id, url, title, current_ep, all_ep, kid, type, create_by, create_time, update_by, update_time from short_user_collect_and_history
    </sql>

    <select id="selectShortUserCollectAndHistoryList" parameterType="ShortUserCollectAndHistory" resultMap="ShortUserCollectAndHistoryResult">
        <include refid="selectShortUserCollectAndHistoryVo"/>
        <where>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="movieId != null "> and movie_id = #{movieId}</if>
            <if test="url != null  and url != ''"> and url = #{url}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="currentEp != null "> and current_ep = #{currentEp}</if>
            <if test="allEp != null "> and all_ep = #{allEp}</if>
            <if test="kid != null  and kid != ''"> and kid = #{kid}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
        </where>
    </select>

    <select id="selectShortUserCollectAndHistoryById" parameterType="Long" resultMap="ShortUserCollectAndHistoryResult">
        <include refid="selectShortUserCollectAndHistoryVo"/>
        where id = #{id}
    </select>

    <insert id="insertShortUserCollectAndHistory" parameterType="ShortUserCollectAndHistory" useGeneratedKeys="true" keyProperty="id">
        insert into short_user_collect_and_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="movieId != null">movie_id,</if>
            <if test="url != null">url,</if>
            <if test="title != null">title,</if>
            <if test="currentEp != null">current_ep,</if>
            <if test="allEp != null">all_ep,</if>
            <if test="kid != null">kid,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="movieId != null">#{movieId},</if>
            <if test="url != null">#{url},</if>
            <if test="title != null">#{title},</if>
            <if test="currentEp != null">#{currentEp},</if>
            <if test="allEp != null">#{allEp},</if>
            <if test="kid != null">#{kid},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateShortUserCollectAndHistory" parameterType="ShortUserCollectAndHistory">
        update short_user_collect_and_history
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="movieId != null">movie_id = #{movieId},</if>
            <if test="url != null">url = #{url},</if>
            <if test="title != null">title = #{title},</if>
            <if test="currentEp != null">current_ep = #{currentEp},</if>
            <if test="allEp != null">all_ep = #{allEp},</if>
            <if test="kid != null">kid = #{kid},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteShortUserCollectAndHistoryById" parameterType="Long">
        delete from short_user_collect_and_history where id = #{id}
    </delete>

    <delete id="deleteShortUserCollectAndHistoryByIds" parameterType="String">
        delete from short_user_collect_and_history where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>