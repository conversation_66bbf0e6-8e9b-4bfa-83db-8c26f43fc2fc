package com.ruoyi.domain.dto;

/**
 * 跨模块关联状态DTO
 *
 * <AUTHOR>
 * @date 2025-09-27
 */
public class CrossModuleStatusDto {

    /** 在退订管理中是否有数据 */
    private Boolean hasUnsubscribeRecord = false;

    /** 在收件箱中是否有数据 */
    private Boolean hasReceiveRecord = false;

    /** 在用户反馈中是否有数据 */
    private Boolean hasFeedbackRecord = false;

    public Boolean getHasUnsubscribeRecord() {
        return hasUnsubscribeRecord;
    }

    public void setHasUnsubscribeRecord(Boolean hasUnsubscribeRecord) {
        this.hasUnsubscribeRecord = hasUnsubscribeRecord;
    }

    public Boolean getHasReceiveRecord() {
        return hasReceiveRecord;
    }

    public void setHasReceiveRecord(Boolean hasReceiveRecord) {
        this.hasReceiveRecord = hasReceiveRecord;
    }

    public Boolean getHasFeedbackRecord() {
        return hasFeedbackRecord;
    }

    public void setHasFeedbackRecord(Boolean hasFeedbackRecord) {
        this.hasFeedbackRecord = hasFeedbackRecord;
    }

    @Override
    public String toString() {
        return "CrossModuleStatusDto{" +
                "hasUnsubscribeRecord=" + hasUnsubscribeRecord +
                ", hasReceiveRecord=" + hasReceiveRecord +
                ", hasFeedbackRecord=" + hasFeedbackRecord +
                '}';
    }
}