package com.ruoyi.web.controller.short_center.controller;


import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.utils.CrossModuleEnhancementUtil;
import com.ruoyi.domain.ShortUnsubscribe;
import com.ruoyi.service.IShortUnsubscribeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 退订Controller
 *
 * <AUTHOR>
 * @date 2025-09-22
 */
@Api("退订Controller")
@RestController
@RequestMapping("/short_center/unsubscribe")
public class ShortUnsubscribeController extends BaseController
{
    @Autowired
    private IShortUnsubscribeService shortUnsubscribeService;

    @Autowired
    private CrossModuleEnhancementUtil crossModuleEnhancementUtil;

    /**
     * 查询退订列表
     */
    @ApiOperation("查询退订列表")
    @PreAuthorize("@ss.hasPermi('system:unsubscribe:list')")
    @GetMapping("/list")
    public TableDataInfo list(ShortUnsubscribe shortUnsubscribe)
    {
        startPage();
        List<ShortUnsubscribe> list = shortUnsubscribeService.selectShortUnsubscribeList(shortUnsubscribe);
        TableDataInfo dataTable = getDataTable(list);
        return crossModuleEnhancementUtil.enhanceTableData(dataTable);
    }

    /**
     * 导出退订列表
     */
    @ApiOperation("导出退订列表")
    @PreAuthorize("@ss.hasPermi('system:unsubscribe:export')")
    @Log(title = "退订", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ShortUnsubscribe shortUnsubscribe)
    {
        List<ShortUnsubscribe> list = shortUnsubscribeService.selectShortUnsubscribeList(shortUnsubscribe);
        ExcelUtil<ShortUnsubscribe> util = new ExcelUtil<ShortUnsubscribe>(ShortUnsubscribe.class);
        util.exportExcel(response, list, "退订数据");
    }

    /**
     * 获取退订详细信息
     */
    @ApiOperation("获取退订详细信息")
    @PreAuthorize("@ss.hasPermi('system:unsubscribe:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(shortUnsubscribeService.selectShortUnsubscribeById(id));
    }

    /**
     * 新增退订
     */
    @ApiOperation("新增退订")
    @PreAuthorize("@ss.hasPermi('system:unsubscribe:add')")
    @Log(title = "退订", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ShortUnsubscribe shortUnsubscribe)
    {
        return toAjax(shortUnsubscribeService.insertShortUnsubscribe(shortUnsubscribe));
    }

    /**
     * 修改退订
     */
    @ApiOperation("修改退订")
    @PreAuthorize("@ss.hasPermi('system:unsubscribe:edit')")
    @Log(title = "退订", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ShortUnsubscribe shortUnsubscribe)
    {
        return toAjax(shortUnsubscribeService.updateShortUnsubscribe(shortUnsubscribe));
    }

    /**
     * 删除退订
     */
    @ApiOperation("删除退订")
    @PreAuthorize("@ss.hasPermi('system:unsubscribe:remove')")
    @Log(title = "退订", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(shortUnsubscribeService.deleteShortUnsubscribeByIds(ids));
    }
}
