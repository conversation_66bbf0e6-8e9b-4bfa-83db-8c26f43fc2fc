package com.ruoyi.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.List;

/**
 * 影片推荐对象 short_movie_rec
 *
 * <AUTHOR>
 * @date 2025-09-08
 */
public class ShortMovieRec extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** appid */
    @Excel(name = "appid")
    private Long appId;

    /** 影片id */
    @Excel(name = "影片id")
    private Long movieId;

    /** 语言 */
    @Excel(name = "语言")
    private String languageCode;

    /** 是否推荐 */
    @Excel(name = "是否推荐")
    private String rec;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    private List<ShortMovieRec> movieRecList;

    private String beginTime;

    private String endTime;

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public List<ShortMovieRec> getMovieRecList() {
        return movieRecList;
    }

    public void setMovieRecList(List<ShortMovieRec> movieRecList) {
        this.movieRecList = movieRecList;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }

    public void setAppId(Long appId)
    {
        this.appId = appId;
    }

    public Long getAppId()
    {
        return appId;
    }

    public void setMovieId(Long movieId)
    {
        this.movieId = movieId;
    }

    public Long getMovieId()
    {
        return movieId;
    }

    public void setLanguageCode(String languageCode)
    {
        this.languageCode = languageCode;
    }

    public String getLanguageCode()
    {
        return languageCode;
    }

    public void setRec(String rec)
    {
        this.rec = rec;
    }

    public String getRec()
    {
        return rec;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("appId", getAppId())
                .append("movieId", getMovieId())
                .append("languageCode", getLanguageCode())
                .append("rec", getRec())
                .append("status", getStatus())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .toString();
    }
}
