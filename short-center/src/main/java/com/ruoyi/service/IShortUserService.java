package com.ruoyi.service;

import java.util.Date;
import java.util.List;

import com.ruoyi.app.api.dto.InitUserDTO;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.domain.*;

/**
 * 用户管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
public interface IShortUserService 
{
    /**
     * 查询用户管理
     * 
     * @param id 用户管理主键
     * @return 用户管理
     */
    public ShortUser selectShortUserById(Long id);

    /**
     * 查询用户管理列表
     * 
     * @param shortUser 用户管理
     * @return 用户管理集合
     */
    public List<ShortUser> selectShortUserList(ShortUser shortUser);

    public List<ShortUserDo> selectShortUserListAll(ShortUser shortUser) throws InterruptedException;

    /**
     * 根据第三方账号ID查询用户
     * 
     * @param subId 第三方账号ID
     * @return 用户信息
     */
    public ShortUser selectShortUserBySubId(String subId);

    /**
     * 新增用户管理
     * 
     * @param shortUser 用户管理
     * @return 结果
     */
    public int insertShortUser(ShortUser shortUser);

    /**
     * 修改用户管理
     * 
     * @param shortUser 用户管理
     * @return 结果
     */
    public int updateShortUser(ShortUser shortUser);

    /**
     * 批量删除用户管理
     * 
     * @param ids 需要删除的用户管理主键集合
     * @return 结果
     */
    public int deleteShortUserByIds(Long[] ids);

    /**
     * 删除用户管理信息
     * 
     * @param id 用户管理主键
     * @return 结果
     */
    public int deleteShortUserById(Long id);


    ShortCountUser getUserCountInfo(ShortUser shortUser);
    
    /**
     * 获取VIP订阅类型
     * 
     * @param vipId VIP ID
     * @return 订阅类型
     */
    public String getVipSubscriptionType(Long vipId);

    ShortUser findByIdAndAppIdAndSource(String uid, Integer nid, String source);

    /**
     * 更新用户的VIP ID
     *
     * @param userId 用户ID
     * @param vipId VIP ID
     * @return 结果
     */
    public int updateUserVipId(Long userId, Long vipId);

    /**
     * 使用行锁查询用户信息
     * 通过SQL的FOR UPDATE实现行级锁定，确保在事务完成前其他事务无法修改用户数据
     *
     * @param userId 用户ID
     * @return 带行锁的用户对象
     */
    public ShortUser selectShortUserWithLock(Long userId);


    void refreshCountry();

    List<ShortUser> getAllSubUsers();

    int updateUnsub(Long id) throws Exception;

    List<ShortUserDo> selectShortUserListExportAll(ShortUser shortUser);

    void updateState(String userId, int i);

    List<ShortUser> getAllByTypeAndExpireTime(String type,String payChannel,int payType);

    List<ShortUser> getRenewSubscribe();

    void updateOther(Long id, String ot);

    String getIconByUserId(Long userId);

    List<ShortUser> getByLaset();

    int updateShortUserChooseLanguage(String uid, String languageCode);

    List<Long> getChildrenByUserId(Long userId,List<Long> resList);

    List<ShortUserCP> selectShortUserByTime(String beginTime, String endTime, Long nextId);

    List<ShortUser> getBackRegEmail();

    ShortUser selectShortUserByUserId(Long id);

    ShortUser selectByAppIdAndUnid(Integer nid, String unid);

    ShortUser getAfterExpireTime(String email);

    void updateExpireTimeById(Long id, Date afterExpireTime);

    List<Long> getResList(List<Long> userIds);

    void setIsSubscriber(Long id);

    void updateUserVipStatus(Long userId) throws Exception;


    List<ShortUser> get816();

    List<ShortUser> getRenewSubscribe1();

    AjaxResult sendUnSubEmailForward(InitUserDTO shortBrevo, ShortMovie shortMovie, String type) throws Exception;

    void sendUnSubEmailForward1(InitUserDTO shortBrevo, ShortMovie shortMovie, String type) throws Exception;

    List<ShortUser> getBy818();

    List<ShortUser> getEmailAndIsSubscriber();

    int countEmail(String receiveEmail);

    ShortUser getOldUser(Long appId, String email);

    int countByEmailAndAppId(String appId, String email);

    ShortUser getLastUser(String email);

    List<Long> getExpiredUser();

    void updateIsSubscriberByIds(List<Long> ids);

    String getJumpUrlByType(Long userId);

    String getRandomDomainByAppId(Long appId);

    void updateUnsubSource(Long id, String content);

    AjaxResult getCheckByUserId(Long userId);

    AjaxResult getCheckByUserIdAll();

    List<Long> getNullEmailListIds();

    List<Long> getEmailListIds();

    List<String> getEmailList(Long appId);

    List<Long> getByEmail(String email, Long appId,int type);

    List<String> getEmailMarkTypeIds();

    List<String> getEmailNotApp25Ids();

    List<ShortUser> getCycleList();

    void updateCodeAndCycleNum(Long id, int cycleNum);

    List<Long> getNoEmailList();

    List<Long> getEmailList1();

    List<Long> getEmailAll25();

    List<String> getEmailListByAll25(List<Long> emailAll25);

    List<Long> getAllNotEmailListByAll25(List<String> emailListByAll25);

    void updateLastCode(Long userId, String code);

    List<ShortUser> getIds(List<Long> mergedList);

    List<String> getEmail25AndOtherList();
}
