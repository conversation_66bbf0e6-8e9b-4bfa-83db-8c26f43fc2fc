<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.ShortMovieRecMapper">

    <resultMap type="ShortMovieRec" id="ShortMovieRecResult">
        <result property="id"    column="id"    />
        <result property="appId"    column="app_id"    />
        <result property="movieId"    column="movie_id"    />
        <result property="languageCode"    column="language_code"    />
        <result property="rec"    column="rec"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectShortMovieRecVo">
        select id, app_id, movie_id, language_code, rec, status, create_by, create_time, update_by, update_time, remark from short_movie_rec
    </sql>

    <select id="selectShortMovieRecList" parameterType="ShortMovieRec" resultMap="ShortMovieRecResult">
        <include refid="selectShortMovieRecVo"/>
        <where>
            <if test="appId != null "> and app_id = #{appId}</if>
            <if test="movieId != null "> and movie_id = #{movieId}</if>
            <if test="languageCode != null  and languageCode != ''"> and language_code = #{languageCode}</if>
            <if test="rec != null  and rec != ''"> and rec = #{rec}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>

    <select id="selectShortMovieRecById" parameterType="Long" resultMap="ShortMovieRecResult">
        <include refid="selectShortMovieRecVo"/>
        where id = #{id}
    </select>
    <select id="getMoviesByRec" resultType="com.ruoyi.domain.ShortMovie">
        SELECT
        sm.id,
        sm.NAME,
        sm.oldname,
        sm.icon,
        sm.icon_no_word,
        sm.director,
        sm.actors,
        sm.up_time,
        sm.TIME,
        sm.num,
        sm.rating,
        sm.description,
        sm.content,
        sm.vip_num,
        sm.is_vip,
        sm.source,
        sm.rec,
        sm.state,
        sm.addtime,
        sm.updatetime,
        sm.cate_id,
        sm.lang_id,
        sm.app_id,
        sm.unit_coin,
        sm.check_state,
        sm.check_log,
        sm.STATUS,
        sm.releasetime
        FROM
        short_movie sm
        INNER JOIN short_movie_rec mi on mi.movie_id = sm.id
        where
        sm.state = 1
        and sm.status = 1
        and mi.app_id = #{nid}
        and mi.status = 0
        <if test="languageCode != null">and mi.language_code = #{languageCode} </if>
        <if test="type > 0">and mi.rec = #{type}</if>
        <if test="ifApp > 0">and sm.id &lt; 60 </if>
        <if test="exTime != null">and sm.releasetime &lt; #{exTime} </if>
        ORDER BY sm.rec
    </select>
    <select id="check" resultType="java.lang.Integer">
        SELECT
            COUNT(id)
        FROM
            short_movie_rec
        WHERE
            app_id = #{appId}
          AND movie_id = #{movieId}
          AND language_code = #{languageCode}
          AND rec =#{rec}
    </select>
    <select id="pageQuery" resultType="com.ruoyi.dto.ShortMovieRecDataDTO">
        SELECT
            t1.app_id appId,
            t1.movie_id movieId,
            t1.rec,
            any_value ( t2.`name` ) movieName,
            any_value ( t2.oldname ) zhMovieName,
            max(t1.create_time) recTime
        FROM
            short_movie_rec t1
                INNER JOIN short_movie t2 ON t1.movie_id = t2.id
        where 1 = 1
        <if test="appId != null">and t1.app_id = #{appId}</if>
        <if test="movieId != null">and t1.movie_id = #{movieId}</if>
        <if test="languageCode != null">and t1.language_code = #{languageCode}</if>
        <if test="rec != null">and t1.rec = #{rec}</if>
        GROUP BY
            t1.app_id,
            t1.movie_id,
            t1.rec
        <if test=" beginTime != null and endTime != null ">
            having recTime between #{beginTime} and #{endTime}
        </if>
        order by recTime desc
    </select>
    <select id="selectByMovieIds" resultMap="ShortMovieRecResult">
        <include refid="selectShortMovieRecVo"/>
        WHERE
            movie_id IN
        <foreach item="item" collection="list" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <insert id="insertShortMovieRec" parameterType="ShortMovieRec" useGeneratedKeys="true" keyProperty="id">
        insert into short_movie_rec
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appId != null">app_id,</if>
            <if test="movieId != null">movie_id,</if>
            <if test="languageCode != null and languageCode != ''">language_code,</if>
            <if test="rec != null and rec != ''">rec,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appId != null">#{appId},</if>
            <if test="movieId != null">#{movieId},</if>
            <if test="languageCode != null and languageCode != ''">#{languageCode},</if>
            <if test="rec != null and rec != ''">#{rec},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateShortMovieRec" parameterType="ShortMovieRec">
        update short_movie_rec
        <trim prefix="SET" suffixOverrides=",">
            <if test="appId != null">app_id = #{appId},</if>
            <if test="movieId != null">movie_id = #{movieId},</if>
            <if test="languageCode != null and languageCode != ''">language_code = #{languageCode},</if>
            <if test="rec != null and rec != ''">rec = #{rec},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateStatus">
        update short_movie_rec set status = #{status} where movie_id = #{movieId} and app_id = #{appId}
    </update>

    <delete id="deleteShortMovieRecById" parameterType="Long">
        delete from short_movie_rec where id = #{id}
    </delete>

    <delete id="deleteShortMovieRecByIds" parameterType="String">
        delete from short_movie_rec where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteMovieRec">
        delete from short_movie_rec where movie_id = #{movieId} and app_id = #{appId} and rec = #{rec}
    </delete>
</mapper>