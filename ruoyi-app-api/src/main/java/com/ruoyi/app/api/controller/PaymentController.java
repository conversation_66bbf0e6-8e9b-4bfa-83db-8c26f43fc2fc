package com.ruoyi.app.api.controller;

import com.alibaba.fastjson.JSON;
import com.ruoyi.app.api.dto.PaymentDisputeDTO;
import com.ruoyi.app.api.request.PayOrderRequest;
import com.ruoyi.app.api.service.PaymentApiService;
import com.ruoyi.app.api.service.impl.AirwallexDisputeService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.DisputeType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.domain.ShortDisputeRecord;
import com.ruoyi.domain.ShortOrder;
import com.ruoyi.domain.ShortRunlog;
import com.ruoyi.service.IShortAppService;
import com.ruoyi.service.IShortDisputeRecordService;
import com.ruoyi.service.IShortOrderService;
import com.ruoyi.service.IShortRunlogService;
import io.swagger.annotations.*;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 支付相关API控制器
 */
@Api(tags = "支付相关API")
@RestController
@RequestMapping("/api")
public class PaymentController {

    private static final Logger log = LoggerFactory.getLogger(PaymentController.class);

    @Autowired
    private PaymentApiService paymentApiService;

    @Autowired
    private IShortAppService iShortAppService;

    @Autowired
    private AirwallexDisputeService airwallexDisputeService;

    @Autowired
    private IShortOrderService orderService;

    @Autowired
    private IShortRunlogService shortRunlogService;

    @Autowired
    private IShortDisputeRecordService shortDisputeRecordService;

    @ApiOperation(value = "获取支付渠道", notes = "根据应用获取对应的支付渠道")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "nid", value = "app_id", paramType = "header", required = true)
    })
    @GetMapping("/get_channel/")
    public AjaxResult getChannel(HttpServletRequest request) {
        String appId = request.getHeader("nid");
        log.info("获取应用id：【{}】支付渠道", appId);
        String channel = iShortAppService.getPayChannelByAppId(appId);
        Map<String, String> result = new HashMap<>();
        result.put("payChannel", channel);
        log.info("获取应用id：【{}】支付渠道为【{}】", appId, channel);
        return AjaxResult.success("success", result);
    }

    /**
     * 创建意向支付
     */
    @ApiOperation(value = "创建意向支付", notes = "获取ext_client_id，用于支付处理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "nid", value = "app_id", paramType = "header", required = true),
            @ApiImplicitParam(name = "emailFlag", value = "邮件营销活动标识", paramType = "header", required = false),
            @ApiImplicitParam(name = "user_id", value = "用户ID", paramType = "query", required = false),
            @ApiImplicitParam(name = "amount_id", value = "金额ID", paramType = "query", required = false)
    })
    @PostMapping("/create_intent/")
    public AjaxResult createIntent(
            @ApiParam(value = "支付创建请求参数", required = false, example =
                    "{\n" +
                            "  \"user_id\": \"temp1234\",\n" +
                            "  \"amount_id\": \"1\"\n" +
                            "}"
            )
            @RequestBody(required = false) Map<String, Object> requestBody,
            @RequestParam(value = "user_id", required = false) String userIdParam,
            @RequestParam(value = "amount_id", required = false) String amountIdParam,
            @RequestParam(value = "paySuccessLink", required = false) String paySuccessLink,
            @RequestParam(value = "locale", required = false) String locale,
            @RequestParam(value = "isLink", required = false, defaultValue = "false") Boolean isLink,
            @RequestParam(value = "originalPrice", required = false) BigDecimal originalPrice,
            HttpServletRequest request) {
        // 封装请求参数
        PayOrderRequest payOrderRequest = packageRequestParams(requestBody, userIdParam, amountIdParam, paySuccessLink, locale, isLink, request,originalPrice);

        // 调用服务创建支付意图，传递request参数
        Map<String, Object> result = paymentApiService.createIntent(payOrderRequest);

        // 处理返回结果
        return getAjaxResult(result);
    }

    @NotNull
    private static AjaxResult getAjaxResult(Map<String, Object> result) {
        Integer code = (Integer) result.get("code");
        String msg = (String) result.get("msg");

        if (code == 200 && result.containsKey("data")) {
            Object data = result.get("data");
            return AjaxResult.success(msg, data);
        }

        return AjaxResult.error(code, msg);
    }

    private PayOrderRequest packageRequestParams(Map<String, Object> requestBody, String userIdParam, String amountIdParam, String link, String language, Boolean isLink, HttpServletRequest request,BigDecimal originalPrice) {
        // 获取AppId
        String appId = request.getHeader("nid");

        // 获取token
        String token = request.getHeader("Authorization");

        // 同时支持URL参数和请求体参数，优先使用有值的参数
        String userId = null;
        String amountId = null;
        String paySuccessLink = null;
        String locale = null;

        // 从请求体获取参数
        if (requestBody != null) {
            if (requestBody.containsKey("user_id")) {
                userId = (String) requestBody.get("user_id");
            }
            if (requestBody.containsKey("amount_id")) {
                amountId = (String) requestBody.get("amount_id");
            }
            if (requestBody.containsKey("paySuccessLink")) {
                paySuccessLink = (String) requestBody.get("paySuccessLink");
            }
            if (requestBody.containsKey("locale")) {
                locale = (String) requestBody.get("locale");
            }
        }

        // 如果URL参数有值，优先使用URL参数
        if (StringUtils.isNotEmpty(userIdParam)) {
            userId = userIdParam;
        }
        if (StringUtils.isNotEmpty(amountIdParam)) {
            amountId = amountIdParam;
        }
        if (StringUtils.isNotEmpty(link)) {
            paySuccessLink = link;
        }
        if (StringUtils.isNotEmpty(language)) {
            locale = language;
        }

        // 获取邮件营销标识
        String emailFlag = request.getHeader("emailFlag") == null ? "false" : request.getHeader("emailFlag");
        boolean isEmailMarketing = Boolean.parseBoolean(emailFlag);

        log.info("【创建意向单请求参数】:用户【{}】，金额id【{}】，应用【{}】,邮件营销标识【{}】", userId, amountId, appId, isEmailMarketing);
        return PayOrderRequest.builder()
                .userId(userId)
                .amountId(amountId)
                .appId(appId)
                .isEmailMarketing(isEmailMarketing)
                .token(token)
                .paySuccessLink(paySuccessLink)
                .locale(locale)
                .isLink(isLink)
                .originalPrice(originalPrice)
                .build();
    }

    /**
     * 获取支付状态
     */
    @ApiOperation(value = "获取支付状态", notes = "通过订单ID获取支付状态")
    @PostMapping("/get_order_stauts/")
    public AjaxResult getOrderStatus(@RequestBody Map<String, String> orderInfo) {
        // 获取商户订单ID
        String merchantOrderId = orderInfo.get("merchant_order_id");

        if (merchantOrderId == null || merchantOrderId.isEmpty()) {
            return AjaxResult.error(400, "Invalid JSON data");
        }

        // 调用服务获取订单状态
        Map<String, Object> result = paymentApiService.getOrderStatus(merchantOrderId);

        // 处理返回结果
        Integer code = (Integer) result.get("code");

        if (code == 200 && result.containsKey("data")) {
            @SuppressWarnings("unchecked")
            Map<String, Object> data = (Map<String, Object>) result.get("data");
            return AjaxResult.success("获取支付状态成功", data);
        }

        String msg = (String) result.get("msg");
        return AjaxResult.error(code, msg);
    }

    /**
     * 支付意图回调
     */
    @ApiOperation(value = "支付意图回调", notes = "处理支付平台的回调请求，包括支付完成和争议事件")
    @PostMapping("/v1/pa/payment_intents/callback")
    public AjaxResult paymentIntentsCallback(@RequestBody Map<String, Object> callbackData) {
        try {
            // 判断是否为争议事件
            if (callbackData != null && callbackData.containsKey("name")
                    && callbackData.get("name") instanceof String
                    && ((String) callbackData.get("name")).startsWith("payment_dispute.")) {

                log.info("接收到争议事件回调: {}", callbackData.get("name"));

                // 转换为争议DTO处理
                PaymentDisputeDTO disputeDTO = JSON.parseObject(JSON.toJSONString(callbackData), PaymentDisputeDTO.class);

                /*if(PaymentDisputeDTO.EVENT_REQUIRES_RESPONSE.equals(disputeDTO.getName())){
                    String disputeId = disputeDTO.getDisputeId();
                    String paymentIntentId = disputeDTO.getPaymentIntentId();
                    ShortOrder queryOrder = new ShortOrder();
                    queryOrder.setPaymentIntentId(paymentIntentId);
                    List<ShortOrder> orders = orderService.selectShortOrderList(queryOrder);
                    // 接受争议
                    PaymentStrategy strategy = paymentFactory.getStrategy("AIRWALLEX");
                    return strategy.disputeOrder(orders.get(0), disputeId);
                }*/

                // 保存争议信息
                saveDisputeOrderInfo(disputeDTO);

                return airwallexDisputeService.handleDisputeCallback(disputeDTO);
            }

            // 处理正常支付回调
            Map<String, Object> result = paymentApiService.handlePaymentCallback(callbackData);

            // 处理返回结果
            Integer code = (Integer) result.get("code");

            if (code == 200 && result.containsKey("data")) {
                @SuppressWarnings("unchecked")
                Map<String, Object> data = (Map<String, Object>) result.get("data");
                return AjaxResult.success("处理回调成功", data);
            }

            String msg = (String) result.get("msg");
            return AjaxResult.error(code, msg);
        } catch (Exception e) {
            log.error("处理回调异常: {}", e.getMessage(), e);
            return AjaxResult.error("处理回调异常: " + e.getMessage());
        }
    }

    private void saveDisputeOrderInfo(PaymentDisputeDTO disputeDTO) {
        // 查询订单
        String disputeId = disputeDTO.getDisputeId();
        String paymentIntentId = disputeDTO.getPaymentIntentId();
        if (StringUtils.isEmpty(disputeId) || StringUtils.isEmpty(paymentIntentId)) {
            log.error("争议回调缺少必要参数");
            ShortRunlog shortRunlog = new ShortRunlog();
            shortRunlog.setType("DISPUTE_ORDER");
            shortRunlog.setContent(String.format("争议回调缺少必要参数:%s", disputeDTO));
            shortRunlog.setState("0");
            shortRunlogService.insertShortRunlog(shortRunlog);
            return;
        }
        ShortOrder queryOrder = new ShortOrder();
        queryOrder.setPaymentIntentId(paymentIntentId);
        List<ShortOrder> orders = orderService.selectShortOrderList(queryOrder);
        if (CollectionUtils.isEmpty(orders)) {
            log.info("订单不存在, 订单ID: {}, 支付意图ID: {}", orders.get(0).getId(), paymentIntentId);
            ShortRunlog shortRunlog = new ShortRunlog();
            shortRunlog.setType("DISPUTE_ORDER");
            shortRunlog.setContent("订单不存在, 争议ID: " + disputeId + ", 支付意图ID: " + paymentIntentId);
            shortRunlog.setState("0");
            shortRunlogService.insertShortRunlog(shortRunlog);
            return;
        }
        ShortOrder order = orders.get(0);
        ShortDisputeRecord shortDisputeRecord = shortDisputeRecordService.selectByDisputeId(disputeId);
        if (shortDisputeRecord != null) {
            return;
        }
        ShortDisputeRecord insert = new ShortDisputeRecord();
        insert.setDisputeId(disputeId);
        insert.setOrderId(order.getId());
        insert.setPaymentId(paymentIntentId);
        insert.setDisputeStatus(disputeDTO.getName());
        insert.setDisputeResult("争议通知");
        if (disputeDTO.getData() != null && disputeDTO.getData().getObject() != null) {
            PaymentDisputeDTO.DisputeObject object = disputeDTO.getData().getObject();
            DisputeType disputeType = DisputeType.fromString(object.getType());
            insert.setDisputeType(disputeType.getCode());
            insert.setCurrency(object.getCurrency());
        }
        insert.setDisputeReason(disputeDTO.getReason());
        insert.setDisputeAmount(disputeDTO.getAmount());
        insert.setDisputeTime(DateUtils.getNowDate());
        shortDisputeRecordService.insertShortDisputeRecord(insert);
    }

    /**
     * 获取VIP类型数据
     */
    @ApiOperation(value = "获取VIP类型数据", notes = "获取指定充值模板的VIP类型数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "tid", value = "充值模板ID", paramType = "header", required = true)
    })
    @GetMapping("/get_vip_list/")
    public AjaxResult getVipList(HttpServletRequest request) {
        // 获取充值模板ID
        String templateId = request.getHeader("tid");
        String userId = request.getHeader("userId") == null ? "-1" : request.getHeader("userId");
        String emailFlag = request.getHeader("emailFlag") == null ? "false" : request.getHeader("emailFlag");

        String linkAppId = request.getHeader("linkAppId");
        String linkId = request.getHeader("linkId");
        log.info("【获取VIP列表请求参数】:充值模板ID【{}】,用户ID【{}】,邮件营销标识【{}】,链接应用ID【{}】,链接ID【{}】", templateId, userId, emailFlag, linkAppId, linkId);
        // 调用服务获取VIP列表
        Map<String, Object> result = paymentApiService.getVipList(templateId, Boolean.parseBoolean(emailFlag), userId, linkAppId, linkId);

        // 处理返回结果
        Integer code = (Integer) result.get("code");

        if (code == 200 && result.containsKey("data")) {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> data = (List<Map<String, Object>>) result.get("data");
            return AjaxResult.success("success", data);
        }

        String msg = (String) result.get("msg");
        return AjaxResult.error(code, msg);
    }

    @ApiOperation(value = "获取纯金币充值模版", notes = "获取纯金币充值模版")
    @GetMapping("/get_coin_template")
    public AjaxResult getCoinTemplate() {
        Map<String, Object> result = paymentApiService.getCoinTemplate(548L);
        // 处理返回结果
        Integer code = (Integer) result.get("code");

        if (code == 200 && result.containsKey("data")) {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> data = (List<Map<String, Object>>) result.get("data");
            return AjaxResult.success("success", data);
        }
        String msg = (String) result.get("msg");
        return AjaxResult.error(code, msg);
    }

    /**
     * 获取金币消费记录
     */
    @ApiOperation(value = "获取金币消费记录", notes = "获取用户的所有金币消费记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "Authorization", paramType = "header", required = false),
            @ApiImplicitParam(name = "uid", value = "用户ID", paramType = "query", required = false)
    })
    @GetMapping("/coin-records/")
    public AjaxResult getCoinRecords(HttpServletRequest request, @RequestParam(value = "uid", required = false) String uid) {
        // 获取token或uid
        String token = request.getHeader("Authorization");
        String uidHeader = request.getHeader("uid");

        // 如果请求参数提供了uid，优先使用
        if (uid == null || uid.isEmpty()) {
            uid = uidHeader;
        }

        // 调用服务获取金币记录
        Map<String, Object> result = paymentApiService.getCoinRecords(token, uid);

        // 处理返回结果
        Integer code = (Integer) result.get("code");

        if (code == 200 && result.containsKey("data")) {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> data = (List<Map<String, Object>>) result.get("data");
            return AjaxResult.success(data);
        }

        String msg = (String) result.get("msg");
        return AjaxResult.error(code, msg);
    }

    /**
     * 扣除用户金币
     */
    @ApiOperation(value = "扣除用户金币", notes = "从用户账户扣除金币，用于解锁视频内容")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "Authorization", paramType = "header", required = false, dataType = "String"),
            @ApiImplicitParam(name = "nid", value = "app_id", paramType = "header", required = true, dataType = "Integer"),
            @ApiImplicitParam(name = "uid", value = "uid,临时用户id", paramType = "header", required = false, dataType = "String")
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "扣除成功", response = Map.class),
            @ApiResponse(code = 403, message = "金币不足"),
            @ApiResponse(code = 404, message = "用户不存在"),
            @ApiResponse(code = 400, message = "无效的JSON数据或参数"),
            @ApiResponse(code = 500, message = "内部服务器错误")
    })
    @PostMapping("/deduct_user_coin/")
    public AjaxResult deductUserCoin(HttpServletRequest request,
                                     @ApiParam(value = "扣除金币请求参数", required = true, example =
                                             "{\n" +
                                                     "  \"movie\": 1,\n" +
                                                     "  \"video\": 1,\n" +
                                                     "  \"constype\": \"unlock_video\",\n" +
                                                     "  \"unlock_type\": \"0\",\n" +
                                                     "  \"kid\": \"1\"\n" +
                                                     "}"
                                     )
                                     @RequestBody Map<String, Object> deductData) {
        // 获取请求头参数
        String token = request.getHeader("Authorization");
        String appId = request.getHeader("nid");
        String uid = request.getHeader("uid");

        Map<String, Object> result = paymentApiService.deductUserCoin(token, appId, uid, deductData);

        // 处理返回结果
        Integer code = (Integer) result.get("code");

        if (code == 200 && result.containsKey("data")) {
            @SuppressWarnings("unchecked")
            Map<String, Object> data = (Map<String, Object>) result.get("data");
            return AjaxResult.success((String) result.get("msg"), data);
        }

        String msg = (String) result.get("msg");
        if (result.containsKey("data")) {
            @SuppressWarnings("unchecked")
            Map<String, Object> data = (Map<String, Object>) result.get("data");
            return new AjaxResult(code, msg, data);
        }
        return AjaxResult.error(code, msg);
    }

//    @ApiOperation(value = "接受争议", notes = "接受争议")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "orderId", value = "订单ID", required = true, paramType = "path")
//    })
//    @GetMapping("/payment/acceptDispute/{orderId}")
//    public AjaxResult handleAcceptDispute(@PathVariable("orderId") Long orderId) {
//        // 调用服务处理争议用户
//        return paymentApiService.handleAcceptDispute(orderId);
//    }
}
