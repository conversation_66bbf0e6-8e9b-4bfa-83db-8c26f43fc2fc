<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.ShortRenewalRecordMapper">

    <resultMap type="ShortRenewalRecord" id="ShortRenewalRecordResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="appId"    column="app_id"    />
        <result property="logDate"    column="log_date"    />
        <result property="type"    column="type"    />
        <result property="createTime"    column="create_time"    />
        <result property="emailType"    column="email_type"    />
    </resultMap>

    <sql id="selectShortRenewalRecordVo">
        select id, user_id, app_id, log_date, type, create_time,email_type from short_renewal_record
    </sql>

    <select id="selectShortRenewalRecordList" parameterType="ShortRenewalRecord" resultMap="ShortRenewalRecordResult">
        <include refid="selectShortRenewalRecordVo"/>
        <where>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="appId != null "> and app_id = #{appId}</if>
            <if test="logDate != null  and logDate != ''"> and log_date = #{logDate}</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="emailType != null "> and email_type = #{emailType}</if>

        </where>
    </select>

    <select id="selectShortRenewalRecordById" parameterType="Long" resultMap="ShortRenewalRecordResult">
        <include refid="selectShortRenewalRecordVo"/>
        where id = #{id}
    </select>

    <insert id="insertShortRenewalRecord" parameterType="ShortRenewalRecord">
        insert into short_renewal_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userId != null">user_id,</if>
            <if test="appId != null">app_id,</if>
            <if test="logDate != null and logDate != ''">log_date,</if>
            <if test="type != null">type,</if>
            <if test="createTime != null">create_time,</if>
            <if test="emailType != null">email_type,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userId != null">#{userId},</if>
            <if test="appId != null">#{appId},</if>
            <if test="logDate != null and logDate != ''">#{logDate},</if>
            <if test="type != null">#{type},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="emailType != null">#{emailType},</if>
        </trim>
    </insert>
    <insert id="insertBatch">
        insert into short_renewal_record
        ( user_id,
        app_id,
        log_date,
        type,
        create_time,
        email_type
        ) values
        <foreach collection="list" item="item" separator=",">
            (#{item.userId},
            #{item.appId},
            #{item.logDate},
            #{item.type},
            #{item.createTime},
            #{item.emailType}
            )
        </foreach>
    </insert>

    <update id="updateShortRenewalRecord" parameterType="ShortRenewalRecord">
        update short_renewal_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="appId != null">app_id = #{appId},</if>
            <if test="logDate != null and logDate != ''">log_date = #{logDate},</if>
            <if test="type != null">type = #{type},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="emailType != null">email_type = #{emailType},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteShortRenewalRecordById" parameterType="Long">
        delete from short_renewal_record where id = #{id}
    </delete>

    <delete id="deleteShortRenewalRecordByIds" parameterType="String">
        delete from short_renewal_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>