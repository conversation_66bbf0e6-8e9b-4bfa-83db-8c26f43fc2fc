<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.BankCardStatisticsMapper">

    <resultMap type="BankCardStatistics" id="BankCardStatisticsResult">
        <result property="id"    column="id"    />
        <result property="cardNumber"    column="card_number"    />
        <result property="userId"    column="user_id"    />
        <result property="historyUserIds"    column="history_user_ids"    />
        <result property="relatedUsers"    column="related_users"    />
        <result property="status"    column="status"    />
        <result property="subTotalAmount"    column="sub_total_amount"    />
        <result property="subTotalCount"    column="sub_total_count"    />
        <result property="recTotalAmount"    column="rec_total_amount"    />
        <result property="recTotalCount"    column="rec_total_count"    />
        <result property="createTime"    column="created_at"    />
        <result property="updateTime"    column="last_update"    />
    </resultMap>

    <sql id="selectBankCardStatisticsVo">
        select id, card_number, user_id, history_user_ids, related_users, status,
               sub_total_amount, sub_total_count, rec_total_amount, rec_total_count,
               created_at, last_update from bank_card_statistics
    </sql>

    <select id="selectBankCardStatisticsList" parameterType="BankCardStatistics" resultMap="BankCardStatisticsResult">
        <include refid="selectBankCardStatisticsVo"/>
        <where>
            <if test="cardNumber != null  and cardNumber != ''"> and card_number like concat('%', #{cardNumber}, '%')</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="relatedUsers != null "> and related_users = #{relatedUsers}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by last_update desc
    </select>

    <select id="selectBankCardStatisticsById" parameterType="Long" resultMap="BankCardStatisticsResult">
        <include refid="selectBankCardStatisticsVo"/>
        where id = #{id}
    </select>

    <select id="selectBankCardStatisticsByCardNumber" parameterType="String" resultMap="BankCardStatisticsResult">
        <include refid="selectBankCardStatisticsVo"/>
        where card_number = #{cardNumber}
    </select>

    <insert id="insertBankCardStatistics" parameterType="BankCardStatistics" useGeneratedKeys="true" keyProperty="id">
        insert into bank_card_statistics
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="cardNumber != null and cardNumber != ''">card_number,</if>
            <if test="userId != null">user_id,</if>
            <if test="historyUserIds != null">history_user_ids,</if>
            <if test="relatedUsers != null">related_users,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="subTotalAmount != null">sub_total_amount,</if>
            <if test="subTotalCount != null">sub_total_count,</if>
            <if test="recTotalAmount != null">rec_total_amount,</if>
            <if test="recTotalCount != null">rec_total_count,</if>
            <if test="createTime != null">created_at,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="cardNumber != null and cardNumber != ''">#{cardNumber},</if>
            <if test="userId != null">#{userId},</if>
            <if test="historyUserIds != null">#{historyUserIds},</if>
            <if test="relatedUsers != null">#{relatedUsers},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="subTotalAmount != null">#{subTotalAmount},</if>
            <if test="subTotalCount != null">#{subTotalCount},</if>
            <if test="recTotalAmount != null">#{recTotalAmount},</if>
            <if test="recTotalCount != null">#{recTotalCount},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <update id="updateBankCardStatistics" parameterType="BankCardStatistics">
        update bank_card_statistics
        <trim prefix="SET" suffixOverrides=",">
            <if test="cardNumber != null and cardNumber != ''">card_number = #{cardNumber},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="historyUserIds != null">history_user_ids = #{historyUserIds},</if>
            <if test="relatedUsers != null">related_users = #{relatedUsers},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="subTotalAmount != null">sub_total_amount = #{subTotalAmount},</if>
            <if test="subTotalCount != null">sub_total_count = #{subTotalCount},</if>
            <if test="recTotalAmount != null">rec_total_amount = #{recTotalAmount},</if>
            <if test="recTotalCount != null">rec_total_count = #{recTotalCount},</if>
            last_update = NOW(),
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBankCardStatisticsById" parameterType="Long">
        delete from bank_card_statistics where id = #{id}
    </delete>

    <delete id="deleteBankCardStatisticsByIds" parameterType="String">
        delete from bank_card_statistics where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 业务相关查询 -->
    <update id="updateSubscriptionStats">
        UPDATE bank_card_statistics SET
            sub_total_count = sub_total_count + #{count},
            sub_total_amount = sub_total_amount + #{amount},
            last_update = NOW()
        WHERE card_number = #{cardNumber}
    </update>

    <update id="updateRechargeStats">
        UPDATE bank_card_statistics SET
            rec_total_count = rec_total_count + #{count},
            rec_total_amount = rec_total_amount + #{amount},
            last_update = NOW()
        WHERE card_number = #{cardNumber}
    </update>

    <update id="updateUserAssociation">
        UPDATE bank_card_statistics SET
            user_id = #{userId},
            history_user_ids = #{historyUserIds},
            related_users = #{relatedUsers},
            last_update = NOW()
        WHERE card_number = #{cardNumber}
    </update>

    <select id="selectActiveCardNumbers" parameterType="java.time.LocalDate" resultType="String">
        SELECT DISTINCT card_number FROM short_order
        WHERE card_number IS NOT NULL
        AND DATE(create_time) = #{targetDate}
    </select>

    <select id="selectCardStatisticsByDateRange" resultType="Object">
        SELECT
            COUNT(*) as order_count,
            COALESCE(SUM(amount), 0) as total_amount,
            COALESCE(SUM(CASE WHEN pay_type IN ('订阅', '订阅续费') THEN 1 ELSE 0 END), 0) as subscription_count,
            COALESCE(SUM(CASE WHEN pay_type IN ('订阅', '订阅续费') THEN amount ELSE 0 END), 0) as subscription_amount,
            COALESCE(SUM(CASE WHEN pay_type = '充值' THEN 1 ELSE 0 END), 0) as recharge_count,
            COALESCE(SUM(CASE WHEN pay_type = '充值' THEN amount ELSE 0 END), 0) as recharge_amount
        FROM short_order
        WHERE card_number = #{cardNumber}
        AND DATE(create_time) >= #{startDate}
        AND DATE(create_time) &lt;= #{endDate}
    </select>

    <select id="selectUserSubscriptionStartDate" parameterType="Long" resultType="java.time.LocalDate">
        SELECT DATE(MIN(create_time)) FROM short_order
        WHERE user_id = #{userId} AND pay_type IN ('订阅', '订阅续费')
    </select>

    <select id="selectMultiUserCards" resultMap="BankCardStatisticsResult">
        <include refid="selectBankCardStatisticsVo"/>
        WHERE related_users > 1
        ORDER BY related_users DESC, last_update DESC
    </select>

    <select id="countByStatus" resultType="Object">
        SELECT status, COUNT(*) as count FROM bank_card_statistics GROUP BY status
    </select>

    <select id="selectCardOrdersByDate" resultType="map">
        SELECT user_id, pay_type, amount FROM short_order
        WHERE card_number = #{cardNumber}
        AND DATE(create_time) = #{targetDate}
        AND status = 'SUCCEEDED'
    </select>

    <select id="getCardFirstUseDate" parameterType="String" resultType="java.time.LocalDate">
        SELECT DATE(MIN(create_time)) FROM short_order
        WHERE card_number = #{cardNumber}
        AND status = 'SUCCEEDED'
    </select>

    <select id="getCardStatsByDateRange" resultType="map">
        SELECT
            COUNT(*) as total_count,
            COALESCE(SUM(amount), 0) as total_amount,
            COALESCE(SUM(CASE WHEN pay_type IN ('订阅', '订阅续费') THEN 1 ELSE 0 END), 0) as subscription_count,
            COALESCE(SUM(CASE WHEN pay_type IN ('订阅', '订阅续费') THEN amount ELSE 0 END), 0) as subscription_amount,
            COALESCE(SUM(CASE WHEN pay_type = '充值' THEN 1 ELSE 0 END), 0) as recharge_count,
            COALESCE(SUM(CASE WHEN pay_type = '充值' THEN amount ELSE 0 END), 0) as recharge_amount,
            GROUP_CONCAT(DISTINCT CASE WHEN pay_type IN ('订阅', '订阅续费') THEN user_id END) as subscription_user_ids
        FROM short_order
        WHERE card_number = #{cardNumber}
        AND DATE(create_time) >= #{startDate}
        AND DATE(create_time) &lt;= #{endDate}
        AND status = 'SUCCEEDED'
    </select>

    <select id="getCardSubscriptionUsersByDateRange" resultType="Long">
        SELECT DISTINCT user_id
        FROM short_order
        WHERE card_number = #{cardNumber}
        AND DATE(create_time) >= #{startDate}
        AND DATE(create_time) &lt;= #{endDate}
        AND status = 'SUCCEEDED'
        AND pay_type IN ('订阅', '订阅续费')
        ORDER BY user_id
    </select>

    <select id="getCardSubscriptionRenewalUsers" resultType="Long">
        SELECT DISTINCT user_id
        FROM short_order
        WHERE card_number = #{cardNumber}
        AND status = 'SUCCEEDED'
        AND pay_type = '订阅续费'
        ORDER BY user_id
    </select>

</mapper>