-- 添加争议相关字段到short_order表
ALTER TABLE short_order ADD COLUMN dispute_id VARCHAR(64) DEFAULT NULL COMMENT '争议ID';
ALTER TABLE short_order ADD COLUMN dispute_status VARCHAR(32) DEFAULT NULL COMMENT '争议状态：NEEDS_RESPONSE-需要响应, ACCEPTED-接受争议, CHALLENGED-已挑战';
ALTER TABLE short_order ADD COLUMN dispute_type VARCHAR(32) DEFAULT NULL COMMENT '争议类型';
ALTER TABLE short_order ADD COLUMN dispute_reason VARCHAR(255) DEFAULT NULL COMMENT '争议原因';
ALTER TABLE short_order ADD COLUMN dispute_amount DECIMAL(10,2) DEFAULT NULL COMMENT '争议金额';
ALTER TABLE short_order ADD COLUMN dispute_time DATETIME DEFAULT NULL COMMENT '争议时间';
ALTER TABLE short_order ADD COLUMN dispute_result VARCHAR(32) DEFAULT NULL COMMENT '争议处理结果';
ALTER TABLE short_order ADD COLUMN dispute_process_time DATETIME DEFAULT NULL COMMENT '争议处理时间';

-- 添加索引
CREATE INDEX idx_short_order_dispute_id ON short_order(dispute_id);
CREATE INDEX idx_short_order_dispute_status ON short_order(dispute_status); 