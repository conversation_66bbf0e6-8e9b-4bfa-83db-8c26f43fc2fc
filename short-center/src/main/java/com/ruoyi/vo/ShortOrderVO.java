package com.ruoyi.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.config.serializer.DynamicTimeZoneLocalDateTimeSerializer;
import com.ruoyi.common.utils.TimeZoneUtils;
import com.ruoyi.common.utils.language.CountryLanguageUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@ApiModel(value = "ShortOrderVO", description = "订单VO")
public class ShortOrderVO {

    @Excel(name = "订单ID")
    @ApiModelProperty("订单ID")
    private Long id;

    @Excel(name = "用户ID")
    @ApiModelProperty("用户")
    private Long userId;

    @Excel(name = "支付意向ID")
    @ApiModelProperty("支付意向ID")
    private String paymentIntentId;

    @Excel(name = "订单编号")
    @ApiModelProperty("订单编号")
    private String ordersn;

    @Excel(name = "广告信息")
    @ApiModelProperty("广告信息，包括名称")
    private String adInfo;

    @Excel(name = "广告ID")
    private String adid;

    @Excel(name = "广告名称")
    private String adName;

    @Excel(name = "链接名称")
    @ApiModelProperty("链接名称")
    private String linkName;

    @Excel(name = "应用")
    @ApiModelProperty("应用")
    private String appName;

    @Excel(name = "应用类型")
    @ApiModelProperty("应用类型")
    private String appType;

    @ApiModelProperty("支付类型")
    private String payType;

    @Excel(name = "支付类型")
    @ApiModelProperty("支付类型")
    private String payTypeStr;

    @Excel(name = "支付方式")
    @ApiModelProperty("支付方式")
    private String paymentMethod;

    @Excel(name = "金额", cellType = Excel.ColumnType.NUMERIC)
    @ApiModelProperty("金额")
    private Double paymentAmount;

    @Excel(name = "支付状态")
    @ApiModelProperty("支付状态")
    private String status;

    @Excel(name = "手机型号")
    @ApiModelProperty("手机型号")
    private String phoneVersion;

    //@Excel(name = "语言")
    @ApiModelProperty("语言")
    private String language;

    @Excel(name = "国家")
    @ApiModelProperty("国家")
    private String countryName;

    public String getCountryName() {
        return CountryLanguageUtil.findCnByEn(countryName);
    }

    @Excel(name = "剧名")
    private String movieName;

    @Excel(name = "进入链接时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("进入链接时间")
    @JsonSerialize(using = DynamicTimeZoneLocalDateTimeSerializer.class)
    private LocalDateTime linkTime;

    @ApiModelProperty("更新时间")
    private String updateTime;

    @Excel(name = "支付时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("支付时间")
    @JsonSerialize(using = DynamicTimeZoneLocalDateTimeSerializer.class)
    private LocalDateTime payTime;

    private String subscriptionType;

    private String other;

    private Integer type;

    private Long pixelId;

    private Integer pixelStatus;

    private Long movieId;

    private Integer userState;

    private Long appId;

    @Excel(name = "时区")
    private String timeZoneOffset;

    //@Excel(name = "语言")
    private String linkLanguage;

    @Excel(name = "语言")
    private String linkLanguageName;

    private String vipType;

    @Excel(name = "付费模板类型")
    private String vipTypeStr;

    @Excel(name = "商户名称")
    private String payName;
    @Excel(name = "商户渠道")
    private String payChannel;

    private BigDecimal originalPrice;

    private Integer markType;

    public String getTimeZoneOffset() {
        return TimeZoneUtils.convertToUTCOffset(DynamicTimeZoneLocalDateTimeSerializer.timeZoneMap.get(appId.toString()));
    }

    public String getAppType() {
    	return 1 == type ? "H5" : "App";
    }

}
