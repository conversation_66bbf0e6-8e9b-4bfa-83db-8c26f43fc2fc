package com.ruoyi.service;

import com.ruoyi.domain.ShortForwardEmailReceive;

import java.util.List;

/**
 * 收件箱Service接口
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
public interface IShortForwardEmailReceiveService
{
    /**
     * 查询收件箱
     *
     * @param id 收件箱主键
     * @return 收件箱
     */
    public ShortForwardEmailReceive selectShortForwardEmailReceiveById(Long id);

    /**
     * 查询收件箱列表
     *
     * @param shortForwardEmailReceive 收件箱
     * @return 收件箱集合
     */
    public List<ShortForwardEmailReceive> selectShortForwardEmailReceiveList(ShortForwardEmailReceive shortForwardEmailReceive);

    /**
     * 新增收件箱
     *
     * @param shortForwardEmailReceive 收件箱
     * @return 结果
     */
    public int insertShortForwardEmailReceive(ShortForwardEmailReceive shortForwardEmailReceive);

    /**
     * 修改收件箱
     *
     * @param shortForwardEmailReceive 收件箱
     * @return 结果
     */
    public int updateShortForwardEmailReceive(ShortForwardEmailReceive shortForwardEmailReceive);

    /**
     * 批量删除收件箱
     *
     * @param ids 需要删除的收件箱主键集合
     * @return 结果
     */
    public int deleteShortForwardEmailReceiveByIds(Long[] ids);

    /**
     * 删除收件箱信息
     *
     * @param id 收件箱主键
     * @return 结果
     */
    public int deleteShortForwardEmailReceiveById(Long id);

    List<ShortForwardEmailReceive>  getEmailContentByEmailAndAppId(ShortForwardEmailReceive shortForwardEmailReceive);

    List<ShortForwardEmailReceive> getSendNullUser();

    /**
     * 检查邮箱在指定应用中是否存在收件记录
     *
     * @param email 邮箱
     * @param appId 应用ID
     * @return 是否存在
     */
    public boolean existsByEmailAndAppId(String email, Long appId);

    /**
     * 批量检查邮箱在指定应用中是否存在收件记录
     *
     * @param emails 邮箱列表
     * @param appId 应用ID
     * @return 存在的邮箱列表
     */
    public List<String> batchExistsByEmailAndAppId(List<String> emails, Long appId);
}
