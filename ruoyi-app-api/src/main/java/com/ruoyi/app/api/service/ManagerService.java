package com.ruoyi.app.api.service;

import com.ruoyi.app.api.vo.MovieDo;
import com.ruoyi.app.api.vo.VideoDo;
import com.ruoyi.common.core.domain.AjaxResult;
import org.json.JSONException;

/**
 * 管理类信息服务接口
 */
public interface ManagerService {

    AjaxResult addMovie(MovieDo movieDo);

    AjaxResult addVideo(VideoDo movieDo);

    AjaxResult getAllSubUsers();

    AjaxResult getAutoSubUser(String uid) throws Exception;

    AjaxResult getAutoSubUserNew(String uid) throws Exception;

    AjaxResult getAutoSubUserUseePay(String uid) throws Exception;

    AjaxResult getautoSub(String publicKey) throws InterruptedException;

    AjaxResult getautoSubUseePay(String publicKey) throws InterruptedException;

    AjaxResult testAllSub(String publicKey);

    AjaxResult updateIcon(String publicKey);

    AjaxResult updateValidStatus(String uid,String linkId);

    AjaxResult getForwardEmail(String nid);

    AjaxResult addVideoI18n(VideoDo movieDo);

    AjaxResult getByAppId(String nid);

    AjaxResult updateUnsub(String uid) throws Exception;

    String startBatchProcessing(int batches) throws Exception;

    AjaxResult getMovieByLimit(String nid, int limit);

    AjaxResult cleaningOrderData();

    AjaxResult newAutomaticRenewal(String publicKey);


    AjaxResult newAutomaticRenewalRun(String publicKey);
}