package com.ruoyi.system.mapper;

import com.ruoyi.system.domain.BankCardStatistics;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 银行卡统计Mapper接口
 */
public interface BankCardStatisticsMapper
{
    /**
     * 查询银行卡统计
     *
     * @param id 银行卡统计主键
     * @return 银行卡统计
     */
    public BankCardStatistics selectBankCardStatisticsById(Long id);

    /**
     * 根据银行卡号查询统计信息
     */
    public BankCardStatistics selectBankCardStatisticsByCardNumber(String cardNumber);

    /**
     * 查询银行卡统计列表
     *
     * @param bankCardStatistics 银行卡统计
     * @return 银行卡统计集合
     */
    public List<BankCardStatistics> selectBankCardStatisticsList(BankCardStatistics bankCardStatistics);

    /**
     * 新增银行卡统计
     *
     * @param bankCardStatistics 银行卡统计
     * @return 结果
     */
    public int insertBankCardStatistics(BankCardStatistics bankCardStatistics);

    /**
     * 修改银行卡统计
     *
     * @param bankCardStatistics 银行卡统计
     * @return 结果
     */
    public int updateBankCardStatistics(BankCardStatistics bankCardStatistics);

    /**
     * 删除银行卡统计
     *
     * @param id 银行卡统计主键
     * @return 结果
     */
    public int deleteBankCardStatisticsById(Long id);

    /**
     * 批量删除银行卡统计
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBankCardStatisticsByIds(Long[] ids);

    /**
     * 更新订阅统计
     */
    public int updateSubscriptionStats(@Param("cardNumber") String cardNumber,
                                      @Param("count") Integer count,
                                      @Param("amount") BigDecimal amount);

    /**
     * 更新充值统计
     */
    public int updateRechargeStats(@Param("cardNumber") String cardNumber,
                                  @Param("count") Integer count,
                                  @Param("amount") BigDecimal amount);

    /**
     * 更新用户关联信息
     */
    public int updateUserAssociation(@Param("cardNumber") String cardNumber,
                                    @Param("userId") Long userId,
                                    @Param("historyUserIds") String historyUserIds,
                                    @Param("relatedUsers") Integer relatedUsers);

    /**
     * 获取需要统计的银行卡列表（指定日期有订单的卡）
     */
    public List<String> selectActiveCardNumbers(@Param("targetDate") LocalDate targetDate);

    /**
     * 获取指定银行卡在指定日期范围内的订单统计
     */
    public List<Object[]> selectCardStatisticsByDateRange(@Param("cardNumber") String cardNumber,
                                                         @Param("startDate") LocalDate startDate,
                                                         @Param("endDate") LocalDate endDate);

    /**
     * 获取用户订阅开始时间
     */
    public LocalDate selectUserSubscriptionStartDate(@Param("userId") Long userId);

    /**
     * 获取多用户使用的银行卡
     */
    public List<BankCardStatistics> selectMultiUserCards();

    /**
     * 按状态统计银行卡数量
     */
    public List<Object[]> countByStatus();

    /**
     * 查询指定银行卡在指定日期的订单数据
     */
    public List<Map<String, Object>> selectCardOrdersByDate(@Param("cardNumber") String cardNumber,
                                                           @Param("targetDate") LocalDate targetDate);

    /**
     * 获取银行卡首次使用时间
     */
    public LocalDate getCardFirstUseDate(@Param("cardNumber") String cardNumber);

    /**
     * 获取银行卡在指定日期范围内的统计数据
     */
    public Map<String, Object> getCardStatsByDateRange(@Param("cardNumber") String cardNumber,
                                                      @Param("startDate") LocalDate startDate,
                                                      @Param("endDate") LocalDate endDate);

    /**
     * 获取银行卡在指定日期范围内有订阅订单的用户ID列表
     */
    public List<Long> getCardSubscriptionUsersByDateRange(@Param("cardNumber") String cardNumber,
                                                         @Param("startDate") LocalDate startDate,
                                                         @Param("endDate") LocalDate endDate);

    /**
     * 获取银行卡的所有订阅续费用户ID列表
     */
    public List<Long> getCardSubscriptionRenewalUsers(@Param("cardNumber") String cardNumber);
}