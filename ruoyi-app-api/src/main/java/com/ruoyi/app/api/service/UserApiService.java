package com.ruoyi.app.api.service;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.domain.ShortUser;
import com.ruoyi.domain.ShortUserCollectAndHistory;
import com.ruoyi.dto.CheckShortUserDTO;

import java.text.ParseException;
import java.util.Map;

/**
 * 用户API服务接口
 */
public interface UserApiService {

    /**
     * 获取用户信息
     *
     * @param userId 用户ID
     * @param appId 应用ID
     * @param token 用户token
     * @return 用户信息
     */
    Map<String, Object> getUserInfo(String userId, String appId, String token) throws ParseException;

    /**
     * 用户退出登录
     *
     * @param userId 用户ID
     * @return 处理结果
     */
    Map<String, Object> logout(String userId, String appId);

    /**
     * 删除用户
     *
     * @param userId 用户ID
     * @param appId 应用ID
     * @return 处理结果
     */
    Map<String, Object> deleteUser(String userId, String appId);

    /**
     * 用户注册或登录
     *
     * @param registrationData 注册数据
     * @return 处理结果
     */
    Map<String, Object> registerOrLogin(Map<String, Object> registrationData);

    /**
     * 用户签到
     *
     * @param userId 用户ID
     * @param appId 应用ID
     * @return 处理结果
     */
    Map<String, Object> signIn(String userId, String appId);

    /**
     * 获取用户签到信息
     *
     * @param appId 应用ID
     * @param token 用户token
     * @return 处理结果
     */
    Map<String, Object> getSignInInfo(String appId, String token);

    void refreshCountry(Integer flag);

    Map<String, Object> registerByEmail(Map<String, Object> registrationData);

    ShortUser getUserByLinkIdAndIp(CheckShortUserDTO shortUser);

    /**
     * 用户订阅记录
     *
     * @param email
     * @return 处理结果
     */
    Map<String, Object> subscribeRecord(String email);

    /**
     * 同步用户数据
     *
     * @param userId
     * @return
     */
    AjaxResult syncUserData(String userId);

    /**
     * 用户收藏和历史记录
     *
     * @param userId
     * @param type
     * @return
     */
    AjaxResult userCollectHistory(String userId, String type);

    /**
     * 添加用户收藏和历史记录
     *
     * @param shortUserCollectAndHistory
     * @return
     */
    AjaxResult addUserCollectHistory(ShortUserCollectAndHistory shortUserCollectAndHistory);

    /**
     * 删除用户收藏和历史记录
     *
     * @param id
     * @return
     */
    AjaxResult deleteUserCollectHistory(String id);

    /**
     * 判断用户是否收藏
     *
     * @param aLong
     * @param movieId
     * @return
     */
    AjaxResult isCollect(Long aLong, String movieId);
}
