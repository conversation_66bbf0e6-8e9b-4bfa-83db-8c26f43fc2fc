package com.ruoyi.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 【请填写功能名称】对象 short_user_link_movie_bind
 *
 * <AUTHOR>
 * @date 2025-09-22
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ShortUserLinkMovieBind extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * user id
     */
    @Excel(name = "user id")
    private Long userId;

    /**
     * sem link id
     */
    @Excel(name = "sem link id")
    private Long linkId;

    /**
     * movie id
     */
    @Excel(name = "movie id")
    private Long movieId;

    /**
     * 记录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "记录时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date logTime;

    /**
     * 状态：1有效，0无效
     */
    @Excel(name = "状态：1有效，0无效")
    private Integer status;
}
