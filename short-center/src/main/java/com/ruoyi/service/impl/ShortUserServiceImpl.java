package com.ruoyi.service.impl;

import brevo.ApiClient;
import brevo.Configuration;
import brevo.auth.ApiKeyAuth;
import brevoApi.TransactionalEmailsApi;
import brevoModel.CreateSmtpEmail;
import brevoModel.SendSmtpEmail;
import brevoModel.SendSmtpEmailSender;
import brevoModel.SendSmtpEmailTo;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageHelper;
import com.ruoyi.app.api.dto.InitUserDTO;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.dto.EmailDTO;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.domain.*;
import com.ruoyi.mapper.*;
import com.ruoyi.service.*;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.*;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StreamUtils;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.net.Inet4Address;
import java.net.Inet6Address;
import java.net.InetAddress;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * 用户管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
@Slf4j
@Service
public class ShortUserServiceImpl implements IShortUserService {
    @Autowired
    private ShortUserMapper shortUserMapper;

    @Autowired
    private ShortAppMapper shortAppMapper;

    @Autowired
    private ShortVipMapper shortVipMapper;

    @Autowired
    private ShortOrderMapper shortOrderMapper;

    @Autowired
    private ShortUserEamilMapper shortUserEamilMapper;

    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;
    @Autowired
    private ShortSysUserAppMovieMapper shortSysUserAppMovieMapper;

    @Autowired
    private IShortAppLanguageService shortAppLanguageService;


    @Autowired
    private IShortMovieService shortMovieService;

    @Autowired
    private IShortRunlogService shortRunlogService;

    @Autowired
    private ShortForwartEmailDomainMapper shortForwartEmailDomainMapper;

    @Autowired
    private IShortEmailDomainService shortEmailDomainService;

    @Value("${brevo.api-key}")
    private String brevoApiKey;

    @Value("${brevo.sender.email}")
    private String senderEmail;

    @Value("${brevo.sender.name}")
    private String senderName;

    @Autowired
    private ShortForwardEmailSendMapper shortForwardEmailSendMapper;

    @Autowired
    private IShortPageService shortPageService;

    @Autowired
    private IShortSemLinkService shortSemLinkService;

    @Autowired
    private RestTemplate restTemplate;

    @Value("${payment.use.dev:false}")
    private boolean useDevApi;

    @Value("${payment.api.dev.url:https://api-demo.airwallex.com}")
    private String devApiUrl;

    @Value("${payment.api.prod.url:https://api.airwallex.com}")
    private String prodApiUrl;

    @Autowired
    private IShortExtplatsService shortExtplatsService;

    @Autowired
    private ShortUserRenewLogMapper shortUserRenewLogMapper;

    @Autowired
    private ShortCheckUserMapper shortCheckUserMapper;


    /**
     * 查询用户管理
     *
     * @param id 用户管理主键
     * @return 用户管理
     */
    @Override
    public ShortUser selectShortUserById(Long id)
    {
        return shortUserMapper.selectShortUserById(id);
    }

    /**
     * 查询用户管理列表
     *
     * @param shortUser 用户管理
     * @return 用户管理
     */
    @Override
    public List<ShortUser> selectShortUserList(ShortUser shortUser) {
        return shortUserMapper.selectShortUserList(shortUser);
    }

    @Override
    public List<ShortUserDo> selectShortUserListAll(ShortUser shortUser) throws InterruptedException {
        if (null != shortUser.getExpireTime()) {
            // 转换为Java 8的LocalDateTime
            LocalDateTime ldt = shortUser.getExpireTime().toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime();

            // 定义格式化器
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

            // 格式化
            String dateString = ldt.format(formatter);
            String sStratTime = dateString + " 00:00:00";
            String eStratTime = dateString + " 23:59:59";
            shortUser.setsStratTime(sStratTime);
            shortUser.seteStratTime(eStratTime);
        }
        if (StringUtils.isNotEmpty(shortUser.getUpdateStartDate()) && StringUtils.isNotEmpty(shortUser.getUpdateEndDate())) {
            shortUser.setUpdateStartDate(shortUser.getUpdateStartDate() + " 00:00:00");
            shortUser.setUpdateEndDate(shortUser.getUpdateEndDate() + " 23:59:59");
        }
        if (StringUtils.isNotEmpty(shortUser.getBeginTime()) && StringUtils.isNotEmpty(shortUser.getEndTime())) {
            shortUser.setBeginTime(shortUser.getBeginTime() + " 00:00:00");
            shortUser.setEndTime(shortUser.getEndTime() + " 23:59:59");
        }
        //判断如果查询条件入参都为空 则查询当天数据 否则查询全量数据
      /*  if(StringUtils.isEmpty(shortUser.getSubscriptionType())
                && StringUtils.isEmpty(shortUser.getBeginTime())
                && StringUtils.isEmpty(shortUser.getEndTime())
                && StringUtils.isEmpty(shortUser.getUpdateStartDate())
                && StringUtils.isEmpty(shortUser.getUpdateEndDate())
                && StringUtils.isEmpty(shortUser.getUserIdOrName())
                && null == shortUser.getAppId()
                && null == shortUser.getPixelStatus()){
            shortUser.setBeginTime(DateUtils.getDate() + " 00:00:00");
            shortUser.setEndTime(DateUtils.getDate() + " 23:59:59");
        }*/



        long startTime = System.currentTimeMillis();
        List<ShortUserDo> list = shortUserMapper.selectShortUserListAll(shortUser);
        log.info("查询用户列表耗时:{}ms", System.currentTimeMillis() - startTime);


        CountDownLatch latch = new CountDownLatch(list.size());
        for (ShortUserDo shortUserDo : list) {
            threadPoolTaskExecutor.execute(() -> {
                log.info("总用户数：{},{}线程--查询用户:{}订单信息", list.size(), Thread.currentThread().getName(), shortUserDo.getId());
                try {
                    String payType = shortOrderMapper.selectNewByUserId(shortUserDo.getId());
                    shortUserDo.setPayType(payType);

                    if (StringUtils.isEmpty(shortUserDo.getEmail())) {
                        ShortUserEamil shortUserEamil = shortUserEamilMapper.getByUserId(shortUserDo.getId());
                        if (null != shortUserEamil) {
                            shortUserDo.setEmail(shortUserEamil.getEmail());
                        }
                    }
                } catch (Exception e) {
                    log.error("查询用户:{}订单信息异常", shortUserDo.getId(), e);
                } finally {
                    latch.countDown();
                }
            });
        }
        latch.await();
        log.info("查询用户订单耗时:{}ms", System.currentTimeMillis() - startTime);
        return list;
    }

    public List<Long> getChildrenByUserId(Long userId,List<Long> resList) {
       List<ShortSysUserAppMovie> shortSysUserAppMovieList = shortSysUserAppMovieMapper.selectBySysUserId(userId);
        // 2. 如果有子节点，递归查询
        if (CollectionUtil.isNotEmpty(shortSysUserAppMovieList)) {
            for (ShortSysUserAppMovie child : shortSysUserAppMovieList) {
                // 根据图片，第二列是关联值（129/130/1）
                Long childId = child.getChildId(); // 假设这是存储129/130的字段

                // 如果有关联值（129/130），添加到结果集
                if (childId != null && !childId.equals(userId)) {
                    resList.add(childId);
                }

                // 继续递归查询（使用sysuserId作为新的父ID）
                getChildrenByUserId(childId, resList);
            }
        }

        return resList;
    }

    @Override
    public List<ShortUserCP> selectShortUserByTime(String beginTime, String endTime, Long nextId) {
        return shortUserMapper.selectShortUserByTime(beginTime, endTime, nextId);
    }

    @Override
    public List<ShortUser> getBackRegEmail() {
        return shortUserMapper.getBackRegEmail();
    }

    @Override
    public ShortUser selectShortUserByUserId(Long id) {
        return shortUserMapper.selectShortUserByUserId(id);
    }

    @Override
    public ShortUser selectByAppIdAndUnid(Integer nid, String unid) {
        return shortUserMapper.selectByAppIdAndUnid(nid,unid);
    }

    @Override
    public ShortUser getAfterExpireTime(String email) {
        return shortUserMapper.getAfterExpireTime(email);
    }

    @Override
    public void updateExpireTimeById(Long id, Date afterExpireTime) {
        shortUserMapper.updateExpireTimeById(id,afterExpireTime);
    }

    @Override
    public List<Long> getResList(List<Long> userIds) {
        return shortUserMapper.getResList(userIds);
    }

    @Override
    public void setIsSubscriber(Long id) {
        shortUserMapper.setIsSubscriber(id);
    }

    @Override
    public void updateUserVipStatus(Long userId) throws Exception {
        //查询用户关联的邮箱
        ShortUser shortUser = shortUserMapper.selectShortUserById(userId);
        if(StringUtils.isNotEmpty(shortUser.getEmail())){
            //根据邮箱查询所有站点的用户 统一取消续订修改会员状态
            ShortUser shortUserQuery = new ShortUser();
            shortUserQuery.setEmail(shortUser.getEmail());
            List<ShortUser> userList = shortUserMapper.selectShortUserList(shortUserQuery);
            shortUserMapper.batchUpdateUserVipStatus(userList.stream().map(ShortUser::getId).collect(Collectors.toList()));
        }else {
            shortUserMapper.updateUserVipStatus(userId);
        }


        ShortUser user = shortUserMapper.selectShortUserById(userId);

        disablePaymentConsent(user);

        InitUserDTO shortBrevo = new InitUserDTO();
        shortBrevo.setReceiveEmail(user.getEmail());
        shortBrevo.setAppId(user.getAppId());
        if(null == shortBrevo.getLanguageCode())
            shortBrevo.setLanguageCode("en-US");
        if(null != user.getLinkidId()){
            ShortSemLink shortSemLink = shortSemLinkService.selectShortSemLinkById(user.getLinkidId());
            if(null != shortSemLink && null != shortSemLink.getLanguage())
                shortBrevo.setLanguageCode(shortSemLink.getLanguage());
        }
        shortBrevo.setUserId(String.valueOf(user.getId()));
        shortBrevo.setUniqueId(user.getUniqueId());
        shortBrevo.setExpireTime(user.getExpireTime());
        ShortMovie shortMovie = shortMovieService.selectShortMovieById(59L);
        sendSubFaildEmailForward(shortBrevo, shortMovie, "brevo");

    }

    @Override
    public List<ShortUser> get816() {
        return shortUserMapper.get816();
    }

    @Override
    public List<ShortUser> getRenewSubscribe1() {
        return shortUserMapper.getRenewSubscribe1();
    }


    /**
     * 根据第三方账号ID查询用户
     *
     * @param subId 第三方账号ID
     * @return 用户信息
     */
    @Override
    public ShortUser selectShortUserBySubId(String subId) {
        return shortUserMapper.selectShortUserBySubId(subId);
    }

    /**
     * 新增用户管理
     *
     * @param shortUser 用户管理
     * @return 结果
     */
    @Override
    public int insertShortUser(ShortUser shortUser) {
        shortUser.setUnsub(null != shortUser.getUnsub() ? shortUser.getUnsub() : 0);
        shortUser.setPixelStatus(null != shortUser.getPixelStatus() ? shortUser.getPixelStatus() : 0);
        shortUser.setValidStatus(null != shortUser.getValidStatus() ? shortUser.getValidStatus() : 0);
        shortUser.setCreateTime(DateUtils.getNowDate());
        return shortUserMapper.insertShortUser(shortUser);
    }

    /**
     * 修改用户管理
     *
     * @param shortUser 用户管理
     * @return 结果
     */
    @Override
    public int updateShortUser(ShortUser shortUser) {
        shortUser.setUnsub(null != shortUser.getUnsub() ? shortUser.getUnsub() : 0);
        shortUser.setPixelStatus(null != shortUser.getPixelStatus() ? shortUser.getPixelStatus() : 0);
        shortUser.setUpdateTime(DateUtils.getNowDate());
        return shortUserMapper.updateShortUser(shortUser);
    }

    /**
     * 批量删除用户管理
     *
     * @param ids 需要删除的用户管理主键
     * @return 结果
     */
    @Override
    public int deleteShortUserByIds(Long[] ids) {
        return shortUserMapper.deleteShortUserByIds(ids);
    }

    /**
     * 删除用户管理信息
     *
     * @param id 用户管理主键
     * @return 结果
     */
    @Override
    public int deleteShortUserById(Long id) {
        return shortUserMapper.deleteShortUserById(id);
    }


    @Override
    public ShortCountUser getUserCountInfo(ShortUser shortUser) {
        ShortCountUser shortCountUser = new ShortCountUser();
        shortUser.setCountType(0);
        Long loggedInUserId = SecurityUtils.getUserId();
        Long filterUserId = (loggedInUserId != null && loggedInUserId == 1) ? null : loggedInUserId;
        shortUser.setFilterUserId(filterUserId);

        if(null != loggedInUserId) {
            List<Long> resList = new ArrayList<>();
            List<Long> childIds = getChildrenByUserId(loggedInUserId, resList);
            if (null != filterUserId) {
                childIds.add(filterUserId);
                shortUser.setFilterUserId(null);
            }
            shortUser.setChildIds(childIds);
        }

        shortCountUser.setCountNum(shortUserMapper.getCount(shortUser));
        if (shortCountUser.getCountNum() > 0) {
            shortUser.setCountType(1);
            shortCountUser.setCountLinkNum(shortUserMapper.getCount(shortUser));
            shortCountUser.setCountNoLinkNum(shortCountUser.getCountNum() - shortCountUser.getCountLinkNum());
            shortCountUser.setLinkIdLapsed(BigDecimal.valueOf(shortCountUser.getCountNoLinkNum()).divide(BigDecimal.valueOf(shortCountUser.getCountLinkNum()), 6, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP));
        }
        return shortCountUser;
    }

    /**
     * 获取VIP订阅类型
     *
     * @param vipId VIP ID
     * @return 订阅类型
     */
    @Override
    public String getVipSubscriptionType(Long vipId) {
        if (vipId == null) {
            return null;
        }

        // 通过vipId查询VIP信息
        ShortVip vip = shortVipMapper.selectShortVipById(vipId);
        if (vip != null) {
            return vip.getSubscriptionType();
        }

        return null;
    }

    /**
     * 更新用户的VIP ID
     */
    @Override
    public int updateUserVipId(Long userId, Long vipId) {
        ShortUser user = selectShortUserById(userId);
        if (user != null) {
            user.setVipId(vipId);
            return updateShortUser(user);
        }
        return 0;
    }

    /**
     * 使用行锁查询用户信息
     * 通过SQL的FOR UPDATE实现行级锁定，确保在事务完成前其他事务无法修改用户数据
     *
     * @param userId 用户ID
     * @return 带行锁的用户对象
     */
    @Transactional
    public ShortUser selectShortUserWithLock(Long userId) {
        ShortUser user = null;
        try {
            // 使用Mapper扩展方法或直接执行SQL都可以
            user = shortUserMapper.selectShortUserWithLock(userId);
            if (user != null && user.getAppId() != null) {
                ShortApp app = shortAppMapper.selectShortAppById(user.getAppId());
                if (app != null) {
                    user.setAppName(app.getName());
                }
            }
        } catch (Exception e) {
            // 如果行锁查询失败，退回到普通查询
            user = selectShortUserById(userId);
        }
        return user;
    }

    @Override
    public void refreshCountry() {
        for (int i = 1; i <= 1000; i++) {
            PageHelper.startPage(i, 100);

            List<ShortUser> userList = shortUserMapper.scanUser();
            if (CollUtil.isNotEmpty(userList)) {
                for (ShortUser user : userList) {
                    if (StrUtil.isNotBlank(user.getIp())) {
                        try {
                            InetAddress inetAddress = InetAddress.getByName(user.getIp());

                            if (inetAddress instanceof Inet4Address) {
                                // 获取 IPv4 地址的字节数组
                                byte[] bytes = inetAddress.getAddress();
                                BigInteger bigInteger = new BigInteger(1, bytes);
                                shortUserMapper.updateIpv4Country(user.getId(), bigInteger);
                            }

                            if (inetAddress instanceof Inet6Address) {
                                // 获取 IPv6 地址的字节数组
                                byte[] bytes = inetAddress.getAddress();
                                BigInteger bigInteger = new BigInteger(1, bytes);
                                shortUserMapper.updateIpv6Country(user.getId(), bigInteger);
                            }
                        } catch (Exception e) {
                            // 静默
                        }
                    }
                }
                if (userList.size() < 100) {
                    break;
                }
            }
            i++;
        }
    }

    @Override
    public List<ShortUser> getAllSubUsers() {
        return shortUserMapper.getAllSubUsers();
    }

    @Override
    public int updateUnsub(Long id) throws Exception {
        int res = shortUserMapper.updateUnsub(id);
        shortUserMapper.updateUnsubTime(id);

        ShortUser user = shortUserMapper.selectShortUserById(id);

        disablePaymentConsent(user);

        InitUserDTO shortBrevo = new InitUserDTO();
        shortBrevo.setReceiveEmail(user.getEmail());
        shortBrevo.setAppId(user.getAppId());
        if(null == shortBrevo.getLanguageCode())
            shortBrevo.setLanguageCode("en-US");
        if(null != user.getLinkidId()){
            ShortSemLink shortSemLink = shortSemLinkService.selectShortSemLinkById(user.getLinkidId());
            if(null != shortSemLink && null != shortSemLink.getLanguage())
                shortBrevo.setLanguageCode(shortSemLink.getLanguage());
        }
        shortBrevo.setUserId(String.valueOf(user.getId()));
        shortBrevo.setUniqueId(user.getUniqueId());
        shortBrevo.setExpireTime(user.getExpireTime());
        ShortMovie shortMovie = shortMovieService.selectShortMovieById(59L);
        sendUnSubEmailForward(shortBrevo, shortMovie, "brevo");

        return res;
    }

    public void disablePaymentConsent(ShortUser user) throws JsonProcessingException {

        try {
            // 解析支付信息
            String paymentConsentId = null;

            // payInfo是JSON字符串格式的数组
            ObjectMapper mapper = new ObjectMapper();
            List<Map<String, String>> payInfoList = mapper.readValue(user.getPayInfo(),
                    mapper.getTypeFactory().constructCollectionType(List.class, Map.class));

            if (!payInfoList.isEmpty()) {
                Map<String, String> firstPayInfo = payInfoList.get(0);
                paymentConsentId = firstPayInfo.get("payment_consent_id");
            }

            if(StringUtils.isEmpty(paymentConsentId)){
                return;
            }

            // 获取 Token
            String token = shortExtplatsService.getPaymentToken(user.getAppId() != null ? user.getAppId().toString() : null);
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + token);

            // 确认付款意向
            Map<String, Object> confirmRequestBody = new HashMap<>();
            confirmRequestBody.put("request_id", generateRandomCode());

            HttpEntity<Map<String, Object>> confirmRequest = new HttpEntity<>(confirmRequestBody, headers);

            ResponseEntity<Map> confirmResponse = restTemplate.postForEntity(
                    (useDevApi ? devApiUrl : prodApiUrl) + "/api/v1/pa/payment_consents/" + paymentConsentId + "/disable",
                    confirmRequest,
                    Map.class
            );
            System.out.println(confirmResponse.getBody());
            // 记录日志并处理结果
            ShortRunlog runlog = new ShortRunlog();
            runlog.setType("disablePaymentConsent");
            runlog.setCreateTime(DateUtils.getNowDate());
            runlog.setUpdateTime(DateUtils.getNowDate());
            runlog.setState("1");
            runlog.setContent(String.format("用户：" + user.getId() + "，disablePaymentConsent"+ confirmResponse.getBody().toString()));
            runlog.setNote(confirmResponse.getBody().toString());
            shortRunlogService.insertShortRunlog(runlog);
        }catch (Exception e){

        }


    }
    private String generateRandomCode() {
        // 生成15位数字
        StringBuilder numbers = new StringBuilder();
        Random random = new Random();
        for (int i = 0; i < 15; i++) {
            numbers.append(random.nextInt(10));
        }

        // 生成3位字母
        StringBuilder letters = new StringBuilder();
        String alphabet = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
        for (int i = 0; i < 3; i++) {
            letters.append(alphabet.charAt(random.nextInt(alphabet.length())));
        }

        // 组合返回
        return numbers.toString() + letters.toString();
    }

    public AjaxResult sendUnSubEmailForward(InitUserDTO shortBrevo, ShortMovie shortMovie, String type) throws Exception {
        SendSmtpEmail email = new SendSmtpEmail();
        TransactionalEmailsApi apiInstance = new TransactionalEmailsApi();
        EmailDTO emailDTO = new EmailDTO();
        ShortForwardEmailSend shortForwardEmailSend = new ShortForwardEmailSend();
        shortForwardEmailSend.setReEmail(shortBrevo.getReceiveEmail());
        Message message = null;
        if(type.equals("forward")){

            shortForwardEmailSend.setCreateTime(DateUtils.getNowDate());
            shortForwardEmailSend.setSentDate(DateUtils.getNowDate());
            shortForwardEmailSend.setAppId(shortBrevo.getAppId());


            ShortForwartEmailDomain shortForwartEmailDomain = new ShortForwartEmailDomain();
            shortForwartEmailDomain.setAppId(shortBrevo.getAppId());
            List<ShortForwartEmailDomain> list = shortForwartEmailDomainMapper.selectShortForwartEmailDomainList(shortForwartEmailDomain);
            if(!list.isEmpty()){

                String username = list.get(0).getDomain(); // 你的Forward Email地址
                String password = AESEncryption.decrypt(list.get(0).getPassword()); // 你的密码
                shortForwardEmailSend.setDomain(username);


                String host = "smtp.forwardemail.net"; // Forward Email SMTP服务器
                Properties props = new Properties();
                props.put("mail.smtp.host", host);
                props.put("mail.smtp.port", "465");
                props.put("mail.smtp.ssl.enable", "true");
                props.put("mail.smtp.auth", "true");


                try {
                    // 创建会话
                    Session session = Session.getInstance(props,
                            new Authenticator() {
                                protected PasswordAuthentication getPasswordAuthentication() {
                                    return new PasswordAuthentication(username, password);
                                }
                            });

                    // 创建邮件

                    message = new MimeMessage(session);
                    message.setFrom(new InternetAddress(username));
                    message.setRecipients(Message.RecipientType.TO,
                            InternetAddress.parse(shortForwardEmailSend.getReEmail()));

                } catch (MessagingException e) {}
            }


        }

        // 3. 设置发件人
        SendSmtpEmailSender sender = new SendSmtpEmailSender();
        ShortEmailDomain shortEmailDomain = shortEmailDomainService.getrandomDomain(shortBrevo.getAppId());

        if (type.equals("brevo")) {
            // 2. 初始化邮件API
            ApiClient defaultClient = Configuration.getDefaultApiClient();
            ApiKeyAuth apiKey = (ApiKeyAuth) defaultClient.getAuthentication("api-key");
            apiKey.setApiKey(brevoApiKey); // 替换为你的Brevo API密钥

            if(null != shortEmailDomain){
                sender.setEmail(shortEmailDomain.getDomain()); // 必须已验证的邮箱
                sender.setName(shortEmailDomain.getAppName());
            }else{
                sender.setEmail(senderEmail); // 必须已验证的邮箱
                sender.setName(senderName);
            }


            List<SendSmtpEmailTo> toList = new ArrayList<>();
            // 4. 设置收件人
            SendSmtpEmailTo recipient = new SendSmtpEmailTo();
            recipient.setEmail(null != shortBrevo.getReceiveEmail() ? shortBrevo.getReceiveEmail() : null);
            recipient.setName(null != shortBrevo.getReceiveName() ? shortBrevo.getReceiveName() : null);
            toList.add(recipient);
            email.setSender(sender);
            email.setTo(toList);
        }else if(type.equals("aws")){
            emailDTO.setReceiveEmail(ListUtil.toList(shortBrevo.getReceiveEmail()));
            emailDTO.setSendChannel("aws");
            emailDTO.setServiceType("awsSimpl");
            emailDTO.setTitle("Kushort");
        }
        // 记录日志并处理结果
        ShortRunlog runlog = new ShortRunlog();
        runlog.setType("发送取消订阅邮件");
        runlog.setCreateTime(DateUtils.getNowDate());
        runlog.setUpdateTime(DateUtils.getNowDate());

        try {

//            String subject = getSubject(shortBrevo);
            String subject =shortEmailDomain.getAppName()+ " Subscription Cancellation";

//            String textContent = getUnSubTextContent(shortBrevo);
            // 5. 从HTML文件读取内容
            String htmlName = "";

            if(null == shortBrevo.getLanguageCode() || shortBrevo.getLanguageCode().equals("en-US")){
                if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                    htmlName = "unsub.html";
                else
                    htmlName = shortBrevo.getHtmlName();
            }else if(shortBrevo.getLanguageCode().equals("es")){
                if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                    htmlName = "es-unsub.html";
                else
                    htmlName = shortBrevo.getHtmlName();
            }else if(shortBrevo.getLanguageCode().equals("pt")){
                if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                    htmlName = "pt-unsub.html";
                else
                    htmlName = shortBrevo.getHtmlName();
            }else if(shortBrevo.getLanguageCode().equals("zh-TW")){
                if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                    htmlName = "zh-TW-unsub.html";
                else
                    htmlName = shortBrevo.getHtmlName();
            }else if(shortBrevo.getLanguageCode().equals("ja")){
                if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                    htmlName = "ja-unsub.html";
                else
                    htmlName = shortBrevo.getHtmlName();
            }else if(shortBrevo.getLanguageCode().equals("de")){
                if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                    htmlName = "de-unsub.html";
                else
                    htmlName = shortBrevo.getHtmlName();
            }

            ClassPathResource resource = new ClassPathResource("templates/" + htmlName);
            String htmlContent = StreamUtils.copyToString(
                    resource.getInputStream(),
                    StandardCharsets.UTF_8
            );

            String appDomain = null != shortEmailDomain.getAppDomain() ? shortEmailDomain.getAppDomain() : "";

            String randomNumApp1 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
            String randomNumApp2 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
//            ?userId=前六   + +  后六&uniqueId=
            ShortUser shortUser = shortUserMapper.selectShortUserById(Long.valueOf(shortBrevo.getUserId()));
            if(null != shortUser)
                appDomain = appDomain+"?userId="+randomNumApp1+shortUser.getHashId()+randomNumApp2+"&uniqueId="+shortBrevo.getUniqueId()+"&language="+shortBrevo.getLanguageCode();


            ShortPage shortPage = new ShortPage();
            shortPage.setAppId(String.valueOf(shortUser.getAppId()));
            shortPage.setCname("投诉和反馈");
            List<ShortPage> shortPageList = shortPageService.selectShortPageList(shortPage);

            // 使用更安全的占位符格式
            htmlContent = htmlContent
                    .replace("${user_name}", null != shortBrevo.getUser_name() ? shortBrevo.getUser_name() : "")
                    .replace("${movieurl}", null != shortMovie.getIcon() ? shortMovie.getIcon() : "")
                    .replace("{{movieurl}}", null != shortMovie.getIcon() ? shortMovie.getIcon() : "")

                    .replace("${appName}", null != shortEmailDomain.getAppName() ? shortEmailDomain.getAppName() : "")
                    .replace("{{appName}}", null != shortEmailDomain.getAppName() ? shortEmailDomain.getAppName() : "")

                    .replace("${expireTime}", shortUser.getExpireTime().toString())
                    .replace("{{expireTime}}", shortUser.getExpireTime().toString())

                    .replace("${appDomain}", appDomain)
                    .replace("{{appDomain}}", appDomain)


                    .replace("${jumpUrl}", shortPageList.get(0).getUrl()+"?selevt=3&email="+shortBrevo.getReceiveEmail())
                    .replace("{{jumpUrl}}", shortPageList.get(0).getUrl()+"?selevt=3&email="+shortBrevo.getReceiveEmail())

                    .replace("{{user_name}}", null != shortBrevo.getUser_name() ? shortBrevo.getUser_name() : ""); // 兼容旧格式

            if (type.equals("brevo")) {
                // 6. 创建邮件内容
                email.setSubject(subject);
                email.setHtmlContent(htmlContent);// 使用HTML内容
            }else if(type.equals("forward")){

                message.setSubject(subject);
                shortForwardEmailSend.setSubject(subject);

                // 创建邮件正文部分
                MimeBodyPart textPart = new MimeBodyPart();

                // 组合 HTML 内容
                textPart.setContent(htmlContent, "text/html; charset=utf-8");

                // 组合各部分
                Multipart multipart = new MimeMultipart();
                multipart.addBodyPart(textPart);

                message.setContent(multipart);
                shortForwardEmailSend.setContent(htmlContent);

            }else{
                emailDTO.setSubject(subject);
                emailDTO.setContent(htmlContent);
            }

            Message finalMessage = message;
            new Thread(() -> {
                try {
                    runlog.setState("1");
                    String content = "";
                    if (type.equals("brevo")) {
                        // 7. 发送邮件
                        CreateSmtpEmail response = apiInstance.sendTransacEmail(email);
                        content = String.format(",邮件发送成功！消息ID: " + response.getMessageId() + "用户Id：" + shortBrevo.getUserId() +
                                ",邮件主题：" + shortBrevo.getSubject() + ",收件人名称：" + shortBrevo.getReceiveName() +
                                ",收件人邮件：" + shortBrevo.getReceiveEmail()+",邮件服务商:"+type);
                        runlog.setNote(response.toString());
                    }else if (type.equals("forward")){
                        // 发送邮件
                        Transport.send(finalMessage);
                        content = String.format(",邮件发送成功！" +  "用户Id：" + shortBrevo.getUserId() +
                                ",邮件主题：" + shortBrevo.getSubject() + ",收件人名称：" + shortBrevo.getReceiveName() +
                                ",收件人邮件：" + shortBrevo.getReceiveEmail()+",邮件服务商:"+type);

                        shortForwardEmailSend.setType(1L);
                        shortForwardEmailSendMapper.insertShortForwardEmailSend(shortForwardEmailSend);

                    }
                    runlog.setContent(content);
                    shortRunlogService.insertShortRunlog(runlog);
                } catch (Exception e) {
                    runlog.setState("0");
                    runlog.setContent(String.format(",发送失败：" + e.getMessage() + ",邮件主题：" + shortBrevo.getSubject() +
                            ",收件人名称：" + shortBrevo.getReceiveName() + ",收件人邮件：" + shortBrevo.getReceiveEmail())+",邮件服务商:"+type);
                    runlog.setNote(e.toString());
                    shortRunlogService.insertShortRunlog(runlog);
                }
            }).start();

            String randomNum = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 4);
            Map<String, String> vars = new HashMap<>();
            vars.put("uId", randomNum + shortBrevo.getUserId());
            vars.put("uniqueId", shortBrevo.getUniqueId());
            return AjaxResult.success(vars);
        } catch (Exception e) {
            System.err.println("发送失败: " + e.getMessage());
            runlog.setState("0");
            runlog.setContent(String.format(",发送失败：" + e.getMessage() + ",邮件主题：" + shortBrevo.getSubject() + ",收件人名称：" + shortBrevo.getReceiveName() + ",收件人邮件：" + shortBrevo.getReceiveEmail()));
            runlog.setNote(e.toString());
            shortRunlogService.insertShortRunlog(runlog);
            return AjaxResult.error("发送失败: " + e.getMessage());
        }
    }


    public void sendUnSubEmailForward1(InitUserDTO shortBrevo, ShortMovie shortMovie, String type) throws Exception {
        SendSmtpEmail email = new SendSmtpEmail();
        TransactionalEmailsApi apiInstance = new TransactionalEmailsApi();
        EmailDTO emailDTO = new EmailDTO();
        ShortForwardEmailSend shortForwardEmailSend = new ShortForwardEmailSend();
        shortForwardEmailSend.setReEmail(shortBrevo.getReceiveEmail());
        Message message = null;
        if(type.equals("forward")){

            shortForwardEmailSend.setCreateTime(DateUtils.getNowDate());
            shortForwardEmailSend.setSentDate(DateUtils.getNowDate());
            shortForwardEmailSend.setAppId(shortBrevo.getAppId());


            ShortForwartEmailDomain shortForwartEmailDomain = new ShortForwartEmailDomain();
            shortForwartEmailDomain.setAppId(shortBrevo.getAppId());
            List<ShortForwartEmailDomain> list = shortForwartEmailDomainMapper.selectShortForwartEmailDomainList(shortForwartEmailDomain);
            if(!list.isEmpty()){

                String username = list.get(0).getDomain(); // 你的Forward Email地址
                String password = AESEncryption.decrypt(list.get(0).getPassword()); // 你的密码
                shortForwardEmailSend.setDomain(username);


                String host = "smtp.forwardemail.net"; // Forward Email SMTP服务器
                Properties props = new Properties();
                props.put("mail.smtp.host", host);
                props.put("mail.smtp.port", "465");
                props.put("mail.smtp.ssl.enable", "true");
                props.put("mail.smtp.auth", "true");


                try {
                    // 创建会话
                    Session session = Session.getInstance(props,
                            new Authenticator() {
                                protected PasswordAuthentication getPasswordAuthentication() {
                                    return new PasswordAuthentication(username, password);
                                }
                            });

                    // 创建邮件

                    message = new MimeMessage(session);
                    message.setFrom(new InternetAddress(username));
                    message.setRecipients(Message.RecipientType.TO,
                            InternetAddress.parse(shortForwardEmailSend.getReEmail()));

                } catch (MessagingException e) {}
            }


        }

        // 3. 设置发件人
        SendSmtpEmailSender sender = new SendSmtpEmailSender();
        ShortEmailDomain shortEmailDomain = shortEmailDomainService.getrandomDomain(shortBrevo.getAppId());

        if (type.equals("brevo")) {
            // 2. 初始化邮件API
            ApiClient defaultClient = Configuration.getDefaultApiClient();
            ApiKeyAuth apiKey = (ApiKeyAuth) defaultClient.getAuthentication("api-key");
            apiKey.setApiKey(brevoApiKey); // 替换为你的Brevo API密钥

            if(null != shortEmailDomain){
                sender.setEmail(shortEmailDomain.getDomain()); // 必须已验证的邮箱
                sender.setName(shortEmailDomain.getAppName());
            }else{
                sender.setEmail(senderEmail); // 必须已验证的邮箱
                sender.setName(senderName);
            }


            List<SendSmtpEmailTo> toList = new ArrayList<>();
            // 4. 设置收件人
            SendSmtpEmailTo recipient = new SendSmtpEmailTo();
            recipient.setEmail(null != shortBrevo.getReceiveEmail() ? shortBrevo.getReceiveEmail() : null);
            recipient.setName(null != shortBrevo.getReceiveName() ? shortBrevo.getReceiveName() : null);
            toList.add(recipient);
            email.setSender(sender);
            email.setTo(toList);
        }else if(type.equals("aws")){
            emailDTO.setReceiveEmail(ListUtil.toList(shortBrevo.getReceiveEmail()));
            emailDTO.setSendChannel("aws");
            emailDTO.setServiceType("awsSimpl");
            emailDTO.setTitle("Kushort");
        }
        // 记录日志并处理结果
        ShortRunlog runlog = new ShortRunlog();
        runlog.setType("发送取消订阅邮件");
        runlog.setCreateTime(DateUtils.getNowDate());
        runlog.setUpdateTime(DateUtils.getNowDate());

        try {

//            String subject = getSubject(shortBrevo);
            String subject =shortEmailDomain.getAppName()+ " Subscription Cancellation";

//            String textContent = getUnSubTextContent(shortBrevo);
            // 5. 从HTML文件读取内容
            String htmlName = "";

            if(null == shortBrevo.getLanguageCode() || shortBrevo.getLanguageCode().equals("en-US")){
                if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                    htmlName = "unsub.html";
                else
                    htmlName = shortBrevo.getHtmlName();
            }else if(shortBrevo.getLanguageCode().equals("es")){
                if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                    htmlName = "es-unsub.html";
                else
                    htmlName = shortBrevo.getHtmlName();
            }else if(shortBrevo.getLanguageCode().equals("pt")){
                if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                    htmlName = "pt-unsub.html";
                else
                    htmlName = shortBrevo.getHtmlName();
            }else if(shortBrevo.getLanguageCode().equals("zh-TW")){
                if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                    htmlName = "zh-TW-unsub.html";
                else
                    htmlName = shortBrevo.getHtmlName();
            }else if(shortBrevo.getLanguageCode().equals("ja")){
                if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                    htmlName = "ja-unsub.html";
                else
                    htmlName = shortBrevo.getHtmlName();
            }else if(shortBrevo.getLanguageCode().equals("de")){
                if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                    htmlName = "de-unsub.html";
                else
                    htmlName = shortBrevo.getHtmlName();
            }

            ClassPathResource resource = new ClassPathResource("templates/" + htmlName);
            String htmlContent = StreamUtils.copyToString(
                    resource.getInputStream(),
                    StandardCharsets.UTF_8
            );

            String appDomain = null != shortEmailDomain.getAppDomain() ? shortEmailDomain.getAppDomain() : "";

            String randomNumApp1 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
            String randomNumApp2 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
//            ?userId=前六   + +  后六&uniqueId=
            ShortUser shortUser = shortUserMapper.selectShortUserById(Long.valueOf(shortBrevo.getUserId()));
            if(null != shortUser)
                appDomain = appDomain+"?userId="+randomNumApp1+shortUser.getHashId()+randomNumApp2+"&uniqueId="+shortBrevo.getUniqueId()+"&language="+shortBrevo.getLanguageCode();


            ShortPage shortPage = new ShortPage();
            shortPage.setAppId(String.valueOf(shortUser.getAppId()));
            shortPage.setCname("投诉和反馈");
            List<ShortPage> shortPageList = shortPageService.selectShortPageList(shortPage);

            // 使用更安全的占位符格式
            htmlContent = htmlContent
                    .replace("${user_name}", null != shortBrevo.getUser_name() ? shortBrevo.getUser_name() : "")
                    .replace("${movieurl}", null != shortMovie.getIcon() ? shortMovie.getIcon() : "")
                    .replace("{{movieurl}}", null != shortMovie.getIcon() ? shortMovie.getIcon() : "")

                    .replace("${appName}", null != shortEmailDomain.getAppName() ? shortEmailDomain.getAppName() : "")
                    .replace("{{appName}}", null != shortEmailDomain.getAppName() ? shortEmailDomain.getAppName() : "")

                    .replace("${expireTime}", shortUser.getExpireTime().toString())
                    .replace("{{expireTime}}", shortUser.getExpireTime().toString())

                    .replace("${appDomain}", appDomain)
                    .replace("{{appDomain}}", appDomain)


                    .replace("${jumpUrl}", shortPageList.get(0).getUrl()+"?selevt=3&email="+shortBrevo.getReceiveEmail())
                    .replace("{{jumpUrl}}", shortPageList.get(0).getUrl()+"?selevt=3&email="+shortBrevo.getReceiveEmail())

                    .replace("{{user_name}}", null != shortBrevo.getUser_name() ? shortBrevo.getUser_name() : ""); // 兼容旧格式

            if (type.equals("brevo")) {
                // 6. 创建邮件内容
                email.setSubject(subject);
                email.setHtmlContent(htmlContent);// 使用HTML内容
            }else if(type.equals("forward")){

                message.setSubject(subject);
                shortForwardEmailSend.setSubject(subject);

                // 创建邮件正文部分
                MimeBodyPart textPart = new MimeBodyPart();

                // 组合 HTML 内容
                textPart.setContent(htmlContent, "text/html; charset=utf-8");

                // 组合各部分
                Multipart multipart = new MimeMultipart();
                multipart.addBodyPart(textPart);

                message.setContent(multipart);
                shortForwardEmailSend.setContent(htmlContent);

            }else{
                emailDTO.setSubject(subject);
                emailDTO.setContent(htmlContent);
            }

            Message finalMessage = message;
            new Thread(() -> {
                try {
                    runlog.setState("1");
                    String content = "";
                    if (type.equals("brevo")) {
                        // 7. 发送邮件
                        CreateSmtpEmail response = apiInstance.sendTransacEmail(email);
                        content = String.format(",邮件发送成功！消息ID: " + response.getMessageId() + "用户Id：" + shortBrevo.getUserId() +
                                ",邮件主题：" + shortBrevo.getSubject() + ",收件人名称：" + shortBrevo.getReceiveName() +
                                ",收件人邮件：" + shortBrevo.getReceiveEmail()+",邮件服务商:"+type);
                        runlog.setNote(response.toString());
                    }else if (type.equals("forward")){
                        // 发送邮件
                        Transport.send(finalMessage);
                        content = String.format(",邮件发送成功！" +  "用户Id：" + shortBrevo.getUserId() +
                                ",邮件主题：" + shortBrevo.getSubject() + ",收件人名称：" + shortBrevo.getReceiveName() +
                                ",收件人邮件：" + shortBrevo.getReceiveEmail()+",邮件服务商:"+type);

                        shortForwardEmailSend.setType(1L);
                        shortForwardEmailSendMapper.insertShortForwardEmailSend(shortForwardEmailSend);

                    }
                    runlog.setContent(content);
                    shortRunlogService.insertShortRunlog(runlog);
                } catch (Exception e) {
                    runlog.setState("0");
                    runlog.setContent(String.format(",发送失败：" + e.getMessage() + ",邮件主题：" + shortBrevo.getSubject() +
                            ",收件人名称：" + shortBrevo.getReceiveName() + ",收件人邮件：" + shortBrevo.getReceiveEmail())+",邮件服务商:"+type);
                    runlog.setNote(e.toString());
                    shortRunlogService.insertShortRunlog(runlog);
                }
            }).start();

//            String randomNum = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 4);
//            Map<String, String> vars = new HashMap<>();
//            vars.put("uId", randomNum + shortBrevo.getUserId());
//            vars.put("uniqueId", shortBrevo.getUniqueId());
//            return AjaxResult.success(vars);
        } catch (Exception e) {
            System.err.println("发送失败: " + e.getMessage());
            runlog.setState("0");
            runlog.setContent(String.format(",发送失败：" + e.getMessage() + ",邮件主题：" + shortBrevo.getSubject() + ",收件人名称：" + shortBrevo.getReceiveName() + ",收件人邮件：" + shortBrevo.getReceiveEmail()));
            runlog.setNote(e.toString());
            shortRunlogService.insertShortRunlog(runlog);
//            return AjaxResult.error("发送失败: " + e.getMessage());
        }
    }

    @Override
    public List<ShortUser> getBy818() {
        return shortUserMapper.getBy818();
    }

    @Override
    public List<ShortUser> getEmailAndIsSubscriber() {
        return shortUserMapper.getEmailAndIsSubscriber();
    }

    @Override
    public int countEmail(String receiveEmail) {
        return shortUserMapper.countEmail(receiveEmail);
    }

    @Override
    public ShortUser getOldUser(Long appId, String email) {
        return shortUserMapper.getOldUser(appId,email);
    }

    @Override
    public int countByEmailAndAppId(String appId, String email) {
        return shortUserMapper.countByEmailAndAppId(appId,email);
    }

    @Override
    public ShortUser getLastUser(String email) {
        return shortUserMapper.getLastUser(email);
    }

    @Override
    public List<Long> getExpiredUser() {
        return shortUserMapper.getExpiredUser();
    }

    @Override
    public void updateIsSubscriberByIds(List<Long> ids) {
        shortUserMapper.updateIsSubscriberByIds(ids);
    }

    @Override
    public String getJumpUrlByType(Long userId) {
        ShortUser shortUser = shortUserMapper.selectShortUserById(userId);
        if(null == shortUser)
            return "";
        ShortEmailDomain shortEmailDomain = shortEmailDomainService.getrandomDomain(shortUser.getAppId());
        if(null == shortEmailDomain)
            return "";
        ShortSemLink shortSemLink = shortSemLinkService.selectShortSemLinkById(shortUser.getLinkidId());
        Long movieId = 59L;
        if(null != shortSemLink)
            movieId = shortSemLink.getMovieId();

        String randomNum = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 4);


        String randomNum10 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
        String randomNum6 = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 6);
        String res = shortEmailDomain.getAppDomain()+"/video/?id="+randomNum+movieId+"&userId="+randomNum10+randomNum+shortUser.getId()+randomNum6+"&uniqueId="+shortUser.getUniqueId();
        return res;
    }

    @Override
    public String getRandomDomainByAppId(Long appId) {
        ShortEmailDomain shortEmailDomain = shortEmailDomainService.getrandomDomain(appId);
        if(null == shortEmailDomain)
            return "";
        return shortEmailDomain.getAppDomain();
    }

    @Override
    public void updateUnsubSource(Long id, String content) {
        shortUserMapper.updateUnsubSource(id,content);
    }

    @Override
    public AjaxResult getCheckByUserId(Long userId) {

        ShortCheckUser checkUser1 = new ShortCheckUser();
        checkUser1.setUserId(userId);


        ShortUser shortUser1 = shortUserMapper.selectShortUserById(userId);

        ShortApp shortApp1 = shortAppMapper.selectShortAppById(shortUser1.getAppId());
        if(null != shortApp1){
            ShortExtplats shortExtplats = shortExtplatsService.selectShortExtplatsById(shortApp1.getExtplatsId());
            if (null != shortExtplats)
                checkUser1.setExtplatsName(shortExtplats.getName());
        }

        checkUser1.setCreateTime(shortUser1.getCreateTime());
        checkUser1.setEmail(shortUser1.getEmail());
        checkUser1.setIsSubscriber(shortUser1.getIsSubscriber());
        checkUser1.setUnsub(shortUser1.getUnsub());

        List<ShortOrder> orderList1 = shortOrderMapper.getInitIdByUserId(userId);
        if(orderList1.isEmpty()) {
            checkUser1.setStatus("不可续订用户");
            checkUser1.setReason("不可续订用户");

            shortUserMapper.updateIsCheck(userId);
            checkUser1.setCheckTime(DateUtils.getNowDate());
            checkUser1.setType("最新数据");
            shortCheckUserMapper.updateType(userId);
            shortCheckUserMapper.insertShortCheckUser(checkUser1);
        }else{
            List<String> paymentIntentIds = orderList1.stream().map(ShortOrder::getPaymentIntentId).collect(Collectors.toList());


            List<ShortUser> userList = shortUserMapper.selectBankCard(paymentIntentIds);
            if(userList.size() == 1){

                ShortUser resUser = userList.get(0);
                ShortCheckUser resCheckUser = getCheckUser(resUser,checkUser1,orderList1.get(0));

                shortUserMapper.updateIsCheck(userId);
                resCheckUser.setCheckTime(DateUtils.getNowDate());
                resCheckUser.setType("最新数据");
                shortCheckUserMapper.updateType(userId);
                shortCheckUserMapper.insertShortCheckUser(resCheckUser);
            }

            if(userList.size() > 1){

                for (ShortUser su :userList){
                    ShortCheckUser checkUser = new ShortCheckUser();
                    checkUser.setUserId(su.getId());


                    ShortUser shortUser = shortUserMapper.selectShortUserById(su.getId());

                    ShortApp shortApp = shortAppMapper.selectShortAppById(shortUser.getAppId());
                    if(null != shortApp){
                        ShortExtplats shortExtplats = shortExtplatsService.selectShortExtplatsById(shortApp.getExtplatsId());
                        if (null != shortExtplats)
                            checkUser.setExtplatsName(shortExtplats.getName());
                    }
                    checkUser.setCreateTime(shortUser.getCreateTime());
                    checkUser.setEmail(shortUser.getEmail());
                    checkUser.setIsSubscriber(shortUser.getIsSubscriber());
                    checkUser.setUnsub(shortUser.getUnsub());

                    List<ShortOrder> orderList = shortOrderMapper.getInitIdByUserId(su.getId());
                    if(orderList.isEmpty()) {
                        checkUser.setStatus("不可续订用户");
                        checkUser.setReason("不可续订用户");

                        shortUserMapper.updateIsCheck(su.getId());
                        checkUser.setCheckTime(DateUtils.getNowDate());
                        checkUser.setType("最新数据");
                        shortCheckUserMapper.updateType(su.getId());
                        shortCheckUserMapper.insertShortCheckUser(checkUser);
                    }else{
                        ShortCheckUser resCheckUser = getCheckUser(su,checkUser,orderList.get(0));
                        shortUserMapper.updateIsCheck(su.getId());
                        resCheckUser.setCheckTime(DateUtils.getNowDate());
                        resCheckUser.setType("最新数据");
                        shortCheckUserMapper.updateType(su.getId());
                        shortCheckUserMapper.insertShortCheckUser(resCheckUser);
                    }
                }
            }
        }

        return AjaxResult.success();
    }

    @Override
    public AjaxResult getCheckByUserIdAll() {

        List<Long> longList = shortUserMapper.getNotNullExpireTime();
        for (Long id :longList) {
            System.out.println("id================="+id);
            getCheckByUserId(id);
        }
        return AjaxResult.success();
    }

    @Override
    public List<Long> getNullEmailListIds() {
        return shortUserMapper.getNullEmailListIds();
    }

    @Override
    public List<Long> getEmailListIds() {
        return shortUserMapper.getEmailListIds();
    }

    @Override
    public List<String> getEmailList(Long appId) {
        return shortUserMapper.getEmailList(appId);
    }

    @Override
    public List<Long> getByEmail(String email, Long appId,int type) {
        return shortUserMapper.getByEmail(email,appId,type);
    }

    @Override
    public List<String> getEmailMarkTypeIds() {
        return shortUserMapper.getEmailMarkTypeIds();
    }

    @Override
    public List<String> getEmailNotApp25Ids() {
        return shortUserMapper.getEmailNotApp25Ids();
    }

    @Override
    public List<ShortUser> getCycleList() {
        return shortUserMapper.getCycleList();
    }

    @Override
    public void updateCodeAndCycleNum(Long id, int cycleNum) {
        shortUserMapper.updateCodeAndCycleNum(id,cycleNum);
    }

    @Override
    public List<Long> getNoEmailList() {
        return shortUserMapper.getNoEmailList();
    }

    @Override
    public List<Long> getEmailList1() {
        return shortUserMapper.getEmailList1();
    }

    @Override
    public List<Long> getEmailAll25() {
        return shortUserMapper.getEmailAll25();
    }

    @Override
    public List<String> getEmailListByAll25(List<Long> emailAll25) {
        return shortUserMapper.getEmailListByAll25(emailAll25);
    }

    @Override
    public List<Long> getAllNotEmailListByAll25(List<String> emailListByAll25) {
        return shortUserMapper.getAllNotEmailListByAll25(emailListByAll25);
    }

    @Override
    public void updateLastCode(Long userId, String code) {
        shortUserMapper.updateLastCode(userId,code);
    }

    @Override
    public List<ShortUser> getIds(List<Long> mergedList) {
        return shortUserMapper.getIds(mergedList);
    }

    @Override
    public List<String> getEmail25AndOtherList() {
        return shortUserMapper.getEmail25AndOtherList();
    }

    private ShortCheckUser getCheckUser(ShortUser resUser,ShortCheckUser checkUser,ShortOrder order){

        checkUser.setLastOrderid(order.getId());
        checkUser.setLastRenewalTime(order.getPayTime());
        String bankCard = shortUserRenewLogMapper.getBankCardByInitId(order.getPaymentIntentId());
        checkUser.setBankCard(bankCard);
        String lastCode = shortUserRenewLogMapper.selectShortUserCodeByUserId(resUser.getId());
        checkUser.setLastCode(lastCode);

        if(null != resUser.getUnsub() && resUser.getUnsub() == 1){
            checkUser.setStatus("取消订阅");
            checkUser.setReason("取消订阅用户");
        }else{
            ShortOrder shortOrder = shortOrderMapper.getLastRenewal(resUser.getId(),null);
            //正常
            if(null != resUser.getExpireTime() && resUser.getExpireTime().after(DateUtils.getNowDate())){
                ShortOrder shortOrder1 = shortOrderMapper.getLastRenewal(resUser.getId(),"SUCCEEDED");
                checkUser.setStatus("正常续订用户");
                checkUser.setReason("正常"+shortOrder1.getSubscriptionType()+"日续订用户");
            }else{
                if(shortOrder.getStatus().equals("SUCCEEDED")) {

                    if(checkUser.getIsSubscriber().equals("0")) {
                        checkUser.setStatus("过期会员状态异常用户");
                        checkUser.setReason("过期会员状态异常用户");
                    }else{
                        int check = shortCheckUserMapper.checkTypeByUserId(bankCard);
                        if(check>0){
                            checkUser.setStatus("同银行卡有其他用户正在执行续订");
                            checkUser.setReason("同银行卡有其他用户正在执行续订");
                        }else{
                            checkUser.setStatus("过期正常续订用户");
                            checkUser.setReason("过期正常续订用户");
                        }


                    }

                }else{
                    checkUser.setStatus("非正常续订用户");
                    if(null != lastCode && lastCode.equals("51")){
                        int check = shortUserRenewLogMapper.checkLast3ByUserId(resUser.getId());
                        if(check>0){
                            checkUser.setReason("51状态码x3");
                        }else
                            checkUser.setReason("最后状态码为"+lastCode);
                    }else
                        checkUser.setReason("最后状态码为"+lastCode);
                }
            }
        }
        return checkUser;
    }

    public AjaxResult sendSubFaildEmailForward(InitUserDTO shortBrevo, ShortMovie shortMovie, String type) throws Exception {
        SendSmtpEmail email = new SendSmtpEmail();
        TransactionalEmailsApi apiInstance = new TransactionalEmailsApi();
        EmailDTO emailDTO = new EmailDTO();
        ShortForwardEmailSend shortForwardEmailSend = new ShortForwardEmailSend();
        shortForwardEmailSend.setReEmail(shortBrevo.getReceiveEmail());
        Message message = null;
        if(type.equals("forward")){

            shortForwardEmailSend.setCreateTime(DateUtils.getNowDate());
            shortForwardEmailSend.setSentDate(DateUtils.getNowDate());
            shortForwardEmailSend.setAppId(shortBrevo.getAppId());


            ShortForwartEmailDomain shortForwartEmailDomain = new ShortForwartEmailDomain();
            shortForwartEmailDomain.setAppId(shortBrevo.getAppId());
            List<ShortForwartEmailDomain> list = shortForwartEmailDomainMapper.selectShortForwartEmailDomainList(shortForwartEmailDomain);
            if(!list.isEmpty()){

                String username = list.get(0).getDomain(); // 你的Forward Email地址
                String password = AESEncryption.decrypt(list.get(0).getPassword()); // 你的密码
                shortForwardEmailSend.setDomain(username);


                String host = "smtp.forwardemail.net"; // Forward Email SMTP服务器
                Properties props = new Properties();
                props.put("mail.smtp.host", host);
                props.put("mail.smtp.port", "465");
                props.put("mail.smtp.ssl.enable", "true");
                props.put("mail.smtp.auth", "true");


                try {
                    // 创建会话
                    Session session = Session.getInstance(props,
                            new Authenticator() {
                                protected PasswordAuthentication getPasswordAuthentication() {
                                    return new PasswordAuthentication(username, password);
                                }
                            });

                    // 创建邮件

                    message = new MimeMessage(session);
                    message.setFrom(new InternetAddress(username));
                    message.setRecipients(Message.RecipientType.TO,
                            InternetAddress.parse(shortForwardEmailSend.getReEmail()));

                } catch (MessagingException e) {}
            }


        }

        // 3. 设置发件人
        SendSmtpEmailSender sender = new SendSmtpEmailSender();
        ShortEmailDomain shortEmailDomain = shortEmailDomainService.getrandomDomain(shortBrevo.getAppId());

        if (type.equals("brevo")) {
            // 2. 初始化邮件API
            ApiClient defaultClient = Configuration.getDefaultApiClient();
            ApiKeyAuth apiKey = (ApiKeyAuth) defaultClient.getAuthentication("api-key");
            apiKey.setApiKey(brevoApiKey); // 替换为你的Brevo API密钥

            if(null != shortEmailDomain){
                sender.setEmail(shortEmailDomain.getDomain()); // 必须已验证的邮箱
                sender.setName(shortEmailDomain.getAppName());
            }else{
                sender.setEmail(senderEmail); // 必须已验证的邮箱
                sender.setName(senderName);
            }


            List<SendSmtpEmailTo> toList = new ArrayList<>();
            // 4. 设置收件人
            SendSmtpEmailTo recipient = new SendSmtpEmailTo();
            recipient.setEmail(null != shortBrevo.getReceiveEmail() ? shortBrevo.getReceiveEmail() : null);
            recipient.setName(null != shortBrevo.getReceiveName() ? shortBrevo.getReceiveName() : null);
            toList.add(recipient);
            email.setSender(sender);
            email.setTo(toList);
        }else if(type.equals("aws")){
            emailDTO.setReceiveEmail(ListUtil.toList(shortBrevo.getReceiveEmail()));
            emailDTO.setSendChannel("aws");
            emailDTO.setServiceType("awsSimpl");
            emailDTO.setTitle("Kushort");
        }
        // 记录日志并处理结果
        ShortRunlog runlog = new ShortRunlog();
        runlog.setType("发送退款邮件");
        runlog.setCreateTime(DateUtils.getNowDate());
        runlog.setUpdateTime(DateUtils.getNowDate());

        try {

//            String subject = getSubject(shortBrevo);
            String subject =shortEmailDomain.getAppName()+ " Refund Confirmation";

//            String textContent = getUnSubTextContent(shortBrevo);
            // 5. 从HTML文件读取内容
            String htmlName = "";

            if(null == shortBrevo.getLanguageCode() || shortBrevo.getLanguageCode().equals("en-US")){
                if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                    htmlName = "subFaild.html";
                else
                    htmlName = shortBrevo.getHtmlName();
            }else if(shortBrevo.getLanguageCode().equals("es")){
                if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                    htmlName = "es-subFaild.html";
                else
                    htmlName = shortBrevo.getHtmlName();
            }else if(shortBrevo.getLanguageCode().equals("pt")){
                if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                    htmlName = "pt-subFaild.html";
                else
                    htmlName = shortBrevo.getHtmlName();
            }else if(shortBrevo.getLanguageCode().equals("zh-TW")){
                if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                    htmlName = "zh-TW-subFaild.html";
                else
                    htmlName = shortBrevo.getHtmlName();
            }else if(shortBrevo.getLanguageCode().equals("ja")){
                if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                    htmlName = "ja-subFaild.html";
                else
                    htmlName = shortBrevo.getHtmlName();
            }else if(shortBrevo.getLanguageCode().equals("de")){
                if (StringUtils.isEmpty(shortBrevo.getHtmlName()))
                    htmlName = "de-subFaild.html";
                else
                    htmlName = shortBrevo.getHtmlName();
            }

            ClassPathResource resource = new ClassPathResource("templates/" + htmlName);
            String htmlContent = StreamUtils.copyToString(
                    resource.getInputStream(),
                    StandardCharsets.UTF_8
            );

            // 使用更安全的占位符格式
            htmlContent = htmlContent
                    .replace("${appName}", shortEmailDomain.getAppName())
                    .replace("{{appName}}", shortEmailDomain.getAppName());

            if (type.equals("brevo")) {
                // 6. 创建邮件内容
                email.setSubject(subject);
                email.setHtmlContent(htmlContent);// 使用HTML内容
            }else if(type.equals("forward")){

                message.setSubject(subject);
                shortForwardEmailSend.setSubject(subject);

                // 创建邮件正文部分
                MimeBodyPart textPart = new MimeBodyPart();

                // 组合 HTML 内容
                textPart.setContent(htmlContent, "text/html; charset=utf-8");

                // 组合各部分
                Multipart multipart = new MimeMultipart();
                multipart.addBodyPart(textPart);

                message.setContent(multipart);
                shortForwardEmailSend.setContent(htmlContent);

            }else{
                emailDTO.setSubject(subject);
                emailDTO.setContent(htmlContent);
            }

            Message finalMessage = message;
            new Thread(() -> {
                try {
                    runlog.setState("1");
                    String content = "";
                    if (type.equals("brevo")) {
                        // 7. 发送邮件
                        CreateSmtpEmail response = apiInstance.sendTransacEmail(email);
                        content = String.format(",邮件发送成功！消息ID: " + response.getMessageId() + "用户Id：" + shortBrevo.getUserId() +
                                ",邮件主题：" + shortBrevo.getSubject() + ",收件人名称：" + shortBrevo.getReceiveName() +
                                ",收件人邮件：" + shortBrevo.getReceiveEmail()+",邮件服务商:"+type);
                        runlog.setNote(response.toString());
                    }else if (type.equals("forward")){
                        // 发送邮件
                        Transport.send(finalMessage);
                        content = String.format(",邮件发送成功！" +  "用户Id：" + shortBrevo.getUserId() +
                                ",邮件主题：" + shortBrevo.getSubject() + ",收件人名称：" + shortBrevo.getReceiveName() +
                                ",收件人邮件：" + shortBrevo.getReceiveEmail()+",邮件服务商:"+type);

                        shortForwardEmailSend.setType(1L);
                        shortForwardEmailSendMapper.insertShortForwardEmailSend(shortForwardEmailSend);

                    }
                    runlog.setContent(content);
                    shortRunlogService.insertShortRunlog(runlog);
                } catch (Exception e) {
                    runlog.setState("0");
                    runlog.setContent(String.format(",发送失败：" + e.getMessage() + ",邮件主题：" + shortBrevo.getSubject() +
                            ",收件人名称：" + shortBrevo.getReceiveName() + ",收件人邮件：" + shortBrevo.getReceiveEmail())+",邮件服务商:"+type);
                    runlog.setNote(e.toString());
                    shortRunlogService.insertShortRunlog(runlog);
                }
            }).start();

            String randomNum = String.valueOf(Math.abs(UUID.randomUUID().hashCode())).substring(0, 4);
            Map<String, String> vars = new HashMap<>();
            vars.put("uId", randomNum + shortBrevo.getUserId());
            vars.put("uniqueId", shortBrevo.getUniqueId());
            return AjaxResult.success(vars);
        } catch (Exception e) {
            System.err.println("发送失败: " + e.getMessage());
            runlog.setState("0");
            runlog.setContent(String.format(",发送失败：" + e.getMessage() + ",邮件主题：" + shortBrevo.getSubject() + ",收件人名称：" + shortBrevo.getReceiveName() + ",收件人邮件：" + shortBrevo.getReceiveEmail()));
            runlog.setNote(e.toString());
            shortRunlogService.insertShortRunlog(runlog);
            return AjaxResult.error("发送失败: " + e.getMessage());
        }
    }

    @NotNull
    private static String getUnSubTextContent(InitUserDTO shortBrevo) {
        String textContent = "";
        if(null == shortBrevo.getLanguageCode() || shortBrevo.getLanguageCode().equals("en-US")){
            textContent = "Dear member, your subscription has been canceled. If you have any subsequent questions, please feel free to contact us.";
        }else if(shortBrevo.getLanguageCode().equals("es")){
            textContent = "Estimado miembro, su suscripción ha sido cancelada. Si tiene alguna pregunta, no dude en contactarnos.";
        }else if(shortBrevo.getLanguageCode().equals("pt")){
            textContent = "Prezado membro, sua assinatura foi cancelada. Caso tenha alguma dúvida, entre em contato conosco.";
        }else if(shortBrevo.getLanguageCode().equals("zh-TW")){
            textContent = "尊敬的会员，您的订阅已取消。如有任何后续问题，请随时联系我们。";
        }else if(shortBrevo.getLanguageCode().equals("ja")){
            textContent = "会員様、ご登録はキャンセルされました。その後ご質問等ございましたら、お気軽にお問い合わせください。";
        }else if(shortBrevo.getLanguageCode().equals("de")){
            textContent = "Sehr geehrtes Mitglied, Ihr Abonnement wurde gekündigt. Bei weiteren Fragen können Sie sich gerne an uns wenden.";
        }
        return textContent;
    }

    private static String getSubject(InitUserDTO shortBrevo) {
        String subject = "";
        if(null == shortBrevo.getLanguageCode() || shortBrevo.getLanguageCode().equals("en-US"))
            subject = null != shortBrevo.getSubject() ? shortBrevo.getSubject() : " Valued Member";
        else if(shortBrevo.getLanguageCode().equals("es"))
            subject = null != shortBrevo.getSubject() ? shortBrevo.getSubject() : " Miembro valioso";
        else if(shortBrevo.getLanguageCode().equals("pt"))
            subject = null != shortBrevo.getSubject() ? shortBrevo.getSubject() : " Membro Valioso";
        else if(shortBrevo.getLanguageCode().equals("zh-TW"))
            subject = null != shortBrevo.getSubject() ? shortBrevo.getSubject() : " 尊贵的会员";
        else if(shortBrevo.getLanguageCode().equals("ja"))
            subject = null != shortBrevo.getSubject() ? shortBrevo.getSubject() : " 大切なメンバー";
        else if(shortBrevo.getLanguageCode().equals("de"))
            subject = null != shortBrevo.getSubject() ? shortBrevo.getSubject() : " Geschätztes Mitglied";
        return subject;
    }

    @Override
    public List<ShortUserDo> selectShortUserListExportAll(ShortUser shortUser) {
        if (null != shortUser.getExpireTime()) {
            // 转换为Java 8的LocalDateTime
            LocalDateTime ldt = shortUser.getExpireTime().toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime();

            // 定义格式化器
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

            // 格式化
            String dateString = ldt.format(formatter);
            String sStratTime = dateString + " 00:00:00";
            String eStratTime = dateString + " 23:59:59";
            shortUser.setsStratTime(sStratTime);
            shortUser.seteStratTime(eStratTime);
        }
        if (StringUtils.isNotEmpty(shortUser.getUpdateStartDate()) && StringUtils.isNotEmpty(shortUser.getUpdateEndDate())) {
            shortUser.setUpdateStartDate(shortUser.getUpdateStartDate() + " 00:00:00");
            shortUser.setUpdateEndDate(shortUser.getUpdateEndDate() + " 23:59:59");
        }
        if (StringUtils.isNotEmpty(shortUser.getBeginTime()) && StringUtils.isNotEmpty(shortUser.getEndTime())) {
            shortUser.setBeginTime(shortUser.getBeginTime() + " 00:00:00");
            shortUser.setEndTime(shortUser.getEndTime() + " 23:59:59");
        }

        Long loggedInUserId = null == shortUser.getUserId() ? SecurityUtils.getUserId() : shortUser.getUserId();
        Long filterUserId = (loggedInUserId != null && loggedInUserId == 1) ? null : loggedInUserId;
        shortUser.setFilterUserId(filterUserId);

        if(null != loggedInUserId) {
            List<Long> resList = new ArrayList<>();
            List<Long> childIds = getChildrenByUserId(loggedInUserId, resList);
            if (null != filterUserId) {
                childIds.add(filterUserId);
                shortUser.setFilterUserId(null);
            }
            shortUser.setChildIds(childIds);
        }
        List<ShortAppLanguage> shortAppLanguageList = shortAppLanguageService.selectShortAppLanguageList(new ShortAppLanguage());
        Map<String, String> languageCodeMap = shortAppLanguageList.stream().collect(Collectors.toMap(ShortAppLanguage::getCode, ShortAppLanguage::getZhName));
        List<ShortUserDo> list = shortUserMapper.selectShortUserListAll(shortUser);
        if (CollectionUtil.isNotEmpty(list)) {
            for (ShortUserDo shortUserDo : list) {
                //设置广告信息
                String other = shortUserDo.getOther();
                if (StringUtils.isNotEmpty(other)) {
                    JSONObject jsonObject = JSONUtil.parseObj(other);
                    try {
                        if (jsonObject.containsKey("adName") && StringUtils.isNotEmpty(jsonObject.getStr("adName"))) {
                            //url编码转为中文
                            shortUserDo.setAdName(URLDecoder.decode(jsonObject.getStr("adName"), "UTF-8"));
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                if (StringUtils.isEmpty(shortUserDo.getEmail())) {
                    ShortUserEamil shortUserEamil = shortUserEamilMapper.getByUserId(shortUserDo.getId());
                    if (null != shortUserEamil) {
                        shortUserDo.setEmail(shortUserEamil.getEmail());
                    }
                }
                if(languageCodeMap.containsKey(shortUserDo.getLinkLanguage())){
                    shortUserDo.setLinkLanguageName(languageCodeMap.get(shortUserDo.getLinkLanguage()));
                }
                shortUserDo.setAppType(shortUserDo.getAppType());
            }
        }
        return list;
    }

    @Override
    public void updateState(String userId, int state) {
        shortUserMapper.updateState(userId, state);
    }

    @Override
    public List<ShortUser> getAllByTypeAndExpireTime(String type,String payChannel,int payType) {
        return shortUserMapper.getAllByTypeAndExpireTime(type,payChannel,payType);
    }

    @Override
    public List<ShortUser> getRenewSubscribe() {
        return shortUserMapper.getRenewSubscribe();
    }

    @Override
    public void updateOther(Long id, String ot) {
        shortUserMapper.updateOther(id, ot);
    }

    @Override
    public String getIconByUserId(Long userId) {
        return shortUserMapper.getIconByUserId(userId);
    }

    @Override
    public List<ShortUser> getByLaset() {
        return shortUserMapper.getByLaset();
    }

    @Override
    public int updateShortUserChooseLanguage(String uid, String languageCode) {
        ShortUser shortUser = new ShortUser();
        shortUser.setId(Long.valueOf(uid));
        shortUser.setChooseLanguage(languageCode);
        return shortUserMapper.updateShortUser(shortUser);
    }

    @Override
    public ShortUser findByIdAndAppIdAndSource(String uid, Integer nid, String source) {
        return shortUserMapper.findByIdAndAppIdAndSource(uid, nid, source);
    }
}
