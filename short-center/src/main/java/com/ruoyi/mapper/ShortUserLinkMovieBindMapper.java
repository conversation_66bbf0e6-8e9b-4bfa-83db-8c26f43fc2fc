package com.ruoyi.mapper;

import com.ruoyi.domain.ShortUserLinkMovieBind;

import java.util.List;

/**
 * 【请填写功能名称】Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-09-22
 */
public interface ShortUserLinkMovieBindMapper 
{
    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public ShortUserLinkMovieBind selectShortUserLinkMovieBindById(Long id);

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param shortUserLinkMovieBind 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<ShortUserLinkMovieBind> selectShortUserLinkMovieBindList(ShortUserLinkMovieBind shortUserLinkMovieBind);

    /**
     * 新增【请填写功能名称】
     * 
     * @param shortUserLinkMovieBind 【请填写功能名称】
     * @return 结果
     */
    public int insertShortUserLinkMovieBind(ShortUserLinkMovieBind shortUserLinkMovieBind);

    /**
     * 修改【请填写功能名称】
     * 
     * @param shortUserLinkMovieBind 【请填写功能名称】
     * @return 结果
     */
    public int updateShortUserLinkMovieBind(ShortUserLinkMovieBind shortUserLinkMovieBind);

    /**
     * 删除【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteShortUserLinkMovieBindById(Long id);

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteShortUserLinkMovieBindByIds(Long[] ids);
}
