package com.ruoyi.app.api.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.ibm.icu.util.ULocale;
import com.maxmind.geoip2.DatabaseReader;
import com.maxmind.geoip2.model.CountryResponse;
import com.ruoyi.app.api.service.IpInfoService;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.domain.*;
import com.ruoyi.framework.web.service.token.JwtTokenService;
import com.ruoyi.mapper.ShortSemLinkMapper;
import com.ruoyi.mapper.ShortUserLinkMovieBindMapper;
import com.ruoyi.service.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStream;
import java.net.InetAddress;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.*;
import java.util.stream.Collectors;


/**
 * IP信息服务实现
 */
@Service
public class IpInfoServiceImpl implements IpInfoService {

    private static final Logger log = LoggerFactory.getLogger(IpInfoServiceImpl.class);

    @Autowired
    private IShortBannerService shortBannerService;

    @Autowired
    private IShortPageService shortPageService;

    @Autowired
    private IShortMovieService shortMovieService;

    @Autowired
    private IShortVideoService shortVideoService;

    @Autowired
    private IShortVideoChannelCoinService shortVideoChannelCoinService;

    @Autowired
    private IShortPayTemplateAmtService shortPayTemplateAmtService;

    @Autowired
    private IShortUserService shortUserService;

    @Autowired
    private IShortUserUnlockVideoService shortUserUnlockVideoService;

    @Autowired
    private JwtTokenService jwtTokenService;

    @Autowired
    private IShortUserActivityLogService shortUserActivityLogService;

    @Autowired
    private IShortCoinRuleService iShortCoinRuleService;

    @Autowired
    private IShortCoverImageService shortCoverImageService;

    @Autowired
    private IShortFeedbackService shortFeedbackService;

    @Value("${geoip.database.path:GeoLite2-Country.mmdb}")
    private String geoipDatabasePath;

    private DatabaseReader databaseReader;

    @Value("${brevo.api-key}")
    private String brevoApiKey;

    @Value("${brevo.sender.email}")
    private String senderEmail;

    @Value("${brevo.sender.name}")
    private String senderName;

    @Autowired
    private IShortRunlogService shortRunlogService;

    @Autowired
    private IShortMovieI18nService shortMovieI18nService;

    @Autowired
    private IShortVideoI18nService shortVideoI18nService;

    @Autowired
    private IShortPageI18nService shortPageI18nService;

    @Autowired
    private IShortMovieRecService shortMovieRecService;
    @Autowired
    private ShortSemLinkMapper shortSemLinkMapper;
    @Autowired
    private ShortUserLinkMovieBindMapper shortUserLinkMovieBindMapper;

    @Autowired
    private IShortUnsubscribeService shortUnsubscribeService;

    public IpInfoServiceImpl() {
    }

    @PostConstruct
    public void init() {
        try {
            // 直接从类路径加载GeoIP2数据库
            ClassPathResource resource = new ClassPathResource("GeoLite2-Country.mmdb");
            if (resource.exists()) {
                // 临时文件方式加载数据库
                Path tempFile = Files.createTempFile("geoip-", ".mmdb");
                try (InputStream is = resource.getInputStream()) {
                    Files.copy(is, tempFile, StandardCopyOption.REPLACE_EXISTING);
                }
                databaseReader = new DatabaseReader.Builder(tempFile.toFile()).build();
                log.info("已成功加载GeoIP2数据库");
            } else {
                log.warn("GeoIP2数据库文件不存在，将使用默认实现");
            }
        } catch (IOException e) {
            log.error("初始化GeoIP2数据库读取器失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public String getClientIp(HttpServletRequest request) {
        String ip = null;

        // 获取X-Forwarded-For头
        ip = request.getHeader("X-Forwarded-For");
        if (ip != null && ip.length() > 0 && !"unknown".equalsIgnoreCase(ip)) {
            // 如果是多级代理，取第一个IP地址
            if (ip.indexOf(",") > 0) {
                ip = ip.split(",")[0].trim();
            }
            return ip;
        }

        // 获取X-Real-IP头
        ip = request.getHeader("X-Real-IP");
        if (ip != null && ip.length() > 0 && !"unknown".equalsIgnoreCase(ip)) {
            return ip;
        }

        // 使用请求的远程地址
        return request.getRemoteAddr();
    }

    @Override
    public Map<String, String> getCountryByIp(String ip) {
        Map<String, String> result = new HashMap<>();

        // 默认值
        result.put("countryName", null);
        result.put("countryCode", null);

        try {
            // 对于特殊IP地址进行处理
            if (ip.startsWith("192.168.") || ip.startsWith("10.") || ip.equals("127.0.0.1") || ip.equals("0:0:0:0:0:0:0:1")) {
                result.put("countryName", "Local");
                result.put("countryCode", "LO");
                return result;
            }

            // 使用GeoIP2查询
            if (databaseReader != null) {
                InetAddress ipAddress = InetAddress.getByName(ip);
                CountryResponse response = databaseReader.country(ipAddress);
                if (response != null && response.getCountry() != null) {
                    String isoCode = response.getCountry().getIsoCode();
                    result.put("countryCode", response.getCountry().getIsoCode());
                    if (StringUtils.isNotEmpty(isoCode)) {
                        // countryName 转中文
                        ULocale locale = new ULocale("", isoCode.toUpperCase());
                        String displayCountry = locale.getDisplayCountry(ULocale.CHINESE);
                        result.put("countryName", displayCountry);
                    }
                }
            } else {
                // 如果GeoIP2数据库未初始化，使用简单判断
                if (ip.startsWith("172.")) {
                    result.put("countryName", "United States");
                    result.put("countryCode", "US");
                } else {
                    // 默认返回中国
                    result.put("countryName", "China");
                    result.put("countryCode", "CN");
                }
            }
        } catch (Exception e) {
            result.put("countryName", "Unknown");
            log.error("获取IP地址所属国家失败: {}", e.getMessage(), e);
        }

        return result;
    }

    @Override
    public List<Map<String, Object>> getBannerListByAppId(Integer appId) {
        List<Map<String, Object>> data = new ArrayList<>();

        // 查询banner信息
        List<ShortBanner> shortBannerList = shortBannerService.getBannerListByAppId(appId);
        for (ShortBanner banner : shortBannerList) {
            Map<String, Object> bannerData = new HashMap<>();
            bannerData.put("id", banner.getId());
            bannerData.put("app", banner.getAppId());
            bannerData.put("image", banner.getImage());
            bannerData.put("movie", banner.getMovieId());
            bannerData.put("addtime", banner.getAddtime());
            bannerData.put("updatetime", banner.getUpdateBy());

            bannerData.put("movieName", banner.getMovieName());
            bannerData.put("movieDescription", banner.getMovieDescription());
            bannerData.put("movieNum", banner.getMovieNum());

            data.add(bannerData);
        }

        return data;
    }

    @Override
    public List<Map<String, Object>> getBannerListByAppId(Integer appId, String languageCode) {
        List<Map<String, Object>> data = new ArrayList<>();

        // 查询banner信息
        ShortBanner shortBannerQuery = new ShortBanner();
        shortBannerQuery.setAppId(Long.valueOf(appId));
        shortBannerQuery.setLanguageCode(languageCode);
        List<ShortBanner> shortBannerList = shortBannerService.selectShortBannerList(shortBannerQuery);
        for (ShortBanner banner : shortBannerList) {
            Map<String, Object> bannerData = new HashMap<>();
            bannerData.put("id", banner.getId());
            bannerData.put("app", banner.getAppId());
            bannerData.put("image", banner.getImage());
            bannerData.put("movie", banner.getMovieId());
            bannerData.put("addtime", banner.getAddtime());
            bannerData.put("updatetime", banner.getUpdateBy());

            bannerData.put("movieName", banner.getMovieName());
            bannerData.put("movieDescription", banner.getMovieDescription());
            bannerData.put("movieNum", banner.getMovieNum());

            data.add(bannerData);
        }

        return data;
    }

    @Override
    public List<Map<String, Object>> getPageListByAppId(Integer nid) {
        List<Map<String, Object>> data = new ArrayList<>();

        // 查询page信息
        List<ShortPage> shortPageList = shortPageService.getPageListByAppId(nid);
        for (ShortPage shortPage : shortPageList) {
            Map<String, Object> pageData = new HashMap<>();
            pageData.put("id", shortPage.getId());
            pageData.put("app", shortPage.getAppId());
            pageData.put("name", shortPage.getName());
            pageData.put("url", shortPage.getUrl());
            pageData.put("addtime", shortPage.getAddtime());
            pageData.put("cname", shortPage.getCname());

            data.add(pageData);
        }

        return data;
    }

    @Override
    public List<Map<String, Object>> getMovies(Integer nid, String type, int ifApp, String unid) {
        List<Map<String, Object>> data = new ArrayList<>();

        Date exTime = null;
        if (null != unid) {
            ShortUser shortUser = shortUserService.selectByAppIdAndUnid(nid, unid);
            if (null != shortUser)
                exTime = shortUser.getCreateTime();

            if ((shortUser != null && shortUser.getState().equals("0") /*&& null != shortUser.getExpireTime() && shortUser.getExpireTime().before(new Date()) */)) {
                Map<String, Object> movieData = new HashMap<>();
                data.add(movieData);
                return data;
            }
        }

        // 获取电影列表
        List<ShortMovie> serviceMoviesList = shortMovieService.getMovies(nid, type, ifApp, exTime);

        //查询归属影片
        List<ShortMovie> serviceMoviesAppList = shortMovieService.getMoviesByApps(nid, type, ifApp, exTime);
        serviceMoviesList.addAll(serviceMoviesAppList);

        serviceMoviesList = serviceMoviesList.stream()
                .collect(Collectors.toMap(
                        ShortMovie::getId,
                        user -> user,
                        (existing, replacement) -> existing  // 保留先出现的元素
                ))
                .values()
                .stream()
                .sorted(Comparator.comparing(ShortMovie::getId).reversed())  // 按ID倒序
                .collect(Collectors.toList());


        for (ShortMovie movie : serviceMoviesList) {

            Map<String, Object> movieData = new HashMap<>();
            movieData.put("id", movie.getId());
            movieData.put("app", movie.getAppId());
            movieData.put("name", movie.getName());
            movieData.put("icon", movie.getIcon());
            movieData.put("time", movie.getTime());
            movieData.put("num", movie.getNum());
            movieData.put("rating", movie.getRating());
            movieData.put("description", movie.getDescription());
            movieData.put("content", movie.getContent());
            movieData.put("rec", movie.getRec());
            movieData.put("source", movie.getSource());
            movieData.put("is_vip", movie.getIsVip());
            movieData.put("state", movie.getState());


            ShortVideo shortVideo = shortVideoService.getVideoByMovieIdAndNum(movie.getId(), 1);
            if (null != shortVideo) {
                Map<String, Object> firstVideoMap = new HashMap<>();
                firstVideoMap.put("pic", shortVideo.getPic());
                firstVideoMap.put("url", shortVideo.getUrl());
                movieData.put("first_video", firstVideoMap);
            } else {
                movieData.put("first_video", null);
            }

            data.add(movieData);
        }

        return data;
    }

    @Override
    public List<Map<String, Object>> getFilterMovieList(Map<String, Object> requestData) throws IOException {
        List<Map<String, Object>> dataList = new ArrayList<>();
//        List<Map<String, Object>> data = (List<Map<String, Object>>) requestData.getOrDefault("data", new ArrayList<>());


        // 获取data数组（需类型转换）
        List<Map<String, Object>> reData = (List<Map<String, Object>>) requestData.get("data");

        reData.forEach(item -> {

            String movieId = String.valueOf(item.get("movie"));
            String kid = String.valueOf(item.get("kid"));
            String pid = String.valueOf(item.get("pid"));

            ShortMovie movie = shortMovieService.selectShortMovieById(Long.valueOf(movieId));
            if (null != movie) {
                ShortVideoChannelCoin qdVideo = shortVideoChannelCoinService.findByMovieAndStateAndCoinRuleId(movie.getId(), 1, StringUtils.isEmpty(kid) ? null : Integer.valueOf(kid));

                //            ShortPayTemplateAmt qdPlan = shortPayTemplateAmtService.selectShortPayTemplateAmtById(Long.valueOf(pid));

                List<Map<String, Object>> videoList = new ArrayList<>();
                if (qdVideo != null) {
                    Map<String, Object> videoMap = new HashMap<>();
                    videoMap.put("pic", qdVideo.getPic());
                    videoMap.put("url", qdVideo.getUrl());
                    videoMap.put("is_vip", qdVideo.getIsVip());
                    videoList.add(videoMap);
                }

                Map<String, Object> resultItem = new HashMap<>();
                resultItem.put("id", movie.getId());
                resultItem.put("app", movie.getAppId());
                resultItem.put("name", movie.getName());
                resultItem.put("icon", movie.getIcon());
                resultItem.put("time", movie.getTime());
                resultItem.put("num", movie.getNum());
                resultItem.put("rating", movie.getRating());
                resultItem.put("description", movie.getDescription());
                resultItem.put("content", movie.getContent());
                resultItem.put("rec", movie.getRec());
                resultItem.put("source", movie.getSource());
                resultItem.put("is_vip", movie.getIsVip());
                resultItem.put("state", movie.getState());
                resultItem.put("first_video", videoList);

                dataList.add(resultItem);
            }

        });


        return dataList;
    }

    @Override
    public List<Map<String, Object>> getChannelMovies(String authorization, String uid, Integer nid, String recId) {
        // 处理用户 ID
//        Integer uid = null;
//        if (uidHeader != null) {
//            try {
//                if (uidHeader.length() > 4) {
//                    uid = Integer.parseInt(uidHeader.substring(4));
//                } else {
//                    uid = Integer.parseInt(uidHeader);
//                }
//            } catch (NumberFormatException e) {
//                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(new Response(400, "无效的用户 ID", null));
//            }
//        }

        // 验证参数
//        if (nid == null) {
//            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(new Response(400, "无效的 app_id", null));
//        }
        List<Map<String, Object>> movieList = new ArrayList<>();
        // 验证用户
        if (uid == null) {
            String jtoken = jwtTokenService.getUserIdFromToken(authorization);
            if (StringUtils.isNotEmpty(jtoken))
                uid = jtoken;
            else
                return movieList;
        }

        ShortUser userOptional = shortUserService.findByIdAndAppIdAndSource(uid, nid, "Facebook");
        if (null != userOptional) {
            // 处理推荐 ID
            List<Integer> recIdList = new ArrayList<>();
            if (recId != null && !recId.isEmpty()) {
                recIdList = Arrays.stream(recId.split(","))
                        .map(String::trim)
                        .filter(s -> !s.isEmpty())
                        .map(Integer::parseInt)
                        .collect(Collectors.toList());
            }

            // 查询短剧
            List<ShortMovie> movies = shortMovieService.findBySourceAndStateAndIdNotIn("Facebook", "1", recIdList, 30);

            // 构建返回数据

            for (ShortMovie movie : movies) {
                ShortVideo firstVideoOptional = shortVideoService.getVideoByMovieIdAndNum(movie.getId(), 1);
                Map<String, Object> firstVideoData = null;
                if (null != firstVideoOptional) {
                    firstVideoData = new HashMap<>();
                    firstVideoData.put("pic", firstVideoOptional.getPic());
                    firstVideoData.put("url", firstVideoOptional.getUrl());
                }

                Map<String, Object> movieData = new HashMap<>();
                movieData.put("id", movie.getId());
                movieData.put("app", nid);
                movieData.put("name", movie.getName()); // 原代码未获取，暂时置空
                movieData.put("icon", movie.getIcon()); // 原代码未获取，暂时置空
                movieData.put("is_vip", false); // 原代码未获取，暂时置默认值
                movieData.put("state", 1);
                movieData.put("first_video", firstVideoData);

                movieList.add(movieData);
            }

        }

        return movieList;
    }

    @Override
    public List<Map<String, Object>> getMovieList(Integer nid, String vid) {
//        if (nid == null) {
//            return new ResponseEntity<>(Map.of("code", 400, "msg", "获取电影失败"), HttpStatus.BAD_REQUEST);
//        }

        List<Integer> midList = new ArrayList<>();
        if (vid != null && !vid.isEmpty()) {
            midList = Arrays.stream(vid.split(","))
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
        }

//        if (midList.isEmpty()) {
//            return new ResponseEntity<>(Map.of("code", 400, "msg", "获取电影失败"), HttpStatus.BAD_REQUEST);
//        }


        List<ShortMovie> movies = shortMovieService.findByIdInAndAppId(midList, nid);

        //查询归属
        List<ShortMovie> serviceMoviesAppList = shortMovieService.findByIdInAndAppIds(midList, nid);

        movies.addAll(serviceMoviesAppList);
        movies = movies.stream()
                .collect(Collectors.toMap(
                        ShortMovie::getId,
                        user -> user,
                        (existing, replacement) -> existing  // 保留先出现的元素
                ))
                .values()
                .stream()
                .sorted(Comparator.comparing(ShortMovie::getId).reversed())  // 按ID倒序
                .collect(Collectors.toList());


        List<Map<String, Object>> movieList = new ArrayList<>();
        for (ShortMovie movie : movies) {
            if (movie != null) {
                Map<String, Object> movieData = new HashMap<>();
                movieData.put("name", movie.getName());
                movieData.put("icon", movie.getIcon());

                Map<String, Object> videoId = new HashMap<>();
                videoId.put("id", movie.getId());
                movieData.put("video", videoId);
                movieList.add(movieData);
            }
        }

        return movieList;
    }

    @Override
    public Map<String, Object> getMovie(Integer movieId, Integer nid, String qd, String kid, String tempId, String userId) {
        List<Integer> midList = new ArrayList<>();
        midList.add(movieId);
        List<ShortMovie> movieOptional = shortMovieService.findByIdInAndAppId(midList, nid);

        //查询归属
        List<ShortMovie> serviceMoviesAppListAll = shortMovieService.findByIdInAndAppIds(midList, nid);

        movieOptional.addAll(serviceMoviesAppListAll);
        movieOptional = movieOptional.stream()
                .collect(Collectors.toMap(
                        ShortMovie::getId,
                        user -> user,
                        (existing, replacement) -> existing  // 保留先出现的元素
                ))
                .values()
                .stream()
                .sorted(Comparator.comparing(ShortMovie::getId).reversed())  // 按ID倒序
                .collect(Collectors.toList());


        if (movieOptional.isEmpty()) {
            return new HashMap<>();
        }
        ShortMovie movie = movieOptional.get(0);
        Map<String, Object> movieData = new HashMap<>();
//        List<ShortVideo> videos = Collections.emptyList();
//        if (qd != null && !qd.isEmpty()) {
//            videos = shortVideoChannelCoinService.findByMovieIdAndStateAndCoinRuleId(movieId, 1, StringUtils.isEmpty(kid) || "null".equals(kid)? null :Integer.parseInt(kid));
//        } else {
//            videos = shortVideoService.findByMovieIdAndState(movieId, 1);
//        }
        List<ShortVideo> videos;
        if (null != qd && !qd.isEmpty()) {
            ShortMovie shortMovie = shortMovieService.selectShortMovieById(movieId.longValue());
            ShortVideoChannelCoin shortVideoChannelCoin = new ShortVideoChannelCoin();
            shortVideoChannelCoin.setCoinRuleId(Long.parseLong(kid));
            shortVideoChannelCoin.setMovieId(movieId.longValue());

            Integer popnum = iShortCoinRuleService.getPopnumById(Long.parseLong(kid));


            videos = shortVideoChannelCoinService.selectShortVideoChannelCoinList(shortVideoChannelCoin).stream().map(videoChannelCoin -> {
                ShortVideo annotatedVideo = new ShortVideo();
                annotatedVideo.setName(shortMovie.getName());
                annotatedVideo.setId(videoChannelCoin.getId());
                annotatedVideo.setCoin(videoChannelCoin.getCoin().intValue());
                annotatedVideo.setNum(videoChannelCoin.getNum().intValue());
                annotatedVideo.setUrl(videoChannelCoin.getUrl());
                annotatedVideo.setState(videoChannelCoin.getState());
                annotatedVideo.setAddtime(videoChannelCoin.getAddtime());
                annotatedVideo.setUpdatetime(videoChannelCoin.getUpdatetime());
                annotatedVideo.setMovieId(videoChannelCoin.getMovieId());
                annotatedVideo.setPic(videoChannelCoin.getPic());
                annotatedVideo.setIsVip(videoChannelCoin.getIsVip());
                annotatedVideo.setState(videoChannelCoin.getState());
                annotatedVideo.setCreateBy(videoChannelCoin.getCreateBy());
                annotatedVideo.setUpdateBy(videoChannelCoin.getUpdateBy());
                annotatedVideo.setCreateTime(videoChannelCoin.getCreateTime());
                annotatedVideo.setUpdateTime(videoChannelCoin.getUpdateTime());
                annotatedVideo.setRemark(videoChannelCoin.getRemark());
                if (annotatedVideo.getNum().intValue() == popnum.intValue())
                    annotatedVideo.setPopnum(1);
                else
                    annotatedVideo.setPopnum(0);
                return annotatedVideo;
            }).collect(Collectors.toList());

            if (videos.isEmpty()) {
                videos = shortVideoService.findByMovieAppId(nid, movieId);
                //            //查询归属
                List<ShortVideo> serviceMoviesAppList = shortVideoService.findByMovieAppIds(nid, movieId);

                videos.addAll(serviceMoviesAppList);
                videos = videos.stream()
                        .collect(Collectors.toMap(
                                ShortVideo::getId,
                                user -> user,
                                (existing, replacement) -> existing  // 保留先出现的元素
                        ))
                        .values()
                        .stream()
                        .sorted(Comparator.comparing(ShortVideo::getId).reversed())  // 按ID倒序
                        .collect(Collectors.toList());
            }

        } else {
            videos = shortVideoService.findByMovieAppId(nid, movieId);
            //            //查询归属
            List<ShortVideo> serviceMoviesAppList = shortVideoService.findByMovieAppIds(nid, movieId);

            videos.addAll(serviceMoviesAppList);
            videos = videos.stream()
                    .collect(Collectors.toMap(
                            ShortVideo::getId,
                            user -> user,
                            (existing, replacement) -> existing  // 保留先出现的元素
                    ))
                    .values()
                    .stream()
                    .sorted(Comparator.comparing(ShortVideo::getId).reversed())  // 按ID倒序
                    .collect(Collectors.toList());
        }


        if (StringUtils.isNotEmpty(userId)) {
            // 处理uid前缀
            if (userId.length() > 4) {
                userId = userId.substring(4);
            }
        } else {
            if (StringUtils.isNotEmpty(tempId)) {
                // 处理uid前缀
                if (tempId.length() > 4) {
                    userId = tempId.substring(4);
                }
            }
        }


        List<Integer> unlockedVideoIds = shortUserUnlockVideoService.findVideoIdByMovieIdAndUser(movieId, userId);
        List<Integer> unlockedChannelCoinIds = shortVideoChannelCoinService.findChannelCoinIdByMovieIdAndUser(movieId, StringUtils.isEmpty(kid) ? null : Integer.parseInt(kid));
        List<ShortVideo> queryset = videos.stream()
                .map(video -> {
                    ShortVideo annotatedVideo = new ShortVideo();
                    annotatedVideo.setId(video.getId());
                    annotatedVideo.setPic(video.getPic());
                    annotatedVideo.setNum(video.getNum());
                    annotatedVideo.setUrl(video.getUrl());
                    annotatedVideo.setIsVip(video.getIsVip());
                    annotatedVideo.setState(video.getState());
                    annotatedVideo.setPopnum(video.getPopnum());

                    if ((qd != null && !qd.isEmpty() && unlockedChannelCoinIds.contains(video.getId())) ||
                            (qd == null && unlockedVideoIds.contains(video.getId()))) {
                        annotatedVideo.setCoin(0);
                        annotatedVideo.setIsVip(false);
                    } else {
                        annotatedVideo.setCoin(video.getCoin());
                        annotatedVideo.setIsVip(video.getIsVip());
                    }
                    return annotatedVideo;
                })
                .sorted(Comparator.comparingInt(ShortVideo::getNum))
                .collect(Collectors.toList());

        List<Map<String, Object>> videoList = new ArrayList<>();
        for (ShortVideo video : queryset) {
            Map<String, Object> videoMap = new HashMap<>();
            videoMap.put("id", video.getId());
            videoMap.put("pic", video.getPic());
            videoMap.put("num", video.getNum());
            videoMap.put("url", video.getUrl());
            videoMap.put("popnum", video.getPopnum());
            videoMap.put("state", video.getState());
            videoMap.put("movie", movieId);
            for (Integer i : unlockedVideoIds) {

                if (Long.valueOf(i).longValue() == video.getId().longValue()) {
                    video.setCoin(0);
                    video.setIsVip(false);
                }


            }
            videoMap.put("coin", video.getCoin());
            videoMap.put("is_vip", video.getIsVip());
            videoList.add(videoMap);
        }

        movieData.put("id", movie.getId());
        movieData.put("app", movie.getAppId());
        movieData.put("cate", movie.getCateId());
        movieData.put("lang", movie.getLangId());
        movieData.put("name", movie.getName());
        movieData.put("icon", movie.getIcon());
        movieData.put("director", movie.getDirector());
        movieData.put("actors", movie.getActors());
        movieData.put("up_time", movie.getUpTime());
        movieData.put("time", movie.getTime());
        movieData.put("num", movie.getNum());
        movieData.put("tag", "[]");
        movieData.put("rating", movie.getRating());
        movieData.put("description", movie.getDescription());
        movieData.put("content", movie.getContent());
        movieData.put("unit_coin", movie.getUnitCoin());
        movieData.put("vip_num", movie.getVipNum());
        movieData.put("rec", movie.getRec());
        movieData.put("is_vip", movie.getIsVip());
        movieData.put("state", movie.getState());
        movieData.put("addtime", movie.getAddtime());
        movieData.put("updatetime", movie.getUpdatetime());
        movieData.put("videos", videoList);

        return movieData;
    }

    @Override
    public Map<String, Object> getVideos(Integer movieId, Integer nid, String qd, String kid, String tempId, String userId) {
        Map<String, Object> map = new HashMap<>();
        List<Map<String, Object>> movieList = new ArrayList<>();
//        if (nid == null) {
//            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(new ErrorResponse(400, "获取失败，请检查参数"));
//        }
        List<ShortVideo> queryset;
        if (null != qd && !qd.isEmpty()) {
            ShortMovie shortMovie = shortMovieService.selectShortMovieById(movieId.longValue());
            ShortVideoChannelCoin shortVideoChannelCoin = new ShortVideoChannelCoin();
            shortVideoChannelCoin.setCoinRuleId(Long.parseLong(kid));
            shortVideoChannelCoin.setMovieId(movieId.longValue());
            queryset = shortVideoChannelCoinService.selectShortVideoChannelCoinList(shortVideoChannelCoin).stream().map(videoChannelCoin -> {
                ShortVideo annotatedVideo = new ShortVideo();
                annotatedVideo.setName(shortMovie.getName());
                annotatedVideo.setId(videoChannelCoin.getId());
                annotatedVideo.setCoin(videoChannelCoin.getCoin().intValue());
                annotatedVideo.setNum(videoChannelCoin.getNum().intValue());
                annotatedVideo.setUrl(videoChannelCoin.getUrl());
                annotatedVideo.setState(videoChannelCoin.getState());
                annotatedVideo.setAddtime(videoChannelCoin.getAddtime());
                annotatedVideo.setUpdatetime(videoChannelCoin.getUpdatetime());
                annotatedVideo.setMovieId(videoChannelCoin.getMovieId());
                annotatedVideo.setPic(videoChannelCoin.getPic());
                annotatedVideo.setIsVip(videoChannelCoin.getIsVip());
                annotatedVideo.setState(videoChannelCoin.getState());
                annotatedVideo.setCreateBy(videoChannelCoin.getCreateBy());
                annotatedVideo.setUpdateBy(videoChannelCoin.getUpdateBy());
                annotatedVideo.setCreateTime(videoChannelCoin.getCreateTime());
                annotatedVideo.setUpdateTime(videoChannelCoin.getUpdateTime());
                annotatedVideo.setRemark(videoChannelCoin.getRemark());
                return annotatedVideo;
            }).collect(Collectors.toList());

            if (queryset.isEmpty()) {
                queryset = shortVideoService.findByMovieAppId(nid, movieId);
                //查询归属
                List<ShortVideo> serviceMoviesAppList = shortVideoService.findByMovieAppIds(nid, movieId);

                queryset.addAll(serviceMoviesAppList);
                queryset = queryset.stream()
                        .collect(Collectors.toMap(
                                ShortVideo::getId,
                                user -> user,
                                (existing, replacement) -> existing  // 保留先出现的元素
                        ))
                        .values()
                        .stream()
                        .sorted(Comparator.comparing(ShortVideo::getId).reversed())  // 按ID倒序
                        .collect(Collectors.toList());
            }

        } else {
            queryset = shortVideoService.findByMovieAppId(nid, movieId);

            //查询归属
            List<ShortVideo> serviceMoviesAppList = shortVideoService.findByMovieAppIds(nid, movieId);

            queryset.addAll(serviceMoviesAppList);
            queryset = queryset.stream()
                    .collect(Collectors.toMap(
                            ShortVideo::getId,
                            user -> user,
                            (existing, replacement) -> existing  // 保留先出现的元素
                    ))
                    .values()
                    .stream()
                    .sorted(Comparator.comparing(ShortVideo::getId).reversed())  // 按ID倒序
                    .collect(Collectors.toList());
        }

        if (StringUtils.isNotEmpty(userId)) {
            // 处理uid前缀
            if (userId.length() > 4) {
                userId = userId.substring(4);
            }
        } else {
            if (StringUtils.isNotEmpty(tempId)) {
                // 处理uid前缀
                if (tempId.length() > 4) {
                    userId = tempId.substring(4);
                }
            }
        }


//        String processedTempUserId = processUserId(tempId);
//        String processedUserId = processUserId(userId);
//        User user = getUser(processedUserId, processedTempUserId);

//        List<Integer> unlockedVideoIds = userUnlockVideoRepository.findUnlockedVideoIds(movieId, user);
//        List<Integer> unlockedChannelCoinIds = userUnlockVideoRepository.findUnlockedChannelCoinIds(movieId, user);
        List<Integer> unlockedVideoIds = shortUserUnlockVideoService.findVideoIdByMovieIdAndUser(movieId, userId);
        List<Integer> unlockedChannelCoinIds = shortVideoChannelCoinService.findChannelCoinIdByMovieIdAndUser(movieId, StringUtils.isEmpty(kid) ? null : Integer.parseInt(kid));
//        List<ShortVideo> videoDataList = new ArrayList<>();
        String movieName = "";
        if (!queryset.isEmpty()) {
            movieName = queryset.get(0).getName();
        }
        map.put("movie_name", movieName);
        map.put("count", queryset.size());


        List<ShortVideo> queryset1 = queryset.stream()
                .map(video -> {
                    ShortVideo annotatedVideo = new ShortVideo();
                    annotatedVideo.setId(video.getId());
                    annotatedVideo.setPic(video.getPic());
                    annotatedVideo.setNum(video.getNum());
                    annotatedVideo.setUrl(video.getUrl());
                    annotatedVideo.setIsVip(video.getIsVip());
                    annotatedVideo.setState(video.getState());
                    annotatedVideo.setMovieId(video.getMovieId());
                    annotatedVideo.setAddtime(video.getAddtime());
                    annotatedVideo.setUpdatetime(video.getUpdatetime());


                    for (Integer i : unlockedVideoIds) {

                        if (Long.valueOf(i).longValue() == video.getId().longValue()) {
                            video.setCoin(0);
                            video.setIsVip(false);
                        }


                    }
                    if ((qd != null && !qd.isEmpty() && unlockedChannelCoinIds.contains(video.getId())) ||
                            (qd == null && unlockedVideoIds.contains(video.getId()))) {
                        annotatedVideo.setCoin(0);
                        annotatedVideo.setIsVip(false);
                    } else {
                        annotatedVideo.setCoin(video.getCoin());
                        annotatedVideo.setIsVip(video.getIsVip());
                    }
                    return annotatedVideo;
                })
                .sorted(Comparator.comparingInt(ShortVideo::getNum))
                .collect(Collectors.toList());


        for (ShortVideo video : queryset1) {
//            String coin = String.valueOf(video.getCoin());
//            boolean isVip = video.getIsVip();
//
//
//            if (unlockedVideoIds.contains(video.getId())||unlockedChannelCoinIds.contains(video.getId())) {
//                coin = "0";
//                isVip = false;
//            }

            Map<String, Object> movieData = new HashMap<>();
            movieData.put("id", video.getId());
            movieData.put("pic", video.getPic());
            movieData.put("num", video.getNum());
            movieData.put("url", video.getUrl());
            movieData.put("coin", video.getCoin());
            movieData.put("is_vip", video.getIsVip());
            movieData.put("state", video.getState());
            movieData.put("addtime", video.getAddtime());
            movieData.put("updatetime", video.getUpdatetime());
            movieData.put("movie", video.getMovieId());
            movieList.add(movieData);
//            ShortVideo videoData = new ShortVideo(
//                    video.getId(),
//                    video.getPic(),
//                    video.getNum(),
//                    video.getUrl(),
//                    coin,
//                    isVip,
//                    video.getState(),
//                    video.getAddtime(),
//                    video.getUpdatetime(),
//                    video.getMovie().getId()
//            );
//            videoDataList.add(video);
        }
        map.put("results", movieList);
        ShortUserActivityLog shortUserActivityLog = new ShortUserActivityLog();
        ShortMovie newMovie = shortMovieService.selectShortMovieById(Long.valueOf(movieId));
        if (null != newMovie) {
            map.put("icon", newMovie.getIcon());
            Integer mtype = 1;
            if ("自制".equals(newMovie.getSource()))
                mtype = 0;
            ShortUser shortUser = new ShortUser();
            if (StringUtils.isEmpty(userId)) {
                shortUser = shortUserService.selectShortUserById(Long.valueOf(1));
            } else {
                shortUser = shortUserService.selectShortUserById(Long.valueOf(userId));
            }
            shortUserActivityLog.setUserId(shortUser.getId());
            shortUserActivityLog.setAppId(shortUser.getAppId());
            shortUserActivityLog.setState("1");
            if (StringUtils.isNotEmpty(shortUser.getSource()))
                shortUserActivityLog.setNote(shortUser.getSource());
            else
                shortUserActivityLog.setNote("常规用户");
            if (StringUtils.isNotEmpty(kid) && mtype == 1) {
                String msg = "用户：" + shortUser.getId() + "，打开渠道短剧成功, 短剧：" + movieId + "，渠道：" + qd + "，扣费面板：" + kid + "，临时ID：" + tempId + ", 来源：" + shortUser.getSource();
                shortUserActivityLog.setContent(msg);

            } else {
                String msg = "用户：" + shortUser.getId() + "，打开自制短剧成功, 短剧：" + movieId + "，渠道：" + qd + "，扣费面板：" + kid + "，临时ID：" + tempId + ", 来源：" + shortUser.getSource();
                shortUserActivityLog.setContent(msg);
            }
            shortUserActivityLogService.insertShortUserActivityLog(shortUserActivityLog);

        }
        return map;
    }

    @Override
    public void saveUserActivityLog(ShortUserActivityLog shortUserActivityLog) {
        String userId = String.valueOf(shortUserActivityLog.getUserId());
        if (StringUtils.isNotEmpty(userId)) {
            // 处理uid前缀
            if (userId.length() > 4) {
                userId = userId.substring(4);
            }
        }
        shortUserActivityLog.setUserId(Long.valueOf(userId));
        shortUserActivityLog.setState("1");
        shortUserActivityLog.setStatus("0");
        shortUserActivityLogService.insertShortUserActivityLog(shortUserActivityLog);
    }

    @Override
    public List<ShortCoverImage> listLimit() {
        return shortCoverImageService.listLimit();
    }

    @Override
    public void saveFeedback(ShortFeedback shortFeedback) {

        String userId = String.valueOf(shortFeedback.getUserId());
        if (StringUtils.isNotEmpty(userId)) {
            // 处理uid前缀
            if (userId.length() > 4) {
                userId = userId.substring(4);
            }
        }
        shortFeedback.setUserId(Long.valueOf(userId));
        shortFeedback.setStatus("0");
        shortFeedbackService.insertShortFeedback(shortFeedback);

//        ShortUser user = shortUserService.selectShortUserById(shortFeedback.getUserId());
//        if (null != user) {
//            // 1. 配置API密钥
//            ApiClient defaultClient = Configuration.getDefaultApiClient();
//            ApiKeyAuth apiKey = (ApiKeyAuth) defaultClient.getAuthentication("api-key");
//            apiKey.setApiKey(brevoApiKey); // 替换为你的Brevo API密钥
//
//            // 2. 初始化邮件API
//            TransactionalEmailsApi apiInstance = new TransactionalEmailsApi();
//
//            // 3. 设置发件人
//            SendSmtpEmailSender sender = new SendSmtpEmailSender();
//
//
//            String[] stringArray = {"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"};
//            Random random = new Random();
//
//            String randomString = stringArray[random.nextInt(stringArray.length)];
//
//            sender.setEmail(randomString); // 必须已验证的邮箱
//            sender.setName(senderName);
//
//            // 4. 设置收件人
//            List<SendSmtpEmailTo> toList = new ArrayList<>();
//            SendSmtpEmailTo recipient = new SendSmtpEmailTo();
//            recipient.setEmail(null != shortFeedback.getEmail() ? shortFeedback.getEmail() : null);
//            recipient.setName("dearuser");
//            toList.add(recipient);
//
//            // 记录日志并处理结果
//            ShortRunlog runlog = new ShortRunlog();
//            runlog.setType("投诉建议邮件");
//            runlog.setCreateTime(DateUtils.getNowDate());
//            runlog.setUpdateTime(DateUtils.getNowDate());
//
//            try {
//                // 6. 创建邮件内容
//                SendSmtpEmail email = new SendSmtpEmail();
//                email.setSender(sender);
//                email.setTo(toList);
//                email.setSubject("Complaints and suggestions");
//                email.setHtmlContent("Your complaint has been received. Please submit a screenshot of your credit card subscription record, your registered email address, and your city area."); // 使用HTML内容
//
//                new Thread(() -> {
//                    try {
//                        // 7. 发送邮件
//                        CreateSmtpEmail response = apiInstance.sendTransacEmail(email);
//                        System.out.println("邮件发送成功！消息ID: " + response.getMessageId());
//                        runlog.setState("1");
//                        runlog.setContent(String.format(",投诉建议邮件发送成功！消息ID: " + response.getMessageId() + "用户Id：" + user.getId() +
//                                ",邮件主题：" + "Complaints and suggestions" +
//                                ",收件人邮件：" + shortFeedback.getEmail()));
//                        runlog.setNote(response.toString());
//                        shortRunlogService.insertShortRunlog(runlog);
//                    } catch (Exception e) {
//                        runlog.setState("0");
//                        runlog.setContent(String.format(",发送失败：" + e.getMessage() + ",邮件主题：" + "Complaints and suggestions" + ",收件人邮件：" + shortFeedback.getEmail()));
//                        runlog.setNote(e.toString());
//                        shortRunlogService.insertShortRunlog(runlog);
//                    }
//                }).start();
//
//            } catch (Exception e) {
//                System.err.println("发送失败: " + e.getMessage());
//                runlog.setState("0");
//                runlog.setContent(String.format(",发送失败：" + e.getMessage() + ",邮件主题：Complaints and suggestions" + ",收件人邮件：" + shortFeedback.getEmail()));
//                runlog.setNote(e.toString());
//                shortRunlogService.insertShortRunlog(runlog);
//            }
//        }


    }

    @Override
    public Map<String, Object> getVideos(Integer movieId, Integer nid, String qd, String kid, String tempId, String userId, String languageCode) {
        Map<String, Object> map = new HashMap<>();
        List<Map<String, Object>> movieList = new ArrayList<>();
        List<ShortVideo> queryset;
        if (null != qd && !qd.isEmpty()) {
            ShortMovie shortMovie = shortMovieService.selectShortMovieById(movieId.longValue());
            ShortVideoChannelCoin shortVideoChannelCoin = new ShortVideoChannelCoin();
            shortVideoChannelCoin.setCoinRuleId(Long.parseLong(kid));
            shortVideoChannelCoin.setMovieId(movieId.longValue());
            queryset = shortVideoChannelCoinService.selectShortVideoChannelCoinList(shortVideoChannelCoin).stream().map(videoChannelCoin -> {
                ShortVideo annotatedVideo = new ShortVideo();
                annotatedVideo.setName(shortMovie.getName());
                annotatedVideo.setId(videoChannelCoin.getId());
                annotatedVideo.setCoin(videoChannelCoin.getCoin().intValue());
                annotatedVideo.setNum(videoChannelCoin.getNum().intValue());
                annotatedVideo.setUrl(videoChannelCoin.getUrl());
                annotatedVideo.setState(videoChannelCoin.getState());
                annotatedVideo.setAddtime(videoChannelCoin.getAddtime());
                annotatedVideo.setUpdatetime(videoChannelCoin.getUpdatetime());
                annotatedVideo.setMovieId(videoChannelCoin.getMovieId());
                annotatedVideo.setPic(videoChannelCoin.getPic());
                annotatedVideo.setIsVip(videoChannelCoin.getIsVip());
                annotatedVideo.setState(videoChannelCoin.getState());
                annotatedVideo.setCreateBy(videoChannelCoin.getCreateBy());
                annotatedVideo.setUpdateBy(videoChannelCoin.getUpdateBy());
                annotatedVideo.setCreateTime(videoChannelCoin.getCreateTime());
                annotatedVideo.setUpdateTime(videoChannelCoin.getUpdateTime());
                annotatedVideo.setRemark(videoChannelCoin.getRemark());
                return annotatedVideo;
            }).collect(Collectors.toList());

            if (queryset.isEmpty()) {
                queryset = shortVideoService.findByMovieAppId(nid, movieId);
                //查询归属
                List<ShortVideo> serviceMoviesAppList = shortVideoService.findByMovieAppIds(nid, movieId);

                queryset.addAll(serviceMoviesAppList);
                queryset = queryset.stream()
                        .collect(Collectors.toMap(
                                ShortVideo::getId,
                                user -> user,
                                (existing, replacement) -> existing  // 保留先出现的元素
                        ))
                        .values()
                        .stream()
                        .sorted(Comparator.comparing(ShortVideo::getId).reversed())  // 按ID倒序
                        .collect(Collectors.toList());
            }

        } else {
            queryset = shortVideoService.findByMovieAppId(nid, movieId);

            //查询归属
            List<ShortVideo> serviceMoviesAppList = shortVideoService.findByMovieAppIds(nid, movieId);

            queryset.addAll(serviceMoviesAppList);
            queryset = queryset.stream()
                    .collect(Collectors.toMap(
                            ShortVideo::getId,
                            user -> user,
                            (existing, replacement) -> existing  // 保留先出现的元素
                    ))
                    .values()
                    .stream()
                    .sorted(Comparator.comparing(ShortVideo::getId).reversed())  // 按ID倒序
                    .collect(Collectors.toList());
        }

        if (StringUtils.isNotEmpty(userId)) {
            // 处理uid前缀
            if (userId.length() > 4) {
                userId = userId.substring(4);
            }
        } else {
            if (StringUtils.isNotEmpty(tempId)) {
                // 处理uid前缀
                if (tempId.length() > 4) {
                    userId = tempId.substring(4);
                }
            }
        }
        List<Integer> unlockedVideoIds = shortUserUnlockVideoService.findVideoIdByMovieIdAndUser(movieId, userId);
        List<Integer> unlockedChannelCoinIds = shortVideoChannelCoinService.findChannelCoinIdByMovieIdAndUser(movieId, StringUtils.isEmpty(kid) ? null : Integer.parseInt(kid));
        String movieName = "";
        if (!queryset.isEmpty()) {
            movieName = queryset.get(0).getName();
        }
        map.put("movie_name", movieName);
        map.put("count", queryset.size());


        List<ShortVideo> queryset1 = queryset.stream()
                .map(video -> {
                    ShortVideo annotatedVideo = new ShortVideo();
                    annotatedVideo.setId(video.getId());
                    annotatedVideo.setPic(video.getPic());
                    annotatedVideo.setNum(video.getNum());
                    annotatedVideo.setUrl(video.getUrl());
                    annotatedVideo.setIsVip(video.getIsVip());
                    annotatedVideo.setState(video.getState());
                    annotatedVideo.setMovieId(video.getMovieId());
                    annotatedVideo.setAddtime(video.getAddtime());
                    annotatedVideo.setUpdatetime(video.getUpdatetime());


                    for (Integer i : unlockedVideoIds) {

                        if (Long.valueOf(i).longValue() == video.getId().longValue()) {
                            video.setCoin(0);
                            video.setIsVip(false);
                        }


                    }
                    if ((qd != null && !qd.isEmpty() && unlockedChannelCoinIds.contains(video.getId())) ||
                            (qd == null && unlockedVideoIds.contains(video.getId()))) {
                        annotatedVideo.setCoin(0);
                        annotatedVideo.setIsVip(false);
                    } else {
                        annotatedVideo.setCoin(video.getCoin());
                        annotatedVideo.setIsVip(video.getIsVip());
                    }
                    return annotatedVideo;
                })
                .sorted(Comparator.comparingInt(ShortVideo::getNum))
                .collect(Collectors.toList());

        //查询指定翻译后的剧
        //查询翻译后的剧
        List<ShortMovieI18n> shortMovieI18nList = shortMovieI18nService.queryTranslateMovieList(Long.valueOf(movieId), languageCode);
        ShortMovieI18n shortMovieI18n = null;
        if (CollectionUtil.isNotEmpty(shortMovieI18nList)) {
            shortMovieI18n = shortMovieI18nList.get(0);
        }
        //List<Long> videoIdList = queryset.stream().map(ShortVideo::getId).collect(Collectors.toList());
        //查询原始剧的 videoId
        List<ShortVideo> shortVideos = shortVideoService.selectShortVideoByMovieId(Long.valueOf(movieId));
        //构造 movieId-num videoId 的映射关系
        Map<Integer, Long> numVideoIdMap = shortVideos.stream().collect(Collectors.toMap(ShortVideo::getNum, ShortVideo::getId));
        List<Long> videoIdList = shortVideos.stream().map(ShortVideo::getId).collect(Collectors.toList());
        List<ShortVideoI18n> shortVideoI18ns = shortVideoI18nService.queryTranslateVideoList(videoIdList, null);
        Map<Long, List<ShortVideoI18n>> videoI18nMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(shortVideoI18ns)) {
            videoI18nMap = shortVideoI18ns.stream().collect(Collectors.groupingBy(ShortVideoI18n::getVideoId));
        }
        //查询指定语言的翻译结果
        for (ShortVideo video : queryset1) {
            Map<String, Object> movieData = new HashMap<>();
            movieData.put("id", video.getId());
            movieData.put("pic", video.getPic());
            movieData.put("num", video.getNum());
            movieData.put("url", video.getUrl());
            movieData.put("coin", video.getCoin());
            movieData.put("is_vip", video.getIsVip());
            movieData.put("state", video.getState());
            movieData.put("addtime", video.getAddtime());
            movieData.put("updatetime", video.getUpdatetime());
            movieData.put("movie", video.getMovieId());
            //走深链
            if (null != qd && !qd.isEmpty()) {
                if (videoI18nMap.containsKey(numVideoIdMap.get(video.getNum()))) {
                    movieData.put("subtitleUrlList", videoI18nMap.get(numVideoIdMap.get(video.getNum())));
                }
            } else {
                if (videoI18nMap.containsKey(video.getId())) {
                    List<ShortVideoI18n> shortVideoI18nCollect = videoI18nMap.get(video.getId());
                    movieData.put("subtitleUrlList", shortVideoI18nCollect);
                }
            }
            movieList.add(movieData);
        }
        map.put("results", movieList);
        ShortUserActivityLog shortUserActivityLog = new ShortUserActivityLog();
        ShortMovie newMovie = shortMovieService.selectShortMovieById(Long.valueOf(movieId));
        if (null != newMovie) {
            map.put("icon", newMovie.getIcon());
            if (null != shortMovieI18n) {
                map.put("icon", shortMovieI18n.getIcon());
                map.put("movie_name", shortMovieI18n.getName());
            }
            Integer mtype = 1;
            if ("自制".equals(newMovie.getSource()))
                mtype = 0;
            ShortUser shortUser = new ShortUser();
            if (StringUtils.isEmpty(userId)) {
                shortUser = shortUserService.selectShortUserById(Long.valueOf(1));
            } else {
                shortUser = shortUserService.selectShortUserById(Long.valueOf(userId));
            }
            shortUserActivityLog.setUserId(shortUser.getId());
            shortUserActivityLog.setAppId(shortUser.getAppId());
            shortUserActivityLog.setState("1");
            if (StringUtils.isNotEmpty(shortUser.getSource()))
                shortUserActivityLog.setNote(shortUser.getSource());
            else
                shortUserActivityLog.setNote("常规用户");
            if (StringUtils.isNotEmpty(kid) && mtype == 1) {
                String msg = "用户：" + shortUser.getId() + "，打开渠道短剧成功, 短剧：" + movieId + "，渠道：" + qd + "，扣费面板：" + kid + "，临时ID：" + tempId + ", 来源：" + shortUser.getSource();
                shortUserActivityLog.setContent(msg);

            } else {
                String msg = "用户：" + shortUser.getId() + "，打开自制短剧成功, 短剧：" + movieId + "，渠道：" + qd + "，扣费面板：" + kid + "，临时ID：" + tempId + ", 来源：" + shortUser.getSource();
                shortUserActivityLog.setContent(msg);
            }
            shortUserActivityLogService.insertShortUserActivityLog(shortUserActivityLog);

        }
        return map;
    }

    @Override
    public Map<String, Object> getMovie(Integer movieId, Integer nid, String qd, String kid, String tempId, String userId, String languageCode) {
        List<Integer> midList = new ArrayList<>();
        midList.add(movieId);
        List<ShortMovie> movieOptional = shortMovieService.findByIdInAndAppId(midList, nid);

        //查询归属
        List<ShortMovie> serviceMoviesAppListAll = shortMovieService.findByIdInAndAppIds(midList, nid);

        movieOptional.addAll(serviceMoviesAppListAll);
        movieOptional = movieOptional.stream()
                .collect(Collectors.toMap(
                        ShortMovie::getId,
                        user -> user,
                        (existing, replacement) -> existing  // 保留先出现的元素
                ))
                .values()
                .stream()
                .sorted(Comparator.comparing(ShortMovie::getId).reversed())  // 按ID倒序
                .collect(Collectors.toList());


        if (movieOptional.isEmpty()) {
            return new HashMap<>();
        }
        ShortMovie movie = movieOptional.get(0);
        Map<String, Object> movieData = new HashMap<>();
        List<ShortVideo> videos;
        if (null != qd && !qd.isEmpty()) {
            ShortMovie shortMovie = shortMovieService.selectShortMovieById(movieId.longValue());
            ShortVideoChannelCoin shortVideoChannelCoin = new ShortVideoChannelCoin();
            shortVideoChannelCoin.setCoinRuleId(Long.parseLong(kid));
            shortVideoChannelCoin.setMovieId(movieId.longValue());

            Integer popnum = iShortCoinRuleService.getPopnumById(Long.parseLong(kid));


            videos = shortVideoChannelCoinService.selectShortVideoChannelCoinList(shortVideoChannelCoin).stream().map(videoChannelCoin -> {
                ShortVideo annotatedVideo = new ShortVideo();
                annotatedVideo.setName(shortMovie.getName());
                annotatedVideo.setId(videoChannelCoin.getId());
                annotatedVideo.setCoin(videoChannelCoin.getCoin().intValue());
                annotatedVideo.setNum(videoChannelCoin.getNum().intValue());
                annotatedVideo.setUrl(videoChannelCoin.getUrl());
                annotatedVideo.setState(videoChannelCoin.getState());
                annotatedVideo.setAddtime(videoChannelCoin.getAddtime());
                annotatedVideo.setUpdatetime(videoChannelCoin.getUpdatetime());
                annotatedVideo.setMovieId(videoChannelCoin.getMovieId());
                annotatedVideo.setPic(videoChannelCoin.getPic());
                annotatedVideo.setIsVip(videoChannelCoin.getIsVip());
                annotatedVideo.setState(videoChannelCoin.getState());
                annotatedVideo.setCreateBy(videoChannelCoin.getCreateBy());
                annotatedVideo.setUpdateBy(videoChannelCoin.getUpdateBy());
                annotatedVideo.setCreateTime(videoChannelCoin.getCreateTime());
                annotatedVideo.setUpdateTime(videoChannelCoin.getUpdateTime());
                annotatedVideo.setRemark(videoChannelCoin.getRemark());
                if (annotatedVideo.getNum().intValue() == popnum.intValue())
                    annotatedVideo.setPopnum(1);
                else
                    annotatedVideo.setPopnum(0);
                return annotatedVideo;
            }).collect(Collectors.toList());

            if (videos.isEmpty()) {
                videos = shortVideoService.findByMovieAppId(nid, movieId);
                //            //查询归属
                List<ShortVideo> serviceMoviesAppList = shortVideoService.findByMovieAppIds(nid, movieId);

                videos.addAll(serviceMoviesAppList);
                videos = videos.stream()
                        .collect(Collectors.toMap(
                                ShortVideo::getId,
                                user -> user,
                                (existing, replacement) -> existing  // 保留先出现的元素
                        ))
                        .values()
                        .stream()
                        .sorted(Comparator.comparing(ShortVideo::getId).reversed())  // 按ID倒序
                        .collect(Collectors.toList());
            }

        } else {
            videos = shortVideoService.findByMovieAppId(nid, movieId);
            //            //查询归属
            List<ShortVideo> serviceMoviesAppList = shortVideoService.findByMovieAppIds(nid, movieId);

            videos.addAll(serviceMoviesAppList);
            videos = videos.stream()
                    .collect(Collectors.toMap(
                            ShortVideo::getId,
                            user -> user,
                            (existing, replacement) -> existing  // 保留先出现的元素
                    ))
                    .values()
                    .stream()
                    .sorted(Comparator.comparing(ShortVideo::getId).reversed())  // 按ID倒序
                    .collect(Collectors.toList());
        }


        if (StringUtils.isNotEmpty(userId)) {
            // 处理uid前缀
            if (userId.length() > 4) {
                userId = userId.substring(4);
            }
        } else {
            if (StringUtils.isNotEmpty(tempId)) {
                // 处理uid前缀
                if (tempId.length() > 4) {
                    userId = tempId.substring(4);
                }
            }
        }


        List<Integer> unlockedVideoIds = shortUserUnlockVideoService.findVideoIdByMovieIdAndUser(movieId, userId);
        List<Integer> unlockedChannelCoinIds = shortVideoChannelCoinService.findChannelCoinIdByMovieIdAndUser(movieId, StringUtils.isEmpty(kid) ? null : Integer.parseInt(kid));
        List<ShortVideo> queryset = videos.stream()
                .map(video -> {
                    ShortVideo annotatedVideo = new ShortVideo();
                    annotatedVideo.setId(video.getId());
                    annotatedVideo.setPic(video.getPic());
                    annotatedVideo.setNum(video.getNum());
                    annotatedVideo.setUrl(video.getUrl());
                    annotatedVideo.setIsVip(video.getIsVip());
                    annotatedVideo.setState(video.getState());
                    annotatedVideo.setPopnum(video.getPopnum());

                    if ((qd != null && !qd.isEmpty() && unlockedChannelCoinIds.contains(video.getId())) ||
                            (qd == null && unlockedVideoIds.contains(video.getId()))) {
                        annotatedVideo.setCoin(0);
                        annotatedVideo.setIsVip(false);
                    } else {
                        annotatedVideo.setCoin(video.getCoin());
                        annotatedVideo.setIsVip(video.getIsVip());
                    }
                    return annotatedVideo;
                })
                .sorted(Comparator.comparingInt(ShortVideo::getNum))
                .collect(Collectors.toList());

        //查询翻译后的剧
        List<ShortMovieI18n> shortMovieI18nList = shortMovieI18nService.queryTranslateMovieList(Long.valueOf(movieId), languageCode);
        ShortMovieI18n shortMovieI18n = null;
        if (CollectionUtil.isNotEmpty(shortMovieI18nList)) {
            shortMovieI18n = shortMovieI18nList.get(0);
        }
        //List<Long> videoIdList = queryset.stream().map(ShortVideo::getId).collect(Collectors.toList());
        //查询原始剧的 videoId
        List<ShortVideo> shortVideos = shortVideoService.selectShortVideoByMovieId(Long.valueOf(movieId));
        //构造 movieId-num videoId 的映射关系
        Map<Integer, Long> numVideoIdMap = shortVideos.stream().collect(Collectors.toMap(ShortVideo::getNum, ShortVideo::getId));
        List<Long> videoIdList = shortVideos.stream().map(ShortVideo::getId).collect(Collectors.toList());
        List<ShortVideoI18n> shortVideoI18ns = shortVideoI18nService.queryTranslateVideoList(videoIdList, null);
        Map<Long, List<ShortVideoI18n>> videoI18nMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(shortVideoI18ns)) {
            videoI18nMap = shortVideoI18ns.stream().collect(Collectors.groupingBy(ShortVideoI18n::getVideoId));
        }


        List<Map<String, Object>> videoList = new ArrayList<>();
        for (ShortVideo video : queryset) {
            Map<String, Object> videoMap = new HashMap<>();
            videoMap.put("id", video.getId());
            videoMap.put("pic", video.getPic());
            videoMap.put("num", video.getNum());
            videoMap.put("url", video.getUrl());
            videoMap.put("popnum", video.getPopnum());
            videoMap.put("state", video.getState());
            videoMap.put("movie", movieId);
            //走深链
            if (null != qd && !qd.isEmpty()) {
                if (videoI18nMap.containsKey(numVideoIdMap.get(video.getNum()))) {
                    videoMap.put("subtitleUrlList", videoI18nMap.get(numVideoIdMap.get(video.getNum())));
                }
            } else {
                if (videoI18nMap.containsKey(video.getId())) {
                    List<ShortVideoI18n> shortVideoI18nCollect = videoI18nMap.get(video.getId());
                    videoMap.put("subtitleUrlList", shortVideoI18nCollect);
                }
            }
            for (Integer i : unlockedVideoIds) {

                if (Long.valueOf(i).longValue() == video.getId().longValue()) {
                    video.setCoin(0);
                    video.setIsVip(false);
                }


            }
            videoMap.put("coin", video.getCoin());
            videoMap.put("is_vip", video.getIsVip());
            videoList.add(videoMap);
        }

        movieData.put("id", movie.getId());
        movieData.put("app", movie.getAppId());
        movieData.put("cate", movie.getCateId());
        movieData.put("lang", movie.getLangId());
        movieData.put("name", null != shortMovieI18n ? shortMovieI18n.getName() : movie.getName());
        movieData.put("icon", null != shortMovieI18n ? shortMovieI18n.getIcon() : movie.getIcon());
        movieData.put("director", null != shortMovieI18n ? shortMovieI18n.getDirector() : movie.getDirector());
        movieData.put("actors", null != shortMovieI18n ? shortMovieI18n.getActors() : movie.getActors());
        movieData.put("up_time", movie.getUpTime());
        movieData.put("time", movie.getTime());
        movieData.put("num", movie.getNum());
        movieData.put("tag", "[]");
        movieData.put("rating", movie.getRating());
        movieData.put("description", null != shortMovieI18n ? shortMovieI18n.getDescription() : movie.getDescription());
        movieData.put("content", null != shortMovieI18n ? shortMovieI18n.getContent() : movie.getContent());
        movieData.put("unit_coin", movie.getUnitCoin());
        movieData.put("vip_num", movie.getVipNum());
        movieData.put("rec", movie.getRec());
        movieData.put("is_vip", movie.getIsVip());
        movieData.put("state", movie.getState());
        movieData.put("addtime", movie.getAddtime());
        movieData.put("updatetime", movie.getUpdatetime());
        movieData.put("videos", videoList);
        // movie id 在有效的sem link 中

        ShortUserLinkMovieBind bind = new ShortUserLinkMovieBind();
        bind.setMovieId(movie.getId());
        bind.setUserId(Long.valueOf(userId));
        bind.setStatus(1);
        ShortUserLinkMovieBind shortUserLinkMovieBind = shortUserLinkMovieBindMapper.selectShortUserLinkMovieBindList(bind)
                .stream()
                .findFirst()
                .orElse(null);
        if (shortUserLinkMovieBind != null) {
            Date logTime = shortUserLinkMovieBind.getLogTime();
            long diffInMillis = System.currentTimeMillis() - logTime.getTime();
            long diffInHours = diffInMillis / (60 * 60 * 1000);
            if (diffInHours > 24) {
                movieData.put("isLinkMovie", false);
            } else {
                movieData.put("isLinkMovie", true);
            }
        } else {
            movieData.put("isLinkMovie", false);
        }

        return movieData;
    }

    @Override
    public List<Map<String, Object>> getFilterMovieI18nList(Map<String, Object> requestData) {
        List<Map<String, Object>> dataList = new ArrayList<>();
//        List<Map<String, Object>> data = (List<Map<String, Object>>) requestData.getOrDefault("data", new ArrayList<>());


        // 获取data数组（需类型转换）
        List<Map<String, Object>> reData = (List<Map<String, Object>>) requestData.get("data");
        String languageCode = (String) requestData.get("languageCode");
        reData.forEach(item -> {

            String movieId = String.valueOf(item.get("movie"));
            String kid = String.valueOf(item.get("kid"));
            String pid = String.valueOf(item.get("pid"));

            ShortMovie movie = shortMovieService.selectShortMovieById(Long.valueOf(movieId));
            //查询翻译后的剧
            List<ShortMovieI18n> shortMovieI18nList = shortMovieI18nService.queryTranslateMovieList(Long.valueOf(movieId), languageCode);
            ShortMovieI18n shortMovieI18n = null;
            if (CollectionUtil.isNotEmpty(shortMovieI18nList)) {
                shortMovieI18n = shortMovieI18nList.get(0);
            }
            if (null != movie) {
                ShortVideoChannelCoin qdVideo = shortVideoChannelCoinService.findByMovieAndStateAndCoinRuleId(movie.getId(), 1, StringUtils.isEmpty(kid) ? null : Integer.valueOf(kid));

                ShortVideo shortVideo = shortVideoService.getVideoByMovieIdAndNum(movie.getId(), 1);
                List<ShortVideoI18n> shortVideoI18nList = null;
                if (null != shortVideo) {
                    shortVideoI18nList = shortVideoI18nService.queryTranslateVideoList(Arrays.asList(shortVideo.getId()), languageCode);
                }


                List<Map<String, Object>> videoList = new ArrayList<>();
                if (qdVideo != null) {
                    Map<String, Object> videoMap = new HashMap<>();
                    videoMap.put("pic", qdVideo.getPic());
                    videoMap.put("url", qdVideo.getUrl());
                    videoMap.put("is_vip", qdVideo.getIsVip());
                    if (CollectionUtil.isNotEmpty(shortVideoI18nList)) {
                        videoMap.put("subtitle_url", shortVideoI18nList.get(0).getSubtitleUrl());
                    }
                    videoList.add(videoMap);
                }

                Map<String, Object> resultItem = new HashMap<>();
                resultItem.put("id", movie.getId());
                resultItem.put("app", movie.getAppId());
                resultItem.put("name", null != shortMovieI18n ? shortMovieI18n.getName() : movie.getName());
                resultItem.put("icon", null != shortMovieI18n ? shortMovieI18n.getIcon() : movie.getIcon());
                resultItem.put("time", movie.getTime());
                resultItem.put("num", movie.getNum());
                resultItem.put("rating", movie.getRating());
                resultItem.put("description", null != shortMovieI18n ? shortMovieI18n.getDescription() : movie.getDescription());
                resultItem.put("content", movie.getContent());
                resultItem.put("rec", movie.getRec());
                resultItem.put("source", movie.getSource());
                resultItem.put("is_vip", movie.getIsVip());
                resultItem.put("state", movie.getState());
                resultItem.put("first_video", videoList);

                dataList.add(resultItem);
            }

        });
        return dataList;
    }

    @Override
    public List<Map<String, Object>> getMovies(Integer nid, String type, String languageCode, int ifApp, String unid) {
        List<Map<String, Object>> data = new ArrayList<>();

        Date exTime = null;
        if (null != unid) {
            ShortUser shortUser = shortUserService.selectByAppIdAndUnid(nid, unid);
            if (null != shortUser)
                exTime = shortUser.getCreateTime();

            if ((shortUser != null && shortUser.getState().equals("0") /*&& null != shortUser.getExpireTime() && shortUser.getExpireTime().before(new Date())*/)) {
                Map<String, Object> movieData = new HashMap<>();
                data.add(movieData);
                return data;
            }
        }

//        shortMovieRecService.selectShortMovieRecList()
        List<ShortMovie> serviceMoviesList = shortMovieRecService.getMoviesByRec(nid, type, ifApp, exTime, languageCode);
        if (serviceMoviesList.isEmpty()) {
            // 获取电影列表
            serviceMoviesList = shortMovieService.getMoviesI18n(nid, type, ifApp, exTime, languageCode);

            //查询归属影片
            List<ShortMovie> serviceMoviesAppList = shortMovieService.getMoviesByAppsI18n(nid, type, ifApp, exTime, languageCode);
            serviceMoviesList.addAll(serviceMoviesAppList);

            serviceMoviesList = serviceMoviesList.stream()
                    .collect(Collectors.toMap(
                            ShortMovie::getId,
                            user -> user,
                            (existing, replacement) -> existing  // 保留先出现的元素
                    ))
                    .values()
                    .stream()
                    .sorted(Comparator.comparing(ShortMovie::getId).reversed())  // 按ID倒序
                    .collect(Collectors.toList());
        }


        //获取movieId
        List<Long> movieIdCollect = serviceMoviesList.stream().map(ShortMovie::getId).collect(Collectors.toList());
        //查询翻译后的剧
        List<ShortMovieI18n> shortMovieI18nList = shortMovieI18nService.queryTranslateMovieList(movieIdCollect, languageCode);
        Map<Long, ShortMovieI18n> shortMovieI18nMap = shortMovieI18nList.stream().collect(Collectors.toMap(ShortMovieI18n::getMovieId, item -> item));
        //查询根据movieId查询每个剧的第一集
        List<Long> translateMovieIdList = shortMovieI18nList.stream().map(ShortMovieI18n::getMovieId).collect(Collectors.toList());
        List<ShortVideo> shortVideoList = shortVideoService.selectByMovieIdListAndNum(translateMovieIdList, 1);
        Map<Long, ShortVideoI18n> shortVideoI18nMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(shortVideoList)) {
            List<Long> videoIdList = shortVideoList.stream().map(ShortVideo::getId).collect(Collectors.toList());
            List<ShortVideoI18n> shortVideoI18ns = shortVideoI18nService.queryTranslateVideoList(videoIdList, languageCode);
            shortVideoI18nMap = shortVideoI18ns.stream().collect(Collectors.toMap(ShortVideoI18n::getVideoId, item -> item));
        }
        for (ShortMovie movie : serviceMoviesList) {

            Map<String, Object> movieData = new HashMap<>();
            movieData.put("id", movie.getId());
            movieData.put("app", movie.getAppId());
            movieData.put("name", shortMovieI18nMap.containsKey(movie.getId()) ? shortMovieI18nMap.get(movie.getId()).getName() : movie.getName());
            movieData.put("icon", shortMovieI18nMap.containsKey(movie.getId()) ? shortMovieI18nMap.get(movie.getId()).getIcon() : movie.getIcon());
            movieData.put("time", movie.getTime());
            movieData.put("num", movie.getNum());
            movieData.put("rating", movie.getRating());
            movieData.put("description", shortMovieI18nMap.containsKey(movie.getId()) ? shortMovieI18nMap.get(movie.getId()).getDescription() : movie.getDescription());
            movieData.put("content", shortMovieI18nMap.containsKey(movie.getId()) ? shortMovieI18nMap.get(movie.getId()).getContent() : movie.getContent());
            movieData.put("rec", movie.getRec());
            movieData.put("source", movie.getSource());
            movieData.put("is_vip", movie.getIsVip());
            movieData.put("state", movie.getState());


            ShortVideo shortVideo = shortVideoService.getVideoByMovieIdAndNum(movie.getId(), 1);
            if (null != shortVideo) {
                Map<String, Object> firstVideoMap = new HashMap<>();
                firstVideoMap.put("pic", shortVideo.getPic());
                firstVideoMap.put("url", shortVideo.getUrl());
                if (shortVideoI18nMap.containsKey(shortVideo.getId())) {
                    firstVideoMap.put("subtitleUrl", shortVideoI18nMap.get(shortVideo.getId()).getSubtitleUrl());
                } else {
                    firstVideoMap.put("subtitleUrl", "https://file.flareshort.com/videos/Reborn To Wreck/webvtt/zh-TW/zh-TW_1.webvtt");
                }
                movieData.put("first_video", firstVideoMap);
            } else {
                movieData.put("first_video", null);
            }

            data.add(movieData);
        }

        return data;
    }

    @Override
    public List<Map<String, Object>> getSeriesEpisodes(Integer nid, String type, String languageCode, int ifApp, String unid) {
        List<Map<String, Object>> data = new ArrayList<>();

        Date exTime = null;
        if (null != unid) {
            ShortUser shortUser = shortUserService.selectByAppIdAndUnid(nid, unid);
            if (null != shortUser)
                exTime = shortUser.getCreateTime();

            if ((shortUser != null && shortUser.getUnsub() == 1 && null != shortUser.getExpireTime() && shortUser.getExpireTime().before(new Date()))) {
                Map<String, Object> movieData = new HashMap<>();
                data.add(movieData);
                return data;
            }
        }

        // 专门查询剧集类型（type=3）的内容
        List<ShortMovie> serviceMoviesList = shortMovieService.getMovies(nid, type, ifApp, exTime);

        // 查询归属影片
        List<ShortMovie> serviceMoviesAppList = shortMovieService.getMoviesByApps(nid, type, ifApp, exTime);
        serviceMoviesList.addAll(serviceMoviesAppList);

        // 去重并按ID倒序排列
        serviceMoviesList = serviceMoviesList.stream()
                .collect(Collectors.toMap(
                        ShortMovie::getId,
                        user -> user,
                        (existing, replacement) -> existing
                ))
                .values()
                .stream()
                .sorted(Comparator.comparing(ShortMovie::getId).reversed())
                .collect(Collectors.toList());

        // 获取movieId
        List<Long> movieIdCollect = serviceMoviesList.stream().map(ShortMovie::getId).collect(Collectors.toList());

        // 查询翻译后的剧集
        List<ShortMovieI18n> shortMovieI18nList = shortMovieI18nService.queryTranslateMovieList(movieIdCollect, languageCode);
        Map<Long, ShortMovieI18n> shortMovieI18nMap = shortMovieI18nList.stream().collect(Collectors.toMap(ShortMovieI18n::getMovieId, item -> item));

        // 查询每个剧集的第一集
        List<ShortVideo> shortVideoList = shortVideoService.selectByMovieIdListAndNum(movieIdCollect, 1);

        // 构建第一集的所有多语言字幕映射
        Map<Long, List<ShortVideoI18n>> videoAllSubtitlesMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(shortVideoList)) {
            List<Long> videoIdList = shortVideoList.stream().map(ShortVideo::getId).collect(Collectors.toList());
            // 查询每个视频的所有语言字幕，传null获取所有语言
            List<ShortVideoI18n> allVideoI18ns = shortVideoI18nService.queryTranslateVideoList(videoIdList, null);
            // 按videoId分组
            videoAllSubtitlesMap = allVideoI18ns.stream()
                    .collect(Collectors.groupingBy(ShortVideoI18n::getVideoId));
        }

        // 组装返回数据，专注于剧集第一集和多语言字幕
        for (ShortMovie movie : serviceMoviesList) {
            Map<String, Object> movieData = new HashMap<>();
            movieData.put("id", movie.getId());
            movieData.put("app", movie.getAppId());
            movieData.put("name", shortMovieI18nMap.containsKey(movie.getId()) ? shortMovieI18nMap.get(movie.getId()).getName() : movie.getName());
            movieData.put("icon", shortMovieI18nMap.containsKey(movie.getId()) ? shortMovieI18nMap.get(movie.getId()).getIcon() : movie.getIcon());
            movieData.put("time", movie.getTime());
            movieData.put("num", movie.getNum());
            movieData.put("rating", movie.getRating());
            movieData.put("description", shortMovieI18nMap.containsKey(movie.getId()) ? shortMovieI18nMap.get(movie.getId()).getDescription() : movie.getDescription());
            movieData.put("content", shortMovieI18nMap.containsKey(movie.getId()) ? shortMovieI18nMap.get(movie.getId()).getContent() : movie.getContent());
            movieData.put("rec", movie.getRec());
            movieData.put("source", movie.getSource());
            movieData.put("is_vip", movie.getIsVip());
            movieData.put("state", movie.getState());

            // 获取剧集第一集信息，返回所有语言的字幕文件
            ShortVideo shortVideo = shortVideoService.getVideoByMovieIdAndNum(movie.getId(), 1);
            if (null != shortVideo) {
                Map<String, Object> firstEpisodeMap = new HashMap<>();
                firstEpisodeMap.put("pic", shortVideo.getPic());
                firstEpisodeMap.put("url", shortVideo.getUrl());
                firstEpisodeMap.put("episode_number", 1);

                // 获取该集的所有多语言字幕文件
                List<Map<String, Object>> subtitleUrlList = new ArrayList<>();
                if (videoAllSubtitlesMap.containsKey(shortVideo.getId())) {
                    List<ShortVideoI18n> allSubtitles = videoAllSubtitlesMap.get(shortVideo.getId());
                    for (ShortVideoI18n subtitle : allSubtitles) {
                        Map<String, Object> subtitleData = new HashMap<>();
                        subtitleData.put("id", subtitle.getId());
                        subtitleData.put("videoId", subtitle.getVideoId());
                        subtitleData.put("languageCode", subtitle.getLanguageCode());
                        subtitleData.put("subtitleUrl", subtitle.getSubtitleUrl());
                        subtitleData.put("status", subtitle.getStatus());
                        subtitleData.put("createBy", subtitle.getCreateBy());
                        subtitleData.put("createTime", subtitle.getCreateTime());
                        subtitleData.put("updateBy", subtitle.getUpdateBy());
                        subtitleData.put("updateTime", subtitle.getUpdateTime());
                        subtitleData.put("remark", subtitle.getRemark());
                        subtitleUrlList.add(subtitleData);
                    }
                }

                firstEpisodeMap.put("subtitleUrlList", subtitleUrlList);
                movieData.put("first_episode", firstEpisodeMap);
            } else {
                movieData.put("first_episode", null);
            }

            data.add(movieData);
        }

        return data;
    }

    @Override
    public List<Map<String, Object>> getPageListByAppId(Integer nid, String languageCode) {
        List<Map<String, Object>> data = new ArrayList<>();
        // 查询page信息
        List<ShortPage> shortPageList = shortPageService.getPageListByAppId(nid);
        List<Long> collect = shortPageList.stream().map(ShortPage::getId).collect(Collectors.toList());
        List<ShortPageI18n> shortPageI18nList = shortPageI18nService.selectByPageIdListAndLanguageCode(collect, languageCode);
        Map<Long, ShortPageI18n> translateShortPageI18nMap = shortPageI18nList.stream().collect(Collectors.toMap(ShortPageI18n::getPageId, item -> item));
        for (ShortPage shortPage : shortPageList) {
            Map<String, Object> pageData = new HashMap<>();
            pageData.put("id", shortPage.getId());
            pageData.put("app", shortPage.getAppId());
            pageData.put("name", translateShortPageI18nMap.containsKey(shortPage.getId()) ? translateShortPageI18nMap.get(shortPage.getId()).getName() : shortPage.getName());
            // todo  待配置
            pageData.put("url", shortPage.getUrl());
            pageData.put("addtime", shortPage.getAddtime());
            pageData.put("cname", shortPage.getCname());

            data.add(pageData);
        }

        return data;
    }

    @Override
    public List<Map<String, Object>> getMovieList(Integer nid, String vid, String languageCode) {
        List<Integer> midList = new ArrayList<>();
        if (vid != null && !vid.isEmpty()) {
            midList = Arrays.stream(vid.split(","))
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
        }
        List<ShortMovie> movies = shortMovieService.findByIdInAndAppId(midList, nid);

        //查询归属
        List<ShortMovie> serviceMoviesAppList = shortMovieService.findByIdInAndAppIds(midList, nid);

        movies.addAll(serviceMoviesAppList);
        movies = movies.stream()
                .collect(Collectors.toMap(
                        ShortMovie::getId,
                        user -> user,
                        (existing, replacement) -> existing  // 保留先出现的元素
                ))
                .values()
                .stream()
                .sorted(Comparator.comparing(ShortMovie::getId).reversed())  // 按ID倒序
                .collect(Collectors.toList());


        List<Map<String, Object>> movieList = new ArrayList<>();
        for (ShortMovie movie : movies) {
            if (movie != null) {
                Map<String, Object> movieData = new HashMap<>();
                movieData.put("name", movie.getName());
                movieData.put("icon", movie.getIcon());

                Map<String, Object> videoId = new HashMap<>();
                videoId.put("id", movie.getId());
                movieData.put("video", videoId);
                movieList.add(movieData);
            }
        }

        return movieList;
    }

    @Override
    public void saveUnsubscribe(ShortUnsubscribe shortUnsubscribe) {
        String userId = String.valueOf(shortUnsubscribe.getUserId());
        if (StringUtils.isNotEmpty(userId)) {
            // 处理uid前缀
            if (userId.length() > 4 ) {
                userId = userId.substring(4);
            }
        }
        shortUnsubscribe.setUserId(Long.valueOf(userId));
        shortUnsubscribe.setStatus("0");
        shortUnsubscribeService.insertShortUnsubscribe(shortUnsubscribe);
    }

    @Override
    public List<Map<String, Object>> getBannerListByAppIdAndLanguageCode(Integer appId, String languageCode, String ifApp) {
        List<Map<String, Object>> data = new ArrayList<>();

        // 查询banner信息
        List<ShortBanner> shortBannerList = shortBannerService.getBannerListByAppIdAndLanguage(appId, languageCode,ifApp);
        //如果为空 则取底档数据
        for (ShortBanner banner : shortBannerList) {
            Map<String, Object> bannerData = new HashMap<>();
            bannerData.put("id", banner.getId());
            bannerData.put("app", banner.getAppId());
            bannerData.put("image", banner.getImage());
            bannerData.put("movie", banner.getMovieId());
            bannerData.put("addtime", banner.getAddtime());
            bannerData.put("updatetime", banner.getUpdateBy());

            bannerData.put("movieName",banner.getMovieName());
            bannerData.put("movieDescription",banner.getMovieDescription());
            bannerData.put("movieNum",banner.getMovieNum());

            data.add(bannerData);
        }

        return data;
    }


}
