package com.ruoyi.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.mapper.ShortUserCollectAndHistoryMapper;
import com.ruoyi.domain.ShortUserCollectAndHistory;
import com.ruoyi.service.IShortUserCollectAndHistoryService;

/**
 * 【请填写功能名称】Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-25
 */
@Service
public class ShortUserCollectAndHistoryServiceImpl implements IShortUserCollectAndHistoryService 
{
    @Autowired
    private ShortUserCollectAndHistoryMapper shortUserCollectAndHistoryMapper;

    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public ShortUserCollectAndHistory selectShortUserCollectAndHistoryById(Long id)
    {
        return shortUserCollectAndHistoryMapper.selectShortUserCollectAndHistoryById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param shortUserCollectAndHistory 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<ShortUserCollectAndHistory> selectShortUserCollectAndHistoryList(ShortUserCollectAndHistory shortUserCollectAndHistory)
    {
        return shortUserCollectAndHistoryMapper.selectShortUserCollectAndHistoryList(shortUserCollectAndHistory);
    }

    /**
     * 新增【请填写功能名称】
     * 
     * @param shortUserCollectAndHistory 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertShortUserCollectAndHistory(ShortUserCollectAndHistory shortUserCollectAndHistory)
    {
        shortUserCollectAndHistory.setCreateTime(DateUtils.getNowDate());
        return shortUserCollectAndHistoryMapper.insertShortUserCollectAndHistory(shortUserCollectAndHistory);
    }

    /**
     * 修改【请填写功能名称】
     * 
     * @param shortUserCollectAndHistory 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateShortUserCollectAndHistory(ShortUserCollectAndHistory shortUserCollectAndHistory)
    {
        shortUserCollectAndHistory.setUpdateTime(DateUtils.getNowDate());
        return shortUserCollectAndHistoryMapper.updateShortUserCollectAndHistory(shortUserCollectAndHistory);
    }

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteShortUserCollectAndHistoryByIds(Long[] ids)
    {
        return shortUserCollectAndHistoryMapper.deleteShortUserCollectAndHistoryByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteShortUserCollectAndHistoryById(Long id)
    {
        return shortUserCollectAndHistoryMapper.deleteShortUserCollectAndHistoryById(id);
    }
}
