<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.ShortUserLinkMovieBindMapper">

    <resultMap type="ShortUserLinkMovieBind" id="ShortUserLinkMovieBindResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="linkId"    column="link_id"    />
        <result property="movieId"    column="movie_id"    />
        <result property="logTime"    column="log_time"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectShortUserLinkMovieBindVo">
        select id, user_id, link_id, movie_id, log_time, status, create_time, create_by, update_time, update_by, remark from short_user_link_movie_bind
    </sql>

    <select id="selectShortUserLinkMovieBindList" parameterType="ShortUserLinkMovieBind" resultMap="ShortUserLinkMovieBindResult">
        <include refid="selectShortUserLinkMovieBindVo"/>
        <where>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="linkId != null "> and link_id = #{linkId}</if>
            <if test="movieId != null "> and movie_id = #{movieId}</if>
            <if test="logTime != null "> and log_time = #{logTime}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>

    <select id="selectShortUserLinkMovieBindById" parameterType="Long" resultMap="ShortUserLinkMovieBindResult">
        <include refid="selectShortUserLinkMovieBindVo"/>
        where id = #{id}
    </select>

    <insert id="insertShortUserLinkMovieBind" parameterType="ShortUserLinkMovieBind" useGeneratedKeys="true" keyProperty="id">
        insert into short_user_link_movie_bind
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="linkId != null">link_id,</if>
            <if test="movieId != null">movie_id,</if>
            <if test="logTime != null">log_time,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="linkId != null">#{linkId},</if>
            <if test="movieId != null">#{movieId},</if>
            <if test="logTime != null">#{logTime},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateShortUserLinkMovieBind" parameterType="ShortUserLinkMovieBind">
        update short_user_link_movie_bind
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="linkId != null">link_id = #{linkId},</if>
            <if test="movieId != null">movie_id = #{movieId},</if>
            <if test="logTime != null">log_time = #{logTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteShortUserLinkMovieBindById" parameterType="Long">
        delete from short_user_link_movie_bind where id = #{id}
    </delete>

    <delete id="deleteShortUserLinkMovieBindByIds" parameterType="String">
        delete from short_user_link_movie_bind where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>