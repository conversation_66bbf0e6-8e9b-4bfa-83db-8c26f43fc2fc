package com.ruoyi.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageInfo;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.domain.*;
import com.ruoyi.mapper.*;
import com.ruoyi.service.IShortAppLanguageService;
import com.ruoyi.service.IShortMovieService;
import lombok.extern.slf4j.Slf4j;
import org.apache.tika.Tika;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.model.PutObjectResponse;
import software.amazon.awssdk.services.s3.model.S3Exception;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 影片Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
@Slf4j
@Service
public class ShortMovieServiceImpl implements IShortMovieService {
    @Autowired
    private ShortMovieMapper shortMovieMapper;

    @Autowired
    private ShortVideoMapper shortVideoMapper;

    @Autowired
    private ShortVideoChannelCoinMapper shortVideoChannelCoinMapper;

    @Autowired
    private ShortMovieAppMapper shortMovieAppMapper;

    @Autowired
    private IShortMovieService shortMovieService;

    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Autowired
    private ChekMovieM3u8FileService chekMovieM3u8FileService;

    @Value("${cloud.r2.bucket-name}")
    private String bucketName;

    @Value("${cloud.r2.file-prefix}")
    private String filePrefix;

    @Autowired
    private S3Client s3Client;
    @Autowired
    private ShortMovieI18nMapper shortMovieI18nMapper;
    @Autowired
    private IShortAppLanguageService iShortAppLanguageService;

    @Autowired
    private ShortMovieRecMapper shortMovieRecMapper;


    @Override
    public String addMovie(ShortMovie shortMovie) {
        //根据 剧名检查短剧是否重复，已存在返回已存在
        ShortMovie sm = shortMovieMapper.selectShortMovieByName(shortMovie.getName());
        if (sm != null) {
            return "exist";
        }
        shortMovie.setCreateTime(DateUtils.getNowDate());
        shortMovie.setStatus(null != shortMovie.getStatus() ? shortMovie.getStatus():"1");
        if (shortMovieMapper.insertShortMovie(shortMovie) > 0) {
            return "success";
        }
        return "failed";
    }

    /**
     * 查询影片
     *
     * @param id 影片主键
     * @return 影片
     */
    @Override
    public ShortMovie selectShortMovieById(Long id) {
        return shortMovieMapper.selectShortMovieById(id);
    }

    /**
     * 查询影片列表
     *
     * @param shortMovie 影片
     * @return 影片
     */
    @Override
    public List<ShortMovie> selectShortMovieList(ShortMovie shortMovie) {
        List<ShortMovie> list = shortMovieMapper.selectShortMovieList(shortMovie);


        long sumTotal = 0;

        sumTotal = new PageInfo(list).getTotal();
        List<Long> movieIds = list.stream().map(ShortMovie::getId).collect(Collectors.toList());
        if (!movieIds.isEmpty()) {
            List<ShortMovieApp> shortMovieAppList = shortMovieAppMapper.selectByMovieIds(movieIds);
            updateUsersByName(list, shortMovieAppList);

            List<ShortVideo> videos = shortVideoMapper.selectByMovieIds(movieIds);
            updatecount(list, videos);

            List<ShortMovieI18n> shortMovieI18ns = shortMovieI18nMapper.selectByMovieIds(movieIds);
            updateI18n(list, shortMovieI18ns);
        }

        if (null != shortMovie.getAppIds()) {
            List<Integer> midList = new ArrayList<>();
            for (Long id : movieIds) {
                midList.add(Integer.valueOf(Math.toIntExact(id)));
            }
            //查询归属
            List<ShortMovie> serviceMoviesAppList = shortMovieService.findByIdInAndAppIds1(midList, shortMovie.getAppIds(), shortMovie.getCreateBy());

            list.addAll(serviceMoviesAppList);
            list = list.stream()
                    .collect(Collectors.toMap(
                            ShortMovie::getId,
                            user -> user,
                            (existing, replacement) -> existing  // 保留先出现的元素
                    ))
                    .values()
                    .stream()
                    .sorted(Comparator.comparing(ShortMovie::getId).reversed())  // 按ID倒序
                    .collect(Collectors.toList());
        }
        //查询短剧的推荐版位情况
        if(CollectionUtil.isNotEmpty(list)){
            List<Long> movieIdList = list.stream().map(ShortMovie::getId).distinct().collect(Collectors.toList());
            List<ShortMovieRec> movieRec = shortMovieRecMapper.selectByMovieIds(movieIdList);
            Map<Long, List<ShortMovieRec>> recMap = movieRec.stream().collect(Collectors.groupingBy(ShortMovieRec::getMovieId));
            list.forEach(movie ->{
                List<ShortMovieRec> shortMovieRecs = recMap.get(movie.getId());
                if(CollectionUtil.isNotEmpty(shortMovieRecs)){
                    List<String> collect = shortMovieRecs.stream().filter(item -> item.getStatus().equals("0"))
                            .map(ShortMovieRec::getRec).distinct().sorted().collect(Collectors.toList());
                    movie.setRecList(collect);
                }
            });
        }

        if (!list.isEmpty()) {
            list.get(0).setSumTotal(sumTotal);
        }
        return list;
    }

    private void updateI18n(List<ShortMovie> list, List<ShortMovieI18n> shortMovieI18ns) {

        Map<Long, List<String>> movieIdToLanguageCodes = shortMovieI18ns.stream()
                .collect(Collectors.groupingBy(
                        ShortMovieI18n::getMovieId,
                        Collectors.mapping(
                                ShortMovieI18n::getLanguageCode,
                                Collectors.toList()
                        )
                ));
        Map<String, List<ShortMovieI18n>> movieTranslateCodeMap = shortMovieI18ns.stream().collect(Collectors.groupingBy(shortMovieI18n -> shortMovieI18n.getMovieId() + shortMovieI18n.getLanguageCode()));
        for (ShortMovie shortMovie : list) {
            List<String> languageCodes = movieIdToLanguageCodes.getOrDefault(shortMovie.getId(), Collections.emptyList());
            if (CollectionUtil.isNotEmpty(languageCodes)) {
                List<ShortAppLanguage> shortAppLanguages = iShortAppLanguageService.queryByCodes(languageCodes);
                for (ShortAppLanguage shortAppLanguage : shortAppLanguages) {
                    if(movieTranslateCodeMap.containsKey(shortMovie.getId() + shortAppLanguage.getCode())){
                        List<ShortMovieI18n> shortMovieI18nsTmpList = movieTranslateCodeMap.get(shortMovie.getId() + shortAppLanguage.getCode());
                        String status = shortMovieI18nsTmpList.iterator().next().getStatus();
                        shortAppLanguage.setTranslateStatus(status);
                    }else {
                        shortAppLanguage.setTranslateStatus("0");
                    }
                }
                shortMovie.setLanguages(shortAppLanguages);
            } else {
                shortMovie.setLanguages(Collections.emptyList());
            }
        }
    }

    public static void updateUsersByName(List<ShortMovie> users, List<ShortMovieApp> userDTOs) {
        // 将UserDTO列表转换为以id为key的Map
        Map<Long, ShortMovieApp> dtoMap = userDTOs.stream()
                .collect(Collectors.toMap(ShortMovieApp::getId, Function.identity()));

        // 遍历User列表进行匹配和赋值
        users.forEach(user -> {
            ShortMovieApp matchedDTO = dtoMap.get(user.getId());
            if (matchedDTO != null) {
                if (null == user.getAppName())
                    user.setAppName(matchedDTO.getName());
                else
                    user.setAppName(user.getAppName() + "," + matchedDTO.getName());
            }
        });
    }

    public static void updatecount(List<ShortMovie> users, List<ShortVideo> userDTOs) {
        // 将UserDTO列表转换为以id为key的Map
        Map<Long, ShortVideo> dtoMap = userDTOs.stream()
                .collect(Collectors.toMap(ShortVideo::getMovieId, Function.identity()));

        // 遍历User列表进行匹配和赋值
        users.forEach(user -> {
            ShortVideo matchedDTO = dtoMap.get(user.getId());
            if (matchedDTO != null) {
                user.setSumVideoCount(matchedDTO.getNum());
            }
        });
    }

    /**
     * 新增影片
     *
     * @param shortMovie 影片
     * @return 结果
     */
    @Override
    public int insertShortMovie(ShortMovie shortMovie) {
        shortMovie.setCreateTime(DateUtils.getNowDate());
        shortMovie.setCreateBy(SecurityUtils.getUserId() + "");
        shortMovie.setStatus("1");
        return shortMovieMapper.insertShortMovie(shortMovie);
    }

    /**
     * 修改影片
     *
     * @param shortMovie 影片
     * @return 结果
     */
    @Override
    public int updateShortMovie(ShortMovie shortMovie) {
        shortMovie.setUpdatetime(DateUtils.getNowDate());
        shortMovie.setUpdateBy(SecurityUtils.getUserId() + "");
        shortMovie.setSource("Facebook");
//
        new Thread(() -> {
            String res = updateIconMovie(shortMovie.getIcon());
            if (StringUtils.isNotEmpty(res)) {
                shortMovieService.updateIconById(shortMovie.getId(), res);

            }
            String iconNoWord = updateIconMovie(shortMovie.getIconNoWord());
            if (StringUtils.isNotEmpty(iconNoWord)) {
                shortMovieService.updateIconNoWordById(shortMovie.getId(), iconNoWord);
            }
        }).start();

        new Thread(() -> {
                List<ShortVideo> shortVideoList = shortVideoMapper.findByMovieId(shortMovie.getId());
                for (ShortVideo shortVideo : shortVideoList) {
                    String respic = updateIconMovie(shortVideo.getPic());
                    if (StringUtils.isNotEmpty(respic)) {
                        shortVideoMapper.updatePicById(shortVideo.getId(), respic);
                    }
                }

        }).start();


        shortVideoMapper.updateIsVipCoinByMovieId(shortMovie.getId(), false, 0, 0);
        shortVideoMapper.updateIsVipCoinByMovieId(shortMovie.getId(), true, 80, 1);

        shortVideoMapper.updateStateByMovieId(shortMovie.getId(), shortMovie.getState());
        shortVideoChannelCoinMapper.updateStateByMovieId(shortMovie.getId(), shortMovie.getState());

        if(null !=shortMovie.getState() && shortMovie.getState().equals("1")){
            Date releasetime = shortMovieMapper.selectByReleasetime(shortMovie.getId());
            if (releasetime == null)
                shortMovie.setReleasetime(DateUtils.getNowDate());
        }
        return shortMovieMapper.updateShortMovie1(shortMovie);
    }

    private String updateIconMovie(String icon) {
        String responseUrl = null;
        try {

            MultipartFile file = getMultipartFileFromUrl(icon);

            // 现在可以使用multipartFile了
            //objectKey 对象键（文件在 S3 中的路径和名称）
            String objectKey = "uploads/" + UUID.randomUUID().toString() + "_" + file.getOriginalFilename();

            // 检查图片格式
            String imageType = detectImageType(file);
            if (imageType == null) {
                throw new Exception("仅支持JPG/PNG/JPEG/WEBP/GIF格式");
            }

            // 2. 处理图片
            byte[] processedImage = ImageProcessor.processImage(file.getBytes(), imageType);

            try {
                PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                        .bucket(bucketName)
                        .key(objectKey)
                        .contentType(imageType)
                        .contentDisposition("inline")            // ✅ 指定为 inline，防止下载
                        .build();

                PutObjectResponse response = s3Client.putObject(
                        putObjectRequest,
                        RequestBody.fromBytes(processedImage)
                );
                //判断文件是否上传成功
                if (response.sdkHttpResponse().isSuccessful()) {
                    log.debug("文件上传成功，构建访问链接");
                    responseUrl = filePrefix + objectKey;
                    System.out.println("responseUrl============" + responseUrl);
                }
                return responseUrl;
            } catch (S3Exception e) {
//                        throw new RuntimeException("Failed to read the file", e);
            }

        } catch (Exception e) {
//            return "";
        }
        return responseUrl;
    }

    public static MultipartFile getMultipartFileFromUrl(String imageUrl) throws Exception {
        try {
            URL url = new URL(imageUrl);
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

            // 添加User-Agent等请求头避免403
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestProperty("User-Agent", "Mozilla/5.0");

            try (InputStream inputStream = connection.getInputStream()) {
                byte[] buffer = new byte[4096];
                int bytesRead;

                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
            }

            // 获取文件名
            String fileName = imageUrl.substring(imageUrl.lastIndexOf('/') + 1);

            return new MockMultipartFile(
                    "file",
                    fileName,
                    URLConnection.guessContentTypeFromName(fileName),
                    outputStream.toByteArray()
            );

        } catch (Exception e) {
            throw new RuntimeException("获取远程图片失败: " + e.getMessage());
        }
    }

    public String detectImageType(MultipartFile file) throws IOException {
        Tika tika = new Tika();
        String mimeType = tika.detect(file.getInputStream());

        switch (mimeType) {
            case "image/jpeg":
                return "jpg";
            case "image/png":
                return "png";
            case "image/gif":
                return "gif";
            case "image/webp":
                return "webp";
            default:
                return null;
        }
    }

    /**
     * 批量删除影片
     *
     * @param ids 需要删除的影片主键
     * @return 结果
     */
    @Override
    public int deleteShortMovieByIds(Long[] ids) {

        shortMovieMapper.updateNullById(ids[0]);
        shortVideoMapper.deleteShortVideoByMovieIds(ids);
        shortVideoMapper.deleteShortVideoChannelCoinByIds(ids);

        return 1;
    }

    /**
     * 删除影片信息
     *
     * @param id 影片主键
     * @return 结果
     */
    @Override
    public int deleteShortMovieById(Long id) {
        return shortMovieMapper.deleteShortMovieById(id);
    }

    @Override
    public List<ShortMovie> getMovies(Integer nid, String type,int ifApp,Date exTime) {
        return shortMovieMapper.getMovies(nid, type,ifApp,exTime);
    }

    @Override
    public List<ShortMovie> findBySourceAndStateAndIdNotIn(String source, String state, List<Integer> recIdList, int limitNum) {
        return shortMovieMapper.findBySourceAndStateAndIdNotIn(source, state, recIdList, limitNum);
    }

    @Override
    public List<ShortMovie> findByIdInAndAppId(List<Integer> midList, Integer nid) {
        return shortMovieMapper.findByIdInAndAppId(midList, nid);
    }

    @Override
    public ShortMovie findByName(String name) {
        return shortMovieMapper.findByName(name);
    }

    @Override
    public Long addAppMovie(ShortMovie movie) {
        movie.setStatus(null != movie.getStatus() ? movie.getStatus() : "1");
        return Long.valueOf(shortMovieMapper.insertShortMovie(movie));
    }

    @Override
    @Transactional
    public String addApps(ShortMovie shortMovie) {
        if (!shortMovie.getAppList().isEmpty()) {
            for (Long appId : shortMovie.getAppList()) {
                ShortMovieApp shortMovieApp = new ShortMovieApp();
                shortMovieApp.setMovieId(shortMovie.getId());
                shortMovieApp.setAppId(appId);
                List<ShortMovieApp> shortMovieApps = shortMovieAppMapper.selectShortMovieAppList(shortMovieApp);
                if (CollectionUtil.isNotEmpty(shortMovieApps))
                    return "failed";
                shortMovieApp.setStatus(String.valueOf(0));
                shortMovieApp.setCreateTime(DateUtils.getNowDate());
                shortMovieApp.setUpdateTime(DateUtils.getNowDate());
                shortMovieApp.setCreateBy(SecurityUtils.getUserId() + "");
                if (shortMovieAppMapper.insertShortMovieApp(shortMovieApp) == 0)
                    return "failed";
            }
        }
        return "success";
    }

    @Override
    public List<ShortMovie> getMoviesByApps(Integer nid, String type,int ifApp,Date exTime) {
        return shortMovieMapper.getMoviesByApps(nid, type,ifApp,exTime);
    }

    @Override
    public List<ShortMovie> findByIdInAndAppIds(List<Integer> midList, Integer nid) {
        return shortMovieMapper.findByIdInAndAppIds(midList, nid);
    }

    @Override
    public List<ShortMovie> findByIdInAndAppIds1(List<Integer> midList, List<Long> nids, String createBy) {
        return shortMovieMapper.findByIdInAndAppIds1(midList, nids, createBy);
    }

    @Override
    public void checkMovieFileM3u8(Long movieId) {
        //先执行更新短剧状态
        ShortMovie shortMovie = shortMovieMapper.selectShortMovieById(movieId);
        if (null != shortMovie) {
            //设置检测状态为检测中
            shortMovie.setCheckState(3);
            shortMovie.setCheckLog("");
            shortMovieMapper.updateShortMovie(shortMovie);
        }
        chekMovieM3u8FileService.check(movieId);
    }

    @Override
    public void checkAllMovieFileM3u8() {
       /* List<ShortMovie> shortMovies = shortMovieMapper.selectShortMovieList(new ShortMovie()).stream()
                .filter(shortMovie -> null == shortMovie.getCheckState() || shortMovie.getCheckState() == 0).collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(shortMovies)){
            for (ShortMovie shortMovie : shortMovies) {
                checkMovieFileM3u8(shortMovie.getId());
            }
        }*/
    }

    @Override
    public void updateIconById(Long id, String res) {
        shortMovieMapper.updateIconById(id, res);
    }

    @Override
    public void updateIconNoWordById(Long id, String res) {
        shortMovieMapper.updateIconNoWordById(id, res);
    }

    @Override
    public List<ShortMovie> getAll() {
        return shortMovieMapper.getAll();
    }

    @Override
    public List<ShortMovie> getByRand4(Long appId) {
        return shortMovieMapper.getByRand4(appId);
    }

    @Override
    public List<ShortMovie> getByRand4I18n(Long appId,String language) {
        return shortMovieMapper.getByRand4I18n(appId,language);
    }

    @Override
    public List<ShortMovie> getByRand10(Long appId,int limit) {
        return shortMovieMapper.getByRand10(appId,limit);
    }

    @Override
    public List<ShortMovie> getMoviesI18n(Integer nid, String type, int ifApp, Date exTime,String languageCode) {
        return shortMovieMapper.getMoviesI18n(nid, type,ifApp,exTime,languageCode);
    }

    @Override
    public List<ShortMovie> getMoviesByAppsI18n(Integer nid, String type, int ifApp, Date exTime, String languageCode) {
        return shortMovieMapper.getMoviesByAppsI18n(nid, type,ifApp,exTime,languageCode);
    }

    @Override
    public List<ShortMovie> selectShortMovieListByIds(List<Long> movieIdList) {
        return shortMovieMapper.selectShortMovieByIds(movieIdList);
    }

    @Override
    public ShortMovie getMovie1(Long appId) {
        return shortMovieMapper.getMovie1(appId);
    }

}
