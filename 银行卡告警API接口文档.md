# 银行卡告警管理API接口文档

## 基础信息
- **基路径**: `/bankcard/alert`
- **请求格式**: JSON
- **响应格式**: JSON
- **认证方式**: <PERSON><PERSON> (登录后获取)

---

## 1. 告警列表查询

### 接口信息
- **URL**: `POST /bankcard/alert/list`
- **功能**: 分页查询银行卡告警列表
- **权限**: 需要登录

### 请求参数
```json
{
  "pageNum": 1,          // 页码，默认1
  "pageSize": 10,        // 每页大小，默认10
  "cardNumber": "",      // 银行卡号（模糊查询，可选）
  "userId": null,        // 用户ID（可选）
  "alertType": "",       // 告警类型：week_overuse/month_overuse/multi_user（可选）
  "processedFlag": null  // 处理状态：0=未处理，1=已处理（可选）
}
```

### 响应示例
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "id": 1,
      "cardNumber": "************1234",
      "userId": 1001,
      "alertType": "week_overuse",
      "alertDetail": "会员第2周超限：实际次数=3(阈值=1)，实际金额=59.97(阈值=40.00)",
      "processedFlag": 0,
      "processedAt": null,
      "processedBy": null,
      "processNote": null,
      "createTime": "2025-09-17 12:30:00"
    }
  ],
  "total": 25
}
```

---

## 2. 未处理告警列表

### 接口信息
- **URL**: `POST /bankcard/alert/unprocessed`
- **功能**: 获取未处理的告警列表
- **权限**: 需要登录

### 请求参数
```json
{
  "limit": 100  // 返回数量限制，默认100
}
```

### 响应示例
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "id": 2,
      "cardNumber": "************9876",
      "userId": 1002,
      "alertType": "multi_user",
      "alertDetail": "银行卡 ************9876 被 3 个用户使用：[1002,1003,1004]",
      "processedFlag": 0,
      "createTime": "2025-09-17 15:45:00"
    }
  ],
  "total": 15
}
```

---

## 3. 处理单个告警

### 接口信息
- **URL**: `POST /bankcard/alert/process/{id}`
- **功能**: 标记单个告警为已处理
- **权限**: 需要登录

### 请求参数
- **路径参数**: `id` - 告警ID
- **表单参数**: `note` - 处理备注（可选）

### 请求示例
```
POST /bankcard/alert/process/1?note=已人工确认，属于正常使用
```

### 响应示例
```json
{
  "code": 200,
  "msg": "告警处理成功"
}
```

---

## 4. 批量处理告警

### 接口信息
- **URL**: `POST /bankcard/alert/batchProcess`
- **功能**: 批量标记告警为已处理
- **权限**: 需要登录

### 请求参数
```
POST /bankcard/alert/batchProcess
Content-Type: application/x-www-form-urlencoded

ids=1,2,3&note=批量处理：经确认为正常使用
```

### 响应示例
```json
{
  "code": 200,
  "msg": "批量处理告警成功"
}
```

---

## 5. 银行卡告警历史

### 接口信息
- **URL**: `GET /bankcard/alert/cardHistory/{cardNumber}`
- **功能**: 获取指定银行卡的告警历史
- **权限**: 需要登录

### 请求参数
- **路径参数**: `cardNumber` - 银行卡号
- **查询参数**: `limit` - 返回数量限制，默认50

### 请求示例
```
GET /bankcard/alert/cardHistory/************1234?limit=20
```

### 响应示例
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "id": 5,
      "cardNumber": "************1234",
      "userId": 1001,
      "alertType": "week_overuse",
      "alertDetail": "会员第1周超限：实际次数=2(阈值=1)，实际金额=39.98(阈值=40.00)",
      "processedFlag": 1,
      "processedAt": "2025-09-17 16:00:00",
      "processedBy": 2001,
      "processNote": "已确认为正常续费",
      "createTime": "2025-09-17 10:15:00"
    }
  ],
  "total": 8
}
```

---

## 6. 用户告警历史

### 接口信息
- **URL**: `GET /bankcard/alert/userHistory/{userId}`
- **功能**: 获取指定用户触发的告警
- **权限**: 需要登录

### 请求参数
- **路径参数**: `userId` - 用户ID
- **查询参数**: `limit` - 返回数量限制，默认50

### 请求示例
```
GET /bankcard/alert/userHistory/1001?limit=30
```

---

## 7. 告警统计信息

### 接口信息
- **URL**: `GET /bankcard/alert/statistics`
- **功能**: 获取告警统计数据
- **权限**: 需要登录

### 响应示例
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "alertTypeStats": [
      ["week_overuse", 15],
      ["month_overuse", 8],
      ["multi_user", 12]
    ],
    "recentAlertCount": 5,      // 最近24小时告警数量
    "unprocessedCount": 23      // 未处理告警数量
  }
}
```

---

## 8. 告警详情查询

### 接口信息
- **URL**: `GET /bankcard/alert/{id}`
- **功能**: 根据ID查询告警详细信息
- **权限**: 需要登录

### 请求示例
```
GET /bankcard/alert/1
```

### 响应示例
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "id": 1,
    "cardNumber": "************1234",
    "userId": 1001,
    "alertType": "week_overuse",
    "alertDetail": "会员第2周超限：实际次数=3(阈值=1)，实际金额=59.97(阈值=40.00)",
    "processedFlag": 0,
    "processedAt": null,
    "processedBy": null,
    "processNote": null,
    "createTime": "2025-09-17 12:30:00"
  }
}
```

---

## 9. 导出告警数据

### 接口信息
- **URL**: `POST /bankcard/alert/export`
- **功能**: 导出告警数据为Excel文件
- **权限**: 需要登录

### 请求参数
```json
{
  "cardNumber": "",      // 银行卡号筛选（可选）
  "alertType": "",       // 告警类型筛选（可选）
  "processedFlag": null  // 处理状态筛选（可选）
}
```

### 响应
- **Content-Type**: `application/vnd.ms-excel`
- **文件名**: `银行卡告警数据.xlsx`

---

## 10. 清理过期告警

### 接口信息
- **URL**: `POST /bankcard/alert/cleanExpired`
- **功能**: 清理30天前的过期告警记录
- **权限**: 需要管理员权限

### 响应示例
```json
{
  "code": 200,
  "msg": "清理完成，删除 156 条过期告警"
}
```

---

## 告警类型说明

| 类型 | 描述 |
|------|------|
| `week_overuse` | 会员周累计超限：单周内订阅次数>1或金额>40美金 |
| `month_overuse` | 会员月累计超限：单月内订阅次数>5或金额>100美金 |
| `multi_user` | 多用户共用：同一银行卡被多个用户使用 |

## 处理状态说明

| 状态 | 描述 |
|------|------|
| `0` | 未处理 |
| `1` | 已处理 |

## 错误码说明

| 错误码 | 描述 |
|--------|------|
| `200` | 成功 |
| `400` | 请求参数错误 |
| `401` | 未授权，需要登录 |
| `403` | 权限不足 |
| `500` | 服务器内部错误 |

---
