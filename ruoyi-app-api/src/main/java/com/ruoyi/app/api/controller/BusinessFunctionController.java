package com.ruoyi.app.api.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.ruoyi.app.api.annotation.TokenRequired;
import com.ruoyi.app.api.dto.EmailLoginDTO;
import com.ruoyi.app.api.service.BusinessFunctionApiService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * 业务功能相关接口
 */
@Api(tags = "业务功能相关接口")
@RestController
@Slf4j
@RequestMapping("/api")
public class BusinessFunctionController {

    @Autowired
    private BusinessFunctionApiService businessFunctionApiService;


    /**
     * 添加Feedback接口
     */
    @ApiOperation(value = "添加Feedback数据", notes = "添加用户的反馈信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "认证token", paramType = "header", required = true),
            @ApiImplicitParam(name = "nid", value = "app_id", paramType = "header", required = true)
    })
    @TokenRequired
    @PostMapping("/feedback/")
    public Map<String, Object> addFeedback(
            @RequestBody Map<String, Object> feedbackData,
            HttpServletRequest request) {

        String token = request.getHeader("Authorization");
        String appId = request.getHeader("nid");

        return businessFunctionApiService.addFeedback(token, appId, feedbackData);
    }

    /**
     * 获取每日签到赠送金币的活动接口
     */
    @ApiOperation(value = "获取每日签到赠送金币的活动", notes = "获取每日签到赠送金币的活动信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "认证token", paramType = "header", required = false),
            @ApiImplicitParam(name = "nid", value = "app_id", paramType = "header", required = true),
            @ApiImplicitParam(name = "activity", value = "活动ID", paramType = "query", required = true),
            @ApiImplicitParam(name = "uid", value = "用户ID", paramType = "query", required = false)
    })
    @GetMapping("/daily_sign/")
    public Map<String, Object> getDailySign(
            @RequestParam Integer activity,
            @RequestParam(required = false) String uid,
            HttpServletRequest request) {

        String token = request.getHeader("Authorization");
        String appId = request.getHeader("nid");

        return businessFunctionApiService.getDailySign(token, appId, uid, activity);
    }

    /**
     * 获取应用版本信息接口
     */
    @ApiOperation(value = "获取应用版本信息", notes = "获取应用程序的开关状态数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "NID", value = "app_id", paramType = "header", required = true),
            @ApiImplicitParam(name = "TYPE", value = "type", paramType = "header", required = true)
    })
    @PostMapping("/get_version/")
    public Map<String, Object> getVersion(
            @RequestBody Map<String, Object> versionData,
            HttpServletRequest request) {
//        System.out.println("Request Method: " + request.getMethod()+","+ DateUtils.getNowDate());
        log.info("获取应用版本信息: appId={}, type={}", request.getMethod() + "," + DateUtils.getNowDate());
        String appId = request.getHeader("NID");
        String type = request.getHeader("TYPE");

        return businessFunctionApiService.getVersion(appId, type, versionData);
    }

    /**
     * 存储设备信息接口
     */
    @ApiOperation(value = "存储设备信息", notes = "存储设备信息和深链接到Redis")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "platform", value = "平台", paramType = "query", required = true),
            @ApiImplicitParam(name = "screenWidth", value = "屏幕宽度", paramType = "query", required = true),
            @ApiImplicitParam(name = "screenHeight", value = "屏幕高度", paramType = "query", required = true),
            @ApiImplicitParam(name = "systemTimeZone", value = "系统时区", paramType = "query", required = true),
            @ApiImplicitParam(name = "ip", value = "IP地址", paramType = "query", required = false),
            @ApiImplicitParam(name = "linkUrl", value = "深链地址", paramType = "query", required = true),
            @ApiImplicitParam(name = "nid", value = "应用ID", paramType = "header", required = false)
    })
    @GetMapping("/store_device_info/")
    public Map<String, Object> storeDeviceInfo(
            @RequestParam String platform,
            @RequestParam String screenWidth,
            @RequestParam String screenHeight,
            @RequestParam String systemTimeZone,
            @RequestParam(required = false) String ip,
            @RequestParam String linkUrl,
            HttpServletRequest request,
            HttpServletResponse response) {

        String appId = request.getHeader("nid");

        // 设置CORS响应头
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET, OPTIONS");
        response.setHeader("Access-Control-Allow-Headers", "Content-Type, NID");

        return businessFunctionApiService.storeDeviceInfo(
                platform, screenWidth, screenHeight, systemTimeZone, ip, linkUrl, appId);
    }

    /**
     * 存储设备信息接口的OPTIONS方法
     */
    @ApiOperation(value = "存储设备信息OPTIONS", notes = "存储设备信息预检请求")
    @RequestMapping(value = "/store_device_info/", method = RequestMethod.OPTIONS)
    public Map<String, Object> storeDeviceInfoOptions(HttpServletResponse response) {
        // 设置CORS响应头
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET, OPTIONS");
        response.setHeader("Access-Control-Allow-Headers", "Content-Type, NID");
        response.setHeader("Access-Control-Max-Age", "86400");  // 预检请求缓存24小时

        return new HashMap<>();
    }

    /**
     * 匹配设备信息接口
     */
    @ApiOperation(value = "匹配设备信息", notes = "匹配设备信息并返回深链接")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "platform", value = "平台", paramType = "query", required = true),
            @ApiImplicitParam(name = "screenWidth", value = "屏幕宽度", paramType = "query", required = true),
            @ApiImplicitParam(name = "screenHeight", value = "屏幕高度", paramType = "query", required = true),
            @ApiImplicitParam(name = "systemTimeZone", value = "系统时区", paramType = "query", required = true),
            @ApiImplicitParam(name = "ip", value = "IP地址", paramType = "query", required = false),
            @ApiImplicitParam(name = "nid", value = "应用ID", paramType = "header", required = false)
    })
    @GetMapping("/match_device_info/")
    public Map<String, Object> matchDeviceInfo(
            @RequestParam String platform,
            @RequestParam String screenWidth,
            @RequestParam String screenHeight,
            @RequestParam String systemTimeZone,
            @RequestParam(required = false) String ip,
            HttpServletRequest request,
            HttpServletResponse response) {

        String appId = request.getHeader("nid");

        // 设置CORS响应头
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET, OPTIONS");
        response.setHeader("Access-Control-Allow-Headers", "Content-Type, NID");

        return businessFunctionApiService.matchDeviceInfo(
                platform, screenWidth, screenHeight, systemTimeZone, ip, appId);
    }

    /**
     * 匹配设备信息接口的OPTIONS方法
     */
    @ApiOperation(value = "匹配设备信息OPTIONS", notes = "匹配设备信息预检请求")
    @RequestMapping(value = "/match_device_info/", method = RequestMethod.OPTIONS)
    public Map<String, Object> matchDeviceInfoOptions(HttpServletResponse response) {
        // 设置CORS响应头
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET, OPTIONS");
        response.setHeader("Access-Control-Allow-Headers", "Content-Type, NID");
        response.setHeader("Access-Control-Max-Age", "86400");  // 预检请求缓存24小时

        return new HashMap<>();
    }

    /**
     * 初始化H5用户接口
     */
    @ApiOperation(value = "初始化H5用户", notes = "初始化H5用户信息并返回用户ID")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "NID", value = "app_id", paramType = "header", required = true)
    })
    @PostMapping("/init_user/")
    public Map<String, Object> initUser(
            @RequestBody com.ruoyi.app.api.dto.InitUserDTO initUserDTO,
            HttpServletRequest request) {

        String appId = request.getHeader("NID");
        String emailFlag = request.getHeader("emailFlag") == null ? "false" : request.getHeader("emailFlag");
        initUserDTO.setPayByEmailFlag(Boolean.valueOf(emailFlag));
        return businessFunctionApiService.initUser(appId, initUserDTO);
    }

    /**
     * H5用户邮箱登录
     */
    @ApiOperation(value = "H5用户邮箱登录", notes = "H5用户邮箱登录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "NID", value = "app_id", paramType = "header", required = true)
    })
    @PostMapping("/email_login/")
    public Map<String, Object> emailLogin(@RequestBody EmailLoginDTO emailLoginDTO, HttpServletRequest request) throws JsonProcessingException {
        String appId = request.getHeader("NID");
        emailLoginDTO.setAppId(Long.valueOf(appId));

        String uid = request.getHeader("uid");
        if (StringUtils.isNotEmpty(uid)) {
            // 处理uid前缀
            if (uid.length() > 4) {
                uid = uid.substring(4);
            }
        } else {
            Map<String, Object> map = new HashMap<>();
            map.put("code", 405);
            map.put("msg", "Please refresh the page!");
        }
        emailLoginDTO.setUserId(Long.valueOf(uid));
        return businessFunctionApiService.emailLogin(emailLoginDTO);
    }

    /**
     * 获取用户深链关联电影接口
     */
    @ApiOperation(value = "获取用户深链关联电影", notes = "通过用户临时ID查询用户所有深链关联的电影")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "NID", value = "app_id", paramType = "header", required = true),
            @ApiImplicitParam(name = "hashId", value = "用户临时ID", paramType = "query", required = true)
    })
    @GetMapping("/get_link_videos/")
    public Map<String, Object> getLinkVideos(
            @RequestParam String hashId,
            HttpServletRequest request) {

        String appId = request.getHeader("NID");

        return businessFunctionApiService.getLinkVideos(appId, hashId);
    }

    /**
     * 根据linkId获取movieId接口
     */
    @ApiOperation(value = "根据linkId获取movieId", notes = "通过推广链接ID查询对应的影片ID")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "NID", value = "app_id", paramType = "header", required = false),
            @ApiImplicitParam(name = "linkId", value = "推广链接ID", paramType = "query", required = true)
    })
    @GetMapping("/get_movie_id_by_link/")
    public Map<String, Object> getMovieIdByLinkId(
            @RequestParam Long linkId,
            HttpServletRequest request) {

        String appId = request.getHeader("NID");
        //是否从邮件地址进来
        String emailFlag = request.getHeader("emailFlag") == null ? "false" : request.getHeader("emailFlag");
        String userId = request.getHeader("userId") == null ? "-1" : request.getHeader("userId");

        return businessFunctionApiService.getMovieIdByLinkId(appId, linkId, Boolean.parseBoolean(emailFlag), Long.valueOf(userId));
    }

    /**
     * 解锁用户深链短剧
     */
    @ApiOperation(value = "解锁用户深链短剧", notes = "解锁用户深链短剧")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uid", value = "userId", paramType = "header", required = true),
            @ApiImplicitParam(name = "Authorization", value = "认证token", paramType = "header", required = true)
    })
    @GetMapping("/unlock_link_movie")
    public AjaxResult unlockLinkMovie(HttpServletRequest request) {
        //获取userId
        String userId = request.getHeader("uid");
        if (StringUtils.isNotEmpty(userId)) {
            // 处理uid前缀
            if (userId.length() > 4) {
                userId = userId.substring(4);
            }
        }

        return businessFunctionApiService.unlockLinkMovieApi(Long.valueOf(userId));
    }

}