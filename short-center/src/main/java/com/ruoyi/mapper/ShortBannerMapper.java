package com.ruoyi.mapper;

import com.ruoyi.domain.ShortBanner;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 轮播图数据Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
public interface ShortBannerMapper {
    /**
     * 查询轮播图数据
     *
     * @param id 轮播图数据主键
     * @return 轮播图数据
     */
    public ShortBanner selectShortBannerById(Long id);

    /**
     * 查询轮播图数据列表
     *
     * @param shortBanner 轮播图数据
     * @return 轮播图数据集合
     */
    public List<ShortBanner> selectShortBannerList(ShortBanner shortBanner);

    /**
     * 新增轮播图数据
     *
     * @param shortBanner 轮播图数据
     * @return 结果
     */
    public int insertShortBanner(ShortBanner shortBanner);

    /**
     * 修改轮播图数据
     *
     * @param shortBanner 轮播图数据
     * @return 结果
     */
    public int updateShortBanner(ShortBanner shortBanner);

    /**
     * 删除轮播图数据
     *
     * @param id 轮播图数据主键
     * @return 结果
     */
    public int deleteShortBannerById(Long id);

    /**
     * 批量删除轮播图数据
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteShortBannerByIds(Long[] ids);

    List<ShortBanner> getBannerListByAppId(Integer appId);

    List<ShortBanner> getBannerListByAppIdAndLanguage(@Param("appId") Integer appId, @Param("languageCode") String languageCode);

    int insertShortBannerList(List<ShortBanner> list);

    void batchUpdateShortBanner(List<ShortBanner> list);

    int deleteByMovieId(@Param("movieId") Long movieId, @Param("appId") Long appId);
}
