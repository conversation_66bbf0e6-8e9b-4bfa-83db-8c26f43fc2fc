package com.ruoyi.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.domain.ShortUnsubscribe;
import com.ruoyi.mapper.ShortUnsubscribeMapper;
import com.ruoyi.service.IShortUnsubscribeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 退订Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-09-22
 */
@Service
public class ShortUnsubscribeServiceImpl implements IShortUnsubscribeService
{
    @Autowired
    private ShortUnsubscribeMapper shortUnsubscribeMapper;

    /**
     * 查询退订
     *
     * @param id 退订主键
     * @return 退订
     */
    @Override
    public ShortUnsubscribe selectShortUnsubscribeById(Long id)
    {
        return shortUnsubscribeMapper.selectShortUnsubscribeById(id);
    }

    /**
     * 查询退订列表
     *
     * @param shortUnsubscribe 退订
     * @return 退订
     */
    @Override
    public List<ShortUnsubscribe> selectShortUnsubscribeList(ShortUnsubscribe shortUnsubscribe)
    {
        return shortUnsubscribeMapper.selectShortUnsubscribeList(shortUnsubscribe);
    }

    /**
     * 新增退订
     *
     * @param shortUnsubscribe 退订
     * @return 结果
     */
    @Override
    public int insertShortUnsubscribe(ShortUnsubscribe shortUnsubscribe)
    {
        shortUnsubscribe.setCreateTime(DateUtils.getNowDate());
        return shortUnsubscribeMapper.insertShortUnsubscribe(shortUnsubscribe);
    }

    /**
     * 修改退订
     *
     * @param shortUnsubscribe 退订
     * @return 结果
     */
    @Override
    public int updateShortUnsubscribe(ShortUnsubscribe shortUnsubscribe)
    {
        shortUnsubscribe.setUpdateTime(DateUtils.getNowDate());
        return shortUnsubscribeMapper.updateShortUnsubscribe(shortUnsubscribe);
    }

    /**
     * 批量删除退订
     *
     * @param ids 需要删除的退订主键
     * @return 结果
     */
    @Override
    public int deleteShortUnsubscribeByIds(Long[] ids)
    {
        return shortUnsubscribeMapper.deleteShortUnsubscribeByIds(ids);
    }

    /**
     * 删除退订信息
     *
     * @param id 退订主键
     * @return 结果
     */
    @Override
    public int deleteShortUnsubscribeById(Long id)
    {
        return shortUnsubscribeMapper.deleteShortUnsubscribeById(id);
    }

    /**
     * 检查邮箱在指定应用中是否存在退订记录
     *
     * @param email 邮箱
     * @param appId 应用ID
     * @return 是否存在
     */
    @Override
    public boolean existsByEmailAndAppId(String email, Long appId) {
        if (email == null || appId == null) {
            return false;
        }

        ShortUnsubscribe query = new ShortUnsubscribe();
        query.setEmail(email);
        query.setAppId(appId);
        List<ShortUnsubscribe> list = shortUnsubscribeMapper.selectShortUnsubscribeList(query);
        return !list.isEmpty();
    }

    /**
     * 批量检查邮箱在指定应用中是否存在退订记录
     *
     * @param emails 邮箱列表
     * @param appId 应用ID
     * @return 存在的邮箱列表
     */
    @Override
    public List<String> batchExistsByEmailAndAppId(List<String> emails, Long appId) {
        if (emails == null || emails.isEmpty() || appId == null) {
            return new java.util.ArrayList<>();
        }

        List<String> existingEmails = new java.util.ArrayList<>();
        for (String email : emails) {
            if (existsByEmailAndAppId(email, appId)) {
                existingEmails.add(email);
            }
        }
        return existingEmails;
    }
}
